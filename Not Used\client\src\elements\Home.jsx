import React, { useState, useEffect } from 'react'
import axios from 'axios'
import { Link } from 'react-router-dom'

function Home() {
    const [students, setStudents] = useState([])
    const [loading, setLoading] = useState(true)
    const [deleted, setDeleted] = useState(true)



    useEffect(() => {
        if (deleted) {
            fetchStudents()
            setDeleted(false)
        }
    }, [deleted])


    useEffect(() => {
        fetchStudents()
    }, [])

    const fetchStudents = () => {
        axios.get('/users')
            .then((res) => {
                console.log('Students data:', res.data)
                setStudents(res.data)
                setLoading(false)
            })
            .catch((err) => {
                console.error('Error fetching students:', err)
                setLoading(false)
            })
    }

    if (loading) {
        return <div className="container mt-5">Loading...</div>
    }


    function handleDelete(id) {
        axios.delete(`/delete_user/${id}`)
            .then((res) => {
                setDeleted(true)
                console.log('Student deleted:', res.data)
                fetchStudents()
            })
            .catch((err) => {
                console.error('Error deleting student:', err)
            })
    }






    return (
        <div className="container mt-5">
            <div className="row">
                <div className="col-12">
                    <div className="d-flex justify-content-between align-items-center mb-4">
                        <h2>Student Management System</h2>
                        <Link to="/create" className="btn btn-primary">Add New Student</Link>
                    </div>

                    {students.length === 0 ? (
                        <div className="alert alert-info">
                            No students found. <Link to="/create">Add the first student</Link>
                        </div>
                    ) : (
                        <div className="table-responsive">
                            <table className="table table-striped table-bordered">
                                <thead className="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Age</th>
                                        <th>Gender</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {students.map((student) => (
                                        <tr key={student.id}>
                                            <td>{student.id}</td>
                                            <td>{student.name}</td>
                                            <td>{student.email}</td>
                                            <td>{student.age}</td>
                                            <td>{student.gender}</td>
                                            <td>
                                                <Link to={`/read/${student.id}`} className="btn btn-info btn-sm me-2">View</Link>
                                                <Link to={`/edit/${student.id}`} className="btn btn-warning btn-sm">Edit</Link>
                                                <button onClick={() => handleDelete(student.id)} className="btn btn-danger btn-sm ms-2">Delete</button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}

export default Home