import React, { useState, useEffect } from 'react'
import axios from 'axios'
import { Link,useParams } from 'react-router-dom'

function Read() {
    const [data, setStudents] = useState([]);
    const [loading, setLoading] = useState(true);
    const { id } = useParams();

    useEffect(() => {
        axios.get(`/get_student/${id}`)
            .then((res) => {
                console.log('Students data:', res.data);
                setStudents(res.data[0]); // Set to the first (and only) student object
                setLoading(false);
            })
            .catch((err) => {
                console.error('Error fetching students:', err);
                setLoading(false);
            });
    }, [id]);

    return (
        <div className="container mt-5">
            {loading ? (
                <p>Loading...</p>
            ) : (
                <div className="card mx-auto" style={{maxWidth: '400px'}}>
                    <div className="card-body">
                        <h4 className="card-title mb-4">Student Details</h4>
                        <ul className="list-group list-group-flush">
                            <li className="list-group-item"><strong>Name:</strong> {data.name}</li>
                            <li className="list-group-item"><strong>Email:</strong> {data.email}</li>
                            <li className="list-group-item"><strong>Gender:</strong> {data.gender}</li>
                            <li className="list-group-item"><strong>Age:</strong> {data.age}</li>
                        </ul>
                        <Link to="/" className="btn btn-primary mt-4 w-100">Back to Home</Link>
                    </div>
                </div>
            )}
        </div>
    );
}

export default Read