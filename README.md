# Simple Student Management Project

A full-stack web application for managing student records with React frontend and Node.js/Express backend.

## Prerequisites

- Node.js (v14 or higher)
- MySQL Server
- npm or yarn

## Setup Instructions

### 1. Database Setup

1. Make sure MySQL server is running on your machine
2. Create the database and table by running the SQL script:
   ```bash
   mysql -u root -p < server/setup-database.sql
   ```
   Or manually execute the SQL commands in `server/setup-database.sql`

### 2. Backend Setup

1. Navigate to the server directory:
   ```bash
   cd server
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Update database configuration in `server.js` if needed:
   - Host: localhost (default)
   - User: root (default)
   - Password: "" (empty by default)
   - Database: students

4. Start the server:
   ```bash
   npm run dev
   ```
   The server will run on http://localhost:5000

### 3. Frontend Setup

1. Navigate to the client directory:
   ```bash
   cd client
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the React development server:
   ```bash
   npm start
   ```
   The client will run on http://localhost:3000

## Features

- Add new student records
- View student list
- Edit student information
- Delete student records

## API Endpoints

- `GET /users` - Get all students
- `POST /add_user` - Add a new student

## Database Schema

The `student_details` table has the following structure:
- `id` (INT, AUTO_INCREMENT, PRIMARY KEY)
- `name` (VARCHAR(255), NOT NULL)
- `email` (VARCHAR(255), NOT NULL, UNIQUE)
- `age` (INT, NOT NULL)
- `gender` (VARCHAR(50), NOT NULL)
- `created_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP)

## Troubleshooting

1. **Database Connection Issues**: 
   - Ensure MySQL server is running
   - Check database credentials in `server/server.js`
   - Make sure the `students` database exists

2. **CORS Issues**: 
   - The server is configured with CORS enabled
   - Client proxy is set to `http://localhost:5000`

3. **Port Conflicts**: 
   - Backend runs on port 5000
   - Frontend runs on port 3000
   - Make sure these ports are available
