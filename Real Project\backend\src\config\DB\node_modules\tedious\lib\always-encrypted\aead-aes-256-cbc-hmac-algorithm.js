"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.algorithmName = exports.AeadAes256CbcHmac256Algorithm = void 0;
var _types = require("./types");
var _crypto = require("crypto");
var _aeadAes256CbcHmacEncryptionKey = require("./aead-aes-256-cbc-hmac-encryption-key");
// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.
// Copyright (c) 2019 Microsoft Corporation

const algorithmName = exports.algorithmName = 'AEAD_AES_256_CBC_HMAC_SHA256';
const algorithmVersion = 0x1;
const blockSizeInBytes = 16;
class AeadAes256CbcHmac256Algorithm {
  constructor(columnEncryptionKey, encryptionType) {
    this.keySizeInBytes = _aeadAes256CbcHmacEncryptionKey.keySize / 8;
    this.version = Buffer.from([algorithmVersion]);
    this.versionSize = Buffer.from([1]);
    this.minimumCipherTextLengthInBytesNoAuthenticationTag = 1 + blockSizeInBytes + blockSizeInBytes;
    this.minimumCipherTextLengthInBytesWithAuthenticationTag = this.minimumCipherTextLengthInBytesNoAuthenticationTag + this.keySizeInBytes;
    this.columnEncryptionkey = columnEncryptionKey;
    this.isDeterministic = encryptionType === _types.SQLServerEncryptionType.Deterministic;
  }
  encryptData(plaintText) {
    let iv;
    if (this.isDeterministic === true) {
      const hmacIv = (0, _crypto.createHmac)('sha256', this.columnEncryptionkey.getIvKey());
      hmacIv.update(plaintText);
      iv = hmacIv.digest().slice(0, blockSizeInBytes);
    } else {
      iv = (0, _crypto.randomBytes)(blockSizeInBytes);
    }
    const encryptCipher = (0, _crypto.createCipheriv)('aes-256-cbc', this.columnEncryptionkey.getEncryptionKey(), iv);
    const encryptedBuffer = Buffer.concat([encryptCipher.update(plaintText), encryptCipher.final()]);
    const authenticationTag = this._prepareAuthenticationTag(iv, encryptedBuffer, 0, encryptedBuffer.length);
    return Buffer.concat([Buffer.from([algorithmVersion]), authenticationTag, iv, encryptedBuffer]);
  }
  decryptData(cipherText) {
    const iv = Buffer.alloc(blockSizeInBytes);
    const minimumCiperTextLength = this.minimumCipherTextLengthInBytesWithAuthenticationTag;
    if (cipherText.length < minimumCiperTextLength) {
      throw new Error(`Specified ciphertext has an invalid size of ${cipherText.length} bytes, which is below the minimum ${minimumCiperTextLength} bytes required for decryption.`);
    }
    let startIndex = 0;
    if (cipherText[0] !== algorithmVersion) {
      throw new Error(`The specified ciphertext's encryption algorithm version ${Buffer.from([cipherText[0]]).toString('hex')} does not match the expected encryption algorithm version ${algorithmVersion}.`);
    }
    startIndex += 1;
    let authenticationTagOffset = 0;
    authenticationTagOffset = startIndex;
    startIndex += this.keySizeInBytes;
    cipherText.copy(iv, 0, startIndex, startIndex + iv.length);
    startIndex += iv.length;
    const cipherTextOffset = startIndex;
    const cipherTextCount = cipherText.length - startIndex;
    const authenticationTag = this._prepareAuthenticationTag(iv, cipherText, cipherTextOffset, cipherTextCount);
    if (0 !== authenticationTag.compare(cipherText, authenticationTagOffset, Math.min(authenticationTagOffset + cipherTextCount, authenticationTagOffset + authenticationTag.length), 0, Math.min(cipherTextCount, authenticationTag.length))) {
      throw new Error('Specified ciphertext has an invalid authentication tag.');
    }
    let plainText;
    const decipher = (0, _crypto.createDecipheriv)('aes-256-cbc', this.columnEncryptionkey.getEncryptionKey(), iv);
    try {
      plainText = decipher.update(cipherText.slice(cipherTextOffset, cipherTextOffset + cipherTextCount));
      plainText = Buffer.concat([plainText, decipher.final()]);
    } catch (error) {
      throw new Error(`Internal error while decryption: ${error.message}`);
    }
    return plainText;
  }
  _prepareAuthenticationTag(iv, cipherText, offset, length) {
    const hmac = (0, _crypto.createHmac)('sha256', this.columnEncryptionkey.getMacKey());
    hmac.update(this.version);
    hmac.update(iv);
    hmac.update(cipherText.slice(offset, offset + length));
    hmac.update(this.versionSize);
    return hmac.digest();
  }
}
exports.AeadAes256CbcHmac256Algorithm = AeadAes256CbcHmac256Algorithm;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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