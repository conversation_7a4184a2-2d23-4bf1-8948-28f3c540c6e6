"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.keySize = exports.generateKeySalt = exports.deriveKey = exports.AeadAes256CbcHmac256EncryptionKey = void 0;
var _crypto = require("crypto");
var _symmetricKey = _interopRequireDefault(require("./symmetric-key"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.
// Copyright (c) 2019 Microsoft Corporation

const keySize = exports.keySize = 256;
const keySizeInBytes = keySize / 8;
const deriveKey = (rootKey, salt) => {
  const hmac = (0, _crypto.createHmac)('sha256', rootKey);
  hmac.update(Buffer.from(salt, 'utf16le'));
  return hmac.digest();
};
exports.deriveKey = deriveKey;
const generateKeySalt = (keyType, algorithmName, keySize) => `Microsoft SQL Server cell ${keyType} key ` + `with encryption algorithm:${algorithmName} and key length:${keySize}`;
exports.generateKeySalt = generateKeySalt;
class AeadAes256CbcHmac256EncryptionKey extends _symmetricKey.default {
  constructor(rootKey, algorithmName) {
    super(rootKey);
    this.algorithmName = algorithmName;
    this.encryptionKeySaltFormat = generateKeySalt('encryption', this.algorithmName, keySize);
    this.macKeySaltFormat = generateKeySalt('MAC', this.algorithmName, keySize);
    this.ivKeySaltFormat = generateKeySalt('IV', this.algorithmName, keySize);
    if (rootKey.length !== keySizeInBytes) {
      throw new Error(`The column encryption key has been successfully decrypted but it's length: ${rootKey.length} does not match the length: ${keySizeInBytes} for algorithm "${this.algorithmName}". Verify the encrypted value of the column encryption key in the database.`);
    }
    try {
      const encKeyBuff = deriveKey(rootKey, this.encryptionKeySaltFormat);
      this.encryptionKey = new _symmetricKey.default(encKeyBuff);
      const macKeyBuff = deriveKey(rootKey, this.macKeySaltFormat);
      this.macKey = new _symmetricKey.default(macKeyBuff);
      const ivKeyBuff = deriveKey(rootKey, this.ivKeySaltFormat);
      this.ivKey = new _symmetricKey.default(ivKeyBuff);
    } catch (error) {
      throw new Error(`Key extraction failed : ${error.message}.`);
    }
  }
  getEncryptionKey() {
    return this.encryptionKey.rootKey;
  }
  getMacKey() {
    return this.macKey.rootKey;
  }
  getIvKey() {
    return this.ivKey.rootKey;
  }
}
exports.AeadAes256CbcHmac256EncryptionKey = AeadAes256CbcHmac256EncryptionKey;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfY3J5cHRvIiwicmVxdWlyZSIsIl9zeW1tZXRyaWNLZXkiLCJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iiwib2JqIiwiX19lc01vZHVsZSIsImRlZmF1bHQiLCJrZXlTaXplIiwiZXhwb3J0cyIsImtleVNpemVJbkJ5dGVzIiwiZGVyaXZlS2V5Iiwicm9vdEtleSIsInNhbHQiLCJobWFjIiwiY3JlYXRlSG1hYyIsInVwZGF0ZSIsIkJ1ZmZlciIsImZyb20iLCJkaWdlc3QiLCJnZW5lcmF0ZUtleVNhbHQiLCJrZXlUeXBlIiwiYWxnb3JpdGhtTmFtZSIsIkFlYWRBZXMyNTZDYmNIbWFjMjU2RW5jcnlwdGlvbktleSIsIlN5bW1ldHJpY0tleSIsImNvbnN0cnVjdG9yIiwiZW5jcnlwdGlvbktleVNhbHRGb3JtYXQiLCJtYWNLZXlTYWx0Rm9ybWF0IiwiaXZLZXlTYWx0Rm9ybWF0IiwibGVuZ3RoIiwiRXJyb3IiLCJlbmNLZXlCdWZmIiwiZW5jcnlwdGlvbktleSIsIm1hY0tleUJ1ZmYiLCJtYWNLZXkiLCJpdktleUJ1ZmYiLCJpdktleSIsImVycm9yIiwibWVzc2FnZSIsImdldEVuY3J5cHRpb25LZXkiLCJnZXRNYWNLZXkiLCJnZXRJdktleSJdLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy9hbHdheXMtZW5jcnlwdGVkL2FlYWQtYWVzLTI1Ni1jYmMtaG1hYy1lbmNyeXB0aW9uLWtleS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUaGlzIGNvZGUgaXMgYmFzZWQgb24gdGhlIGBtc3NxbC1qZGJjYCBsaWJyYXJ5IHB1Ymxpc2hlZCB1bmRlciB0aGUgY29uZGl0aW9ucyBvZiBNSVQgbGljZW5zZS5cbi8vIENvcHlyaWdodCAoYykgMjAxOSBNaWNyb3NvZnQgQ29ycG9yYXRpb25cblxuaW1wb3J0IHsgY3JlYXRlSG1hYyB9IGZyb20gJ2NyeXB0byc7XG5pbXBvcnQgU3ltbWV0cmljS2V5IGZyb20gJy4vc3ltbWV0cmljLWtleSc7XG5cbmV4cG9ydCBjb25zdCBrZXlTaXplID0gMjU2O1xuY29uc3Qga2V5U2l6ZUluQnl0ZXMgPSBrZXlTaXplIC8gODtcblxuZXhwb3J0IGNvbnN0IGRlcml2ZUtleSA9IChyb290S2V5OiBCdWZmZXIsIHNhbHQ6IHN0cmluZyk6IEJ1ZmZlciA9PiB7XG4gIGNvbnN0IGhtYWMgPSBjcmVhdGVIbWFjKCdzaGEyNTYnLCByb290S2V5KTtcbiAgaG1hYy51cGRhdGUoQnVmZmVyLmZyb20oc2FsdCwgJ3V0ZjE2bGUnKSk7XG4gIHJldHVybiBobWFjLmRpZ2VzdCgpO1xufTtcblxuZXhwb3J0IGNvbnN0IGdlbmVyYXRlS2V5U2FsdCA9IChcbiAga2V5VHlwZTogJ2VuY3J5cHRpb24nIHwgJ01BQycgfCAnSVYnLFxuICBhbGdvcml0aG1OYW1lOiBzdHJpbmcsXG4gIGtleVNpemU6IG51bWJlcixcbik6IHN0cmluZyA9PlxuICBgTWljcm9zb2Z0IFNRTCBTZXJ2ZXIgY2VsbCAke2tleVR5cGV9IGtleSBgICtcbiAgYHdpdGggZW5jcnlwdGlvbiBhbGdvcml0aG06JHthbGdvcml0aG1OYW1lfSBhbmQga2V5IGxlbmd0aDoke2tleVNpemV9YDtcblxuZXhwb3J0IGNsYXNzIEFlYWRBZXMyNTZDYmNIbWFjMjU2RW5jcnlwdGlvbktleSBleHRlbmRzIFN5bW1ldHJpY0tleSB7XG4gIGRlY2xhcmUgcHJpdmF0ZSByZWFkb25seSBhbGdvcml0aG1OYW1lOiBzdHJpbmc7XG4gIGRlY2xhcmUgcHJpdmF0ZSBlbmNyeXB0aW9uS2V5U2FsdEZvcm1hdDogc3RyaW5nO1xuICBkZWNsYXJlIHByaXZhdGUgbWFjS2V5U2FsdEZvcm1hdDogc3RyaW5nO1xuICBkZWNsYXJlIHByaXZhdGUgaXZLZXlTYWx0Rm9ybWF0OiBzdHJpbmc7XG4gIGRlY2xhcmUgcHJpdmF0ZSBlbmNyeXB0aW9uS2V5OiBTeW1tZXRyaWNLZXk7XG4gIGRlY2xhcmUgcHJpdmF0ZSBtYWNLZXk6IFN5bW1ldHJpY0tleTtcbiAgZGVjbGFyZSBwcml2YXRlIGl2S2V5OiBTeW1tZXRyaWNLZXk7XG5cbiAgY29uc3RydWN0b3Iocm9vdEtleTogQnVmZmVyLCBhbGdvcml0aG1OYW1lOiBzdHJpbmcpIHtcbiAgICBzdXBlcihyb290S2V5KTtcbiAgICB0aGlzLmFsZ29yaXRobU5hbWUgPSBhbGdvcml0aG1OYW1lO1xuICAgIHRoaXMuZW5jcnlwdGlvbktleVNhbHRGb3JtYXQgPSBnZW5lcmF0ZUtleVNhbHQoJ2VuY3J5cHRpb24nLCB0aGlzLmFsZ29yaXRobU5hbWUsIGtleVNpemUpO1xuICAgIHRoaXMubWFjS2V5U2FsdEZvcm1hdCA9IGdlbmVyYXRlS2V5U2FsdCgnTUFDJywgdGhpcy5hbGdvcml0aG1OYW1lLCBrZXlTaXplKTtcbiAgICB0aGlzLml2S2V5U2FsdEZvcm1hdCA9IGdlbmVyYXRlS2V5U2FsdCgnSVYnLCB0aGlzLmFsZ29yaXRobU5hbWUsIGtleVNpemUpO1xuXG4gICAgaWYgKHJvb3RLZXkubGVuZ3RoICE9PSBrZXlTaXplSW5CeXRlcykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBUaGUgY29sdW1uIGVuY3J5cHRpb24ga2V5IGhhcyBiZWVuIHN1Y2Nlc3NmdWxseSBkZWNyeXB0ZWQgYnV0IGl0J3MgbGVuZ3RoOiAke3Jvb3RLZXkubGVuZ3RofSBkb2VzIG5vdCBtYXRjaCB0aGUgbGVuZ3RoOiAke2tleVNpemVJbkJ5dGVzfSBmb3IgYWxnb3JpdGhtIFwiJHt0aGlzLmFsZ29yaXRobU5hbWV9XCIuIFZlcmlmeSB0aGUgZW5jcnlwdGVkIHZhbHVlIG9mIHRoZSBjb2x1bW4gZW5jcnlwdGlvbiBrZXkgaW4gdGhlIGRhdGFiYXNlLmApO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCBlbmNLZXlCdWZmID0gZGVyaXZlS2V5KHJvb3RLZXksIHRoaXMuZW5jcnlwdGlvbktleVNhbHRGb3JtYXQpO1xuXG4gICAgICB0aGlzLmVuY3J5cHRpb25LZXkgPSBuZXcgU3ltbWV0cmljS2V5KGVuY0tleUJ1ZmYpO1xuXG4gICAgICBjb25zdCBtYWNLZXlCdWZmID0gZGVyaXZlS2V5KHJvb3RLZXksIHRoaXMubWFjS2V5U2FsdEZvcm1hdCk7XG5cbiAgICAgIHRoaXMubWFjS2V5ID0gbmV3IFN5bW1ldHJpY0tleShtYWNLZXlCdWZmKTtcblxuICAgICAgY29uc3QgaXZLZXlCdWZmID0gZGVyaXZlS2V5KHJvb3RLZXksIHRoaXMuaXZLZXlTYWx0Rm9ybWF0KTtcblxuICAgICAgdGhpcy5pdktleSA9IG5ldyBTeW1tZXRyaWNLZXkoaXZLZXlCdWZmKTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEtleSBleHRyYWN0aW9uIGZhaWxlZCA6ICR7ZXJyb3IubWVzc2FnZX0uYCk7XG4gICAgfVxuICB9XG5cbiAgZ2V0RW5jcnlwdGlvbktleSgpOiBCdWZmZXIge1xuICAgIHJldHVybiB0aGlzLmVuY3J5cHRpb25LZXkucm9vdEtleTtcbiAgfVxuXG4gIGdldE1hY0tleSgpOiBCdWZmZXIge1xuICAgIHJldHVybiB0aGlzLm1hY0tleS5yb290S2V5O1xuICB9XG5cbiAgZ2V0SXZLZXkoKTogQnVmZmVyIHtcbiAgICByZXR1cm4gdGhpcy5pdktleS5yb290S2V5O1xuICB9XG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7OztBQUdBLElBQUFBLE9BQUEsR0FBQUMsT0FBQTtBQUNBLElBQUFDLGFBQUEsR0FBQUMsc0JBQUEsQ0FBQUYsT0FBQTtBQUEyQyxTQUFBRSx1QkFBQUMsR0FBQSxXQUFBQSxHQUFBLElBQUFBLEdBQUEsQ0FBQUMsVUFBQSxHQUFBRCxHQUFBLEtBQUFFLE9BQUEsRUFBQUYsR0FBQTtBQUozQztBQUNBOztBQUtPLE1BQU1HLE9BQU8sR0FBQUMsT0FBQSxDQUFBRCxPQUFBLEdBQUcsR0FBRztBQUMxQixNQUFNRSxjQUFjLEdBQUdGLE9BQU8sR0FBRyxDQUFDO0FBRTNCLE1BQU1HLFNBQVMsR0FBR0EsQ0FBQ0MsT0FBZSxFQUFFQyxJQUFZLEtBQWE7RUFDbEUsTUFBTUMsSUFBSSxHQUFHLElBQUFDLGtCQUFVLEVBQUMsUUFBUSxFQUFFSCxPQUFPLENBQUM7RUFDMUNFLElBQUksQ0FBQ0UsTUFBTSxDQUFDQyxNQUFNLENBQUNDLElBQUksQ0FBQ0wsSUFBSSxFQUFFLFNBQVMsQ0FBQyxDQUFDO0VBQ3pDLE9BQU9DLElBQUksQ0FBQ0ssTUFBTSxDQUFDLENBQUM7QUFDdEIsQ0FBQztBQUFDVixPQUFBLENBQUFFLFNBQUEsR0FBQUEsU0FBQTtBQUVLLE1BQU1TLGVBQWUsR0FBR0EsQ0FDN0JDLE9BQW9DLEVBQ3BDQyxhQUFxQixFQUNyQmQsT0FBZSxLQUVkLDZCQUE0QmEsT0FBUSxPQUFNLEdBQzFDLDZCQUE0QkMsYUFBYyxtQkFBa0JkLE9BQVEsRUFBQztBQUFDQyxPQUFBLENBQUFXLGVBQUEsR0FBQUEsZUFBQTtBQUVsRSxNQUFNRyxpQ0FBaUMsU0FBU0MscUJBQVksQ0FBQztFQVNsRUMsV0FBV0EsQ0FBQ2IsT0FBZSxFQUFFVSxhQUFxQixFQUFFO0lBQ2xELEtBQUssQ0FBQ1YsT0FBTyxDQUFDO0lBQ2QsSUFBSSxDQUFDVSxhQUFhLEdBQUdBLGFBQWE7SUFDbEMsSUFBSSxDQUFDSSx1QkFBdUIsR0FBR04sZUFBZSxDQUFDLFlBQVksRUFBRSxJQUFJLENBQUNFLGFBQWEsRUFBRWQsT0FBTyxDQUFDO0lBQ3pGLElBQUksQ0FBQ21CLGdCQUFnQixHQUFHUCxlQUFlLENBQUMsS0FBSyxFQUFFLElBQUksQ0FBQ0UsYUFBYSxFQUFFZCxPQUFPLENBQUM7SUFDM0UsSUFBSSxDQUFDb0IsZUFBZSxHQUFHUixlQUFlLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQ0UsYUFBYSxFQUFFZCxPQUFPLENBQUM7SUFFekUsSUFBSUksT0FBTyxDQUFDaUIsTUFBTSxLQUFLbkIsY0FBYyxFQUFFO01BQ3JDLE1BQU0sSUFBSW9CLEtBQUssQ0FBRSw4RUFBNkVsQixPQUFPLENBQUNpQixNQUFPLCtCQUE4Qm5CLGNBQWUsbUJBQWtCLElBQUksQ0FBQ1ksYUFBYyw2RUFBNEUsQ0FBQztJQUM5UTtJQUVBLElBQUk7TUFDRixNQUFNUyxVQUFVLEdBQUdwQixTQUFTLENBQUNDLE9BQU8sRUFBRSxJQUFJLENBQUNjLHVCQUF1QixDQUFDO01BRW5FLElBQUksQ0FBQ00sYUFBYSxHQUFHLElBQUlSLHFCQUFZLENBQUNPLFVBQVUsQ0FBQztNQUVqRCxNQUFNRSxVQUFVLEdBQUd0QixTQUFTLENBQUNDLE9BQU8sRUFBRSxJQUFJLENBQUNlLGdCQUFnQixDQUFDO01BRTVELElBQUksQ0FBQ08sTUFBTSxHQUFHLElBQUlWLHFCQUFZLENBQUNTLFVBQVUsQ0FBQztNQUUxQyxNQUFNRSxTQUFTLEdBQUd4QixTQUFTLENBQUNDLE9BQU8sRUFBRSxJQUFJLENBQUNnQixlQUFlLENBQUM7TUFFMUQsSUFBSSxDQUFDUSxLQUFLLEdBQUcsSUFBSVoscUJBQVksQ0FBQ1csU0FBUyxDQUFDO0lBQzFDLENBQUMsQ0FBQyxPQUFPRSxLQUFVLEVBQUU7TUFDbkIsTUFBTSxJQUFJUCxLQUFLLENBQUUsMkJBQTBCTyxLQUFLLENBQUNDLE9BQVEsR0FBRSxDQUFDO0lBQzlEO0VBQ0Y7RUFFQUMsZ0JBQWdCQSxDQUFBLEVBQVc7SUFDekIsT0FBTyxJQUFJLENBQUNQLGFBQWEsQ0FBQ3BCLE9BQU87RUFDbkM7RUFFQTRCLFNBQVNBLENBQUEsRUFBVztJQUNsQixPQUFPLElBQUksQ0FBQ04sTUFBTSxDQUFDdEIsT0FBTztFQUM1QjtFQUVBNkIsUUFBUUEsQ0FBQSxFQUFXO0lBQ2pCLE9BQU8sSUFBSSxDQUFDTCxLQUFLLENBQUN4QixPQUFPO0VBQzNCO0FBQ0Y7QUFBQ0gsT0FBQSxDQUFBYyxpQ0FBQSxHQUFBQSxpQ0FBQSJ9