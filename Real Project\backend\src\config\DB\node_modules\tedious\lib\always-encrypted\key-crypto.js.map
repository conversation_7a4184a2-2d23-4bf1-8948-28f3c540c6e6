{"version": 3, "file": "key-crypto.js", "names": ["_symmetric<PERSON>eyCache", "require", "_aeadAes256CbcHmacAlgorithm", "_aeadAes256CbcHmacEncryptionKey", "validateAndGetEncryptionAlgorithmName", "cipherAlgorithmId", "cipherAlgorithmName", "Error", "algorithmName", "exports", "encryptWithKey", "plaintext", "md", "options", "trustedServerNameAE", "cipherAlgorithm", "decryptSymmetricKey", "cipherText", "encryptData", "decryptWithKey", "plainText", "decryptData", "cekEntry", "columnEncryptionKeyValues", "sym<PERSON>ey", "encryptionKeyInfoChosen", "CEKValues", "lastError", "CEKValue", "<PERSON><PERSON><PERSON>", "error", "AeadAes256CbcHmac256Algorithm", "AeadAes256CbcHmac256EncryptionKey", "root<PERSON>ey", "encryptionType", "encryptionKeyInfo"], "sources": ["../../src/always-encrypted/key-crypto.ts"], "sourcesContent": ["// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.\n// Copyright (c) 2019 Microsoft Corporation\n\nimport { type CryptoMetadata, type EncryptionKeyInfo } from './types';\nimport { type InternalConnectionOptions as ConnectionOptions } from '../connection';\nimport SymmetricKey from './symmetric-key';\nimport { getKey } from './symmetric-key-cache';\nimport { AeadAes256CbcHmac256Algorithm, algorithmName } from './aead-aes-256-cbc-hmac-algorithm';\nimport { AeadAes256CbcHmac256EncryptionKey } from './aead-aes-256-cbc-hmac-encryption-key';\n\nexport const validateAndGetEncryptionAlgorithmName = (cipherAlgorithmId: number, cipherAlgorithmName?: string): string => {\n  if (cipherAlgorithmId !== 2) {\n    throw new Error('Custom cipher algorithm not supported.');\n  }\n\n  return algorithmName;\n};\n\nexport const encryptWithKey = async (plaintext: Buffer, md: CryptoMetadata, options: ConnectionOptions): Promise<Buffer> => {\n  if (!options.trustedServerNameAE) {\n    throw new Error('Server name should not be null in EncryptWithKey');\n  }\n\n  if (!md.cipherAlgorithm) {\n    await decryptSymmetricKey(md, options);\n  }\n\n  if (!md.cipherAlgorithm) {\n    throw new Error('Cipher Algorithm should not be null in EncryptWithKey');\n  }\n\n  const cipherText: Buffer = md.cipherAlgorithm.encryptData(plaintext);\n\n  if (!cipherText) {\n    throw new Error('Internal error. Ciphertext value cannot be null.');\n  }\n\n  return cipherText;\n};\n\nexport const decryptWithKey = (cipherText: Buffer, md: CryptoMetadata, options: ConnectionOptions): Buffer => {\n  if (!options.trustedServerNameAE) {\n    throw new Error('Server name should not be null in DecryptWithKey');\n  }\n\n  // if (!md.cipherAlgorithm) {\n  //   await decryptSymmetricKey(md, options);\n  // }\n\n  if (!md.cipherAlgorithm) {\n    throw new Error('Cipher Algorithm should not be null in DecryptWithKey');\n  }\n\n  const plainText: Buffer = md.cipherAlgorithm.decryptData(cipherText);\n\n  if (!plainText) {\n    throw new Error('Internal error. Plaintext value cannot be null.');\n  }\n\n  return plainText;\n};\n\nexport const decryptSymmetricKey = async (md: CryptoMetadata, options: ConnectionOptions): Promise<void> => {\n  if (!md) {\n    throw new Error('md should not be null in DecryptSymmetricKey.');\n  }\n\n  if (!md.cekEntry) {\n    throw new Error('md.EncryptionInfo should not be null in DecryptSymmetricKey.');\n  }\n\n  if (!md.cekEntry.columnEncryptionKeyValues) {\n    throw new Error('md.EncryptionInfo.ColumnEncryptionKeyValues should not be null in DecryptSymmetricKey.');\n  }\n\n  let symKey: SymmetricKey | undefined;\n  let encryptionKeyInfoChosen: EncryptionKeyInfo | undefined;\n  const CEKValues: EncryptionKeyInfo[] = md.cekEntry.columnEncryptionKeyValues;\n  let lastError: Error | undefined;\n\n  for (const CEKValue of CEKValues) {\n    try {\n      symKey = await getKey(CEKValue, options);\n      if (symKey) {\n        encryptionKeyInfoChosen = CEKValue;\n        break;\n      }\n    } catch (error: any) {\n      lastError = error;\n    }\n  }\n\n  if (!symKey) {\n    if (lastError) {\n      throw lastError;\n    } else {\n      throw new Error('Exception while decryption of encrypted column encryption key.');\n    }\n  }\n\n  const algorithmName = validateAndGetEncryptionAlgorithmName(md.cipherAlgorithmId, md.cipherAlgorithmName);\n  const cipherAlgorithm = new AeadAes256CbcHmac256Algorithm(new AeadAes256CbcHmac256EncryptionKey(symKey.rootKey, algorithmName), md.encryptionType);\n\n  md.cipherAlgorithm = cipherAlgorithm;\n  md.encryptionKeyInfo = encryptionKeyInfoChosen as EncryptionKeyInfo;\n};\n"], "mappings": ";;;;;;AAMA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,2BAAA,GAAAD,OAAA;AACA,IAAAE,+BAAA,GAAAF,OAAA;AARA;AACA;;AASO,MAAMG,qCAAqC,GAAGA,CAACC,iBAAyB,EAAEC,mBAA4B,KAAa;EACxH,IAAID,iBAAiB,KAAK,CAAC,EAAE;IAC3B,MAAM,IAAIE,KAAK,CAAC,wCAAwC,CAAC;EAC3D;EAEA,OAAOC,yCAAa;AACtB,CAAC;AAACC,OAAA,CAAAL,qCAAA,GAAAA,qCAAA;AAEK,MAAMM,cAAc,GAAG,MAAAA,CAAOC,SAAiB,EAAEC,EAAkB,EAAEC,OAA0B,KAAsB;EAC1H,IAAI,CAACA,OAAO,CAACC,mBAAmB,EAAE;IAChC,MAAM,IAAIP,KAAK,CAAC,kDAAkD,CAAC;EACrE;EAEA,IAAI,CAACK,EAAE,CAACG,eAAe,EAAE;IACvB,MAAMC,mBAAmB,CAACJ,EAAE,EAAEC,OAAO,CAAC;EACxC;EAEA,IAAI,CAACD,EAAE,CAACG,eAAe,EAAE;IACvB,MAAM,IAAIR,KAAK,CAAC,uDAAuD,CAAC;EAC1E;EAEA,MAAMU,UAAkB,GAAGL,EAAE,CAACG,eAAe,CAACG,WAAW,CAACP,SAAS,CAAC;EAEpE,IAAI,CAACM,UAAU,EAAE;IACf,MAAM,IAAIV,KAAK,CAAC,kDAAkD,CAAC;EACrE;EAEA,OAAOU,UAAU;AACnB,CAAC;AAACR,OAAA,CAAAC,cAAA,GAAAA,cAAA;AAEK,MAAMS,cAAc,GAAGA,CAACF,UAAkB,EAAEL,EAAkB,EAAEC,OAA0B,KAAa;EAC5G,IAAI,CAACA,OAAO,CAACC,mBAAmB,EAAE;IAChC,MAAM,IAAIP,KAAK,CAAC,kDAAkD,CAAC;EACrE;;EAEA;EACA;EACA;;EAEA,IAAI,CAACK,EAAE,CAACG,eAAe,EAAE;IACvB,MAAM,IAAIR,KAAK,CAAC,uDAAuD,CAAC;EAC1E;EAEA,MAAMa,SAAiB,GAAGR,EAAE,CAACG,eAAe,CAACM,WAAW,CAACJ,UAAU,CAAC;EAEpE,IAAI,CAACG,SAAS,EAAE;IACd,MAAM,IAAIb,KAAK,CAAC,iDAAiD,CAAC;EACpE;EAEA,OAAOa,SAAS;AAClB,CAAC;AAACX,OAAA,CAAAU,cAAA,GAAAA,cAAA;AAEK,MAAMH,mBAAmB,GAAG,MAAAA,CAAOJ,EAAkB,EAAEC,OAA0B,KAAoB;EAC1G,IAAI,CAACD,EAAE,EAAE;IACP,MAAM,IAAIL,KAAK,CAAC,+CAA+C,CAAC;EAClE;EAEA,IAAI,CAACK,EAAE,CAACU,QAAQ,EAAE;IAChB,MAAM,IAAIf,KAAK,CAAC,8DAA8D,CAAC;EACjF;EAEA,IAAI,CAACK,EAAE,CAACU,QAAQ,CAACC,yBAAyB,EAAE;IAC1C,MAAM,IAAIhB,KAAK,CAAC,wFAAwF,CAAC;EAC3G;EAEA,IAAIiB,MAAgC;EACpC,IAAIC,uBAAsD;EAC1D,MAAMC,SAA8B,GAAGd,EAAE,CAACU,QAAQ,CAACC,yBAAyB;EAC5E,IAAII,SAA4B;EAEhC,KAAK,MAAMC,QAAQ,IAAIF,SAAS,EAAE;IAChC,IAAI;MACFF,MAAM,GAAG,MAAM,IAAAK,yBAAM,EAACD,QAAQ,EAAEf,OAAO,CAAC;MACxC,IAAIW,MAAM,EAAE;QACVC,uBAAuB,GAAGG,QAAQ;QAClC;MACF;IACF,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnBH,SAAS,GAAGG,KAAK;IACnB;EACF;EAEA,IAAI,CAACN,MAAM,EAAE;IACX,IAAIG,SAAS,EAAE;MACb,MAAMA,SAAS;IACjB,CAAC,MAAM;MACL,MAAM,IAAIpB,KAAK,CAAC,gEAAgE,CAAC;IACnF;EACF;EAEA,MAAMC,aAAa,GAAGJ,qCAAqC,CAACQ,EAAE,CAACP,iBAAiB,EAAEO,EAAE,CAACN,mBAAmB,CAAC;EACzG,MAAMS,eAAe,GAAG,IAAIgB,yDAA6B,CAAC,IAAIC,iEAAiC,CAACR,MAAM,CAACS,OAAO,EAAEzB,aAAa,CAAC,EAAEI,EAAE,CAACsB,cAAc,CAAC;EAElJtB,EAAE,CAACG,eAAe,GAAGA,eAAe;EACpCH,EAAE,CAACuB,iBAAiB,GAAGV,uBAA4C;AACrE,CAAC;AAAChB,OAAA,CAAAO,mBAAA,GAAAA,mBAAA"}