"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ColumnEncryptionAzureKeyVaultProvider = void 0;
var _identity = require("@azure/identity");
var _keyvaultKeys = require("@azure/keyvault-keys");
var _crypto = require("crypto");
var _url = require("url");
// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.
// Copyright (c) 2019 Microsoft Corporation

class ColumnEncryptionAzureKeyVaultProvider {
  constructor(clientId, clientKey, tenantId) {
    this.name = 'AZURE_KEY_VAULT';
    this.azureKeyVaultDomainName = 'vault.azure.net';
    this.rsaEncryptionAlgorithmWithOAEPForAKV = 'RSA-OAEP';
    this.firstVersion = Buffer.from([0x01]);
    this.credentials = new _identity.ClientSecretCredential(tenantId, clientId, clientKey);
  }
  async decryptColumnEncryptionKey(masterKeyPath, encryptionAlgorithm, encryptedColumnEncryptionKey) {
    if (!encryptedColumnEncryptionKey) {
      throw new Error('Internal error. Encrypted column encryption key cannot be null.');
    }
    if (encryptedColumnEncryptionKey.length === 0) {
      throw new Error('Internal error. Empty encrypted column encryption key specified.');
    }
    encryptionAlgorithm = this.validateEncryptionAlgorithm(encryptionAlgorithm);
    const masterKey = await this.getMasterKey(masterKeyPath);
    const keySizeInBytes = this.getAKVKeySize(masterKey);
    const cryptoClient = this.createCryptoClient(masterKey);
    if (encryptedColumnEncryptionKey[0] !== this.firstVersion[0]) {
      throw new Error(`Specified encrypted column encryption key contains an invalid encryption algorithm version ${Buffer.from([encryptedColumnEncryptionKey[0]]).toString('hex')}. Expected version is ${Buffer.from([this.firstVersion[0]]).toString('hex')}.`);
    }
    let currentIndex = this.firstVersion.length;
    const keyPathLength = encryptedColumnEncryptionKey.readInt16LE(currentIndex);
    currentIndex += 2;
    const cipherTextLength = encryptedColumnEncryptionKey.readInt16LE(currentIndex);
    currentIndex += 2;
    currentIndex += keyPathLength;
    if (cipherTextLength !== keySizeInBytes) {
      throw new Error(`The specified encrypted column encryption key's ciphertext length: ${cipherTextLength} does not match the ciphertext length: ${keySizeInBytes} when using column master key (Azure Key Vault key) in ${masterKeyPath}. The encrypted column encryption key may be corrupt, or the specified Azure Key Vault key path may be incorrect.`);
    }
    const signatureLength = encryptedColumnEncryptionKey.length - currentIndex - cipherTextLength;
    if (signatureLength !== keySizeInBytes) {
      throw new Error(`The specified encrypted column encryption key's signature length: ${signatureLength} does not match the signature length: ${keySizeInBytes} when using column master key (Azure Key Vault key) in ${masterKeyPath}. The encrypted column encryption key may be corrupt, or the specified Azure Key Vault key path may be incorrect.`);
    }
    const cipherText = Buffer.alloc(cipherTextLength);
    encryptedColumnEncryptionKey.copy(cipherText, 0, currentIndex, currentIndex + cipherTextLength);
    currentIndex += cipherTextLength;
    const signature = Buffer.alloc(signatureLength);
    encryptedColumnEncryptionKey.copy(signature, 0, currentIndex, currentIndex + signatureLength);
    const hash = Buffer.alloc(encryptedColumnEncryptionKey.length - signature.length);
    encryptedColumnEncryptionKey.copy(hash, 0, 0, encryptedColumnEncryptionKey.length - signature.length);
    const messageDigest = (0, _crypto.createHash)('sha256');
    messageDigest.update(hash);
    const dataToVerify = messageDigest.digest();
    if (!dataToVerify) {
      throw new Error('Hash should not be null while decrypting encrypted column encryption key.');
    }
    const verifyKey = await cryptoClient.verify('RS256', dataToVerify, signature);
    if (!verifyKey.result) {
      throw new Error(`The specified encrypted column encryption key signature does not match the signature computed with the column master key (Asymmetric key in Azure Key Vault) in ${masterKeyPath}. The encrypted column encryption key may be corrupt, or the specified path may be incorrect.`);
    }
    const decryptedCEK = await this.azureKeyVaultUnWrap(cryptoClient, encryptionAlgorithm, cipherText);
    return decryptedCEK;
  }
  async encryptColumnEncryptionKey(masterKeyPath, encryptionAlgorithm, columnEncryptionKey) {
    if (!columnEncryptionKey) {
      throw new Error('Column encryption key cannot be null.');
    }
    if (columnEncryptionKey.length === 0) {
      throw new Error('Empty column encryption key specified.');
    }
    encryptionAlgorithm = this.validateEncryptionAlgorithm(encryptionAlgorithm);
    const masterKey = await this.getMasterKey(masterKeyPath);
    const keySizeInBytes = this.getAKVKeySize(masterKey);
    const cryptoClient = this.createCryptoClient(masterKey);
    const version = Buffer.from([this.firstVersion[0]]);
    const masterKeyPathBytes = Buffer.from(masterKeyPath.toLowerCase(), 'utf8');
    const keyPathLength = Buffer.alloc(2);
    keyPathLength[0] = masterKeyPathBytes.length & 0xff;
    keyPathLength[1] = masterKeyPathBytes.length >> 8 & 0xff;
    const cipherText = await this.azureKeyVaultWrap(cryptoClient, encryptionAlgorithm, columnEncryptionKey);
    const cipherTextLength = Buffer.alloc(2);
    cipherTextLength[0] = cipherText.length & 0xff;
    cipherTextLength[1] = cipherText.length >> 8 & 0xff;
    if (cipherText.length !== keySizeInBytes) {
      throw new Error('CipherText length does not match the RSA key size.');
    }
    const dataToHash = Buffer.alloc(version.length + keyPathLength.length + cipherTextLength.length + masterKeyPathBytes.length + cipherText.length);
    let destinationPosition = version.length;
    version.copy(dataToHash, 0, 0, version.length);
    keyPathLength.copy(dataToHash, destinationPosition, 0, keyPathLength.length);
    destinationPosition += keyPathLength.length;
    cipherTextLength.copy(dataToHash, destinationPosition, 0, cipherTextLength.length);
    destinationPosition += cipherTextLength.length;
    masterKeyPathBytes.copy(dataToHash, destinationPosition, 0, masterKeyPathBytes.length);
    destinationPosition += masterKeyPathBytes.length;
    cipherText.copy(dataToHash, destinationPosition, 0, cipherText.length);
    const messageDigest = (0, _crypto.createHash)('sha256');
    messageDigest.update(dataToHash);
    const dataToSign = messageDigest.digest();
    const signedHash = await this.azureKeyVaultSignedHashedData(cryptoClient, dataToSign);
    if (signedHash.length !== keySizeInBytes) {
      throw new Error('Signed hash length does not match the RSA key size.');
    }
    const verifyKey = await cryptoClient.verify('RS256', dataToSign, signedHash);
    if (!verifyKey.result) {
      throw new Error('Invalid signature of the encrypted column encryption key computed.');
    }
    const encryptedColumnEncryptionKeyLength = version.length + cipherTextLength.length + keyPathLength.length + cipherText.length + masterKeyPathBytes.length + signedHash.length;
    const encryptedColumnEncryptionKey = Buffer.alloc(encryptedColumnEncryptionKeyLength);
    let currentIndex = 0;
    version.copy(encryptedColumnEncryptionKey, currentIndex, 0, version.length);
    currentIndex += version.length;
    keyPathLength.copy(encryptedColumnEncryptionKey, currentIndex, 0, keyPathLength.length);
    currentIndex += keyPathLength.length;
    cipherTextLength.copy(encryptedColumnEncryptionKey, currentIndex, 0, cipherTextLength.length);
    currentIndex += cipherTextLength.length;
    masterKeyPathBytes.copy(encryptedColumnEncryptionKey, currentIndex, 0, masterKeyPathBytes.length);
    currentIndex += masterKeyPathBytes.length;
    cipherText.copy(encryptedColumnEncryptionKey, currentIndex, 0, cipherText.length);
    currentIndex += cipherText.length;
    signedHash.copy(encryptedColumnEncryptionKey, currentIndex, 0, signedHash.length);
    return encryptedColumnEncryptionKey;
  }
  async getMasterKey(masterKeyPath) {
    if (!masterKeyPath) {
      throw new Error('Master key path cannot be null or undefined');
    }
    const keyParts = this.parsePath(masterKeyPath);
    this.createKeyClient(keyParts.vaultUrl);
    return await this.keyClient.getKey(keyParts.name, keyParts.version ? {
      version: keyParts.version
    } : {});
  }
  createKeyClient(keyVaultUrl) {
    if (!keyVaultUrl) {
      throw new Error('Cannot create key client with null or undefined keyVaultUrl');
    }
    if (!this.keyClient) {
      this.url = keyVaultUrl;
      this.keyClient = new _keyvaultKeys.KeyClient(keyVaultUrl, this.credentials);
    }
  }
  createCryptoClient(masterKey) {
    if (!masterKey) {
      throw new Error('Cannot create CryptographyClient with null or undefined masterKey');
    }
    return new _keyvaultKeys.CryptographyClient(masterKey, this.credentials);
  }
  parsePath(masterKeyPath) {
    if (!masterKeyPath || masterKeyPath.trim() === '') {
      throw new Error('Azure Key Vault key path cannot be null.');
    }
    let baseUri;
    try {
      baseUri = (0, _url.parse)(masterKeyPath, true, true);
    } catch {
      throw new Error(`Invalid keys identifier: ${masterKeyPath}. Not a valid URI`);
    }
    if (!baseUri.hostname || !baseUri.hostname.toLowerCase().endsWith(this.azureKeyVaultDomainName)) {
      throw new Error(`Invalid Azure Key Vault key path specified: ${masterKeyPath}.`);
    }

    // Path is of the form '/collection/name[/version]'
    const segments = (baseUri.pathname || '').split('/');
    if (segments.length !== 3 && segments.length !== 4) {
      throw new Error(`Invalid keys identifier: ${masterKeyPath}. Bad number of segments: ${segments.length}`);
    }
    if ('keys' !== segments[1]) {
      throw new Error(`Invalid keys identifier: ${masterKeyPath}. segment [1] should be "keys", found "${segments[1]}"`);
    }
    const vaultUrl = `${baseUri.protocol}//${baseUri.host}`;
    const name = segments[2];
    const version = segments.length === 4 ? segments[3] : undefined;
    return {
      vaultUrl,
      name,
      version
    };
  }
  async azureKeyVaultSignedHashedData(cryptoClient, dataToSign) {
    if (!cryptoClient) {
      throw new Error('Azure KVS Crypto Client is not defined.');
    }
    const signedData = await cryptoClient.sign('RS256', dataToSign);
    return Buffer.from(signedData.result);
  }
  async azureKeyVaultWrap(cryptoClient, encryptionAlgorithm, columnEncryptionKey) {
    if (!cryptoClient) {
      throw new Error('Azure KVS Crypto Client is not defined.');
    }
    if (!columnEncryptionKey) {
      throw new Error('Column encryption key cannot be null.');
    }
    const wrappedKey = await cryptoClient.wrapKey(encryptionAlgorithm, columnEncryptionKey);
    return Buffer.from(wrappedKey.result);
  }
  async azureKeyVaultUnWrap(cryptoClient, encryptionAlgorithm, encryptedColumnEncryptionKey) {
    if (!cryptoClient) {
      throw new Error('Azure KVS Crypto Client is not defined.');
    }
    if (!encryptionAlgorithm) {
      throw new Error('Encryption Algorithm cannot be null or undefined');
    }
    if (!encryptedColumnEncryptionKey) {
      throw new Error('Encrypted column encryption key cannot be null.');
    }
    if (encryptedColumnEncryptionKey.length === 0) {
      throw new Error('Encrypted Column Encryption Key length should not be zero.');
    }
    const unwrappedKey = await cryptoClient.unwrapKey(encryptionAlgorithm, encryptedColumnEncryptionKey);
    return Buffer.from(unwrappedKey.result);
  }
  getAKVKeySize(retrievedKey) {
    if (!retrievedKey) {
      throw new Error('Retrieved key cannot be null or undefined');
    }
    const key = retrievedKey.key;
    if (!key) {
      throw new Error(`Key does not exist ${retrievedKey.name}`);
    }
    const kty = key && key.kty && key.kty.toString().toUpperCase();
    if (!kty || 'RSA'.localeCompare(kty, 'en') !== 0) {
      throw new Error(`Cannot use a non-RSA key: ${kty}.`);
    }
    const keyLength = key && key.n && key.n.length;
    return keyLength || 0;
  }
  validateEncryptionAlgorithm(encryptionAlgorithm) {
    if (!encryptionAlgorithm) {
      throw new Error('Key encryption algorithm cannot be null.');
    }
    if ('RSA_OAEP'.localeCompare(encryptionAlgorithm.toUpperCase(), 'en') === 0) {
      encryptionAlgorithm = 'RSA-OAEP';
    }
    if (this.rsaEncryptionAlgorithmWithOAEPForAKV.localeCompare(encryptionAlgorithm.trim().toUpperCase(), 'en') !== 0) {
      throw new Error(`Invalid key encryption algorithm specified: ${encryptionAlgorithm}. Expected value: ${this.rsaEncryptionAlgorithmWithOAEPForAKV}.`);
    }
    return encryptionAlgorithm;
  }
}
exports.ColumnEncryptionAzureKeyVaultProvider = ColumnEncryptionAzureKeyVaultProvider;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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