{"version": 3, "file": "symmetric-key.js", "names": ["SymmetricKey", "constructor", "root<PERSON>ey", "Error", "length", "zeroOutKey", "<PERSON><PERSON><PERSON>", "alloc", "exports", "_default", "default"], "sources": ["../../src/always-encrypted/symmetric-key.ts"], "sourcesContent": ["// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.\n// Copyright (c) 2019 Microsoft Corporation\n\nexport class SymmetricKey {\n  declare rootKey: Buffer;\n\n  constructor(rootKey: Buffer) {\n    if (!rootKey) {\n      throw new Error('Column encryption key cannot be null.');\n    } else if (0 === rootKey.length) {\n      throw new Error('Empty column encryption key specified.');\n    }\n    this.rootKey = rootKey;\n  }\n\n  zeroOutKey() {\n    this.rootKey = Buffer.alloc(this.rootKey.length);\n  }\n}\nexport default SymmetricKey;\n"], "mappings": ";;;;;;AAAA;AACA;;AAEO,MAAMA,YAAY,CAAC;EAGxBC,WAAWA,CAACC,OAAe,EAAE;IAC3B,IAAI,CAACA,OAAO,EAAE;MACZ,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;IAC1D,CAAC,MAAM,IAAI,CAAC,KAAKD,OAAO,CAACE,MAAM,EAAE;MAC/B,MAAM,IAAID,KAAK,CAAC,wCAAwC,CAAC;IAC3D;IACA,IAAI,CAACD,OAAO,GAAGA,OAAO;EACxB;EAEAG,UAAUA,CAAA,EAAG;IACX,IAAI,CAACH,OAAO,GAAGI,MAAM,CAACC,KAAK,CAAC,IAAI,CAACL,OAAO,CAACE,MAAM,CAAC;EAClD;AACF;AAACI,OAAA,CAAAR,YAAA,GAAAA,YAAA;AAAA,IAAAS,QAAA,GAAAD,OAAA,CAAAE,OAAA,GACcV,YAAY"}