"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SQLServerStatementColumnEncryptionSetting = exports.SQLServerEncryptionType = exports.DescribeParameterEncryptionResultSet2 = exports.DescribeParameterEncryptionResultSet1 = void 0;
// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.
// Copyright (c) 2019 Microsoft Corporation
let SQLServerEncryptionType = exports.SQLServerEncryptionType = /*#__PURE__*/function (SQLServerEncryptionType) {
  SQLServerEncryptionType[SQLServerEncryptionType["Deterministic"] = 1] = "Deterministic";
  SQLServerEncryptionType[SQLServerEncryptionType["Randomized"] = 2] = "Randomized";
  SQLServerEncryptionType[SQLServerEncryptionType["PlainText"] = 0] = "PlainText";
  return SQLServerEncryptionType;
}({});
// Fields in the first resultset of "sp_describe_parameter_encryption"
// We expect the server to return the fields in the resultset in the same order as mentioned below.
// If the server changes the below order, then transparent parameter encryption will break.
let DescribeParameterEncryptionResultSet1 = exports.DescribeParameterEncryptionResultSet1 = /*#__PURE__*/function (DescribeParameterEncryptionResultSet1) {
  DescribeParameterEncryptionResultSet1[DescribeParameterEncryptionResultSet1["KeyOrdinal"] = 0] = "KeyOrdinal";
  DescribeParameterEncryptionResultSet1[DescribeParameterEncryptionResultSet1["DbId"] = 1] = "DbId";
  DescribeParameterEncryptionResultSet1[DescribeParameterEncryptionResultSet1["KeyId"] = 2] = "KeyId";
  DescribeParameterEncryptionResultSet1[DescribeParameterEncryptionResultSet1["KeyVersion"] = 3] = "KeyVersion";
  DescribeParameterEncryptionResultSet1[DescribeParameterEncryptionResultSet1["KeyMdVersion"] = 4] = "KeyMdVersion";
  DescribeParameterEncryptionResultSet1[DescribeParameterEncryptionResultSet1["EncryptedKey"] = 5] = "EncryptedKey";
  DescribeParameterEncryptionResultSet1[DescribeParameterEncryptionResultSet1["ProviderName"] = 6] = "ProviderName";
  DescribeParameterEncryptionResultSet1[DescribeParameterEncryptionResultSet1["KeyPath"] = 7] = "KeyPath";
  DescribeParameterEncryptionResultSet1[DescribeParameterEncryptionResultSet1["KeyEncryptionAlgorithm"] = 8] = "KeyEncryptionAlgorithm";
  return DescribeParameterEncryptionResultSet1;
}({}); // Fields in the second resultset of "sp_describe_parameter_encryption"
// We expect the server to return the fields in the resultset in the same order as mentioned below.
// If the server changes the below order, then transparent parameter encryption will break.
let DescribeParameterEncryptionResultSet2 = exports.DescribeParameterEncryptionResultSet2 = /*#__PURE__*/function (DescribeParameterEncryptionResultSet2) {
  DescribeParameterEncryptionResultSet2[DescribeParameterEncryptionResultSet2["ParameterOrdinal"] = 0] = "ParameterOrdinal";
  DescribeParameterEncryptionResultSet2[DescribeParameterEncryptionResultSet2["ParameterName"] = 1] = "ParameterName";
  DescribeParameterEncryptionResultSet2[DescribeParameterEncryptionResultSet2["ColumnEncryptionAlgorithm"] = 2] = "ColumnEncryptionAlgorithm";
  DescribeParameterEncryptionResultSet2[DescribeParameterEncryptionResultSet2["ColumnEncrytionType"] = 3] = "ColumnEncrytionType";
  DescribeParameterEncryptionResultSet2[DescribeParameterEncryptionResultSet2["ColumnEncryptionKeyOrdinal"] = 4] = "ColumnEncryptionKeyOrdinal";
  DescribeParameterEncryptionResultSet2[DescribeParameterEncryptionResultSet2["NormalizationRuleVersion"] = 5] = "NormalizationRuleVersion";
  return DescribeParameterEncryptionResultSet2;
}({});
let SQLServerStatementColumnEncryptionSetting = exports.SQLServerStatementColumnEncryptionSetting = /*#__PURE__*/function (SQLServerStatementColumnEncryptionSetting) {
  SQLServerStatementColumnEncryptionSetting[SQLServerStatementColumnEncryptionSetting["UseConnectionSetting"] = 0] = "UseConnectionSetting";
  SQLServerStatementColumnEncryptionSetting[SQLServerStatementColumnEncryptionSetting["Enabled"] = 1] = "Enabled";
  SQLServerStatementColumnEncryptionSetting[SQLServerStatementColumnEncryptionSetting["ResultSetOnly"] = 2] = "ResultSetOnly";
  SQLServerStatementColumnEncryptionSetting[SQLServerStatementColumnEncryptionSetting["Disabled"] = 3] = "Disabled";
  return SQLServerStatementColumnEncryptionSetting;
}({});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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