"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.shouldHonorAE = void 0;
var _types = require("./types");
// This code is based on the `mssql-jdbc` library published under the conditions of MIT license.
// Copyright (c) 2019 Microsoft Corporation

const shouldHonorAE = (stmtColumnEncryptionSetting, columnEncryptionSetting) => {
  switch (stmtColumnEncryptionSetting) {
    case _types.SQLServerStatementColumnEncryptionSetting.Disabled:
    case _types.SQLServerStatementColumnEncryptionSetting.ResultSetOnly:
      return false;
    case _types.SQLServerStatementColumnEncryptionSetting.Enabled:
      return true;
    default:
      return columnEncryptionSetting;
  }
};
exports.shouldHonorAE = shouldHonorAE;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfdHlwZXMiLCJyZXF1aXJlIiwic2hvdWxkSG9ub3JBRSIsInN0bXRDb2x1bW5FbmNyeXB0aW9uU2V0dGluZyIsImNvbHVtbkVuY3J5cHRpb25TZXR0aW5nIiwiU1FMU2VydmVyU3RhdGVtZW50Q29sdW1uRW5jcnlwdGlvblNldHRpbmciLCJEaXNhYmxlZCIsIlJlc3VsdFNldE9ubHkiLCJFbmFibGVkIiwiZXhwb3J0cyJdLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy9hbHdheXMtZW5jcnlwdGVkL3V0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgY29kZSBpcyBiYXNlZCBvbiB0aGUgYG1zc3FsLWpkYmNgIGxpYnJhcnkgcHVibGlzaGVkIHVuZGVyIHRoZSBjb25kaXRpb25zIG9mIE1JVCBsaWNlbnNlLlxuLy8gQ29weXJpZ2h0IChjKSAyMDE5IE1pY3Jvc29mdCBDb3Jwb3JhdGlvblxuXG5pbXBvcnQgeyBTUUxTZXJ2ZXJTdGF0ZW1lbnRDb2x1bW5FbmNyeXB0aW9uU2V0dGluZyB9IGZyb20gJy4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3Qgc2hvdWxkSG9ub3JBRSA9IChzdG10Q29sdW1uRW5jcnlwdGlvblNldHRpbmc6IFNRTFNlcnZlclN0YXRlbWVudENvbHVtbkVuY3J5cHRpb25TZXR0aW5nLCBjb2x1bW5FbmNyeXB0aW9uU2V0dGluZzogYm9vbGVhbik6IGJvb2xlYW4gPT4ge1xuICBzd2l0Y2ggKHN0bXRDb2x1bW5FbmNyeXB0aW9uU2V0dGluZykge1xuICAgIGNhc2UgU1FMU2VydmVyU3RhdGVtZW50Q29sdW1uRW5jcnlwdGlvblNldHRpbmcuRGlzYWJsZWQ6XG4gICAgY2FzZSBTUUxTZXJ2ZXJTdGF0ZW1lbnRDb2x1bW5FbmNyeXB0aW9uU2V0dGluZy5SZXN1bHRTZXRPbmx5OlxuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIGNhc2UgU1FMU2VydmVyU3RhdGVtZW50Q29sdW1uRW5jcnlwdGlvblNldHRpbmcuRW5hYmxlZDpcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gY29sdW1uRW5jcnlwdGlvblNldHRpbmc7XG4gIH1cbn07XG4iXSwibWFwcGluZ3MiOiI7Ozs7OztBQUdBLElBQUFBLE1BQUEsR0FBQUMsT0FBQTtBQUhBO0FBQ0E7O0FBSU8sTUFBTUMsYUFBYSxHQUFHQSxDQUFDQywyQkFBc0UsRUFBRUMsdUJBQWdDLEtBQWM7RUFDbEosUUFBUUQsMkJBQTJCO0lBQ2pDLEtBQUtFLGdEQUF5QyxDQUFDQyxRQUFRO0lBQ3ZELEtBQUtELGdEQUF5QyxDQUFDRSxhQUFhO01BQzFELE9BQU8sS0FBSztJQUNkLEtBQUtGLGdEQUF5QyxDQUFDRyxPQUFPO01BQ3BELE9BQU8sSUFBSTtJQUNiO01BQ0UsT0FBT0osdUJBQXVCO0VBQ2xDO0FBQ0YsQ0FBQztBQUFDSyxPQUFBLENBQUFQLGFBQUEsR0FBQUEsYUFBQSJ9