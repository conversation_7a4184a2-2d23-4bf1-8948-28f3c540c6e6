"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _intn = _interopRequireDefault(require("./intn"));
var _writableTrackingBuffer = _interopRequireDefault(require("../tracking-buffer/writable-tracking-buffer"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const DATA_LENGTH = Buffer.from([0x08]);
const NULL_LENGTH = Buffer.from([0x00]);
const MAX_SAFE_BIGINT = 9223372036854775807n;
const MIN_SAFE_BIGINT = -9223372036854775808n;
const BigInt = {
  id: 0x7F,
  type: 'INT8',
  name: 'BigInt',
  declaration: function () {
    return 'bigint';
  },
  generateTypeInfo() {
    return Buffer.from([_intn.default.id, 0x08]);
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    return DATA_LENGTH;
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    const buffer = new _writableTrackingBuffer.default(8);
    buffer.writeBigInt64LE(typeof parameter.value === 'bigint' ? parameter.value : globalThis.BigInt(parameter.value));
    yield buffer.data;
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    if (typeof value !== 'bigint') {
      value = globalThis.BigInt(value);
    }
    if (value < MIN_SAFE_BIGINT || value > MAX_SAFE_BIGINT) {
      throw new TypeError(`Value must be between ${MIN_SAFE_BIGINT} and ${MAX_SAFE_BIGINT}, inclusive.`);
    }
    return value;
  }
};
var _default = exports.default = BigInt;
module.exports = BigInt;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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