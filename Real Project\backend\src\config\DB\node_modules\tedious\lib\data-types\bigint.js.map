{"version": 3, "file": "bigint.js", "names": ["_intn", "_interopRequireDefault", "require", "_writableTrackingBuffer", "obj", "__esModule", "default", "DATA_LENGTH", "<PERSON><PERSON><PERSON>", "from", "NULL_LENGTH", "MAX_SAFE_BIGINT", "MIN_SAFE_BIGINT", "BigInt", "id", "type", "name", "declaration", "generateTypeInfo", "IntN", "generateParameterLength", "parameter", "options", "value", "generateParameterData", "buffer", "WritableTrackingBuffer", "writeBigInt64LE", "globalThis", "data", "validate", "TypeError", "_default", "exports", "module"], "sources": ["../../src/data-types/bigint.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport IntN from './intn';\nimport WritableTrackingBuffer from '../tracking-buffer/writable-tracking-buffer';\n\nconst DATA_LENGTH = Buffer.from([0x08]);\nconst NULL_LENGTH = Buffer.from([0x00]);\nconst MAX_SAFE_BIGINT = 9223372036854775807n;\nconst MIN_SAFE_BIGINT = -9223372036854775808n;\n\nconst BigInt: DataType = {\n  id: 0x7F,\n  type: 'INT8',\n  name: 'BigInt',\n\n  declaration: function() {\n    return 'bigint';\n  },\n\n  generateTypeInfo() {\n    return Buffer.from([IntN.id, 0x08]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    return DATA_LENGTH;\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const buffer = new WritableTrackingBuffer(8);\n    buffer.writeBigInt64LE(typeof parameter.value === 'bigint' ? parameter.value : globalThis.BigInt(parameter.value));\n    yield buffer.data;\n  },\n\n  validate: function(value): null | bigint {\n    if (value == null) {\n      return null;\n    }\n\n    if (typeof value !== 'bigint') {\n      value = globalThis.BigInt(value);\n    }\n\n    if (value < MIN_SAFE_BIGINT || value > MAX_SAFE_BIGINT) {\n      throw new TypeError(`Value must be between ${MIN_SAFE_BIGINT} and ${MAX_SAFE_BIGINT}, inclusive.`);\n    }\n\n    return value;\n  }\n};\n\nexport default BigInt;\nmodule.exports = BigInt;\n"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAiF,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEjF,MAAMG,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,MAAMC,WAAW,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,MAAME,eAAe,GAAG,oBAAoB;AAC5C,MAAMC,eAAe,GAAG,CAAC,oBAAoB;AAE7C,MAAMC,MAAgB,GAAG;EACvBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,QAAQ;EAEdC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,QAAQ;EACjB,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,OAAOV,MAAM,CAACC,IAAI,CAAC,CAACU,aAAI,CAACL,EAAE,EAAE,IAAI,CAAC,CAAC;EACrC,CAAC;EAEDM,uBAAuBA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOb,WAAW;IACpB;IAEA,OAAOH,WAAW;EACpB,CAAC;EAED,CAAEiB,qBAAqBA,CAACH,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAME,MAAM,GAAG,IAAIC,+BAAsB,CAAC,CAAC,CAAC;IAC5CD,MAAM,CAACE,eAAe,CAAC,OAAON,SAAS,CAACE,KAAK,KAAK,QAAQ,GAAGF,SAAS,CAACE,KAAK,GAAGK,UAAU,CAACf,MAAM,CAACQ,SAAS,CAACE,KAAK,CAAC,CAAC;IAClH,MAAME,MAAM,CAACI,IAAI;EACnB,CAAC;EAEDC,QAAQ,EAAE,SAAAA,CAASP,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAGK,UAAU,CAACf,MAAM,CAACU,KAAK,CAAC;IAClC;IAEA,IAAIA,KAAK,GAAGX,eAAe,IAAIW,KAAK,GAAGZ,eAAe,EAAE;MACtD,MAAM,IAAIoB,SAAS,CAAE,yBAAwBnB,eAAgB,QAAOD,eAAgB,cAAa,CAAC;IACpG;IAEA,OAAOY,KAAK;EACd;AACF,CAAC;AAAC,IAAAS,QAAA,GAAAC,OAAA,CAAA3B,OAAA,GAEaO,MAAM;AACrBqB,MAAM,CAACD,OAAO,GAAGpB,MAAM"}