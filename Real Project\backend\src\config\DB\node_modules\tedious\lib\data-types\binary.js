"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
const NULL_LENGTH = Buffer.from([0xFF, 0xFF]);
const Binary = {
  id: 0xAD,
  type: 'BIGBinary',
  name: 'Binary',
  maximumLength: 8000,
  declaration: function (parameter) {
    const value = parameter.value;
    let length;
    if (parameter.length) {
      length = parameter.length;
    } else if (value != null) {
      length = value.length || 1;
    } else if (value === null && !parameter.output) {
      length = 1;
    } else {
      length = this.maximumLength;
    }
    return 'binary(' + length + ')';
  },
  resolveLength: function (parameter) {
    const value = parameter.value;
    if (value != null) {
      return value.length;
    } else {
      return this.maximumLength;
    }
  },
  generateTypeInfo(parameter) {
    const buffer = Buffer.alloc(3);
    buffer.writeUInt8(this.id, 0);
    buffer.writeUInt16LE(parameter.length, 1);
    return buffer;
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    const buffer = Buffer.alloc(2);
    buffer.writeUInt16LE(parameter.length, 0);
    return buffer;
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    yield parameter.value.slice(0, parameter.length !== undefined ? Math.min(parameter.length, this.maximumLength) : this.maximumLength);
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    if (!Buffer.isBuffer(value)) {
      throw new TypeError('Invalid buffer.');
    }
    return value;
  }
};
var _default = exports.default = Binary;
module.exports = Binary;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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