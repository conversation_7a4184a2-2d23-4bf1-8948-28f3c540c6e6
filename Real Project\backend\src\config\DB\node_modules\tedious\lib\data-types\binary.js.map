{"version": 3, "file": "binary.js", "names": ["NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "Binary", "id", "type", "name", "maximumLength", "declaration", "parameter", "value", "length", "output", "<PERSON><PERSON><PERSON><PERSON>", "generateTypeInfo", "buffer", "alloc", "writeUInt8", "writeUInt16LE", "generateParameterLength", "options", "generateParameterData", "slice", "undefined", "Math", "min", "validate", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_default", "exports", "default", "module"], "sources": ["../../src/data-types/binary.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\n\nconst NULL_LENGTH = Buffer.from([0xFF, 0xFF]);\n\nconst Binary: { maximumLength: number } & DataType = {\n  id: 0xAD,\n  type: 'BIGBinary',\n  name: 'Binary',\n  maximumLength: 8000,\n\n  declaration: function(parameter) {\n    const value = parameter.value as Buffer | null;\n\n    let length;\n    if (parameter.length) {\n      length = parameter.length;\n    } else if (value != null) {\n      length = value.length || 1;\n    } else if (value === null && !parameter.output) {\n      length = 1;\n    } else {\n      length = this.maximumLength;\n    }\n\n    return 'binary(' + length + ')';\n  },\n\n  resolveLength: function(parameter) {\n    const value = parameter.value as Buffer | null;\n\n    if (value != null) {\n      return value.length;\n    } else {\n      return this.maximumLength;\n    }\n  },\n\n  generateTypeInfo(parameter) {\n    const buffer = Buffer.alloc(3);\n    buffer.writeUInt8(this.id, 0);\n    buffer.writeUInt16LE(parameter.length!, 1);\n    return buffer;\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    const buffer = Buffer.alloc(2);\n    buffer.writeUInt16LE(parameter.length!, 0);\n    return buffer;\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    yield parameter.value.slice(0, parameter.length !== undefined ? Math.min(parameter.length, this.maximumLength) : this.maximumLength);\n  },\n\n  validate: function(value): Buffer | null {\n    if (value == null) {\n      return null;\n    }\n\n    if (!Buffer.isBuffer(value)) {\n      throw new TypeError('Invalid buffer.');\n    }\n\n    return value;\n  }\n};\n\nexport default Binary;\nmodule.exports = Binary;\n"], "mappings": ";;;;;;AAEA,MAAMA,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAE7C,MAAMC,MAA4C,GAAG;EACnDC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,QAAQ;EACdC,aAAa,EAAE,IAAI;EAEnBC,WAAW,EAAE,SAAAA,CAASC,SAAS,EAAE;IAC/B,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAsB;IAE9C,IAAIC,MAAM;IACV,IAAIF,SAAS,CAACE,MAAM,EAAE;MACpBA,MAAM,GAAGF,SAAS,CAACE,MAAM;IAC3B,CAAC,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxBC,MAAM,GAAGD,KAAK,CAACC,MAAM,IAAI,CAAC;IAC5B,CAAC,MAAM,IAAID,KAAK,KAAK,IAAI,IAAI,CAACD,SAAS,CAACG,MAAM,EAAE;MAC9CD,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM;MACLA,MAAM,GAAG,IAAI,CAACJ,aAAa;IAC7B;IAEA,OAAO,SAAS,GAAGI,MAAM,GAAG,GAAG;EACjC,CAAC;EAEDE,aAAa,EAAE,SAAAA,CAASJ,SAAS,EAAE;IACjC,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAsB;IAE9C,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOA,KAAK,CAACC,MAAM;IACrB,CAAC,MAAM;MACL,OAAO,IAAI,CAACJ,aAAa;IAC3B;EACF,CAAC;EAEDO,gBAAgBA,CAACL,SAAS,EAAE;IAC1B,MAAMM,MAAM,GAAGd,MAAM,CAACe,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACE,UAAU,CAAC,IAAI,CAACb,EAAE,EAAE,CAAC,CAAC;IAC7BW,MAAM,CAACG,aAAa,CAACT,SAAS,CAACE,MAAM,EAAG,CAAC,CAAC;IAC1C,OAAOI,MAAM;EACf,CAAC;EAEDI,uBAAuBA,CAACV,SAAS,EAAEW,OAAO,EAAE;IAC1C,IAAIX,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOV,WAAW;IACpB;IAEA,MAAMe,MAAM,GAAGd,MAAM,CAACe,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACG,aAAa,CAACT,SAAS,CAACE,MAAM,EAAG,CAAC,CAAC;IAC1C,OAAOI,MAAM;EACf,CAAC;EAED,CAAEM,qBAAqBA,CAACZ,SAAS,EAAEW,OAAO,EAAE;IAC1C,IAAIX,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMD,SAAS,CAACC,KAAK,CAACY,KAAK,CAAC,CAAC,EAAEb,SAAS,CAACE,MAAM,KAAKY,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAChB,SAAS,CAACE,MAAM,EAAE,IAAI,CAACJ,aAAa,CAAC,GAAG,IAAI,CAACA,aAAa,CAAC;EACtI,CAAC;EAEDmB,QAAQ,EAAE,SAAAA,CAAShB,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,CAACT,MAAM,CAAC0B,QAAQ,CAACjB,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAIkB,SAAS,CAAC,iBAAiB,CAAC;IACxC;IAEA,OAAOlB,KAAK;EACd;AACF,CAAC;AAAC,IAAAmB,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEa5B,MAAM;AACrB6B,MAAM,CAACF,OAAO,GAAG3B,MAAM"}