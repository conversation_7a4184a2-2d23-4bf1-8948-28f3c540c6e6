{"version": 3, "file": "bit.js", "names": ["_bitn", "_interopRequireDefault", "require", "obj", "__esModule", "default", "DATA_LENGTH", "<PERSON><PERSON><PERSON>", "from", "NULL_LENGTH", "Bit", "id", "type", "name", "declaration", "generateTypeInfo", "BitN", "generateParameterLength", "parameter", "options", "value", "generateParameterData", "validate", "_default", "exports", "module"], "sources": ["../../src/data-types/bit.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport BitN from './bitn';\n\nconst DATA_LENGTH = Buffer.from([0x01]);\nconst NULL_LENGTH = Buffer.from([0x00]);\n\nconst Bit: DataType = {\n  id: 0x32,\n  type: 'BIT',\n  name: 'Bit',\n\n  declaration: function() {\n    return 'bit';\n  },\n\n  generateTypeInfo() {\n    return Buffer.from([BitN.id, 0x01]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    return DATA_LENGTH;\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    yield parameter.value ? Buffer.from([0x01]) : Buffer.from([0x00]);\n  },\n\n  validate: function(value): null | boolean {\n    if (value == null) {\n      return null;\n    }\n    if (value) {\n      return true;\n    } else {\n      return false;\n    }\n  }\n};\n\nexport default Bit;\nmodule.exports = Bit;\n"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0B,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE1B,MAAMG,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,MAAMC,WAAW,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAME,GAAa,GAAG;EACpBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,KAAK;EACXC,IAAI,EAAE,KAAK;EAEXC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,KAAK;EACd,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,OAAOR,MAAM,CAACC,IAAI,CAAC,CAACQ,aAAI,CAACL,EAAE,EAAE,IAAI,CAAC,CAAC;EACrC,CAAC;EAEDM,uBAAuBA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOX,WAAW;IACpB;IAEA,OAAOH,WAAW;EACpB,CAAC;EAED,CAAEe,qBAAqBA,CAACH,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMF,SAAS,CAACE,KAAK,GAAGb,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,GAAGD,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;EACnE,CAAC;EAEDc,QAAQ,EAAE,SAAAA,CAASF,KAAK,EAAkB;IACxC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IACA,IAAIA,KAAK,EAAE;MACT,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF;AACF,CAAC;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAAnB,OAAA,GAEaK,GAAG;AAClBe,MAAM,CAACD,OAAO,GAAGd,GAAG"}