"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _iconvLite = _interopRequireDefault(require("iconv-lite"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const NULL_LENGTH = Buffer.from([0xFF, 0xFF]);
const Char = {
  id: 0xAF,
  type: 'BIG<PERSON><PERSON>',
  name: 'Char',
  maximumLength: 8000,
  declaration: function (parameter) {
    const value = parameter.value;
    let length;
    if (parameter.length) {
      length = parameter.length;
    } else if (value != null) {
      length = value.length || 1;
    } else if (value === null && !parameter.output) {
      length = 1;
    } else {
      length = this.maximumLength;
    }
    if (length < this.maximumLength) {
      return 'char(' + length + ')';
    } else {
      return 'char(' + this.maximumLength + ')';
    }
  },
  // ParameterData<any> is temporary solution. TODO: need to understand what type ParameterData<...> can be.
  resolveLength: function (parameter) {
    const value = parameter.value;
    if (parameter.length != null) {
      return parameter.length;
    } else if (value != null) {
      return value.length || 1;
    } else {
      return this.maximumLength;
    }
  },
  generateTypeInfo(parameter) {
    const buffer = Buffer.alloc(8);
    buffer.writeUInt8(this.id, 0);
    buffer.writeUInt16LE(parameter.length, 1);
    if (parameter.collation) {
      parameter.collation.toBuffer().copy(buffer, 3, 0, 5);
    }
    return buffer;
  },
  generateParameterLength(parameter, options) {
    const value = parameter.value;
    if (value == null) {
      return NULL_LENGTH;
    }
    const buffer = Buffer.alloc(2);
    buffer.writeUInt16LE(value.length, 0);
    return buffer;
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    yield Buffer.from(parameter.value, 'ascii');
  },
  validate: function (value, collation) {
    if (value == null) {
      return null;
    }
    if (typeof value !== 'string') {
      throw new TypeError('Invalid string.');
    }
    if (!collation) {
      throw new Error('No collation was set by the server for the current connection.');
    }
    if (!collation.codepage) {
      throw new Error('The collation set by the server has no associated encoding.');
    }
    return _iconvLite.default.encode(value, collation.codepage);
  }
};
var _default = exports.default = Char;
module.exports = Char;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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