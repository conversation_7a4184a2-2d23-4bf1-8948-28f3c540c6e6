"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _core = require("@js-joda/core");
// globalDate is to be used for JavaScript's global 'Date' object to avoid name clashing with the 'Date' constant below
const globalDate = global.Date;
const EPOCH_DATE = _core.LocalDate.ofYearDay(1, 1);
const NULL_LENGTH = Buffer.from([0x00]);
const DATA_LENGTH = Buffer.from([0x03]);
const Date = {
  id: 0x28,
  type: 'DATEN',
  name: 'Date',
  declaration: function () {
    return 'date';
  },
  generateTypeInfo: function () {
    return Buffer.from([this.id]);
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    return DATA_LENGTH;
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    const value = parameter.value; // Temporary solution. Remove 'any' later.

    let date;
    if (options.useUTC) {
      date = _core.LocalDate.of(value.getUTCFullYear(), value.getUTCMonth() + 1, value.getUTCDate());
    } else {
      date = _core.LocalDate.of(value.getFullYear(), value.getMonth() + 1, value.getDate());
    }
    const days = EPOCH_DATE.until(date, _core.ChronoUnit.DAYS);
    const buffer = Buffer.alloc(3);
    buffer.writeUIntLE(days, 0, 3);
    yield buffer;
  },
  // TODO: value is technically of type 'unknown'.
  validate: function (value, collation, options) {
    if (value == null) {
      return null;
    }
    if (!(value instanceof globalDate)) {
      value = new globalDate(globalDate.parse(value));
    }
    value = value;
    let year;
    if (options && options.useUTC) {
      year = value.getUTCFullYear();
    } else {
      year = value.getFullYear();
    }
    if (year < 1 || year > 9999) {
      throw new TypeError('Out of range.');
    }
    if (isNaN(value)) {
      throw new TypeError('Invalid date.');
    }
    return value;
  }
};
var _default = exports.default = Date;
module.exports = Date;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfY29yZSIsInJlcXVpcmUiLCJnbG9iYWxEYXRlIiwiZ2xvYmFsIiwiRGF0ZSIsIkVQT0NIX0RBVEUiLCJMb2NhbERhdGUiLCJvZlllYXJEYXkiLCJOVUxMX0xFTkdUSCIsIkJ1ZmZlciIsImZyb20iLCJEQVRBX0xFTkdUSCIsImlkIiwidHlwZSIsIm5hbWUiLCJkZWNsYXJhdGlvbiIsImdlbmVyYXRlVHlwZUluZm8iLCJnZW5lcmF0ZVBhcmFtZXRlckxlbmd0aCIsInBhcmFtZXRlciIsIm9wdGlvbnMiLCJ2YWx1ZSIsImdlbmVyYXRlUGFyYW1ldGVyRGF0YSIsImRhdGUiLCJ1c2VVVEMiLCJvZiIsImdldFVUQ0Z1bGxZZWFyIiwiZ2V0VVRDTW9udGgiLCJnZXRVVENEYXRlIiwiZ2V0RnVsbFllYXIiLCJnZXRNb250aCIsImdldERhdGUiLCJkYXlzIiwidW50aWwiLCJDaHJvbm9Vbml0IiwiREFZUyIsImJ1ZmZlciIsImFsbG9jIiwid3JpdGVVSW50TEUiLCJ2YWxpZGF0ZSIsImNvbGxhdGlvbiIsInBhcnNlIiwieWVhciIsIlR5cGVFcnJvciIsImlzTmFOIiwiX2RlZmF1bHQiLCJleHBvcnRzIiwiZGVmYXVsdCIsIm1vZHVsZSJdLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy9kYXRhLXR5cGVzL2RhdGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBEYXRhVHlwZSB9IGZyb20gJy4uL2RhdGEtdHlwZSc7XG5pbXBvcnQgeyBDaHJvbm9Vbml0LCBMb2NhbERhdGUgfSBmcm9tICdAanMtam9kYS9jb3JlJztcblxuLy8gZ2xvYmFsRGF0ZSBpcyB0byBiZSB1c2VkIGZvciBKYXZhU2NyaXB0J3MgZ2xvYmFsICdEYXRlJyBvYmplY3QgdG8gYXZvaWQgbmFtZSBjbGFzaGluZyB3aXRoIHRoZSAnRGF0ZScgY29uc3RhbnQgYmVsb3dcbmNvbnN0IGdsb2JhbERhdGUgPSBnbG9iYWwuRGF0ZTtcbmNvbnN0IEVQT0NIX0RBVEUgPSBMb2NhbERhdGUub2ZZZWFyRGF5KDEsIDEpO1xuY29uc3QgTlVMTF9MRU5HVEggPSBCdWZmZXIuZnJvbShbMHgwMF0pO1xuY29uc3QgREFUQV9MRU5HVEggPSBCdWZmZXIuZnJvbShbMHgwM10pO1xuXG5jb25zdCBEYXRlOiBEYXRhVHlwZSA9IHtcbiAgaWQ6IDB4MjgsXG4gIHR5cGU6ICdEQVRFTicsXG4gIG5hbWU6ICdEYXRlJyxcblxuICBkZWNsYXJhdGlvbjogZnVuY3Rpb24oKSB7XG4gICAgcmV0dXJuICdkYXRlJztcbiAgfSxcblxuICBnZW5lcmF0ZVR5cGVJbmZvOiBmdW5jdGlvbigpIHtcbiAgICByZXR1cm4gQnVmZmVyLmZyb20oW3RoaXMuaWRdKTtcbiAgfSxcblxuICBnZW5lcmF0ZVBhcmFtZXRlckxlbmd0aChwYXJhbWV0ZXIsIG9wdGlvbnMpIHtcbiAgICBpZiAocGFyYW1ldGVyLnZhbHVlID09IG51bGwpIHtcbiAgICAgIHJldHVybiBOVUxMX0xFTkdUSDtcbiAgICB9XG5cbiAgICByZXR1cm4gREFUQV9MRU5HVEg7XG4gIH0sXG5cbiAgKiBnZW5lcmF0ZVBhcmFtZXRlckRhdGEocGFyYW1ldGVyLCBvcHRpb25zKSB7XG4gICAgaWYgKHBhcmFtZXRlci52YWx1ZSA9PSBudWxsKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc3QgdmFsdWUgPSBwYXJhbWV0ZXIudmFsdWUgYXMgYW55OyAvLyBUZW1wb3Jhcnkgc29sdXRpb24uIFJlbW92ZSAnYW55JyBsYXRlci5cblxuICAgIGxldCBkYXRlOiBMb2NhbERhdGU7XG4gICAgaWYgKG9wdGlvbnMudXNlVVRDKSB7XG4gICAgICBkYXRlID0gTG9jYWxEYXRlLm9mKHZhbHVlLmdldFVUQ0Z1bGxZZWFyKCksIHZhbHVlLmdldFVUQ01vbnRoKCkgKyAxLCB2YWx1ZS5nZXRVVENEYXRlKCkpO1xuICAgIH0gZWxzZSB7XG4gICAgICBkYXRlID0gTG9jYWxEYXRlLm9mKHZhbHVlLmdldEZ1bGxZZWFyKCksIHZhbHVlLmdldE1vbnRoKCkgKyAxLCB2YWx1ZS5nZXREYXRlKCkpO1xuICAgIH1cblxuICAgIGNvbnN0IGRheXMgPSBFUE9DSF9EQVRFLnVudGlsKGRhdGUsIENocm9ub1VuaXQuREFZUyk7XG4gICAgY29uc3QgYnVmZmVyID0gQnVmZmVyLmFsbG9jKDMpO1xuICAgIGJ1ZmZlci53cml0ZVVJbnRMRShkYXlzLCAwLCAzKTtcbiAgICB5aWVsZCBidWZmZXI7XG4gIH0sXG5cbiAgLy8gVE9ETzogdmFsdWUgaXMgdGVjaG5pY2FsbHkgb2YgdHlwZSAndW5rbm93bicuXG4gIHZhbGlkYXRlOiBmdW5jdGlvbih2YWx1ZSwgY29sbGF0aW9uLCBvcHRpb25zKTogbnVsbCB8IERhdGUge1xuICAgIGlmICh2YWx1ZSA9PSBudWxsKSB7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICBpZiAoISh2YWx1ZSBpbnN0YW5jZW9mIGdsb2JhbERhdGUpKSB7XG4gICAgICB2YWx1ZSA9IG5ldyBnbG9iYWxEYXRlKGdsb2JhbERhdGUucGFyc2UodmFsdWUpKTtcbiAgICB9XG5cbiAgICB2YWx1ZSA9IHZhbHVlIGFzIERhdGU7XG5cbiAgICBsZXQgeWVhcjtcbiAgICBpZiAob3B0aW9ucyAmJiBvcHRpb25zLnVzZVVUQykge1xuICAgICAgeWVhciA9IHZhbHVlLmdldFVUQ0Z1bGxZZWFyKCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHllYXIgPSB2YWx1ZS5nZXRGdWxsWWVhcigpO1xuICAgIH1cblxuICAgIGlmICh5ZWFyIDwgMSB8fCB5ZWFyID4gOTk5OSkge1xuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignT3V0IG9mIHJhbmdlLicpO1xuICAgIH1cblxuICAgIGlmIChpc05hTih2YWx1ZSkpIHtcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ0ludmFsaWQgZGF0ZS4nKTtcbiAgICB9XG5cbiAgICByZXR1cm4gdmFsdWU7XG4gIH1cbn07XG5cbmV4cG9ydCBkZWZhdWx0IERhdGU7XG5tb2R1bGUuZXhwb3J0cyA9IERhdGU7XG4iXSwibWFwcGluZ3MiOiI7Ozs7OztBQUNBLElBQUFBLEtBQUEsR0FBQUMsT0FBQTtBQUVBO0FBQ0EsTUFBTUMsVUFBVSxHQUFHQyxNQUFNLENBQUNDLElBQUk7QUFDOUIsTUFBTUMsVUFBVSxHQUFHQyxlQUFTLENBQUNDLFNBQVMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDO0FBQzVDLE1BQU1DLFdBQVcsR0FBR0MsTUFBTSxDQUFDQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQztBQUN2QyxNQUFNQyxXQUFXLEdBQUdGLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUM7QUFFdkMsTUFBTU4sSUFBYyxHQUFHO0VBQ3JCUSxFQUFFLEVBQUUsSUFBSTtFQUNSQyxJQUFJLEVBQUUsT0FBTztFQUNiQyxJQUFJLEVBQUUsTUFBTTtFQUVaQyxXQUFXLEVBQUUsU0FBQUEsQ0FBQSxFQUFXO0lBQ3RCLE9BQU8sTUFBTTtFQUNmLENBQUM7RUFFREMsZ0JBQWdCLEVBQUUsU0FBQUEsQ0FBQSxFQUFXO0lBQzNCLE9BQU9QLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDRSxFQUFFLENBQUMsQ0FBQztFQUMvQixDQUFDO0VBRURLLHVCQUF1QkEsQ0FBQ0MsU0FBUyxFQUFFQyxPQUFPLEVBQUU7SUFDMUMsSUFBSUQsU0FBUyxDQUFDRSxLQUFLLElBQUksSUFBSSxFQUFFO01BQzNCLE9BQU9aLFdBQVc7SUFDcEI7SUFFQSxPQUFPRyxXQUFXO0VBQ3BCLENBQUM7RUFFRCxDQUFFVSxxQkFBcUJBLENBQUNILFNBQVMsRUFBRUMsT0FBTyxFQUFFO0lBQzFDLElBQUlELFNBQVMsQ0FBQ0UsS0FBSyxJQUFJLElBQUksRUFBRTtNQUMzQjtJQUNGO0lBRUEsTUFBTUEsS0FBSyxHQUFHRixTQUFTLENBQUNFLEtBQVksQ0FBQyxDQUFDOztJQUV0QyxJQUFJRSxJQUFlO0lBQ25CLElBQUlILE9BQU8sQ0FBQ0ksTUFBTSxFQUFFO01BQ2xCRCxJQUFJLEdBQUdoQixlQUFTLENBQUNrQixFQUFFLENBQUNKLEtBQUssQ0FBQ0ssY0FBYyxDQUFDLENBQUMsRUFBRUwsS0FBSyxDQUFDTSxXQUFXLENBQUMsQ0FBQyxHQUFHLENBQUMsRUFBRU4sS0FBSyxDQUFDTyxVQUFVLENBQUMsQ0FBQyxDQUFDO0lBQzFGLENBQUMsTUFBTTtNQUNMTCxJQUFJLEdBQUdoQixlQUFTLENBQUNrQixFQUFFLENBQUNKLEtBQUssQ0FBQ1EsV0FBVyxDQUFDLENBQUMsRUFBRVIsS0FBSyxDQUFDUyxRQUFRLENBQUMsQ0FBQyxHQUFHLENBQUMsRUFBRVQsS0FBSyxDQUFDVSxPQUFPLENBQUMsQ0FBQyxDQUFDO0lBQ2pGO0lBRUEsTUFBTUMsSUFBSSxHQUFHMUIsVUFBVSxDQUFDMkIsS0FBSyxDQUFDVixJQUFJLEVBQUVXLGdCQUFVLENBQUNDLElBQUksQ0FBQztJQUNwRCxNQUFNQyxNQUFNLEdBQUcxQixNQUFNLENBQUMyQixLQUFLLENBQUMsQ0FBQyxDQUFDO0lBQzlCRCxNQUFNLENBQUNFLFdBQVcsQ0FBQ04sSUFBSSxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUM7SUFDOUIsTUFBTUksTUFBTTtFQUNkLENBQUM7RUFFRDtFQUNBRyxRQUFRLEVBQUUsU0FBQUEsQ0FBU2xCLEtBQUssRUFBRW1CLFNBQVMsRUFBRXBCLE9BQU8sRUFBZTtJQUN6RCxJQUFJQyxLQUFLLElBQUksSUFBSSxFQUFFO01BQ2pCLE9BQU8sSUFBSTtJQUNiO0lBRUEsSUFBSSxFQUFFQSxLQUFLLFlBQVlsQixVQUFVLENBQUMsRUFBRTtNQUNsQ2tCLEtBQUssR0FBRyxJQUFJbEIsVUFBVSxDQUFDQSxVQUFVLENBQUNzQyxLQUFLLENBQUNwQixLQUFLLENBQUMsQ0FBQztJQUNqRDtJQUVBQSxLQUFLLEdBQUdBLEtBQWE7SUFFckIsSUFBSXFCLElBQUk7SUFDUixJQUFJdEIsT0FBTyxJQUFJQSxPQUFPLENBQUNJLE1BQU0sRUFBRTtNQUM3QmtCLElBQUksR0FBR3JCLEtBQUssQ0FBQ0ssY0FBYyxDQUFDLENBQUM7SUFDL0IsQ0FBQyxNQUFNO01BQ0xnQixJQUFJLEdBQUdyQixLQUFLLENBQUNRLFdBQVcsQ0FBQyxDQUFDO0lBQzVCO0lBRUEsSUFBSWEsSUFBSSxHQUFHLENBQUMsSUFBSUEsSUFBSSxHQUFHLElBQUksRUFBRTtNQUMzQixNQUFNLElBQUlDLFNBQVMsQ0FBQyxlQUFlLENBQUM7SUFDdEM7SUFFQSxJQUFJQyxLQUFLLENBQUN2QixLQUFLLENBQUMsRUFBRTtNQUNoQixNQUFNLElBQUlzQixTQUFTLENBQUMsZUFBZSxDQUFDO0lBQ3RDO0lBRUEsT0FBT3RCLEtBQUs7RUFDZDtBQUNGLENBQUM7QUFBQyxJQUFBd0IsUUFBQSxHQUFBQyxPQUFBLENBQUFDLE9BQUEsR0FFYTFDLElBQUk7QUFDbkIyQyxNQUFNLENBQUNGLE9BQU8sR0FBR3pDLElBQUkifQ==