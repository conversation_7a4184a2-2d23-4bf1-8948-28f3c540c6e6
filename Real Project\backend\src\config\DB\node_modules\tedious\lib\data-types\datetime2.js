"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _core = require("@js-joda/core");
var _writableTrackingBuffer = _interopRequireDefault(require("../tracking-buffer/writable-tracking-buffer"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const EPOCH_DATE = _core.LocalDate.ofYearDay(1, 1);
const NULL_LENGTH = Buffer.from([0x00]);
const DateTime2 = {
  id: 0x2A,
  type: 'DATETIME2N',
  name: 'DateTime2',
  declaration: function (parameter) {
    return 'datetime2(' + this.resolveScale(parameter) + ')';
  },
  resolveScale: function (parameter) {
    if (parameter.scale != null) {
      return parameter.scale;
    } else if (parameter.value === null) {
      return 0;
    } else {
      return 7;
    }
  },
  generateTypeInfo(parameter, _options) {
    return Buffer.from([this.id, parameter.scale]);
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    switch (parameter.scale) {
      case 0:
      case 1:
      case 2:
        return Buffer.from([0x06]);
      case 3:
      case 4:
        return Buffer.from([0x07]);
      case 5:
      case 6:
      case 7:
        return Buffer.from([0x08]);
      default:
        throw new Error('invalid scale');
    }
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    const value = parameter.value;
    let scale = parameter.scale;
    const buffer = new _writableTrackingBuffer.default(16);
    scale = scale;
    let timestamp;
    if (options.useUTC) {
      timestamp = ((value.getUTCHours() * 60 + value.getUTCMinutes()) * 60 + value.getUTCSeconds()) * 1000 + value.getUTCMilliseconds();
    } else {
      timestamp = ((value.getHours() * 60 + value.getMinutes()) * 60 + value.getSeconds()) * 1000 + value.getMilliseconds();
    }
    timestamp = timestamp * Math.pow(10, scale - 3);
    timestamp += (value.nanosecondDelta != null ? value.nanosecondDelta : 0) * Math.pow(10, scale);
    timestamp = Math.round(timestamp);
    switch (scale) {
      case 0:
      case 1:
      case 2:
        buffer.writeUInt24LE(timestamp);
        break;
      case 3:
      case 4:
        buffer.writeUInt32LE(timestamp);
        break;
      case 5:
      case 6:
      case 7:
        buffer.writeUInt40LE(timestamp);
    }
    let date;
    if (options.useUTC) {
      date = _core.LocalDate.of(value.getUTCFullYear(), value.getUTCMonth() + 1, value.getUTCDate());
    } else {
      date = _core.LocalDate.of(value.getFullYear(), value.getMonth() + 1, value.getDate());
    }
    const days = EPOCH_DATE.until(date, _core.ChronoUnit.DAYS);
    buffer.writeUInt24LE(days);
    yield buffer.data;
  },
  validate: function (value, collation, options) {
    if (value == null) {
      return null;
    }
    if (!(value instanceof Date)) {
      value = new Date(Date.parse(value));
    }
    value = value;
    let year;
    if (options && options.useUTC) {
      year = value.getUTCFullYear();
    } else {
      year = value.getFullYear();
    }
    if (year < 1 || year > 9999) {
      throw new TypeError('Out of range.');
    }
    if (isNaN(value)) {
      throw new TypeError('Invalid date.');
    }
    return value;
  }
};
var _default = exports.default = DateTime2;
module.exports = DateTime2;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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