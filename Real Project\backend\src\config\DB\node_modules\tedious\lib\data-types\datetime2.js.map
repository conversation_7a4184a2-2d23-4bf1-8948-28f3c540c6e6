{"version": 3, "file": "datetime2.js", "names": ["_core", "require", "_writableTrackingBuffer", "_interopRequireDefault", "obj", "__esModule", "default", "EPOCH_DATE", "LocalDate", "ofYearDay", "NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "DateTime2", "id", "type", "name", "declaration", "parameter", "resolveScale", "scale", "value", "generateTypeInfo", "_options", "generateParameterLength", "options", "Error", "generateParameterData", "buffer", "WritableTrackingBuffer", "timestamp", "useUTC", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "getUTCMilliseconds", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "Math", "pow", "nanosecondDelta", "round", "writeUInt24LE", "writeUInt32LE", "writeUInt40LE", "date", "of", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getFullYear", "getMonth", "getDate", "days", "until", "ChronoUnit", "DAYS", "data", "validate", "collation", "Date", "parse", "year", "TypeError", "isNaN", "_default", "exports", "module"], "sources": ["../../src/data-types/datetime2.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport { ChronoUnit, LocalDate } from '@js-joda/core';\nimport WritableTrackingBuffer from '../tracking-buffer/writable-tracking-buffer';\n\nconst EPOCH_DATE = LocalDate.ofYearDay(1, 1);\nconst NULL_LENGTH = Buffer.from([0x00]);\n\nconst DateTime2: DataType & { resolveScale: NonNullable<DataType['resolveScale']> } = {\n  id: 0x2A,\n  type: 'DATETIME2N',\n  name: 'DateTime2',\n\n  declaration: function(parameter) {\n    return 'datetime2(' + (this.resolveScale(parameter)) + ')';\n  },\n\n  resolveScale: function(parameter) {\n    if (parameter.scale != null) {\n      return parameter.scale;\n    } else if (parameter.value === null) {\n      return 0;\n    } else {\n      return 7;\n    }\n  },\n\n  generateTypeInfo(parameter, _options) {\n    return Buffer.from([this.id, parameter.scale!]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    switch (parameter.scale!) {\n      case 0:\n      case 1:\n      case 2:\n        return Buffer.from([0x06]);\n\n      case 3:\n      case 4:\n        return Buffer.from([0x07]);\n\n      case 5:\n      case 6:\n      case 7:\n        return Buffer.from([0x08]);\n\n      default:\n        throw new Error('invalid scale');\n    }\n  },\n\n  *generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const value = parameter.value;\n    let scale = parameter.scale;\n\n    const buffer = new WritableTrackingBuffer(16);\n    scale = scale!;\n\n    let timestamp: number;\n    if (options.useUTC) {\n      timestamp = ((value.getUTCHours() * 60 + value.getUTCMinutes()) * 60 + value.getUTCSeconds()) * 1000 + value.getUTCMilliseconds();\n    } else {\n      timestamp = ((value.getHours() * 60 + value.getMinutes()) * 60 + value.getSeconds()) * 1000 + value.getMilliseconds();\n    }\n    timestamp = timestamp * Math.pow(10, scale - 3);\n    timestamp += (value.nanosecondDelta != null ? value.nanosecondDelta : 0) * Math.pow(10, scale);\n    timestamp = Math.round(timestamp);\n\n    switch (scale) {\n      case 0:\n      case 1:\n      case 2:\n        buffer.writeUInt24LE(timestamp);\n        break;\n      case 3:\n      case 4:\n        buffer.writeUInt32LE(timestamp);\n        break;\n      case 5:\n      case 6:\n      case 7:\n        buffer.writeUInt40LE(timestamp);\n    }\n\n    let date;\n    if (options.useUTC) {\n      date = LocalDate.of(value.getUTCFullYear(), value.getUTCMonth() + 1, value.getUTCDate());\n    } else {\n      date = LocalDate.of(value.getFullYear(), value.getMonth() + 1, value.getDate());\n    }\n\n    const days = EPOCH_DATE.until(date, ChronoUnit.DAYS);\n    buffer.writeUInt24LE(days);\n    yield buffer.data;\n  },\n\n  validate: function(value: any, collation, options): null | number {\n    if (value == null) {\n      return null;\n    }\n\n    if (!(value instanceof Date)) {\n      value = new Date(Date.parse(value));\n    }\n\n    value = value as Date;\n\n    let year;\n    if (options && options.useUTC) {\n      year = value.getUTCFullYear();\n    } else {\n      year = value.getFullYear();\n    }\n\n    if (year < 1 || year > 9999) {\n      throw new TypeError('Out of range.');\n    }\n\n    if (isNaN(value)) {\n      throw new TypeError('Invalid date.');\n    }\n\n    return value;\n  }\n};\n\nexport default DateTime2;\nmodule.exports = DateTime2;\n"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAiF,SAAAE,uBAAAC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEjF,MAAMG,UAAU,GAAGC,eAAS,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AAC5C,MAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAMC,SAA6E,GAAG;EACpFC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,WAAW;EAEjBC,WAAW,EAAE,SAAAA,CAASC,SAAS,EAAE;IAC/B,OAAO,YAAY,GAAI,IAAI,CAACC,YAAY,CAACD,SAAS,CAAE,GAAG,GAAG;EAC5D,CAAC;EAEDC,YAAY,EAAE,SAAAA,CAASD,SAAS,EAAE;IAChC,IAAIA,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOF,SAAS,CAACE,KAAK;IACxB,CAAC,MAAM,IAAIF,SAAS,CAACG,KAAK,KAAK,IAAI,EAAE;MACnC,OAAO,CAAC;IACV,CAAC,MAAM;MACL,OAAO,CAAC;IACV;EACF,CAAC;EAEDC,gBAAgBA,CAACJ,SAAS,EAAEK,QAAQ,EAAE;IACpC,OAAOZ,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAACE,EAAE,EAAEI,SAAS,CAACE,KAAK,CAAE,CAAC;EACjD,CAAC;EAEDI,uBAAuBA,CAACN,SAAS,EAAEO,OAAO,EAAE;IAC1C,IAAIP,SAAS,CAACG,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOX,WAAW;IACpB;IAEA,QAAQQ,SAAS,CAACE,KAAK;MACrB,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOT,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;MAE5B,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOD,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;MAE5B,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOD,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;MAE5B;QACE,MAAM,IAAIc,KAAK,CAAC,eAAe,CAAC;IACpC;EACF,CAAC;EAED,CAACC,qBAAqBA,CAACT,SAAS,EAAEO,OAAO,EAAE;IACzC,IAAIP,SAAS,CAACG,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMA,KAAK,GAAGH,SAAS,CAACG,KAAK;IAC7B,IAAID,KAAK,GAAGF,SAAS,CAACE,KAAK;IAE3B,MAAMQ,MAAM,GAAG,IAAIC,+BAAsB,CAAC,EAAE,CAAC;IAC7CT,KAAK,GAAGA,KAAM;IAEd,IAAIU,SAAiB;IACrB,IAAIL,OAAO,CAACM,MAAM,EAAE;MAClBD,SAAS,GAAG,CAAC,CAACT,KAAK,CAACW,WAAW,CAAC,CAAC,GAAG,EAAE,GAAGX,KAAK,CAACY,aAAa,CAAC,CAAC,IAAI,EAAE,GAAGZ,KAAK,CAACa,aAAa,CAAC,CAAC,IAAI,IAAI,GAAGb,KAAK,CAACc,kBAAkB,CAAC,CAAC;IACnI,CAAC,MAAM;MACLL,SAAS,GAAG,CAAC,CAACT,KAAK,CAACe,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAGf,KAAK,CAACgB,UAAU,CAAC,CAAC,IAAI,EAAE,GAAGhB,KAAK,CAACiB,UAAU,CAAC,CAAC,IAAI,IAAI,GAAGjB,KAAK,CAACkB,eAAe,CAAC,CAAC;IACvH;IACAT,SAAS,GAAGA,SAAS,GAAGU,IAAI,CAACC,GAAG,CAAC,EAAE,EAAErB,KAAK,GAAG,CAAC,CAAC;IAC/CU,SAAS,IAAI,CAACT,KAAK,CAACqB,eAAe,IAAI,IAAI,GAAGrB,KAAK,CAACqB,eAAe,GAAG,CAAC,IAAIF,IAAI,CAACC,GAAG,CAAC,EAAE,EAAErB,KAAK,CAAC;IAC9FU,SAAS,GAAGU,IAAI,CAACG,KAAK,CAACb,SAAS,CAAC;IAEjC,QAAQV,KAAK;MACX,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACJQ,MAAM,CAACgB,aAAa,CAACd,SAAS,CAAC;QAC/B;MACF,KAAK,CAAC;MACN,KAAK,CAAC;QACJF,MAAM,CAACiB,aAAa,CAACf,SAAS,CAAC;QAC/B;MACF,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACJF,MAAM,CAACkB,aAAa,CAAChB,SAAS,CAAC;IACnC;IAEA,IAAIiB,IAAI;IACR,IAAItB,OAAO,CAACM,MAAM,EAAE;MAClBgB,IAAI,GAAGvC,eAAS,CAACwC,EAAE,CAAC3B,KAAK,CAAC4B,cAAc,CAAC,CAAC,EAAE5B,KAAK,CAAC6B,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE7B,KAAK,CAAC8B,UAAU,CAAC,CAAC,CAAC;IAC1F,CAAC,MAAM;MACLJ,IAAI,GAAGvC,eAAS,CAACwC,EAAE,CAAC3B,KAAK,CAAC+B,WAAW,CAAC,CAAC,EAAE/B,KAAK,CAACgC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEhC,KAAK,CAACiC,OAAO,CAAC,CAAC,CAAC;IACjF;IAEA,MAAMC,IAAI,GAAGhD,UAAU,CAACiD,KAAK,CAACT,IAAI,EAAEU,gBAAU,CAACC,IAAI,CAAC;IACpD9B,MAAM,CAACgB,aAAa,CAACW,IAAI,CAAC;IAC1B,MAAM3B,MAAM,CAAC+B,IAAI;EACnB,CAAC;EAEDC,QAAQ,EAAE,SAAAA,CAASvC,KAAU,EAAEwC,SAAS,EAAEpC,OAAO,EAAiB;IAChE,IAAIJ,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,EAAEA,KAAK,YAAYyC,IAAI,CAAC,EAAE;MAC5BzC,KAAK,GAAG,IAAIyC,IAAI,CAACA,IAAI,CAACC,KAAK,CAAC1C,KAAK,CAAC,CAAC;IACrC;IAEAA,KAAK,GAAGA,KAAa;IAErB,IAAI2C,IAAI;IACR,IAAIvC,OAAO,IAAIA,OAAO,CAACM,MAAM,EAAE;MAC7BiC,IAAI,GAAG3C,KAAK,CAAC4B,cAAc,CAAC,CAAC;IAC/B,CAAC,MAAM;MACLe,IAAI,GAAG3C,KAAK,CAAC+B,WAAW,CAAC,CAAC;IAC5B;IAEA,IAAIY,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,IAAI,EAAE;MAC3B,MAAM,IAAIC,SAAS,CAAC,eAAe,CAAC;IACtC;IAEA,IAAIC,KAAK,CAAC7C,KAAK,CAAC,EAAE;MAChB,MAAM,IAAI4C,SAAS,CAAC,eAAe,CAAC;IACtC;IAEA,OAAO5C,KAAK;EACd;AACF,CAAC;AAAC,IAAA8C,QAAA,GAAAC,OAAA,CAAA9D,OAAA,GAEaO,SAAS;AACxBwD,MAAM,CAACD,OAAO,GAAGvD,SAAS"}