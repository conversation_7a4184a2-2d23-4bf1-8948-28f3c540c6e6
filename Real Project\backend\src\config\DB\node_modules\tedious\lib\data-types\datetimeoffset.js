"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _core = require("@js-joda/core");
var _writableTrackingBuffer = _interopRequireDefault(require("../tracking-buffer/writable-tracking-buffer"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const EPOCH_DATE = _core.LocalDate.ofYearDay(1, 1);
const NULL_LENGTH = Buffer.from([0x00]);
const DateTimeOffset = {
  id: 0x2B,
  type: 'DATETIMEOFFSETN',
  name: 'DateTimeOffset',
  declaration: function (parameter) {
    return 'datetimeoffset(' + this.resolveScale(parameter) + ')';
  },
  resolveScale: function (parameter) {
    if (parameter.scale != null) {
      return parameter.scale;
    } else if (parameter.value === null) {
      return 0;
    } else {
      return 7;
    }
  },
  generateTypeInfo(parameter) {
    return Buffer.from([this.id, parameter.scale]);
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    switch (parameter.scale) {
      case 0:
      case 1:
      case 2:
        return Buffer.from([0x08]);
      case 3:
      case 4:
        return Buffer.from([0x09]);
      case 5:
      case 6:
      case 7:
        return Buffer.from([0x0A]);
      default:
        throw new Error('invalid scale');
    }
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    const value = parameter.value;
    let scale = parameter.scale;
    const buffer = new _writableTrackingBuffer.default(16);
    scale = scale;
    let timestamp;
    timestamp = ((value.getUTCHours() * 60 + value.getUTCMinutes()) * 60 + value.getUTCSeconds()) * 1000 + value.getMilliseconds();
    timestamp = timestamp * Math.pow(10, scale - 3);
    timestamp += (value.nanosecondDelta != null ? value.nanosecondDelta : 0) * Math.pow(10, scale);
    timestamp = Math.round(timestamp);
    switch (scale) {
      case 0:
      case 1:
      case 2:
        buffer.writeUInt24LE(timestamp);
        break;
      case 3:
      case 4:
        buffer.writeUInt32LE(timestamp);
        break;
      case 5:
      case 6:
      case 7:
        buffer.writeUInt40LE(timestamp);
    }
    const date = _core.LocalDate.of(value.getUTCFullYear(), value.getUTCMonth() + 1, value.getUTCDate());
    const days = EPOCH_DATE.until(date, _core.ChronoUnit.DAYS);
    buffer.writeUInt24LE(days);
    const offset = -value.getTimezoneOffset();
    buffer.writeInt16LE(offset);
    yield buffer.data;
  },
  validate: function (value, collation, options) {
    if (value == null) {
      return null;
    }
    if (!(value instanceof Date)) {
      value = new Date(Date.parse(value));
    }
    value = value;
    let year;
    if (options && options.useUTC) {
      year = value.getUTCFullYear();
    } else {
      year = value.getFullYear();
    }
    if (year < 1 || year > 9999) {
      throw new TypeError('Out of range.');
    }
    if (isNaN(value)) {
      throw new TypeError('Invalid date.');
    }
    return value;
  }
};
var _default = exports.default = DateTimeOffset;
module.exports = DateTimeOffset;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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