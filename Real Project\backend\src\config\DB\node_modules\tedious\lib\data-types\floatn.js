"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
const FloatN = {
  id: 0x6D,
  type: 'FLTN',
  name: 'FloatN',
  declaration() {
    throw new Error('not implemented');
  },
  generateTypeInfo() {
    throw new Error('not implemented');
  },
  generateParameterLength() {
    throw new Error('not implemented');
  },
  generateParameterData() {
    throw new Error('not implemented');
  },
  validate() {
    throw new Error('not implemented');
  }
};
var _default = exports.default = FloatN;
module.exports = FloatN;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJGbG9hdE4iLCJpZCIsInR5cGUiLCJuYW1lIiwiZGVjbGFyYXRpb24iLCJFcnJvciIsImdlbmVyYXRlVHlwZUluZm8iLCJnZW5lcmF0ZVBhcmFtZXRlckxlbmd0aCIsImdlbmVyYXRlUGFyYW1ldGVyRGF0YSIsInZhbGlkYXRlIiwiX2RlZmF1bHQiLCJleHBvcnRzIiwiZGVmYXVsdCIsIm1vZHVsZSJdLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy9kYXRhLXR5cGVzL2Zsb2F0bi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIERhdGFUeXBlIH0gZnJvbSAnLi4vZGF0YS10eXBlJztcblxuY29uc3QgRmxvYXROOiBEYXRhVHlwZSA9IHtcbiAgaWQ6IDB4NkQsXG4gIHR5cGU6ICdGTFROJyxcbiAgbmFtZTogJ0Zsb2F0TicsXG5cbiAgZGVjbGFyYXRpb24oKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdub3QgaW1wbGVtZW50ZWQnKTtcbiAgfSxcblxuICBnZW5lcmF0ZVR5cGVJbmZvKCkge1xuICAgIHRocm93IG5ldyBFcnJvcignbm90IGltcGxlbWVudGVkJyk7XG4gIH0sXG5cbiAgZ2VuZXJhdGVQYXJhbWV0ZXJMZW5ndGgoKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdub3QgaW1wbGVtZW50ZWQnKTtcbiAgfSxcblxuICBnZW5lcmF0ZVBhcmFtZXRlckRhdGEoKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdub3QgaW1wbGVtZW50ZWQnKTtcbiAgfSxcblxuICB2YWxpZGF0ZSgpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ25vdCBpbXBsZW1lbnRlZCcpO1xuICB9XG59O1xuXG5leHBvcnQgZGVmYXVsdCBGbG9hdE47XG5tb2R1bGUuZXhwb3J0cyA9IEZsb2F0TjtcbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBRUEsTUFBTUEsTUFBZ0IsR0FBRztFQUN2QkMsRUFBRSxFQUFFLElBQUk7RUFDUkMsSUFBSSxFQUFFLE1BQU07RUFDWkMsSUFBSSxFQUFFLFFBQVE7RUFFZEMsV0FBV0EsQ0FBQSxFQUFHO0lBQ1osTUFBTSxJQUFJQyxLQUFLLENBQUMsaUJBQWlCLENBQUM7RUFDcEMsQ0FBQztFQUVEQyxnQkFBZ0JBLENBQUEsRUFBRztJQUNqQixNQUFNLElBQUlELEtBQUssQ0FBQyxpQkFBaUIsQ0FBQztFQUNwQyxDQUFDO0VBRURFLHVCQUF1QkEsQ0FBQSxFQUFHO0lBQ3hCLE1BQU0sSUFBSUYsS0FBSyxDQUFDLGlCQUFpQixDQUFDO0VBQ3BDLENBQUM7RUFFREcscUJBQXFCQSxDQUFBLEVBQUc7SUFDdEIsTUFBTSxJQUFJSCxLQUFLLENBQUMsaUJBQWlCLENBQUM7RUFDcEMsQ0FBQztFQUVESSxRQUFRQSxDQUFBLEVBQUc7SUFDVCxNQUFNLElBQUlKLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQztFQUNwQztBQUNGLENBQUM7QUFBQyxJQUFBSyxRQUFBLEdBQUFDLE9BQUEsQ0FBQUMsT0FBQSxHQUVhWixNQUFNO0FBQ3JCYSxNQUFNLENBQUNGLE9BQU8sR0FBR1gsTUFBTSJ9