{"version": 3, "file": "floatn.js", "names": ["FloatN", "id", "type", "name", "declaration", "Error", "generateTypeInfo", "generateParameterLength", "generateParameterData", "validate", "_default", "exports", "default", "module"], "sources": ["../../src/data-types/floatn.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\n\nconst FloatN: DataType = {\n  id: 0x6D,\n  type: 'FLTN',\n  name: 'FloatN',\n\n  declaration() {\n    throw new Error('not implemented');\n  },\n\n  generateTypeInfo() {\n    throw new Error('not implemented');\n  },\n\n  generateParameterLength() {\n    throw new Error('not implemented');\n  },\n\n  generateParameterData() {\n    throw new Error('not implemented');\n  },\n\n  validate() {\n    throw new Error('not implemented');\n  }\n};\n\nexport default FloatN;\nmodule.exports = FloatN;\n"], "mappings": ";;;;;;AAEA,MAAMA,MAAgB,GAAG;EACvBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,QAAQ;EAEdC,WAAWA,CAAA,EAAG;IACZ,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,MAAM,IAAID,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDE,uBAAuBA,CAAA,EAAG;IACxB,MAAM,IAAIF,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDG,qBAAqBA,CAAA,EAAG;IACtB,MAAM,IAAIH,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDI,QAAQA,CAAA,EAAG;IACT,MAAM,IAAIJ,KAAK,CAAC,iBAAiB,CAAC;EACpC;AACF,CAAC;AAAC,IAAAK,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEaZ,MAAM;AACrBa,MAAM,CAACF,OAAO,GAAGX,MAAM"}