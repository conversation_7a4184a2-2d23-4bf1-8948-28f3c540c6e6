{"version": 3, "file": "money.js", "names": ["_moneyn", "_interopRequireDefault", "require", "obj", "__esModule", "default", "SHIFT_LEFT_32", "SHIFT_RIGHT_32", "NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "DATA_LENGTH", "Money", "id", "type", "name", "declaration", "generateTypeInfo", "MoneyN", "generateParameterLength", "parameter", "options", "value", "generateParameterData", "buffer", "alloc", "writeInt32LE", "Math", "floor", "validate", "parseFloat", "isNaN", "TypeError", "_default", "exports", "module"], "sources": ["../../src/data-types/money.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport MoneyN from './moneyn';\n\nconst SHIFT_LEFT_32 = (1 << 16) * (1 << 16);\nconst SHIFT_RIGHT_32 = 1 / SHIFT_LEFT_32;\n\nconst NULL_LENGTH = Buffer.from([0x00]);\nconst DATA_LENGTH = Buffer.from([0x08]);\n\nconst Money: DataType = {\n  id: 0x3C,\n  type: 'MONEY',\n  name: 'Money',\n\n  declaration: function() {\n    return 'money';\n  },\n\n  generateTypeInfo: function() {\n    return Buffer.from([MoneyN.id, 0x08]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    return DATA_LENGTH;\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const value = parameter.value * 10000;\n\n    const buffer = Buffer.alloc(8);\n    buffer.writeInt32LE(Math.floor(value * SHIFT_RIGHT_32), 0);\n    buffer.writeInt32LE(value & -1, 4);\n    yield buffer;\n  },\n\n  validate: function(value): number | null {\n    if (value == null) {\n      return null;\n    }\n    value = parseFloat(value);\n    if (isNaN(value)) {\n      throw new TypeError('Invalid number.');\n    }\n    // money： -922337203685477.5808 to 922337203685477.5807\n    // in javascript -922337203685477.5808 === -922337203685477.6\n    //                922337203685477.5807 === 922337203685477.6\n    // javascript number doesn't have enough precision.\n    if (value < -922337203685477.6 || value > 922337203685477.6) {\n      throw new TypeError('Value must be between -922337203685477.5808 and 922337203685477.5807, inclusive.');\n    }\n\n    return value;\n  }\n};\n\nexport default Money;\nmodule.exports = Money;\n"], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA8B,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE9B,MAAMG,aAAa,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC;AAC3C,MAAMC,cAAc,GAAG,CAAC,GAAGD,aAAa;AAExC,MAAME,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,MAAMC,WAAW,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAME,KAAe,GAAG;EACtBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,OAAO;EAEbC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,OAAO;EAChB,CAAC;EAEDC,gBAAgB,EAAE,SAAAA,CAAA,EAAW;IAC3B,OAAOR,MAAM,CAACC,IAAI,CAAC,CAACQ,eAAM,CAACL,EAAE,EAAE,IAAI,CAAC,CAAC;EACvC,CAAC;EAEDM,uBAAuBA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOd,WAAW;IACpB;IAEA,OAAOG,WAAW;EACpB,CAAC;EAED,CAAEY,qBAAqBA,CAACH,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMA,KAAK,GAAGF,SAAS,CAACE,KAAK,GAAG,KAAK;IAErC,MAAME,MAAM,GAAGf,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACE,YAAY,CAACC,IAAI,CAACC,KAAK,CAACN,KAAK,GAAGf,cAAc,CAAC,EAAE,CAAC,CAAC;IAC1DiB,MAAM,CAACE,YAAY,CAACJ,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAClC,MAAME,MAAM;EACd,CAAC;EAEDK,QAAQ,EAAE,SAAAA,CAASP,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IACAA,KAAK,GAAGQ,UAAU,CAACR,KAAK,CAAC;IACzB,IAAIS,KAAK,CAACT,KAAK,CAAC,EAAE;MAChB,MAAM,IAAIU,SAAS,CAAC,iBAAiB,CAAC;IACxC;IACA;IACA;IACA;IACA;IACA,IAAIV,KAAK,GAAG,CAAC,iBAAiB,IAAIA,KAAK,GAAG,iBAAiB,EAAE;MAC3D,MAAM,IAAIU,SAAS,CAAC,kFAAkF,CAAC;IACzG;IAEA,OAAOV,KAAK;EACd;AACF,CAAC;AAAC,IAAAW,QAAA,GAAAC,OAAA,CAAA7B,OAAA,GAEaO,KAAK;AACpBuB,MAAM,CAACD,OAAO,GAAGtB,KAAK"}