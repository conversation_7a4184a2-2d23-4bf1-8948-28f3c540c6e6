"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
const NULL_LENGTH = Buffer.from([0xFF, 0xFF, 0xFF, 0xFF]);
const NText = {
  id: 0x63,
  type: 'NTEXT',
  name: 'NText',
  hasTableName: true,
  declaration: function () {
    return 'ntext';
  },
  resolveLength: function (parameter) {
    const value = parameter.value; // Temporary solution. Remove 'any' later.

    if (value != null) {
      return value.length;
    } else {
      return -1;
    }
  },
  generateTypeInfo(parameter, _options) {
    const buffer = Buffer.alloc(10);
    buffer.writeUInt8(this.id, 0);
    buffer.writeInt32LE(parameter.length, 1);
    if (parameter.collation) {
      parameter.collation.toBuffer().copy(buffer, 5, 0, 5);
    }
    return buffer;
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    const buffer = Buffer.alloc(4);
    buffer.writeInt32LE(Buffer.byteLength(parameter.value, 'ucs2'), 0);
    return buffer;
  },
  generateParameterData: function* (parameter, options) {
    if (parameter.value == null) {
      return;
    }
    yield Buffer.from(parameter.value.toString(), 'ucs2');
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    if (typeof value !== 'string') {
      throw new TypeError('Invalid string.');
    }
    return value;
  }
};
var _default = exports.default = NText;
module.exports = NText;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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