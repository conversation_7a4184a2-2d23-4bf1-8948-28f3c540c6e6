{"version": 3, "file": "ntext.js", "names": ["NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "NText", "id", "type", "name", "hasTableName", "declaration", "<PERSON><PERSON><PERSON><PERSON>", "parameter", "value", "length", "generateTypeInfo", "_options", "buffer", "alloc", "writeUInt8", "writeInt32LE", "collation", "<PERSON><PERSON><PERSON><PERSON>", "copy", "generateParameterLength", "options", "byteLength", "generateParameterData", "toString", "validate", "TypeError", "_default", "exports", "default", "module"], "sources": ["../../src/data-types/ntext.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\n\nconst NULL_LENGTH = Buffer.from([0xFF, 0xFF, 0xFF, 0xFF]);\n\nconst NText: DataType = {\n  id: 0x63,\n  type: 'NTEXT',\n  name: 'NText',\n\n  hasTableName: true,\n\n  declaration: function() {\n    return 'ntext';\n  },\n\n  resolveLength: function(parameter) {\n    const value = parameter.value as any; // Temporary solution. Remove 'any' later.\n\n    if (value != null) {\n      return value.length;\n    } else {\n      return -1;\n    }\n  },\n\n  generateTypeInfo(parameter, _options) {\n    const buffer = Buffer.alloc(10);\n    buffer.writeUInt8(this.id, 0);\n    buffer.writeInt32LE(parameter.length!, 1);\n\n    if (parameter.collation) {\n      parameter.collation.toBuffer().copy(buffer, 5, 0, 5);\n    }\n\n    return buffer;\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    const buffer = Buffer.alloc(4);\n    buffer.writeInt32LE(Buffer.byteLength(parameter.value, 'ucs2'), 0);\n    return buffer;\n  },\n\n  generateParameterData: function*(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    yield Buffer.from(parameter.value.toString(), 'ucs2');\n  },\n\n  validate: function(value): string | null {\n    if (value == null) {\n      return null;\n    }\n\n    if (typeof value !== 'string') {\n      throw new TypeError('Invalid string.');\n    }\n\n    return value;\n  }\n};\n\nexport default NText;\nmodule.exports = NText;\n"], "mappings": ";;;;;;AAEA,MAAMA,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAEzD,MAAMC,KAAe,GAAG;EACtBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,OAAO;EAEbC,YAAY,EAAE,IAAI;EAElBC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,OAAO;EAChB,CAAC;EAEDC,aAAa,EAAE,SAAAA,CAASC,SAAS,EAAE;IACjC,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAY,CAAC,CAAC;;IAEtC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOA,KAAK,CAACC,MAAM;IACrB,CAAC,MAAM;MACL,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAEDC,gBAAgBA,CAACH,SAAS,EAAEI,QAAQ,EAAE;IACpC,MAAMC,MAAM,GAAGd,MAAM,CAACe,KAAK,CAAC,EAAE,CAAC;IAC/BD,MAAM,CAACE,UAAU,CAAC,IAAI,CAACb,EAAE,EAAE,CAAC,CAAC;IAC7BW,MAAM,CAACG,YAAY,CAACR,SAAS,CAACE,MAAM,EAAG,CAAC,CAAC;IAEzC,IAAIF,SAAS,CAACS,SAAS,EAAE;MACvBT,SAAS,CAACS,SAAS,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAACN,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtD;IAEA,OAAOA,MAAM;EACf,CAAC;EAEDO,uBAAuBA,CAACZ,SAAS,EAAEa,OAAO,EAAE;IAC1C,IAAIb,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOX,WAAW;IACpB;IAEA,MAAMe,MAAM,GAAGd,MAAM,CAACe,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACG,YAAY,CAACjB,MAAM,CAACuB,UAAU,CAACd,SAAS,CAACC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;IAClE,OAAOI,MAAM;EACf,CAAC;EAEDU,qBAAqB,EAAE,UAAAA,CAAUf,SAAS,EAAEa,OAAO,EAAE;IACnD,IAAIb,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMV,MAAM,CAACC,IAAI,CAACQ,SAAS,CAACC,KAAK,CAACe,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC;EACvD,CAAC;EAEDC,QAAQ,EAAE,SAAAA,CAAShB,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIiB,SAAS,CAAC,iBAAiB,CAAC;IACxC;IAEA,OAAOjB,KAAK;EACd;AACF,CAAC;AAAC,IAAAkB,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEa5B,KAAK;AACpB6B,MAAM,CAACF,OAAO,GAAG3B,KAAK"}