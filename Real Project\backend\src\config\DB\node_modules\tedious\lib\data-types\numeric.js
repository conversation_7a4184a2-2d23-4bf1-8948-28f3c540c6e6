"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _numericn = _interopRequireDefault(require("./numericn"));
var _writableTrackingBuffer = _interopRequireDefault(require("../tracking-buffer/writable-tracking-buffer"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const NULL_LENGTH = Buffer.from([0x00]);
const Numeric = {
  id: 0x3F,
  type: 'NUMERIC',
  name: 'Numeric',
  declaration: function (parameter) {
    return 'numeric(' + this.resolvePrecision(parameter) + ', ' + this.resolveScale(parameter) + ')';
  },
  resolvePrecision: function (parameter) {
    if (parameter.precision != null) {
      return parameter.precision;
    } else if (parameter.value === null) {
      return 1;
    } else {
      return 18;
    }
  },
  resolveScale: function (parameter) {
    if (parameter.scale != null) {
      return parameter.scale;
    } else {
      return 0;
    }
  },
  generateTypeInfo(parameter) {
    let precision;
    if (parameter.precision <= 9) {
      precision = 0x05;
    } else if (parameter.precision <= 19) {
      precision = 0x09;
    } else if (parameter.precision <= 28) {
      precision = 0x0D;
    } else {
      precision = 0x11;
    }
    return Buffer.from([_numericn.default.id, precision, parameter.precision, parameter.scale]);
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    const precision = parameter.precision;
    if (precision <= 9) {
      return Buffer.from([0x05]);
    } else if (precision <= 19) {
      return Buffer.from([0x09]);
    } else if (precision <= 28) {
      return Buffer.from([0x0D]);
    } else {
      return Buffer.from([0x11]);
    }
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    const sign = parameter.value < 0 ? 0 : 1;
    const value = Math.round(Math.abs(parameter.value * Math.pow(10, parameter.scale)));
    if (parameter.precision <= 9) {
      const buffer = Buffer.alloc(5);
      buffer.writeUInt8(sign, 0);
      buffer.writeUInt32LE(value, 1);
      yield buffer;
    } else if (parameter.precision <= 19) {
      const buffer = new _writableTrackingBuffer.default(10);
      buffer.writeUInt8(sign);
      buffer.writeUInt64LE(value);
      yield buffer.data;
    } else if (parameter.precision <= 28) {
      const buffer = new _writableTrackingBuffer.default(14);
      buffer.writeUInt8(sign);
      buffer.writeUInt64LE(value);
      buffer.writeUInt32LE(0x00000000);
      yield buffer.data;
    } else {
      const buffer = new _writableTrackingBuffer.default(18);
      buffer.writeUInt8(sign);
      buffer.writeUInt64LE(value);
      buffer.writeUInt32LE(0x00000000);
      buffer.writeUInt32LE(0x00000000);
      yield buffer.data;
    }
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    value = parseFloat(value);
    if (isNaN(value)) {
      throw new TypeError('Invalid number.');
    }
    return value;
  }
};
var _default = exports.default = Numeric;
module.exports = Numeric;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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