{"version": 3, "file": "numericn.js", "names": ["NumericN", "id", "type", "name", "declaration", "Error", "generateTypeInfo", "generateParameterLength", "generateParameterData", "validate", "_default", "exports", "default", "module"], "sources": ["../../src/data-types/numericn.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\n\nconst NumericN: DataType = {\n  id: 0x6C,\n  type: 'NUMERICN',\n  name: 'NumericN',\n\n  declaration() {\n    throw new Error('not implemented');\n  },\n\n  generateTypeInfo() {\n    throw new Error('not implemented');\n  },\n\n  generateParameterLength() {\n    throw new Error('not implemented');\n  },\n\n  generateParameterData() {\n    throw new Error('not implemented');\n  },\n\n  validate() {\n    throw new Error('not implemented');\n  }\n};\n\nexport default NumericN;\nmodule.exports = NumericN;\n"], "mappings": ";;;;;;AAEA,MAAMA,QAAkB,GAAG;EACzBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,UAAU;EAEhBC,WAAWA,CAAA,EAAG;IACZ,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,MAAM,IAAID,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDE,uBAAuBA,CAAA,EAAG;IACxB,MAAM,IAAIF,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDG,qBAAqBA,CAAA,EAAG;IACtB,MAAM,IAAIH,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDI,QAAQA,CAAA,EAAG;IACT,MAAM,IAAIJ,KAAK,CAAC,iBAAiB,CAAC;EACpC;AACF,CAAC;AAAC,IAAAK,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEaZ,QAAQ;AACvBa,MAAM,CAACF,OAAO,GAAGX,QAAQ"}