{"version": 3, "file": "nvarchar.js", "names": ["MAX", "UNKNOWN_PLP_LEN", "<PERSON><PERSON><PERSON>", "from", "PLP_TERMINATOR", "NULL_LENGTH", "MAX_NULL_LENGTH", "NVarChar", "id", "type", "name", "maximumLength", "declaration", "parameter", "value", "length", "toString", "output", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "generateTypeInfo", "buffer", "alloc", "writeUInt8", "writeUInt16LE", "collation", "<PERSON><PERSON><PERSON><PERSON>", "copy", "generateParameterLength", "options", "byteLength", "generateParameterData", "writeUInt32LE", "validate", "TypeError", "_default", "exports", "default", "module"], "sources": ["../../src/data-types/nvarchar.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\n\nconst MAX = (1 << 16) - 1;\nconst UNKNOWN_PLP_LEN = Buffer.from([0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff]);\nconst PLP_TERMINATOR = Buffer.from([0x00, 0x00, 0x00, 0x00]);\n\nconst NULL_LENGTH = Buffer.from([0xFF, 0xFF]);\nconst MAX_NULL_LENGTH = Buffer.from([0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF]);\n\nconst NVarChar: { maximumLength: number } & DataType = {\n  id: 0xE7,\n  type: 'NVARCHAR',\n  name: 'NVarChar',\n  maximumLength: 4000,\n\n  declaration: function(parameter) {\n    const value = parameter.value as any; // Temporary solution. Remove 'any' later.\n\n    let length;\n    if (parameter.length) {\n      length = parameter.length;\n    } else if (value != null) {\n      length = value.toString().length || 1;\n    } else if (value === null && !parameter.output) {\n      length = 1;\n    } else {\n      length = this.maximumLength;\n    }\n\n    if (length <= this.maximumLength) {\n      return 'nvarchar(' + length + ')';\n    } else {\n      return 'nvarchar(max)';\n    }\n  },\n\n  resolveLength: function(parameter) {\n    const value = parameter.value as any; // Temporary solution. Remove 'any' later.\n    if (parameter.length != null) {\n      return parameter.length;\n    } else if (value != null) {\n      if (Buffer.isBuffer(value)) {\n        return (value.length / 2) || 1;\n      } else {\n        return value.toString().length || 1;\n      }\n    } else {\n      return this.maximumLength;\n    }\n  },\n\n  generateTypeInfo(parameter) {\n    const buffer = Buffer.alloc(8);\n    buffer.writeUInt8(this.id, 0);\n\n    if (parameter.length! <= this.maximumLength) {\n      buffer.writeUInt16LE(parameter.length! * 2, 1);\n    } else {\n      buffer.writeUInt16LE(MAX, 1);\n    }\n\n    if (parameter.collation) {\n      parameter.collation.toBuffer().copy(buffer, 3, 0, 5);\n    }\n\n    return buffer;\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      if (parameter.length! <= this.maximumLength) {\n        return NULL_LENGTH;\n      } else {\n        return MAX_NULL_LENGTH;\n      }\n    }\n\n    let value = parameter.value;\n    if (parameter.length! <= this.maximumLength) {\n      let length;\n      if (value instanceof Buffer) {\n        length = value.length;\n      } else {\n        value = value.toString();\n        length = Buffer.byteLength(value, 'ucs2');\n      }\n\n      const buffer = Buffer.alloc(2);\n      buffer.writeUInt16LE(length, 0);\n      return buffer;\n    } else {\n      return UNKNOWN_PLP_LEN;\n    }\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    let value = parameter.value;\n\n    if (parameter.length! <= this.maximumLength) {\n      if (value instanceof Buffer) {\n        yield value;\n      } else {\n        value = value.toString();\n        yield Buffer.from(value, 'ucs2');\n      }\n    } else {\n      if (value instanceof Buffer) {\n        const length = value.length;\n\n        if (length > 0) {\n          const buffer = Buffer.alloc(4);\n          buffer.writeUInt32LE(length, 0);\n          yield buffer;\n          yield value;\n        }\n      } else {\n        value = value.toString();\n        const length = Buffer.byteLength(value, 'ucs2');\n\n        if (length > 0) {\n          const buffer = Buffer.alloc(4);\n          buffer.writeUInt32LE(length, 0);\n          yield buffer;\n          yield Buffer.from(value, 'ucs2');\n        }\n      }\n\n      yield PLP_TERMINATOR;\n    }\n  },\n\n  validate: function(value): null | string {\n    if (value == null) {\n      return null;\n    }\n\n    if (typeof value !== 'string') {\n      throw new TypeError('Invalid string.');\n    }\n\n    return value;\n  }\n};\n\nexport default NVarChar;\nmodule.exports = NVarChar;\n"], "mappings": ";;;;;;AAEA,MAAMA,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC;AACzB,MAAMC,eAAe,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACrF,MAAMC,cAAc,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAE5D,MAAME,WAAW,GAAGH,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC7C,MAAMG,eAAe,GAAGJ,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAErF,MAAMI,QAA8C,GAAG;EACrDC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAE,IAAI;EAEnBC,WAAW,EAAE,SAAAA,CAASC,SAAS,EAAE;IAC/B,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAY,CAAC,CAAC;;IAEtC,IAAIC,MAAM;IACV,IAAIF,SAAS,CAACE,MAAM,EAAE;MACpBA,MAAM,GAAGF,SAAS,CAACE,MAAM;IAC3B,CAAC,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxBC,MAAM,GAAGD,KAAK,CAACE,QAAQ,CAAC,CAAC,CAACD,MAAM,IAAI,CAAC;IACvC,CAAC,MAAM,IAAID,KAAK,KAAK,IAAI,IAAI,CAACD,SAAS,CAACI,MAAM,EAAE;MAC9CF,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM;MACLA,MAAM,GAAG,IAAI,CAACJ,aAAa;IAC7B;IAEA,IAAII,MAAM,IAAI,IAAI,CAACJ,aAAa,EAAE;MAChC,OAAO,WAAW,GAAGI,MAAM,GAAG,GAAG;IACnC,CAAC,MAAM;MACL,OAAO,eAAe;IACxB;EACF,CAAC;EAEDG,aAAa,EAAE,SAAAA,CAASL,SAAS,EAAE;IACjC,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAY,CAAC,CAAC;IACtC,IAAID,SAAS,CAACE,MAAM,IAAI,IAAI,EAAE;MAC5B,OAAOF,SAAS,CAACE,MAAM;IACzB,CAAC,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,IAAIZ,MAAM,CAACiB,QAAQ,CAACL,KAAK,CAAC,EAAE;QAC1B,OAAQA,KAAK,CAACC,MAAM,GAAG,CAAC,IAAK,CAAC;MAChC,CAAC,MAAM;QACL,OAAOD,KAAK,CAACE,QAAQ,CAAC,CAAC,CAACD,MAAM,IAAI,CAAC;MACrC;IACF,CAAC,MAAM;MACL,OAAO,IAAI,CAACJ,aAAa;IAC3B;EACF,CAAC;EAEDS,gBAAgBA,CAACP,SAAS,EAAE;IAC1B,MAAMQ,MAAM,GAAGnB,MAAM,CAACoB,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACE,UAAU,CAAC,IAAI,CAACf,EAAE,EAAE,CAAC,CAAC;IAE7B,IAAIK,SAAS,CAACE,MAAM,IAAK,IAAI,CAACJ,aAAa,EAAE;MAC3CU,MAAM,CAACG,aAAa,CAACX,SAAS,CAACE,MAAM,GAAI,CAAC,EAAE,CAAC,CAAC;IAChD,CAAC,MAAM;MACLM,MAAM,CAACG,aAAa,CAACxB,GAAG,EAAE,CAAC,CAAC;IAC9B;IAEA,IAAIa,SAAS,CAACY,SAAS,EAAE;MACvBZ,SAAS,CAACY,SAAS,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAACN,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtD;IAEA,OAAOA,MAAM;EACf,CAAC;EAEDO,uBAAuBA,CAACf,SAAS,EAAEgB,OAAO,EAAE;IAC1C,IAAIhB,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B,IAAID,SAAS,CAACE,MAAM,IAAK,IAAI,CAACJ,aAAa,EAAE;QAC3C,OAAON,WAAW;MACpB,CAAC,MAAM;QACL,OAAOC,eAAe;MACxB;IACF;IAEA,IAAIQ,KAAK,GAAGD,SAAS,CAACC,KAAK;IAC3B,IAAID,SAAS,CAACE,MAAM,IAAK,IAAI,CAACJ,aAAa,EAAE;MAC3C,IAAII,MAAM;MACV,IAAID,KAAK,YAAYZ,MAAM,EAAE;QAC3Ba,MAAM,GAAGD,KAAK,CAACC,MAAM;MACvB,CAAC,MAAM;QACLD,KAAK,GAAGA,KAAK,CAACE,QAAQ,CAAC,CAAC;QACxBD,MAAM,GAAGb,MAAM,CAAC4B,UAAU,CAAChB,KAAK,EAAE,MAAM,CAAC;MAC3C;MAEA,MAAMO,MAAM,GAAGnB,MAAM,CAACoB,KAAK,CAAC,CAAC,CAAC;MAC9BD,MAAM,CAACG,aAAa,CAACT,MAAM,EAAE,CAAC,CAAC;MAC/B,OAAOM,MAAM;IACf,CAAC,MAAM;MACL,OAAOpB,eAAe;IACxB;EACF,CAAC;EAED,CAAE8B,qBAAqBA,CAAClB,SAAS,EAAEgB,OAAO,EAAE;IAC1C,IAAIhB,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,IAAIA,KAAK,GAAGD,SAAS,CAACC,KAAK;IAE3B,IAAID,SAAS,CAACE,MAAM,IAAK,IAAI,CAACJ,aAAa,EAAE;MAC3C,IAAIG,KAAK,YAAYZ,MAAM,EAAE;QAC3B,MAAMY,KAAK;MACb,CAAC,MAAM;QACLA,KAAK,GAAGA,KAAK,CAACE,QAAQ,CAAC,CAAC;QACxB,MAAMd,MAAM,CAACC,IAAI,CAACW,KAAK,EAAE,MAAM,CAAC;MAClC;IACF,CAAC,MAAM;MACL,IAAIA,KAAK,YAAYZ,MAAM,EAAE;QAC3B,MAAMa,MAAM,GAAGD,KAAK,CAACC,MAAM;QAE3B,IAAIA,MAAM,GAAG,CAAC,EAAE;UACd,MAAMM,MAAM,GAAGnB,MAAM,CAACoB,KAAK,CAAC,CAAC,CAAC;UAC9BD,MAAM,CAACW,aAAa,CAACjB,MAAM,EAAE,CAAC,CAAC;UAC/B,MAAMM,MAAM;UACZ,MAAMP,KAAK;QACb;MACF,CAAC,MAAM;QACLA,KAAK,GAAGA,KAAK,CAACE,QAAQ,CAAC,CAAC;QACxB,MAAMD,MAAM,GAAGb,MAAM,CAAC4B,UAAU,CAAChB,KAAK,EAAE,MAAM,CAAC;QAE/C,IAAIC,MAAM,GAAG,CAAC,EAAE;UACd,MAAMM,MAAM,GAAGnB,MAAM,CAACoB,KAAK,CAAC,CAAC,CAAC;UAC9BD,MAAM,CAACW,aAAa,CAACjB,MAAM,EAAE,CAAC,CAAC;UAC/B,MAAMM,MAAM;UACZ,MAAMnB,MAAM,CAACC,IAAI,CAACW,KAAK,EAAE,MAAM,CAAC;QAClC;MACF;MAEA,MAAMV,cAAc;IACtB;EACF,CAAC;EAED6B,QAAQ,EAAE,SAAAA,CAASnB,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIoB,SAAS,CAAC,iBAAiB,CAAC;IACxC;IAEA,OAAOpB,KAAK;EACd;AACF,CAAC;AAAC,IAAAqB,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEa9B,QAAQ;AACvB+B,MAAM,CAACF,OAAO,GAAG7B,QAAQ"}