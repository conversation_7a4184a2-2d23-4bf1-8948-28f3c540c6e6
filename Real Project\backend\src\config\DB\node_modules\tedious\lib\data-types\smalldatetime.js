"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _datetimen = _interopRequireDefault(require("./datetimen"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const EPOCH_DATE = new Date(1900, 0, 1);
const UTC_EPOCH_DATE = new Date(Date.UTC(1900, 0, 1));
const DATA_LENGTH = Buffer.from([0x04]);
const NULL_LENGTH = Buffer.from([0x00]);
const SmallDateTime = {
  id: 0x3A,
  type: 'DATETIM4',
  name: 'SmallDateTime',
  declaration: function () {
    return 'smalldatetime';
  },
  generateTypeInfo() {
    return Buffer.from([_datetimen.default.id, 0x04]);
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    return DATA_LENGTH;
  },
  generateParameterData: function* (parameter, options) {
    if (parameter.value == null) {
      return;
    }
    const buffer = Buffer.alloc(4);
    let days, dstDiff, minutes;
    if (options.useUTC) {
      days = Math.floor((parameter.value.getTime() - UTC_EPOCH_DATE.getTime()) / (1000 * 60 * 60 * 24));
      minutes = parameter.value.getUTCHours() * 60 + parameter.value.getUTCMinutes();
    } else {
      dstDiff = -(parameter.value.getTimezoneOffset() - EPOCH_DATE.getTimezoneOffset()) * 60 * 1000;
      days = Math.floor((parameter.value.getTime() - EPOCH_DATE.getTime() + dstDiff) / (1000 * 60 * 60 * 24));
      minutes = parameter.value.getHours() * 60 + parameter.value.getMinutes();
    }
    buffer.writeUInt16LE(days, 0);
    buffer.writeUInt16LE(minutes, 2);
    yield buffer;
  },
  validate: function (value, collation, options) {
    if (value == null) {
      return null;
    }
    if (!(value instanceof Date)) {
      value = new Date(Date.parse(value));
    }
    value = value;
    let year, month, date;
    if (options && options.useUTC) {
      year = value.getUTCFullYear();
      month = value.getUTCMonth();
      date = value.getUTCDate();
    } else {
      year = value.getFullYear();
      month = value.getMonth();
      date = value.getDate();
    }
    if (year < 1900 || year > 2079) {
      throw new TypeError('Out of range.');
    }
    if (year === 2079) {
      // Month is 0-indexed, i.e. Jan = 0, Dec = 11
      // See: https://learn.microsoft.com/en-us/sql/t-sql/data-types/smalldatetime-transact-sql?view=sql-server-ver16
      if (month > 5 || month === 5 && date > 6) {
        throw new TypeError('Out of range.');
      }
    }
    if (isNaN(value)) {
      throw new TypeError('Invalid date.');
    }
    return value;
  }
};
var _default = exports.default = SmallDateTime;
module.exports = SmallDateTime;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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