{"version": 3, "file": "smalldatetime.js", "names": ["_datetimen", "_interopRequireDefault", "require", "obj", "__esModule", "default", "EPOCH_DATE", "Date", "UTC_EPOCH_DATE", "UTC", "DATA_LENGTH", "<PERSON><PERSON><PERSON>", "from", "NULL_LENGTH", "SmallDateTime", "id", "type", "name", "declaration", "generateTypeInfo", "DateTimeN", "generateParameterLength", "parameter", "options", "value", "generateParameterData", "buffer", "alloc", "days", "dstDiff", "minutes", "useUTC", "Math", "floor", "getTime", "getUTCHours", "getUTCMinutes", "getTimezoneOffset", "getHours", "getMinutes", "writeUInt16LE", "validate", "collation", "parse", "year", "month", "date", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getFullYear", "getMonth", "getDate", "TypeError", "isNaN", "_default", "exports", "module"], "sources": ["../../src/data-types/smalldatetime.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport DateTimeN from './datetimen';\n\nconst EPOCH_DATE = new Date(1900, 0, 1);\nconst UTC_EPOCH_DATE = new Date(Date.UTC(1900, 0, 1));\n\nconst DATA_LENGTH = Buffer.from([0x04]);\nconst NULL_LENGTH = Buffer.from([0x00]);\n\nconst SmallDateTime: DataType = {\n  id: 0x3A,\n  type: 'DATETIM4',\n  name: 'SmallDateTime',\n\n  declaration: function() {\n    return 'smalldatetime';\n  },\n\n  generateTypeInfo() {\n    return Buffer.from([DateTimeN.id, 0x04]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    return DATA_LENGTH;\n  },\n\n  generateParameterData: function*(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const buffer = Buffer.alloc(4);\n\n    let days: number, dstDiff: number, minutes: number;\n    if (options.useUTC) {\n      days = Math.floor((parameter.value.getTime() - UTC_EPOCH_DATE.getTime()) / (1000 * 60 * 60 * 24));\n      minutes = (parameter.value.getUTCHours() * 60) + parameter.value.getUTCMinutes();\n    } else {\n      dstDiff = -(parameter.value.getTimezoneOffset() - EPOCH_DATE.getTimezoneOffset()) * 60 * 1000;\n      days = Math.floor((parameter.value.getTime() - EPOCH_DATE.getTime() + dstDiff) / (1000 * 60 * 60 * 24));\n      minutes = (parameter.value.getHours() * 60) + parameter.value.getMinutes();\n    }\n\n    buffer.writeUInt16LE(days, 0);\n    buffer.writeUInt16LE(minutes, 2);\n\n    yield buffer;\n  },\n\n  validate: function(value, collation, options): null | Date {\n    if (value == null) {\n      return null;\n    }\n\n    if (!(value instanceof Date)) {\n      value = new Date(Date.parse(value));\n    }\n\n    value = value as Date;\n\n    let year, month, date;\n    if (options && options.useUTC) {\n      year = value.getUTCFullYear();\n      month = value.getUTCMonth();\n      date = value.getUTCDate();\n    } else {\n      year = value.getFullYear();\n      month = value.getMonth();\n      date = value.getDate();\n    }\n\n    if (year < 1900 || year > 2079) {\n      throw new TypeError('Out of range.');\n    }\n\n    if (year === 2079) {\n      // Month is 0-indexed, i.e. Jan = 0, Dec = 11\n      // See: https://learn.microsoft.com/en-us/sql/t-sql/data-types/smalldatetime-transact-sql?view=sql-server-ver16\n      if (month > 5 || (month === 5 && date > 6)) {\n        throw new TypeError('Out of range.');\n      }\n    }\n\n    if (isNaN(value)) {\n      throw new TypeError('Invalid date.');\n    }\n\n    return value;\n  }\n};\n\nexport default SmallDateTime;\nmodule.exports = SmallDateTime;\n"], "mappings": ";;;;;;AACA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAoC,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEpC,MAAMG,UAAU,GAAG,IAAIC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AACvC,MAAMC,cAAc,GAAG,IAAID,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAErD,MAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,MAAMC,WAAW,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAME,aAAuB,GAAG;EAC9BC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,eAAe;EAErBC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,eAAe;EACxB,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,OAAOR,MAAM,CAACC,IAAI,CAAC,CAACQ,kBAAS,CAACL,EAAE,EAAE,IAAI,CAAC,CAAC;EAC1C,CAAC;EAEDM,uBAAuBA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC1C,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOX,WAAW;IACpB;IAEA,OAAOH,WAAW;EACpB,CAAC;EAEDe,qBAAqB,EAAE,UAAAA,CAAUH,SAAS,EAAEC,OAAO,EAAE;IACnD,IAAID,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAME,MAAM,GAAGf,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC;IAE9B,IAAIC,IAAY,EAAEC,OAAe,EAAEC,OAAe;IAClD,IAAIP,OAAO,CAACQ,MAAM,EAAE;MAClBH,IAAI,GAAGI,IAAI,CAACC,KAAK,CAAC,CAACX,SAAS,CAACE,KAAK,CAACU,OAAO,CAAC,CAAC,GAAG1B,cAAc,CAAC0B,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MACjGJ,OAAO,GAAIR,SAAS,CAACE,KAAK,CAACW,WAAW,CAAC,CAAC,GAAG,EAAE,GAAIb,SAAS,CAACE,KAAK,CAACY,aAAa,CAAC,CAAC;IAClF,CAAC,MAAM;MACLP,OAAO,GAAG,EAAEP,SAAS,CAACE,KAAK,CAACa,iBAAiB,CAAC,CAAC,GAAG/B,UAAU,CAAC+B,iBAAiB,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI;MAC7FT,IAAI,GAAGI,IAAI,CAACC,KAAK,CAAC,CAACX,SAAS,CAACE,KAAK,CAACU,OAAO,CAAC,CAAC,GAAG5B,UAAU,CAAC4B,OAAO,CAAC,CAAC,GAAGL,OAAO,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MACvGC,OAAO,GAAIR,SAAS,CAACE,KAAK,CAACc,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAIhB,SAAS,CAACE,KAAK,CAACe,UAAU,CAAC,CAAC;IAC5E;IAEAb,MAAM,CAACc,aAAa,CAACZ,IAAI,EAAE,CAAC,CAAC;IAC7BF,MAAM,CAACc,aAAa,CAACV,OAAO,EAAE,CAAC,CAAC;IAEhC,MAAMJ,MAAM;EACd,CAAC;EAEDe,QAAQ,EAAE,SAAAA,CAASjB,KAAK,EAAEkB,SAAS,EAAEnB,OAAO,EAAe;IACzD,IAAIC,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,EAAEA,KAAK,YAAYjB,IAAI,CAAC,EAAE;MAC5BiB,KAAK,GAAG,IAAIjB,IAAI,CAACA,IAAI,CAACoC,KAAK,CAACnB,KAAK,CAAC,CAAC;IACrC;IAEAA,KAAK,GAAGA,KAAa;IAErB,IAAIoB,IAAI,EAAEC,KAAK,EAAEC,IAAI;IACrB,IAAIvB,OAAO,IAAIA,OAAO,CAACQ,MAAM,EAAE;MAC7Ba,IAAI,GAAGpB,KAAK,CAACuB,cAAc,CAAC,CAAC;MAC7BF,KAAK,GAAGrB,KAAK,CAACwB,WAAW,CAAC,CAAC;MAC3BF,IAAI,GAAGtB,KAAK,CAACyB,UAAU,CAAC,CAAC;IAC3B,CAAC,MAAM;MACLL,IAAI,GAAGpB,KAAK,CAAC0B,WAAW,CAAC,CAAC;MAC1BL,KAAK,GAAGrB,KAAK,CAAC2B,QAAQ,CAAC,CAAC;MACxBL,IAAI,GAAGtB,KAAK,CAAC4B,OAAO,CAAC,CAAC;IACxB;IAEA,IAAIR,IAAI,GAAG,IAAI,IAAIA,IAAI,GAAG,IAAI,EAAE;MAC9B,MAAM,IAAIS,SAAS,CAAC,eAAe,CAAC;IACtC;IAEA,IAAIT,IAAI,KAAK,IAAI,EAAE;MACjB;MACA;MACA,IAAIC,KAAK,GAAG,CAAC,IAAKA,KAAK,KAAK,CAAC,IAAIC,IAAI,GAAG,CAAE,EAAE;QAC1C,MAAM,IAAIO,SAAS,CAAC,eAAe,CAAC;MACtC;IACF;IAEA,IAAIC,KAAK,CAAC9B,KAAK,CAAC,EAAE;MAChB,MAAM,IAAI6B,SAAS,CAAC,eAAe,CAAC;IACtC;IAEA,OAAO7B,KAAK;EACd;AACF,CAAC;AAAC,IAAA+B,QAAA,GAAAC,OAAA,CAAAnD,OAAA,GAEaS,aAAa;AAC5B2C,MAAM,CAACD,OAAO,GAAG1C,aAAa"}