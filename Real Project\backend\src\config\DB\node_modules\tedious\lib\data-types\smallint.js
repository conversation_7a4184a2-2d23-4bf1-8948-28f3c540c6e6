"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _intn = _interopRequireDefault(require("./intn"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const DATA_LENGTH = Buffer.from([0x02]);
const NULL_LENGTH = Buffer.from([0x00]);
const SmallInt = {
  id: 0x34,
  type: 'INT2',
  name: 'SmallInt',
  declaration: function () {
    return 'smallint';
  },
  generateTypeInfo() {
    return Buffer.from([_intn.default.id, 0x02]);
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    return DATA_LENGTH;
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    const buffer = Buffer.alloc(2);
    buffer.writeInt16LE(Number(parameter.value), 0);
    yield buffer;
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    if (typeof value !== 'number') {
      value = Number(value);
    }
    if (isNaN(value)) {
      throw new TypeError('Invalid number.');
    }
    if (value < -32768 || value > 32767) {
      throw new TypeError('Value must be between -32768 and 32767, inclusive.');
    }
    return value | 0;
  }
};
var _default = exports.default = SmallInt;
module.exports = SmallInt;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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