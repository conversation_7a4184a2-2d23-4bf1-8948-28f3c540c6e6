"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _moneyn = _interopRequireDefault(require("./moneyn"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const DATA_LENGTH = Buffer.from([0x04]);
const NULL_LENGTH = Buffer.from([0x00]);
const SmallMoney = {
  id: 0x7A,
  type: 'MONEY4',
  name: 'SmallMoney',
  declaration: function () {
    return 'smallmoney';
  },
  generateTypeInfo: function () {
    return Buffer.from([_moneyn.default.id, 0x04]);
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    return DATA_LENGTH;
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      return;
    }
    const buffer = Buffer.alloc(4);
    buffer.writeInt32LE(parameter.value * 10000, 0);
    yield buffer;
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    value = parseFloat(value);
    if (isNaN(value)) {
      throw new TypeError('Invalid number.');
    }
    if (value < -214748.3648 || value > 214748.3647) {
      throw new TypeError('Value must be between -214748.3648 and 214748.3647.');
    }
    return value;
  }
};
var _default = exports.default = SmallMoney;
module.exports = SmallMoney;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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