"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
const Variant = {
  id: 0x62,
  type: 'SSVARIANTTYPE',
  name: 'Variant',
  declaration: function () {
    return 'sql_variant';
  },
  generateTypeInfo() {
    throw new Error('not implemented');
  },
  generateParameterLength() {
    throw new Error('not implemented');
  },
  generateParameterData() {
    throw new Error('not implemented');
  },
  validate() {
    throw new Error('not implemented');
  }
};
var _default = exports.default = Variant;
module.exports = Variant;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJWYXJpYW50IiwiaWQiLCJ0eXBlIiwibmFtZSIsImRlY2xhcmF0aW9uIiwiZ2VuZXJhdGVUeXBlSW5mbyIsIkVycm9yIiwiZ2VuZXJhdGVQYXJhbWV0ZXJMZW5ndGgiLCJnZW5lcmF0ZVBhcmFtZXRlckRhdGEiLCJ2YWxpZGF0ZSIsIl9kZWZhdWx0IiwiZXhwb3J0cyIsImRlZmF1bHQiLCJtb2R1bGUiXSwic291cmNlcyI6WyIuLi8uLi9zcmMvZGF0YS10eXBlcy9zcWwtdmFyaWFudC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIERhdGFUeXBlIH0gZnJvbSAnLi4vZGF0YS10eXBlJztcblxuY29uc3QgVmFyaWFudDogRGF0YVR5cGUgPSB7XG4gIGlkOiAweDYyLFxuICB0eXBlOiAnU1NWQVJJQU5UVFlQRScsXG4gIG5hbWU6ICdWYXJpYW50JyxcblxuICBkZWNsYXJhdGlvbjogZnVuY3Rpb24oKSB7XG4gICAgcmV0dXJuICdzcWxfdmFyaWFudCc7XG4gIH0sXG5cbiAgZ2VuZXJhdGVUeXBlSW5mbygpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ25vdCBpbXBsZW1lbnRlZCcpO1xuICB9LFxuXG4gIGdlbmVyYXRlUGFyYW1ldGVyTGVuZ3RoKCkge1xuICAgIHRocm93IG5ldyBFcnJvcignbm90IGltcGxlbWVudGVkJyk7XG4gIH0sXG5cbiAgZ2VuZXJhdGVQYXJhbWV0ZXJEYXRhKCkge1xuICAgIHRocm93IG5ldyBFcnJvcignbm90IGltcGxlbWVudGVkJyk7XG4gIH0sXG5cbiAgdmFsaWRhdGUoKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdub3QgaW1wbGVtZW50ZWQnKTtcbiAgfVxufTtcblxuZXhwb3J0IGRlZmF1bHQgVmFyaWFudDtcbm1vZHVsZS5leHBvcnRzID0gVmFyaWFudDtcbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBRUEsTUFBTUEsT0FBaUIsR0FBRztFQUN4QkMsRUFBRSxFQUFFLElBQUk7RUFDUkMsSUFBSSxFQUFFLGVBQWU7RUFDckJDLElBQUksRUFBRSxTQUFTO0VBRWZDLFdBQVcsRUFBRSxTQUFBQSxDQUFBLEVBQVc7SUFDdEIsT0FBTyxhQUFhO0VBQ3RCLENBQUM7RUFFREMsZ0JBQWdCQSxDQUFBLEVBQUc7SUFDakIsTUFBTSxJQUFJQyxLQUFLLENBQUMsaUJBQWlCLENBQUM7RUFDcEMsQ0FBQztFQUVEQyx1QkFBdUJBLENBQUEsRUFBRztJQUN4QixNQUFNLElBQUlELEtBQUssQ0FBQyxpQkFBaUIsQ0FBQztFQUNwQyxDQUFDO0VBRURFLHFCQUFxQkEsQ0FBQSxFQUFHO0lBQ3RCLE1BQU0sSUFBSUYsS0FBSyxDQUFDLGlCQUFpQixDQUFDO0VBQ3BDLENBQUM7RUFFREcsUUFBUUEsQ0FBQSxFQUFHO0lBQ1QsTUFBTSxJQUFJSCxLQUFLLENBQUMsaUJBQWlCLENBQUM7RUFDcEM7QUFDRixDQUFDO0FBQUMsSUFBQUksUUFBQSxHQUFBQyxPQUFBLENBQUFDLE9BQUEsR0FFYVosT0FBTztBQUN0QmEsTUFBTSxDQUFDRixPQUFPLEdBQUdYLE9BQU8ifQ==