{"version": 3, "file": "sql-variant.js", "names": ["<PERSON><PERSON><PERSON>", "id", "type", "name", "declaration", "generateTypeInfo", "Error", "generateParameterLength", "generateParameterData", "validate", "_default", "exports", "default", "module"], "sources": ["../../src/data-types/sql-variant.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\n\nconst Variant: DataType = {\n  id: 0x62,\n  type: 'SSVARIANTTYPE',\n  name: 'Variant',\n\n  declaration: function() {\n    return 'sql_variant';\n  },\n\n  generateTypeInfo() {\n    throw new Error('not implemented');\n  },\n\n  generateParameterLength() {\n    throw new Error('not implemented');\n  },\n\n  generateParameterData() {\n    throw new Error('not implemented');\n  },\n\n  validate() {\n    throw new Error('not implemented');\n  }\n};\n\nexport default Variant;\nmodule.exports = Variant;\n"], "mappings": ";;;;;;AAEA,MAAMA,OAAiB,GAAG;EACxBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,SAAS;EAEfC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,aAAa;EACtB,CAAC;EAEDC,gBAAgBA,CAAA,EAAG;IACjB,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDC,uBAAuBA,CAAA,EAAG;IACxB,MAAM,IAAID,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDE,qBAAqBA,CAAA,EAAG;IACtB,MAAM,IAAIF,KAAK,CAAC,iBAAiB,CAAC;EACpC,CAAC;EAEDG,QAAQA,CAAA,EAAG;IACT,MAAM,IAAIH,KAAK,CAAC,iBAAiB,CAAC;EACpC;AACF,CAAC;AAAC,IAAAI,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEaZ,OAAO;AACtBa,MAAM,CAACF,OAAO,GAAGX,OAAO"}