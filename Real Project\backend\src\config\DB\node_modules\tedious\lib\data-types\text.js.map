{"version": 3, "file": "text.js", "names": ["_iconvLite", "_interopRequireDefault", "require", "obj", "__esModule", "default", "NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "Text", "id", "type", "name", "hasTableName", "declaration", "<PERSON><PERSON><PERSON><PERSON>", "parameter", "value", "length", "generateTypeInfo", "_options", "buffer", "alloc", "writeUInt8", "writeInt32LE", "collation", "<PERSON><PERSON><PERSON><PERSON>", "copy", "generateParameterLength", "options", "generateParameterData", "validate", "TypeError", "Error", "codepage", "iconv", "encode", "_default", "exports", "module"], "sources": ["../../src/data-types/text.ts"], "sourcesContent": ["import iconv from 'iconv-lite';\n\nimport { type DataType } from '../data-type';\n\nconst NULL_LENGTH = Buffer.from([0xFF, 0xFF, 0xFF, 0xFF]);\n\nconst Text: DataType = {\n  id: 0x23,\n  type: 'TEXT',\n  name: 'Text',\n\n  hasTableName: true,\n\n  declaration: function() {\n    return 'text';\n  },\n\n  resolveLength: function(parameter) {\n    const value = parameter.value as Buffer | null;\n\n    if (value != null) {\n      return value.length;\n    } else {\n      return -1;\n    }\n  },\n\n  generateTypeInfo(parameter, _options) {\n    const buffer = Buffer.alloc(10);\n    buffer.writeUInt8(this.id, 0);\n    buffer.writeInt32LE(parameter.length!, 1);\n\n    if (parameter.collation) {\n      parameter.collation.toBuffer().copy(buffer, 5, 0, 5);\n    }\n\n    return buffer;\n  },\n\n  generateParameterLength(parameter, options) {\n    const value = parameter.value as Buffer | null;\n\n    if (value == null) {\n      return NULL_LENGTH;\n    }\n\n    const buffer = Buffer.alloc(4);\n    buffer.writeInt32LE(value.length, 0);\n    return buffer;\n  },\n\n  generateParameterData: function*(parameter, options) {\n    const value = parameter.value as Buffer | null;\n\n    if (value == null) {\n      return;\n    }\n\n    yield value;\n  },\n\n  validate: function(value, collation): Buffer | null {\n    if (value == null) {\n      return null;\n    }\n\n    if (typeof value !== 'string') {\n      throw new TypeError('Invalid string.');\n    }\n\n    if (!collation) {\n      throw new Error('No collation was set by the server for the current connection.');\n    }\n\n    if (!collation.codepage) {\n      throw new Error('The collation set by the server has no associated encoding.');\n    }\n\n    return iconv.encode(value, collation.codepage);\n  }\n};\n\nexport default Text;\nmodule.exports = Text;\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA+B,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAI/B,MAAMG,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAEzD,MAAMC,IAAc,GAAG;EACrBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EAEZC,YAAY,EAAE,IAAI;EAElBC,WAAW,EAAE,SAAAA,CAAA,EAAW;IACtB,OAAO,MAAM;EACf,CAAC;EAEDC,aAAa,EAAE,SAAAA,CAASC,SAAS,EAAE;IACjC,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAsB;IAE9C,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOA,KAAK,CAACC,MAAM;IACrB,CAAC,MAAM;MACL,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAEDC,gBAAgBA,CAACH,SAAS,EAAEI,QAAQ,EAAE;IACpC,MAAMC,MAAM,GAAGd,MAAM,CAACe,KAAK,CAAC,EAAE,CAAC;IAC/BD,MAAM,CAACE,UAAU,CAAC,IAAI,CAACb,EAAE,EAAE,CAAC,CAAC;IAC7BW,MAAM,CAACG,YAAY,CAACR,SAAS,CAACE,MAAM,EAAG,CAAC,CAAC;IAEzC,IAAIF,SAAS,CAACS,SAAS,EAAE;MACvBT,SAAS,CAACS,SAAS,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAACN,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtD;IAEA,OAAOA,MAAM;EACf,CAAC;EAEDO,uBAAuBA,CAACZ,SAAS,EAAEa,OAAO,EAAE;IAC1C,MAAMZ,KAAK,GAAGD,SAAS,CAACC,KAAsB;IAE9C,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOX,WAAW;IACpB;IAEA,MAAMe,MAAM,GAAGd,MAAM,CAACe,KAAK,CAAC,CAAC,CAAC;IAC9BD,MAAM,CAACG,YAAY,CAACP,KAAK,CAACC,MAAM,EAAE,CAAC,CAAC;IACpC,OAAOG,MAAM;EACf,CAAC;EAEDS,qBAAqB,EAAE,UAAAA,CAAUd,SAAS,EAAEa,OAAO,EAAE;IACnD,MAAMZ,KAAK,GAAGD,SAAS,CAACC,KAAsB;IAE9C,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB;IACF;IAEA,MAAMA,KAAK;EACb,CAAC;EAEDc,QAAQ,EAAE,SAAAA,CAASd,KAAK,EAAEQ,SAAS,EAAiB;IAClD,IAAIR,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIe,SAAS,CAAC,iBAAiB,CAAC;IACxC;IAEA,IAAI,CAACP,SAAS,EAAE;MACd,MAAM,IAAIQ,KAAK,CAAC,gEAAgE,CAAC;IACnF;IAEA,IAAI,CAACR,SAAS,CAACS,QAAQ,EAAE;MACvB,MAAM,IAAID,KAAK,CAAC,6DAA6D,CAAC;IAChF;IAEA,OAAOE,kBAAK,CAACC,MAAM,CAACnB,KAAK,EAAEQ,SAAS,CAACS,QAAQ,CAAC;EAChD;AACF,CAAC;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAAjC,OAAA,GAEaI,IAAI;AACnB8B,MAAM,CAACD,OAAO,GAAG7B,IAAI"}