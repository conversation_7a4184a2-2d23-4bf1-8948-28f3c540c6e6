{"version": 3, "file": "time.js", "names": ["_writableTrackingBuffer", "_interopRequireDefault", "require", "obj", "__esModule", "default", "NULL_LENGTH", "<PERSON><PERSON><PERSON>", "from", "Time", "id", "type", "name", "declaration", "parameter", "resolveScale", "scale", "value", "generateTypeInfo", "generateParameterLength", "options", "Error", "generateParameterData", "buffer", "WritableTrackingBuffer", "time", "timestamp", "useUTC", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "getUTCMilliseconds", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "Math", "pow", "nanosecondDelta", "round", "writeUInt24LE", "writeUInt32LE", "writeUInt40LE", "data", "validate", "Date", "parse", "isNaN", "TypeError", "_default", "exports", "module"], "sources": ["../../src/data-types/time.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport WritableTrackingBuffer from '../tracking-buffer/writable-tracking-buffer';\n\nconst NULL_LENGTH = Buffer.from([0x00]);\n\nconst Time: DataType = {\n  id: 0x29,\n  type: 'TIMEN',\n  name: 'Time',\n\n  declaration: function(parameter) {\n    return 'time(' + (this.resolveScale!(parameter)) + ')';\n  },\n\n  resolveScale: function(parameter) {\n    if (parameter.scale != null) {\n      return parameter.scale;\n    } else if (parameter.value === null) {\n      return 0;\n    } else {\n      return 7;\n    }\n  },\n\n  generateTypeInfo(parameter) {\n    return Buffer.from([this.id, parameter.scale!]);\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    switch (parameter.scale) {\n      case 0:\n      case 1:\n      case 2:\n        return Buffer.from([0x03]);\n      case 3:\n      case 4:\n        return Buffer.from([0x04]);\n      case 5:\n      case 6:\n      case 7:\n        return Buffer.from([0x05]);\n      default:\n        throw new Error('invalid scale');\n    }\n  },\n\n  * generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      return;\n    }\n\n    const buffer = new WritableTrackingBuffer(16);\n    const time = parameter.value;\n\n    let timestamp;\n    if (options.useUTC) {\n      timestamp = ((time.getUTCHours() * 60 + time.getUTCMinutes()) * 60 + time.getUTCSeconds()) * 1000 + time.getUTCMilliseconds();\n    } else {\n      timestamp = ((time.getHours() * 60 + time.getMinutes()) * 60 + time.getSeconds()) * 1000 + time.getMilliseconds();\n    }\n\n    timestamp = timestamp * Math.pow(10, parameter.scale! - 3);\n    timestamp += (parameter.value.nanosecondDelta != null ? parameter.value.nanosecondDelta : 0) * Math.pow(10, parameter.scale!);\n    timestamp = Math.round(timestamp);\n\n    switch (parameter.scale) {\n      case 0:\n      case 1:\n      case 2:\n        buffer.writeUInt24LE(timestamp);\n        break;\n      case 3:\n      case 4:\n        buffer.writeUInt32LE(timestamp);\n        break;\n      case 5:\n      case 6:\n      case 7:\n        buffer.writeUInt40LE(timestamp);\n    }\n\n    yield buffer.data;\n  },\n\n  validate: function(value): null | number | Date {\n    if (value == null) {\n      return null;\n    }\n\n    if (!(value instanceof Date)) {\n      value = new Date(Date.parse(value));\n    }\n\n    if (isNaN(value)) {\n      throw new TypeError('Invalid time.');\n    }\n\n    return value;\n  }\n};\n\n\nexport default Time;\nmodule.exports = Time;\n"], "mappings": ";;;;;;AACA,IAAAA,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAiF,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEjF,MAAMG,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEvC,MAAMC,IAAc,GAAG;EACrBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,MAAM;EAEZC,WAAW,EAAE,SAAAA,CAASC,SAAS,EAAE;IAC/B,OAAO,OAAO,GAAI,IAAI,CAACC,YAAY,CAAED,SAAS,CAAE,GAAG,GAAG;EACxD,CAAC;EAEDC,YAAY,EAAE,SAAAA,CAASD,SAAS,EAAE;IAChC,IAAIA,SAAS,CAACE,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOF,SAAS,CAACE,KAAK;IACxB,CAAC,MAAM,IAAIF,SAAS,CAACG,KAAK,KAAK,IAAI,EAAE;MACnC,OAAO,CAAC;IACV,CAAC,MAAM;MACL,OAAO,CAAC;IACV;EACF,CAAC;EAEDC,gBAAgBA,CAACJ,SAAS,EAAE;IAC1B,OAAOP,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAACE,EAAE,EAAEI,SAAS,CAACE,KAAK,CAAE,CAAC;EACjD,CAAC;EAEDG,uBAAuBA,CAACL,SAAS,EAAEM,OAAO,EAAE;IAC1C,IAAIN,SAAS,CAACG,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOX,WAAW;IACpB;IAEA,QAAQQ,SAAS,CAACE,KAAK;MACrB,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOT,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;MAC5B,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOD,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;MAC5B,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOD,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;MAC5B;QACE,MAAM,IAAIa,KAAK,CAAC,eAAe,CAAC;IACpC;EACF,CAAC;EAED,CAAEC,qBAAqBA,CAACR,SAAS,EAAEM,OAAO,EAAE;IAC1C,IAAIN,SAAS,CAACG,KAAK,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMM,MAAM,GAAG,IAAIC,+BAAsB,CAAC,EAAE,CAAC;IAC7C,MAAMC,IAAI,GAAGX,SAAS,CAACG,KAAK;IAE5B,IAAIS,SAAS;IACb,IAAIN,OAAO,CAACO,MAAM,EAAE;MAClBD,SAAS,GAAG,CAAC,CAACD,IAAI,CAACG,WAAW,CAAC,CAAC,GAAG,EAAE,GAAGH,IAAI,CAACI,aAAa,CAAC,CAAC,IAAI,EAAE,GAAGJ,IAAI,CAACK,aAAa,CAAC,CAAC,IAAI,IAAI,GAAGL,IAAI,CAACM,kBAAkB,CAAC,CAAC;IAC/H,CAAC,MAAM;MACLL,SAAS,GAAG,CAAC,CAACD,IAAI,CAACO,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAGP,IAAI,CAACQ,UAAU,CAAC,CAAC,IAAI,EAAE,GAAGR,IAAI,CAACS,UAAU,CAAC,CAAC,IAAI,IAAI,GAAGT,IAAI,CAACU,eAAe,CAAC,CAAC;IACnH;IAEAT,SAAS,GAAGA,SAAS,GAAGU,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEvB,SAAS,CAACE,KAAK,GAAI,CAAC,CAAC;IAC1DU,SAAS,IAAI,CAACZ,SAAS,CAACG,KAAK,CAACqB,eAAe,IAAI,IAAI,GAAGxB,SAAS,CAACG,KAAK,CAACqB,eAAe,GAAG,CAAC,IAAIF,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEvB,SAAS,CAACE,KAAM,CAAC;IAC7HU,SAAS,GAAGU,IAAI,CAACG,KAAK,CAACb,SAAS,CAAC;IAEjC,QAAQZ,SAAS,CAACE,KAAK;MACrB,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACJO,MAAM,CAACiB,aAAa,CAACd,SAAS,CAAC;QAC/B;MACF,KAAK,CAAC;MACN,KAAK,CAAC;QACJH,MAAM,CAACkB,aAAa,CAACf,SAAS,CAAC;QAC/B;MACF,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACJH,MAAM,CAACmB,aAAa,CAAChB,SAAS,CAAC;IACnC;IAEA,MAAMH,MAAM,CAACoB,IAAI;EACnB,CAAC;EAEDC,QAAQ,EAAE,SAAAA,CAAS3B,KAAK,EAAwB;IAC9C,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,EAAEA,KAAK,YAAY4B,IAAI,CAAC,EAAE;MAC5B5B,KAAK,GAAG,IAAI4B,IAAI,CAACA,IAAI,CAACC,KAAK,CAAC7B,KAAK,CAAC,CAAC;IACrC;IAEA,IAAI8B,KAAK,CAAC9B,KAAK,CAAC,EAAE;MAChB,MAAM,IAAI+B,SAAS,CAAC,eAAe,CAAC;IACtC;IAEA,OAAO/B,KAAK;EACd;AACF,CAAC;AAAC,IAAAgC,QAAA,GAAAC,OAAA,CAAA7C,OAAA,GAGaI,IAAI;AACnB0C,MAAM,CAACD,OAAO,GAAGzC,IAAI"}