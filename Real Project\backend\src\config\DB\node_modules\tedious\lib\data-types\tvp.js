"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _errors = require("../errors");
var _writableTrackingBuffer = _interopRequireDefault(require("../tracking-buffer/writable-tracking-buffer"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const TVP_ROW_TOKEN = Buffer.from([0x01]);
const TVP_END_TOKEN = Buffer.from([0x00]);
const NULL_LENGTH = Buffer.from([0xFF, 0xFF]);
const TVP = {
  id: 0xF3,
  type: 'TVPTYPE',
  name: 'TVP',
  declaration: function (parameter) {
    const value = parameter.value; // Temporary solution. Remove 'any' later.
    return value.name + ' readonly';
  },
  generateTypeInfo(parameter) {
    const databaseName = '';
    const schema = parameter.value?.schema ?? '';
    const typeName = parameter.value?.name ?? '';
    const bufferLength = 1 + 1 + Buffer.byteLength(databaseName, 'ucs2') + 1 + Buffer.byteLength(schema, 'ucs2') + 1 + Buffer.byteLength(typeName, 'ucs2');
    const buffer = new _writableTrackingBuffer.default(bufferLength, 'ucs2');
    buffer.writeUInt8(this.id);
    buffer.writeBVarchar(databaseName);
    buffer.writeBVarchar(schema);
    buffer.writeBVarchar(typeName);
    return buffer.data;
  },
  generateParameterLength(parameter, options) {
    if (parameter.value == null) {
      return NULL_LENGTH;
    }
    const {
      columns
    } = parameter.value;
    const buffer = Buffer.alloc(2);
    buffer.writeUInt16LE(columns.length, 0);
    return buffer;
  },
  *generateParameterData(parameter, options) {
    if (parameter.value == null) {
      yield TVP_END_TOKEN;
      yield TVP_END_TOKEN;
      return;
    }
    const {
      columns,
      rows
    } = parameter.value;
    for (let i = 0, len = columns.length; i < len; i++) {
      const column = columns[i];
      const buff = Buffer.alloc(6);
      // UserType
      buff.writeUInt32LE(0x00000000, 0);

      // Flags
      buff.writeUInt16LE(0x0000, 4);
      yield buff;

      // TYPE_INFO
      yield column.type.generateTypeInfo(column);

      // ColName
      yield Buffer.from([0x00]);
    }
    yield TVP_END_TOKEN;
    for (let i = 0, length = rows.length; i < length; i++) {
      yield TVP_ROW_TOKEN;
      const row = rows[i];
      for (let k = 0, len2 = row.length; k < len2; k++) {
        const column = columns[k];
        const value = row[k];
        let paramValue;
        try {
          paramValue = column.type.validate(value, parameter.collation);
        } catch (error) {
          throw new _errors.InputError(`TVP column '${column.name}' has invalid data at row index ${i}`, {
            cause: error
          });
        }
        const param = {
          value: paramValue,
          length: column.length,
          scale: column.scale,
          precision: column.precision
        };

        // TvpColumnData
        yield column.type.generateParameterLength(param, options);
        yield* column.type.generateParameterData(param, options);
      }
    }
    yield TVP_END_TOKEN;
  },
  validate: function (value) {
    if (value == null) {
      return null;
    }
    if (typeof value !== 'object') {
      throw new TypeError('Invalid table.');
    }
    if (!Array.isArray(value.columns)) {
      throw new TypeError('Invalid table.');
    }
    if (!Array.isArray(value.rows)) {
      throw new TypeError('Invalid table.');
    }
    return value;
  }
};
var _default = exports.default = TVP;
module.exports = TVP;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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