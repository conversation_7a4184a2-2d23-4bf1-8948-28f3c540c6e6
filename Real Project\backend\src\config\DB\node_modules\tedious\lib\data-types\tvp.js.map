{"version": 3, "file": "tvp.js", "names": ["_errors", "require", "_writableTrackingBuffer", "_interopRequireDefault", "obj", "__esModule", "default", "TVP_ROW_TOKEN", "<PERSON><PERSON><PERSON>", "from", "TVP_END_TOKEN", "NULL_LENGTH", "TVP", "id", "type", "name", "declaration", "parameter", "value", "generateTypeInfo", "databaseName", "schema", "typeName", "bufferLength", "byteLength", "buffer", "WritableTrackingBuffer", "writeUInt8", "writeBVarchar", "data", "generateParameterLength", "options", "columns", "alloc", "writeUInt16LE", "length", "generateParameterData", "rows", "i", "len", "column", "buff", "writeUInt32LE", "row", "k", "len2", "paramValue", "validate", "collation", "error", "InputError", "cause", "param", "scale", "precision", "TypeError", "Array", "isArray", "_default", "exports", "module"], "sources": ["../../src/data-types/tvp.ts"], "sourcesContent": ["import { type DataType } from '../data-type';\nimport { InputError } from '../errors';\nimport WritableTrackingBuffer from '../tracking-buffer/writable-tracking-buffer';\n\nconst TVP_ROW_TOKEN = Buffer.from([0x01]);\nconst TVP_END_TOKEN = Buffer.from([0x00]);\n\nconst NULL_LENGTH = Buffer.from([0xFF, 0xFF]);\n\nconst TVP: DataType = {\n  id: 0xF3,\n  type: 'TVPTYPE',\n  name: 'TVP',\n\n  declaration: function(parameter) {\n    const value = parameter.value as any; // Temporary solution. Remove 'any' later.\n    return value.name + ' readonly';\n  },\n\n  generateTypeInfo(parameter) {\n    const databaseName = '';\n    const schema = parameter.value?.schema ?? '';\n    const typeName = parameter.value?.name ?? '';\n\n    const bufferLength = 1 +\n      1 + Buffer.byteLength(databaseName, 'ucs2') +\n      1 + Buffer.byteLength(schema, 'ucs2') +\n      1 + Buffer.byteLength(typeName, 'ucs2');\n\n    const buffer = new WritableTrackingBuffer(bufferLength, 'ucs2');\n    buffer.writeUInt8(this.id);\n    buffer.writeBVarchar(databaseName);\n    buffer.writeBVarchar(schema);\n    buffer.writeBVarchar(typeName);\n\n    return buffer.data;\n  },\n\n  generateParameterLength(parameter, options) {\n    if (parameter.value == null) {\n      return NULL_LENGTH;\n    }\n\n    const { columns } = parameter.value;\n    const buffer = Buffer.alloc(2);\n    buffer.writeUInt16LE(columns.length, 0);\n    return buffer;\n  },\n\n  *generateParameterData(parameter, options) {\n    if (parameter.value == null) {\n      yield TVP_END_TOKEN;\n      yield TVP_END_TOKEN;\n      return;\n    }\n\n    const { columns, rows } = parameter.value;\n\n    for (let i = 0, len = columns.length; i < len; i++) {\n      const column = columns[i];\n\n      const buff = Buffer.alloc(6);\n      // UserType\n      buff.writeUInt32LE(0x00000000, 0);\n\n      // Flags\n      buff.writeUInt16LE(0x0000, 4);\n      yield buff;\n\n      // TYPE_INFO\n      yield column.type.generateTypeInfo(column);\n\n      // ColName\n      yield Buffer.from([0x00]);\n    }\n\n    yield TVP_END_TOKEN;\n\n    for (let i = 0, length = rows.length; i < length; i++) {\n      yield TVP_ROW_TOKEN;\n\n      const row = rows[i];\n      for (let k = 0, len2 = row.length; k < len2; k++) {\n        const column = columns[k];\n        const value = row[k];\n\n        let paramValue;\n        try {\n          paramValue = column.type.validate(value, parameter.collation);\n        } catch (error) {\n          throw new InputError(`TVP column '${column.name}' has invalid data at row index ${i}`, { cause: error });\n        }\n\n        const param = {\n          value: paramValue,\n          length: column.length,\n          scale: column.scale,\n          precision: column.precision\n        };\n\n        // TvpColumnData\n        yield column.type.generateParameterLength(param, options);\n        yield * column.type.generateParameterData(param, options);\n      }\n    }\n\n    yield TVP_END_TOKEN;\n  },\n\n  validate: function(value): Buffer | null {\n    if (value == null) {\n      return null;\n    }\n\n    if (typeof value !== 'object') {\n      throw new TypeError('Invalid table.');\n    }\n\n    if (!Array.isArray(value.columns)) {\n      throw new TypeError('Invalid table.');\n    }\n\n    if (!Array.isArray(value.rows)) {\n      throw new TypeError('Invalid table.');\n    }\n\n    return value;\n  }\n};\n\nexport default TVP;\nmodule.exports = TVP;\n"], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAiF,SAAAE,uBAAAC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEjF,MAAMG,aAAa,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACzC,MAAMC,aAAa,GAAGF,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAEzC,MAAME,WAAW,GAAGH,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAE7C,MAAMG,GAAa,GAAG;EACpBC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,KAAK;EAEXC,WAAW,EAAE,SAAAA,CAASC,SAAS,EAAE;IAC/B,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAY,CAAC,CAAC;IACtC,OAAOA,KAAK,CAACH,IAAI,GAAG,WAAW;EACjC,CAAC;EAEDI,gBAAgBA,CAACF,SAAS,EAAE;IAC1B,MAAMG,YAAY,GAAG,EAAE;IACvB,MAAMC,MAAM,GAAGJ,SAAS,CAACC,KAAK,EAAEG,MAAM,IAAI,EAAE;IAC5C,MAAMC,QAAQ,GAAGL,SAAS,CAACC,KAAK,EAAEH,IAAI,IAAI,EAAE;IAE5C,MAAMQ,YAAY,GAAG,CAAC,GACpB,CAAC,GAAGf,MAAM,CAACgB,UAAU,CAACJ,YAAY,EAAE,MAAM,CAAC,GAC3C,CAAC,GAAGZ,MAAM,CAACgB,UAAU,CAACH,MAAM,EAAE,MAAM,CAAC,GACrC,CAAC,GAAGb,MAAM,CAACgB,UAAU,CAACF,QAAQ,EAAE,MAAM,CAAC;IAEzC,MAAMG,MAAM,GAAG,IAAIC,+BAAsB,CAACH,YAAY,EAAE,MAAM,CAAC;IAC/DE,MAAM,CAACE,UAAU,CAAC,IAAI,CAACd,EAAE,CAAC;IAC1BY,MAAM,CAACG,aAAa,CAACR,YAAY,CAAC;IAClCK,MAAM,CAACG,aAAa,CAACP,MAAM,CAAC;IAC5BI,MAAM,CAACG,aAAa,CAACN,QAAQ,CAAC;IAE9B,OAAOG,MAAM,CAACI,IAAI;EACpB,CAAC;EAEDC,uBAAuBA,CAACb,SAAS,EAAEc,OAAO,EAAE;IAC1C,IAAId,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B,OAAOP,WAAW;IACpB;IAEA,MAAM;MAAEqB;IAAQ,CAAC,GAAGf,SAAS,CAACC,KAAK;IACnC,MAAMO,MAAM,GAAGjB,MAAM,CAACyB,KAAK,CAAC,CAAC,CAAC;IAC9BR,MAAM,CAACS,aAAa,CAACF,OAAO,CAACG,MAAM,EAAE,CAAC,CAAC;IACvC,OAAOV,MAAM;EACf,CAAC;EAED,CAACW,qBAAqBA,CAACnB,SAAS,EAAEc,OAAO,EAAE;IACzC,IAAId,SAAS,CAACC,KAAK,IAAI,IAAI,EAAE;MAC3B,MAAMR,aAAa;MACnB,MAAMA,aAAa;MACnB;IACF;IAEA,MAAM;MAAEsB,OAAO;MAAEK;IAAK,CAAC,GAAGpB,SAAS,CAACC,KAAK;IAEzC,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGP,OAAO,CAACG,MAAM,EAAEG,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAClD,MAAME,MAAM,GAAGR,OAAO,CAACM,CAAC,CAAC;MAEzB,MAAMG,IAAI,GAAGjC,MAAM,CAACyB,KAAK,CAAC,CAAC,CAAC;MAC5B;MACAQ,IAAI,CAACC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC;;MAEjC;MACAD,IAAI,CAACP,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;MAC7B,MAAMO,IAAI;;MAEV;MACA,MAAMD,MAAM,CAAC1B,IAAI,CAACK,gBAAgB,CAACqB,MAAM,CAAC;;MAE1C;MACA,MAAMhC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;IAC3B;IAEA,MAAMC,aAAa;IAEnB,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEH,MAAM,GAAGE,IAAI,CAACF,MAAM,EAAEG,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAAE;MACrD,MAAM/B,aAAa;MAEnB,MAAMoC,GAAG,GAAGN,IAAI,CAACC,CAAC,CAAC;MACnB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAGF,GAAG,CAACR,MAAM,EAAES,CAAC,GAAGC,IAAI,EAAED,CAAC,EAAE,EAAE;QAChD,MAAMJ,MAAM,GAAGR,OAAO,CAACY,CAAC,CAAC;QACzB,MAAM1B,KAAK,GAAGyB,GAAG,CAACC,CAAC,CAAC;QAEpB,IAAIE,UAAU;QACd,IAAI;UACFA,UAAU,GAAGN,MAAM,CAAC1B,IAAI,CAACiC,QAAQ,CAAC7B,KAAK,EAAED,SAAS,CAAC+B,SAAS,CAAC;QAC/D,CAAC,CAAC,OAAOC,KAAK,EAAE;UACd,MAAM,IAAIC,kBAAU,CAAE,eAAcV,MAAM,CAACzB,IAAK,mCAAkCuB,CAAE,EAAC,EAAE;YAAEa,KAAK,EAAEF;UAAM,CAAC,CAAC;QAC1G;QAEA,MAAMG,KAAK,GAAG;UACZlC,KAAK,EAAE4B,UAAU;UACjBX,MAAM,EAAEK,MAAM,CAACL,MAAM;UACrBkB,KAAK,EAAEb,MAAM,CAACa,KAAK;UACnBC,SAAS,EAAEd,MAAM,CAACc;QACpB,CAAC;;QAED;QACA,MAAMd,MAAM,CAAC1B,IAAI,CAACgB,uBAAuB,CAACsB,KAAK,EAAErB,OAAO,CAAC;QACzD,OAAQS,MAAM,CAAC1B,IAAI,CAACsB,qBAAqB,CAACgB,KAAK,EAAErB,OAAO,CAAC;MAC3D;IACF;IAEA,MAAMrB,aAAa;EACrB,CAAC;EAEDqC,QAAQ,EAAE,SAAAA,CAAS7B,KAAK,EAAiB;IACvC,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIqC,SAAS,CAAC,gBAAgB,CAAC;IACvC;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACvC,KAAK,CAACc,OAAO,CAAC,EAAE;MACjC,MAAM,IAAIuB,SAAS,CAAC,gBAAgB,CAAC;IACvC;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACvC,KAAK,CAACmB,IAAI,CAAC,EAAE;MAC9B,MAAM,IAAIkB,SAAS,CAAC,gBAAgB,CAAC;IACvC;IAEA,OAAOrC,KAAK;EACd;AACF,CAAC;AAAC,IAAAwC,QAAA,GAAAC,OAAA,CAAArD,OAAA,GAEaM,GAAG;AAClBgD,MAAM,CAACD,OAAO,GAAG/C,GAAG"}