"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
const XML = {
  id: 0xF1,
  type: 'XML',
  name: 'Xml',
  declaration() {
    throw new Error('not implemented');
  },
  generateTypeInfo() {
    throw new Error('not implemented');
  },
  generateParameterLength() {
    throw new Error('not implemented');
  },
  generateParameterData() {
    throw new Error('not implemented');
  },
  validate() {
    throw new Error('not implemented');
  }
};
var _default = exports.default = XML;
module.exports = XML;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJYTUwiLCJpZCIsInR5cGUiLCJuYW1lIiwiZGVjbGFyYXRpb24iLCJFcnJvciIsImdlbmVyYXRlVHlwZUluZm8iLCJnZW5lcmF0ZVBhcmFtZXRlckxlbmd0aCIsImdlbmVyYXRlUGFyYW1ldGVyRGF0YSIsInZhbGlkYXRlIiwiX2RlZmF1bHQiLCJleHBvcnRzIiwiZGVmYXVsdCIsIm1vZHVsZSJdLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy9kYXRhLXR5cGVzL3htbC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIERhdGFUeXBlIH0gZnJvbSAnLi4vZGF0YS10eXBlJztcblxuY29uc3QgWE1MOiBEYXRhVHlwZSA9IHtcbiAgaWQ6IDB4RjEsXG4gIHR5cGU6ICdYTUwnLFxuICBuYW1lOiAnWG1sJyxcblxuICBkZWNsYXJhdGlvbigpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ25vdCBpbXBsZW1lbnRlZCcpO1xuICB9LFxuXG4gIGdlbmVyYXRlVHlwZUluZm8oKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdub3QgaW1wbGVtZW50ZWQnKTtcbiAgfSxcblxuICBnZW5lcmF0ZVBhcmFtZXRlckxlbmd0aCgpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ25vdCBpbXBsZW1lbnRlZCcpO1xuICB9LFxuXG4gIGdlbmVyYXRlUGFyYW1ldGVyRGF0YSgpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ25vdCBpbXBsZW1lbnRlZCcpO1xuICB9LFxuXG4gIHZhbGlkYXRlKCkge1xuICAgIHRocm93IG5ldyBFcnJvcignbm90IGltcGxlbWVudGVkJyk7XG4gIH1cbn07XG5cbmV4cG9ydCBkZWZhdWx0IFhNTDtcbm1vZHVsZS5leHBvcnRzID0gWE1MO1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7QUFFQSxNQUFNQSxHQUFhLEdBQUc7RUFDcEJDLEVBQUUsRUFBRSxJQUFJO0VBQ1JDLElBQUksRUFBRSxLQUFLO0VBQ1hDLElBQUksRUFBRSxLQUFLO0VBRVhDLFdBQVdBLENBQUEsRUFBRztJQUNaLE1BQU0sSUFBSUMsS0FBSyxDQUFDLGlCQUFpQixDQUFDO0VBQ3BDLENBQUM7RUFFREMsZ0JBQWdCQSxDQUFBLEVBQUc7SUFDakIsTUFBTSxJQUFJRCxLQUFLLENBQUMsaUJBQWlCLENBQUM7RUFDcEMsQ0FBQztFQUVERSx1QkFBdUJBLENBQUEsRUFBRztJQUN4QixNQUFNLElBQUlGLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQztFQUNwQyxDQUFDO0VBRURHLHFCQUFxQkEsQ0FBQSxFQUFHO0lBQ3RCLE1BQU0sSUFBSUgsS0FBSyxDQUFDLGlCQUFpQixDQUFDO0VBQ3BDLENBQUM7RUFFREksUUFBUUEsQ0FBQSxFQUFHO0lBQ1QsTUFBTSxJQUFJSixLQUFLLENBQUMsaUJBQWlCLENBQUM7RUFDcEM7QUFDRixDQUFDO0FBQUMsSUFBQUssUUFBQSxHQUFBQyxPQUFBLENBQUFDLE9BQUEsR0FFYVosR0FBRztBQUNsQmEsTUFBTSxDQUFDRixPQUFPLEdBQUdYLEdBQUcifQ==