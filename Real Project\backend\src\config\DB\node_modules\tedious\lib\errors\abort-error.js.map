{"version": 3, "file": "abort-error.js", "names": ["AbortError", "Error", "constructor", "code", "name", "exports", "default"], "sources": ["../../src/errors/abort-error.ts"], "sourcesContent": ["export default class AbortError extends Error {\n  declare code: string;\n\n  constructor() {\n    super('The operation was aborted');\n\n    this.code = 'ABORT_ERR';\n    this.name = 'AbortError';\n  }\n}\n"], "mappings": ";;;;;;AAAe,MAAMA,UAAU,SAASC,KAAK,CAAC;EAG5CC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,2BAA2B,CAAC;IAElC,IAAI,CAACC,IAAI,GAAG,WAAW;IACvB,IAAI,CAACC,IAAI,GAAG,YAAY;EAC1B;AACF;AAACC,OAAA,CAAAC,OAAA,GAAAN,UAAA"}