{"version": 3, "file": "incoming-message-stream.js", "names": ["_bl", "_interopRequireDefault", "require", "_stream", "_message", "_packet", "_errors", "obj", "__esModule", "default", "IncomingMessageStream", "Transform", "constructor", "debug", "readableObjectMode", "currentMessage", "undefined", "bl", "BufferList", "pause", "resume", "processBufferedData", "callback", "length", "HEADER_LENGTH", "readUInt16BE", "ConnectionError", "data", "slice", "consume", "packet", "Packet", "message", "Message", "type", "resetConnection", "push", "isLast", "once", "end", "write", "_transform", "chunk", "_encoding", "append", "_default", "exports", "module"], "sources": ["../src/incoming-message-stream.ts"], "sourcesContent": ["import BufferList from 'bl';\nimport { Transform } from 'stream';\n\nimport Debug from './debug';\nimport Message from './message';\nimport { Packet, HEADER_LENGTH } from './packet';\nimport { ConnectionError } from './errors';\n\n/**\n  IncomingMessageStream\n  Transform received TDS data into individual IncomingMessage streams.\n*/\nclass IncomingMessageStream extends Transform {\n  declare debug: Debug;\n  declare bl: any;\n  declare currentMessage: Message | undefined;\n\n  constructor(debug: Debug) {\n    super({ readableObjectMode: true });\n\n    this.debug = debug;\n\n    this.currentMessage = undefined;\n    this.bl = new BufferList();\n  }\n\n  pause() {\n    super.pause();\n\n    if (this.currentMessage) {\n      this.currentMessage.pause();\n    }\n\n    return this;\n  }\n\n  resume() {\n    super.resume();\n\n    if (this.currentMessage) {\n      this.currentMessage.resume();\n    }\n\n    return this;\n  }\n\n  processBufferedData(callback: (err?: ConnectionError) => void) {\n    // The packet header is always 8 bytes of length.\n    while (this.bl.length >= HEADER_LENGTH) {\n      // Get the full packet length\n      const length = this.bl.readUInt16BE(2);\n      if (length < HEADER_LENGTH) {\n        return callback(new ConnectionError('Unable to process incoming packet'));\n      }\n\n      if (this.bl.length >= length) {\n        const data = this.bl.slice(0, length);\n        this.bl.consume(length);\n\n        // TODO: Get rid of creating `Packet` instances here.\n        const packet = new Packet(data);\n        this.debug.packet('Received', packet);\n        this.debug.data(packet);\n\n        let message = this.currentMessage;\n        if (message === undefined) {\n          this.currentMessage = message = new Message({ type: packet.type(), resetConnection: false });\n          this.push(message);\n        }\n\n        if (packet.isLast()) {\n          // Wait until the current message was fully processed before we\n          // continue processing any remaining messages.\n          message.once('end', () => {\n            this.currentMessage = undefined;\n            this.processBufferedData(callback);\n          });\n          message.end(packet.data());\n          return;\n        } else if (!message.write(packet.data())) {\n          // If too much data is buffering up in the\n          // current message, wait for it to drain.\n          message.once('drain', () => {\n            this.processBufferedData(callback);\n          });\n          return;\n        }\n      } else {\n        break;\n      }\n    }\n\n    // Not enough data to read the next packet. Stop here and wait for\n    // the next call to `_transform`.\n    callback();\n  }\n\n  _transform(chunk: Buffer, _encoding: string, callback: () => void) {\n    this.bl.append(chunk);\n    this.processBufferedData(callback);\n  }\n}\n\nexport default IncomingMessageStream;\nmodule.exports = IncomingMessageStream;\n"], "mappings": ";;;;;;AAAA,IAAAA,GAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAGA,IAAAE,QAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AAA2C,SAAAD,uBAAAM,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE3C;AACA;AACA;AACA;AACA,MAAMG,qBAAqB,SAASC,iBAAS,CAAC;EAK5CC,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAAC;MAAEC,kBAAkB,EAAE;IAAK,CAAC,CAAC;IAEnC,IAAI,CAACD,KAAK,GAAGA,KAAK;IAElB,IAAI,CAACE,cAAc,GAAGC,SAAS;IAC/B,IAAI,CAACC,EAAE,GAAG,IAAIC,WAAU,CAAC,CAAC;EAC5B;EAEAC,KAAKA,CAAA,EAAG;IACN,KAAK,CAACA,KAAK,CAAC,CAAC;IAEb,IAAI,IAAI,CAACJ,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACI,KAAK,CAAC,CAAC;IAC7B;IAEA,OAAO,IAAI;EACb;EAEAC,MAAMA,CAAA,EAAG;IACP,KAAK,CAACA,MAAM,CAAC,CAAC;IAEd,IAAI,IAAI,CAACL,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACK,MAAM,CAAC,CAAC;IAC9B;IAEA,OAAO,IAAI;EACb;EAEAC,mBAAmBA,CAACC,QAAyC,EAAE;IAC7D;IACA,OAAO,IAAI,CAACL,EAAE,CAACM,MAAM,IAAIC,qBAAa,EAAE;MACtC;MACA,MAAMD,MAAM,GAAG,IAAI,CAACN,EAAE,CAACQ,YAAY,CAAC,CAAC,CAAC;MACtC,IAAIF,MAAM,GAAGC,qBAAa,EAAE;QAC1B,OAAOF,QAAQ,CAAC,IAAII,uBAAe,CAAC,mCAAmC,CAAC,CAAC;MAC3E;MAEA,IAAI,IAAI,CAACT,EAAE,CAACM,MAAM,IAAIA,MAAM,EAAE;QAC5B,MAAMI,IAAI,GAAG,IAAI,CAACV,EAAE,CAACW,KAAK,CAAC,CAAC,EAAEL,MAAM,CAAC;QACrC,IAAI,CAACN,EAAE,CAACY,OAAO,CAACN,MAAM,CAAC;;QAEvB;QACA,MAAMO,MAAM,GAAG,IAAIC,cAAM,CAACJ,IAAI,CAAC;QAC/B,IAAI,CAACd,KAAK,CAACiB,MAAM,CAAC,UAAU,EAAEA,MAAM,CAAC;QACrC,IAAI,CAACjB,KAAK,CAACc,IAAI,CAACG,MAAM,CAAC;QAEvB,IAAIE,OAAO,GAAG,IAAI,CAACjB,cAAc;QACjC,IAAIiB,OAAO,KAAKhB,SAAS,EAAE;UACzB,IAAI,CAACD,cAAc,GAAGiB,OAAO,GAAG,IAAIC,gBAAO,CAAC;YAAEC,IAAI,EAAEJ,MAAM,CAACI,IAAI,CAAC,CAAC;YAAEC,eAAe,EAAE;UAAM,CAAC,CAAC;UAC5F,IAAI,CAACC,IAAI,CAACJ,OAAO,CAAC;QACpB;QAEA,IAAIF,MAAM,CAACO,MAAM,CAAC,CAAC,EAAE;UACnB;UACA;UACAL,OAAO,CAACM,IAAI,CAAC,KAAK,EAAE,MAAM;YACxB,IAAI,CAACvB,cAAc,GAAGC,SAAS;YAC/B,IAAI,CAACK,mBAAmB,CAACC,QAAQ,CAAC;UACpC,CAAC,CAAC;UACFU,OAAO,CAACO,GAAG,CAACT,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC;UAC1B;QACF,CAAC,MAAM,IAAI,CAACK,OAAO,CAACQ,KAAK,CAACV,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC,EAAE;UACxC;UACA;UACAK,OAAO,CAACM,IAAI,CAAC,OAAO,EAAE,MAAM;YAC1B,IAAI,CAACjB,mBAAmB,CAACC,QAAQ,CAAC;UACpC,CAAC,CAAC;UACF;QACF;MACF,CAAC,MAAM;QACL;MACF;IACF;;IAEA;IACA;IACAA,QAAQ,CAAC,CAAC;EACZ;EAEAmB,UAAUA,CAACC,KAAa,EAAEC,SAAiB,EAAErB,QAAoB,EAAE;IACjE,IAAI,CAACL,EAAE,CAAC2B,MAAM,CAACF,KAAK,CAAC;IACrB,IAAI,CAACrB,mBAAmB,CAACC,QAAQ,CAAC;EACpC;AACF;AAAC,IAAAuB,QAAA,GAAAC,OAAA,CAAArC,OAAA,GAEcC,qBAAqB;AACpCqC,MAAM,CAACD,OAAO,GAAGpC,qBAAqB"}