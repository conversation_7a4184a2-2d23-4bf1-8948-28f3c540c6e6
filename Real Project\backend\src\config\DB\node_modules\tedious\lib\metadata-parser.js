"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
exports.readCollation = readCollation;
exports.readMetadata = readMetadata;
var _collation = require("./collation");
var _dataType = require("./data-type");
var _sprintfJs = require("sprintf-js");
var _helpers = require("./token/helpers");
function readCollation(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 5) {
    throw new _helpers.NotEnoughDataError(offset + 5);
  }
  const collation = _collation.Collation.fromBuffer(buf.slice(offset, offset + 5));
  return new _helpers.Result(collation, offset + 5);
}
function readSchema(buf, offset) {
  offset = +offset;
  let schemaPresent;
  ({
    offset,
    value: schemaPresent
  } = (0, _helpers.readUInt8)(buf, offset));
  if (schemaPresent !== 0x01) {
    return new _helpers.Result(undefined, offset);
  }
  let dbname;
  ({
    offset,
    value: dbname
  } = (0, _helpers.readBVarChar)(buf, offset));
  let owningSchema;
  ({
    offset,
    value: owningSchema
  } = (0, _helpers.readBVarChar)(buf, offset));
  let xmlSchemaCollection;
  ({
    offset,
    value: xmlSchemaCollection
  } = (0, _helpers.readUsVarChar)(buf, offset));
  return new _helpers.Result({
    dbname,
    owningSchema,
    xmlSchemaCollection
  }, offset);
}
function readUDTInfo(buf, offset) {
  let maxByteSize;
  ({
    offset,
    value: maxByteSize
  } = (0, _helpers.readUInt16LE)(buf, offset));
  let dbname;
  ({
    offset,
    value: dbname
  } = (0, _helpers.readBVarChar)(buf, offset));
  let owningSchema;
  ({
    offset,
    value: owningSchema
  } = (0, _helpers.readBVarChar)(buf, offset));
  let typeName;
  ({
    offset,
    value: typeName
  } = (0, _helpers.readBVarChar)(buf, offset));
  let assemblyName;
  ({
    offset,
    value: assemblyName
  } = (0, _helpers.readUsVarChar)(buf, offset));
  return new _helpers.Result({
    maxByteSize: maxByteSize,
    dbname: dbname,
    owningSchema: owningSchema,
    typeName: typeName,
    assemblyName: assemblyName
  }, offset);
}
function readMetadata(buf, offset, options) {
  let userType;
  ({
    offset,
    value: userType
  } = (options.tdsVersion < '7_2' ? _helpers.readUInt16LE : _helpers.readUInt32LE)(buf, offset));
  let flags;
  ({
    offset,
    value: flags
  } = (0, _helpers.readUInt16LE)(buf, offset));
  let typeNumber;
  ({
    offset,
    value: typeNumber
  } = (0, _helpers.readUInt8)(buf, offset));
  const type = _dataType.TYPE[typeNumber];
  if (!type) {
    throw new Error((0, _sprintfJs.sprintf)('Unrecognised data type 0x%02X', typeNumber));
  }
  switch (type.name) {
    case 'Null':
    case 'TinyInt':
    case 'SmallInt':
    case 'Int':
    case 'BigInt':
    case 'Real':
    case 'Float':
    case 'SmallMoney':
    case 'Money':
    case 'Bit':
    case 'SmallDateTime':
    case 'DateTime':
    case 'Date':
      return new _helpers.Result({
        userType: userType,
        flags: flags,
        type: type,
        collation: undefined,
        precision: undefined,
        scale: undefined,
        dataLength: undefined,
        schema: undefined,
        udtInfo: undefined
      }, offset);
    case 'IntN':
    case 'FloatN':
    case 'MoneyN':
    case 'BitN':
    case 'UniqueIdentifier':
    case 'DateTimeN':
      {
        let dataLength;
        ({
          offset,
          value: dataLength
        } = (0, _helpers.readUInt8)(buf, offset));
        return new _helpers.Result({
          userType: userType,
          flags: flags,
          type: type,
          collation: undefined,
          precision: undefined,
          scale: undefined,
          dataLength: dataLength,
          schema: undefined,
          udtInfo: undefined
        }, offset);
      }
    case 'Variant':
      {
        let dataLength;
        ({
          offset,
          value: dataLength
        } = (0, _helpers.readUInt32LE)(buf, offset));
        return new _helpers.Result({
          userType: userType,
          flags: flags,
          type: type,
          collation: undefined,
          precision: undefined,
          scale: undefined,
          dataLength: dataLength,
          schema: undefined,
          udtInfo: undefined
        }, offset);
      }
    case 'VarChar':
    case 'Char':
    case 'NVarChar':
    case 'NChar':
      {
        let dataLength;
        ({
          offset,
          value: dataLength
        } = (0, _helpers.readUInt16LE)(buf, offset));
        let collation;
        ({
          offset,
          value: collation
        } = readCollation(buf, offset));
        return new _helpers.Result({
          userType: userType,
          flags: flags,
          type: type,
          collation: collation,
          precision: undefined,
          scale: undefined,
          dataLength: dataLength,
          schema: undefined,
          udtInfo: undefined
        }, offset);
      }
    case 'Text':
    case 'NText':
      {
        let dataLength;
        ({
          offset,
          value: dataLength
        } = (0, _helpers.readUInt32LE)(buf, offset));
        let collation;
        ({
          offset,
          value: collation
        } = readCollation(buf, offset));
        return new _helpers.Result({
          userType: userType,
          flags: flags,
          type: type,
          collation: collation,
          precision: undefined,
          scale: undefined,
          dataLength: dataLength,
          schema: undefined,
          udtInfo: undefined
        }, offset);
      }
    case 'VarBinary':
    case 'Binary':
      {
        let dataLength;
        ({
          offset,
          value: dataLength
        } = (0, _helpers.readUInt16LE)(buf, offset));
        return new _helpers.Result({
          userType: userType,
          flags: flags,
          type: type,
          collation: undefined,
          precision: undefined,
          scale: undefined,
          dataLength: dataLength,
          schema: undefined,
          udtInfo: undefined
        }, offset);
      }
    case 'Image':
      {
        let dataLength;
        ({
          offset,
          value: dataLength
        } = (0, _helpers.readUInt32LE)(buf, offset));
        return new _helpers.Result({
          userType: userType,
          flags: flags,
          type: type,
          collation: undefined,
          precision: undefined,
          scale: undefined,
          dataLength: dataLength,
          schema: undefined,
          udtInfo: undefined
        }, offset);
      }
    case 'Xml':
      {
        let schema;
        ({
          offset,
          value: schema
        } = readSchema(buf, offset));
        return new _helpers.Result({
          userType: userType,
          flags: flags,
          type: type,
          collation: undefined,
          precision: undefined,
          scale: undefined,
          dataLength: undefined,
          schema: schema,
          udtInfo: undefined
        }, offset);
      }
    case 'Time':
    case 'DateTime2':
    case 'DateTimeOffset':
      {
        let scale;
        ({
          offset,
          value: scale
        } = (0, _helpers.readUInt8)(buf, offset));
        return new _helpers.Result({
          userType: userType,
          flags: flags,
          type: type,
          collation: undefined,
          precision: undefined,
          scale: scale,
          dataLength: undefined,
          schema: undefined,
          udtInfo: undefined
        }, offset);
      }
    case 'NumericN':
    case 'DecimalN':
      {
        let dataLength;
        ({
          offset,
          value: dataLength
        } = (0, _helpers.readUInt8)(buf, offset));
        let precision;
        ({
          offset,
          value: precision
        } = (0, _helpers.readUInt8)(buf, offset));
        let scale;
        ({
          offset,
          value: scale
        } = (0, _helpers.readUInt8)(buf, offset));
        return new _helpers.Result({
          userType: userType,
          flags: flags,
          type: type,
          collation: undefined,
          precision: precision,
          scale: scale,
          dataLength: dataLength,
          schema: undefined,
          udtInfo: undefined
        }, offset);
      }
    case 'UDT':
      {
        let udtInfo;
        ({
          offset,
          value: udtInfo
        } = readUDTInfo(buf, offset));
        return new _helpers.Result({
          userType: userType,
          flags: flags,
          type: type,
          collation: undefined,
          precision: undefined,
          scale: undefined,
          dataLength: undefined,
          schema: undefined,
          udtInfo: udtInfo
        }, offset);
      }
    default:
      throw new Error((0, _sprintfJs.sprintf)('Unrecognised type %s', type.name));
  }
}
function metadataParse(parser, options, callback) {
  (async () => {
    while (true) {
      let result;
      try {
        result = readMetadata(parser.buffer, parser.position, options);
      } catch (err) {
        if (err instanceof _helpers.NotEnoughDataError) {
          await parser.waitForChunk();
          continue;
        }
        throw err;
      }
      parser.position = result.offset;
      return callback(result.value);
    }
  })();
}
var _default = exports.default = metadataParse;
module.exports = metadataParse;
module.exports.readCollation = readCollation;
module.exports.readMetadata = readMetadata;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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