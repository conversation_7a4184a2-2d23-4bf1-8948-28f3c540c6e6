"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _writableTrackingBuffer = _interopRequireDefault(require("./tracking-buffer/writable-tracking-buffer"));
var crypto = _interopRequireWildcard(require("crypto"));
var _jsMd = _interopRequireDefault(require("js-md4"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class NTLMResponsePayload {
  constructor(loginData) {
    this.data = this.createResponse(loginData);
  }
  toString(indent = '') {
    return indent + 'NTLM Auth';
  }
  createResponse(challenge) {
    const client_nonce = this.createClientNonce();
    const lmv2len = 24;
    const ntlmv2len = 16;
    const domain = challenge.domain;
    const username = challenge.userName;
    const password = challenge.password;
    const ntlmData = challenge.ntlmpacket;
    const server_data = ntlmData.target;
    const server_nonce = ntlmData.nonce;
    const bufferLength = 64 + domain.length * 2 + username.length * 2 + lmv2len + ntlmv2len + 8 + 8 + 8 + 4 + server_data.length + 4;
    const data = new _writableTrackingBuffer.default(bufferLength);
    data.position = 0;
    data.writeString('NTLMSSP\u0000', 'utf8');
    data.writeUInt32LE(0x03);
    const baseIdx = 64;
    const dnIdx = baseIdx;
    const unIdx = dnIdx + domain.length * 2;
    const l2Idx = unIdx + username.length * 2;
    const ntIdx = l2Idx + lmv2len;
    data.writeUInt16LE(lmv2len);
    data.writeUInt16LE(lmv2len);
    data.writeUInt32LE(l2Idx);
    data.writeUInt16LE(ntlmv2len);
    data.writeUInt16LE(ntlmv2len);
    data.writeUInt32LE(ntIdx);
    data.writeUInt16LE(domain.length * 2);
    data.writeUInt16LE(domain.length * 2);
    data.writeUInt32LE(dnIdx);
    data.writeUInt16LE(username.length * 2);
    data.writeUInt16LE(username.length * 2);
    data.writeUInt32LE(unIdx);
    data.writeUInt16LE(0);
    data.writeUInt16LE(0);
    data.writeUInt32LE(baseIdx);
    data.writeUInt16LE(0);
    data.writeUInt16LE(0);
    data.writeUInt32LE(baseIdx);
    data.writeUInt16LE(0x8201);
    data.writeUInt16LE(0x08);
    data.writeString(domain, 'ucs2');
    data.writeString(username, 'ucs2');
    const lmv2Data = this.lmv2Response(domain, username, password, server_nonce, client_nonce);
    data.copyFrom(lmv2Data);
    const genTime = new Date().getTime();
    const ntlmDataBuffer = this.ntlmv2Response(domain, username, password, server_nonce, server_data, client_nonce, genTime);
    data.copyFrom(ntlmDataBuffer);
    data.writeUInt32LE(0x0101);
    data.writeUInt32LE(0x0000);
    const timestamp = this.createTimestamp(genTime);
    data.copyFrom(timestamp);
    data.copyFrom(client_nonce);
    data.writeUInt32LE(0x0000);
    data.copyFrom(server_data);
    data.writeUInt32LE(0x0000);
    return data.data;
  }
  createClientNonce() {
    const client_nonce = Buffer.alloc(8, 0);
    let nidx = 0;
    while (nidx < 8) {
      client_nonce.writeUInt8(Math.ceil(Math.random() * 255), nidx);
      nidx++;
    }
    return client_nonce;
  }
  ntlmv2Response(domain, user, password, serverNonce, targetInfo, clientNonce, mytime) {
    const timestamp = this.createTimestamp(mytime);
    const hash = this.ntv2Hash(domain, user, password);
    const dataLength = 40 + targetInfo.length;
    const data = Buffer.alloc(dataLength, 0);
    serverNonce.copy(data, 0, 0, 8);
    data.writeUInt32LE(0x101, 8);
    data.writeUInt32LE(0x0, 12);
    timestamp.copy(data, 16, 0, 8);
    clientNonce.copy(data, 24, 0, 8);
    data.writeUInt32LE(0x0, 32);
    targetInfo.copy(data, 36, 0, targetInfo.length);
    data.writeUInt32LE(0x0, 36 + targetInfo.length);
    return this.hmacMD5(data, hash);
  }
  createTimestamp(time) {
    const tenthsOfAMicrosecond = (BigInt(time) + BigInt(11644473600)) * BigInt(10000000);
    const lo = Number(tenthsOfAMicrosecond & BigInt(0xffffffff));
    const hi = Number(tenthsOfAMicrosecond >> BigInt(32) & BigInt(0xffffffff));
    const result = Buffer.alloc(8);
    result.writeUInt32LE(lo, 0);
    result.writeUInt32LE(hi, 4);
    return result;
  }
  lmv2Response(domain, user, password, serverNonce, clientNonce) {
    const hash = this.ntv2Hash(domain, user, password);
    const data = Buffer.alloc(serverNonce.length + clientNonce.length, 0);
    serverNonce.copy(data);
    clientNonce.copy(data, serverNonce.length, 0, clientNonce.length);
    const newhash = this.hmacMD5(data, hash);
    const response = Buffer.alloc(newhash.length + clientNonce.length, 0);
    newhash.copy(response);
    clientNonce.copy(response, newhash.length, 0, clientNonce.length);
    return response;
  }
  ntv2Hash(domain, user, password) {
    const hash = this.ntHash(password);
    const identity = Buffer.from(user.toUpperCase() + domain.toUpperCase(), 'ucs2');
    return this.hmacMD5(identity, hash);
  }
  ntHash(text) {
    const unicodeString = Buffer.from(text, 'ucs2');
    return Buffer.from(_jsMd.default.arrayBuffer(unicodeString));
  }
  hmacMD5(data, key) {
    return crypto.createHmac('MD5', key).update(data).digest();
  }
}
var _default = exports.default = NTLMResponsePayload;
module.exports = NTLMResponsePayload;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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