{"version": 3, "file": "ntlm-payload.js", "names": ["_writableTrackingBuffer", "_interopRequireDefault", "require", "crypto", "_interopRequireWildcard", "_jsMd", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "obj", "NTLMResponsePayload", "constructor", "loginData", "data", "createResponse", "toString", "indent", "challenge", "client_nonce", "createClientNonce", "lmv2len", "ntlmv2len", "domain", "username", "userName", "password", "ntlmData", "ntlmpacket", "server_data", "target", "server_nonce", "nonce", "bufferLength", "length", "WritableTrackingBuffer", "position", "writeString", "writeUInt32LE", "baseIdx", "dnIdx", "unIdx", "l2Idx", "ntIdx", "writeUInt16LE", "lmv2Data", "lmv2Response", "copyFrom", "genTime", "Date", "getTime", "ntlmDataBuffer", "ntlmv2Response", "timestamp", "createTimestamp", "<PERSON><PERSON><PERSON>", "alloc", "nidx", "writeUInt8", "Math", "ceil", "random", "user", "serverNonce", "targetInfo", "clientNonce", "mytime", "hash", "ntv2Hash", "dataLength", "copy", "hmacMD5", "time", "tenthsOfAMicrosecond", "BigInt", "lo", "Number", "hi", "result", "<PERSON><PERSON><PERSON>", "response", "ntHash", "identity", "from", "toUpperCase", "text", "unicodeString", "md4", "arrayBuffer", "key", "createHmac", "update", "digest", "_default", "exports", "module"], "sources": ["../src/ntlm-payload.ts"], "sourcesContent": ["import WritableTrackingBuffer from './tracking-buffer/writable-tracking-buffer';\nimport * as crypto from 'crypto';\nimport md4 from 'js-md4';\n\ninterface Options {\n  domain: string;\n  userName: string;\n  password: string;\n  ntlmpacket: {\n    target: Buffer;\n    nonce: Buffer;\n  };\n}\n\nclass NTLMResponsePayload {\n  declare data: Buffer;\n\n  constructor(loginData: Options) {\n    this.data = this.createResponse(loginData);\n  }\n\n  toString(indent = '') {\n    return indent + 'NTLM Auth';\n  }\n\n  createResponse(challenge: Options) {\n    const client_nonce = this.createClientNonce();\n    const lmv2len = 24;\n    const ntlmv2len = 16;\n    const domain = challenge.domain;\n    const username = challenge.userName;\n    const password = challenge.password;\n    const ntlmData = challenge.ntlmpacket;\n    const server_data = ntlmData.target;\n    const server_nonce = ntlmData.nonce;\n    const bufferLength = 64 + (domain.length * 2) + (username.length * 2) + lmv2len + ntlmv2len + 8 + 8 + 8 + 4 + server_data.length + 4;\n    const data = new WritableTrackingBuffer(bufferLength);\n    data.position = 0;\n    data.writeString('NTLMSSP\\u0000', 'utf8');\n    data.writeUInt32LE(0x03);\n    const baseIdx = 64;\n    const dnIdx = baseIdx;\n    const unIdx = dnIdx + domain.length * 2;\n    const l2Idx = unIdx + username.length * 2;\n    const ntIdx = l2Idx + lmv2len;\n    data.writeUInt16LE(lmv2len);\n    data.writeUInt16LE(lmv2len);\n    data.writeUInt32LE(l2Idx);\n    data.writeUInt16LE(ntlmv2len);\n    data.writeUInt16LE(ntlmv2len);\n    data.writeUInt32LE(ntIdx);\n    data.writeUInt16LE(domain.length * 2);\n    data.writeUInt16LE(domain.length * 2);\n    data.writeUInt32LE(dnIdx);\n    data.writeUInt16LE(username.length * 2);\n    data.writeUInt16LE(username.length * 2);\n    data.writeUInt32LE(unIdx);\n    data.writeUInt16LE(0);\n    data.writeUInt16LE(0);\n    data.writeUInt32LE(baseIdx);\n    data.writeUInt16LE(0);\n    data.writeUInt16LE(0);\n    data.writeUInt32LE(baseIdx);\n    data.writeUInt16LE(0x8201);\n    data.writeUInt16LE(0x08);\n    data.writeString(domain, 'ucs2');\n    data.writeString(username, 'ucs2');\n    const lmv2Data = this.lmv2Response(domain, username, password, server_nonce, client_nonce);\n    data.copyFrom(lmv2Data);\n    const genTime = new Date().getTime();\n    const ntlmDataBuffer = this.ntlmv2Response(domain, username, password, server_nonce, server_data, client_nonce, genTime);\n    data.copyFrom(ntlmDataBuffer);\n    data.writeUInt32LE(0x0101);\n    data.writeUInt32LE(0x0000);\n    const timestamp = this.createTimestamp(genTime);\n    data.copyFrom(timestamp);\n    data.copyFrom(client_nonce);\n    data.writeUInt32LE(0x0000);\n    data.copyFrom(server_data);\n    data.writeUInt32LE(0x0000);\n    return data.data;\n  }\n\n  createClientNonce() {\n    const client_nonce = Buffer.alloc(8, 0);\n    let nidx = 0;\n    while (nidx < 8) {\n      client_nonce.writeUInt8(Math.ceil(Math.random() * 255), nidx);\n      nidx++;\n    }\n    return client_nonce;\n  }\n\n  ntlmv2Response(domain: string, user: string, password: string, serverNonce: Buffer, targetInfo: Buffer, clientNonce: Buffer, mytime: number) {\n    const timestamp = this.createTimestamp(mytime);\n    const hash = this.ntv2Hash(domain, user, password);\n    const dataLength = 40 + targetInfo.length;\n    const data = Buffer.alloc(dataLength, 0);\n    serverNonce.copy(data, 0, 0, 8);\n    data.writeUInt32LE(0x101, 8);\n    data.writeUInt32LE(0x0, 12);\n    timestamp.copy(data, 16, 0, 8);\n    clientNonce.copy(data, 24, 0, 8);\n    data.writeUInt32LE(0x0, 32);\n    targetInfo.copy(data, 36, 0, targetInfo.length);\n    data.writeUInt32LE(0x0, 36 + targetInfo.length);\n    return this.hmacMD5(data, hash);\n  }\n\n  createTimestamp(time: number) {\n    const tenthsOfAMicrosecond = (BigInt(time) + BigInt(11644473600)) * BigInt(10000000);\n\n    const lo = Number(tenthsOfAMicrosecond & BigInt(0xffffffff));\n    const hi = Number((tenthsOfAMicrosecond >> BigInt(32)) & BigInt(0xffffffff));\n\n    const result = Buffer.alloc(8);\n    result.writeUInt32LE(lo, 0);\n    result.writeUInt32LE(hi, 4);\n    return result;\n  }\n\n  lmv2Response(domain: string, user: string, password: string, serverNonce: Buffer, clientNonce: Buffer) {\n    const hash = this.ntv2Hash(domain, user, password);\n    const data = Buffer.alloc(serverNonce.length + clientNonce.length, 0);\n\n    serverNonce.copy(data);\n    clientNonce.copy(data, serverNonce.length, 0, clientNonce.length);\n\n    const newhash = this.hmacMD5(data, hash);\n    const response = Buffer.alloc(newhash.length + clientNonce.length, 0);\n\n    newhash.copy(response);\n    clientNonce.copy(response, newhash.length, 0, clientNonce.length);\n\n    return response;\n  }\n\n  ntv2Hash(domain: string, user: string, password: string) {\n    const hash = this.ntHash(password);\n    const identity = Buffer.from(user.toUpperCase() + domain.toUpperCase(), 'ucs2');\n    return this.hmacMD5(identity, hash);\n  }\n\n  ntHash(text: string) {\n    const unicodeString = Buffer.from(text, 'ucs2');\n    return Buffer.from(md4.arrayBuffer(unicodeString));\n  }\n\n  hmacMD5(data: Buffer, key: Buffer) {\n    return crypto.createHmac('MD5', key).update(data).digest();\n  }\n}\n\nexport default NTLMResponsePayload;\nmodule.exports = NTLMResponsePayload;\n"], "mappings": ";;;;;;AAAA,IAAAA,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAAyB,SAAAI,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAH,wBAAAG,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAd,uBAAA0B,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAhB,UAAA,GAAAgB,GAAA,KAAAf,OAAA,EAAAe,GAAA;AAYzB,MAAMC,mBAAmB,CAAC;EAGxBC,WAAWA,CAACC,SAAkB,EAAE;IAC9B,IAAI,CAACC,IAAI,GAAG,IAAI,CAACC,cAAc,CAACF,SAAS,CAAC;EAC5C;EAEAG,QAAQA,CAACC,MAAM,GAAG,EAAE,EAAE;IACpB,OAAOA,MAAM,GAAG,WAAW;EAC7B;EAEAF,cAAcA,CAACG,SAAkB,EAAE;IACjC,MAAMC,YAAY,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC7C,MAAMC,OAAO,GAAG,EAAE;IAClB,MAAMC,SAAS,GAAG,EAAE;IACpB,MAAMC,MAAM,GAAGL,SAAS,CAACK,MAAM;IAC/B,MAAMC,QAAQ,GAAGN,SAAS,CAACO,QAAQ;IACnC,MAAMC,QAAQ,GAAGR,SAAS,CAACQ,QAAQ;IACnC,MAAMC,QAAQ,GAAGT,SAAS,CAACU,UAAU;IACrC,MAAMC,WAAW,GAAGF,QAAQ,CAACG,MAAM;IACnC,MAAMC,YAAY,GAAGJ,QAAQ,CAACK,KAAK;IACnC,MAAMC,YAAY,GAAG,EAAE,GAAIV,MAAM,CAACW,MAAM,GAAG,CAAE,GAAIV,QAAQ,CAACU,MAAM,GAAG,CAAE,GAAGb,OAAO,GAAGC,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGO,WAAW,CAACK,MAAM,GAAG,CAAC;IACpI,MAAMpB,IAAI,GAAG,IAAIqB,+BAAsB,CAACF,YAAY,CAAC;IACrDnB,IAAI,CAACsB,QAAQ,GAAG,CAAC;IACjBtB,IAAI,CAACuB,WAAW,CAAC,eAAe,EAAE,MAAM,CAAC;IACzCvB,IAAI,CAACwB,aAAa,CAAC,IAAI,CAAC;IACxB,MAAMC,OAAO,GAAG,EAAE;IAClB,MAAMC,KAAK,GAAGD,OAAO;IACrB,MAAME,KAAK,GAAGD,KAAK,GAAGjB,MAAM,CAACW,MAAM,GAAG,CAAC;IACvC,MAAMQ,KAAK,GAAGD,KAAK,GAAGjB,QAAQ,CAACU,MAAM,GAAG,CAAC;IACzC,MAAMS,KAAK,GAAGD,KAAK,GAAGrB,OAAO;IAC7BP,IAAI,CAAC8B,aAAa,CAACvB,OAAO,CAAC;IAC3BP,IAAI,CAAC8B,aAAa,CAACvB,OAAO,CAAC;IAC3BP,IAAI,CAACwB,aAAa,CAACI,KAAK,CAAC;IACzB5B,IAAI,CAAC8B,aAAa,CAACtB,SAAS,CAAC;IAC7BR,IAAI,CAAC8B,aAAa,CAACtB,SAAS,CAAC;IAC7BR,IAAI,CAACwB,aAAa,CAACK,KAAK,CAAC;IACzB7B,IAAI,CAAC8B,aAAa,CAACrB,MAAM,CAACW,MAAM,GAAG,CAAC,CAAC;IACrCpB,IAAI,CAAC8B,aAAa,CAACrB,MAAM,CAACW,MAAM,GAAG,CAAC,CAAC;IACrCpB,IAAI,CAACwB,aAAa,CAACE,KAAK,CAAC;IACzB1B,IAAI,CAAC8B,aAAa,CAACpB,QAAQ,CAACU,MAAM,GAAG,CAAC,CAAC;IACvCpB,IAAI,CAAC8B,aAAa,CAACpB,QAAQ,CAACU,MAAM,GAAG,CAAC,CAAC;IACvCpB,IAAI,CAACwB,aAAa,CAACG,KAAK,CAAC;IACzB3B,IAAI,CAAC8B,aAAa,CAAC,CAAC,CAAC;IACrB9B,IAAI,CAAC8B,aAAa,CAAC,CAAC,CAAC;IACrB9B,IAAI,CAACwB,aAAa,CAACC,OAAO,CAAC;IAC3BzB,IAAI,CAAC8B,aAAa,CAAC,CAAC,CAAC;IACrB9B,IAAI,CAAC8B,aAAa,CAAC,CAAC,CAAC;IACrB9B,IAAI,CAACwB,aAAa,CAACC,OAAO,CAAC;IAC3BzB,IAAI,CAAC8B,aAAa,CAAC,MAAM,CAAC;IAC1B9B,IAAI,CAAC8B,aAAa,CAAC,IAAI,CAAC;IACxB9B,IAAI,CAACuB,WAAW,CAACd,MAAM,EAAE,MAAM,CAAC;IAChCT,IAAI,CAACuB,WAAW,CAACb,QAAQ,EAAE,MAAM,CAAC;IAClC,MAAMqB,QAAQ,GAAG,IAAI,CAACC,YAAY,CAACvB,MAAM,EAAEC,QAAQ,EAAEE,QAAQ,EAAEK,YAAY,EAAEZ,YAAY,CAAC;IAC1FL,IAAI,CAACiC,QAAQ,CAACF,QAAQ,CAAC;IACvB,MAAMG,OAAO,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACpC,MAAMC,cAAc,GAAG,IAAI,CAACC,cAAc,CAAC7B,MAAM,EAAEC,QAAQ,EAAEE,QAAQ,EAAEK,YAAY,EAAEF,WAAW,EAAEV,YAAY,EAAE6B,OAAO,CAAC;IACxHlC,IAAI,CAACiC,QAAQ,CAACI,cAAc,CAAC;IAC7BrC,IAAI,CAACwB,aAAa,CAAC,MAAM,CAAC;IAC1BxB,IAAI,CAACwB,aAAa,CAAC,MAAM,CAAC;IAC1B,MAAMe,SAAS,GAAG,IAAI,CAACC,eAAe,CAACN,OAAO,CAAC;IAC/ClC,IAAI,CAACiC,QAAQ,CAACM,SAAS,CAAC;IACxBvC,IAAI,CAACiC,QAAQ,CAAC5B,YAAY,CAAC;IAC3BL,IAAI,CAACwB,aAAa,CAAC,MAAM,CAAC;IAC1BxB,IAAI,CAACiC,QAAQ,CAAClB,WAAW,CAAC;IAC1Bf,IAAI,CAACwB,aAAa,CAAC,MAAM,CAAC;IAC1B,OAAOxB,IAAI,CAACA,IAAI;EAClB;EAEAM,iBAAiBA,CAAA,EAAG;IAClB,MAAMD,YAAY,GAAGoC,MAAM,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACvC,IAAIC,IAAI,GAAG,CAAC;IACZ,OAAOA,IAAI,GAAG,CAAC,EAAE;MACftC,YAAY,CAACuC,UAAU,CAACC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,EAAEJ,IAAI,CAAC;MAC7DA,IAAI,EAAE;IACR;IACA,OAAOtC,YAAY;EACrB;EAEAiC,cAAcA,CAAC7B,MAAc,EAAEuC,IAAY,EAAEpC,QAAgB,EAAEqC,WAAmB,EAAEC,UAAkB,EAAEC,WAAmB,EAAEC,MAAc,EAAE;IAC3I,MAAMb,SAAS,GAAG,IAAI,CAACC,eAAe,CAACY,MAAM,CAAC;IAC9C,MAAMC,IAAI,GAAG,IAAI,CAACC,QAAQ,CAAC7C,MAAM,EAAEuC,IAAI,EAAEpC,QAAQ,CAAC;IAClD,MAAM2C,UAAU,GAAG,EAAE,GAAGL,UAAU,CAAC9B,MAAM;IACzC,MAAMpB,IAAI,GAAGyC,MAAM,CAACC,KAAK,CAACa,UAAU,EAAE,CAAC,CAAC;IACxCN,WAAW,CAACO,IAAI,CAACxD,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/BA,IAAI,CAACwB,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC;IAC5BxB,IAAI,CAACwB,aAAa,CAAC,GAAG,EAAE,EAAE,CAAC;IAC3Be,SAAS,CAACiB,IAAI,CAACxD,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;IAC9BmD,WAAW,CAACK,IAAI,CAACxD,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;IAChCA,IAAI,CAACwB,aAAa,CAAC,GAAG,EAAE,EAAE,CAAC;IAC3B0B,UAAU,CAACM,IAAI,CAACxD,IAAI,EAAE,EAAE,EAAE,CAAC,EAAEkD,UAAU,CAAC9B,MAAM,CAAC;IAC/CpB,IAAI,CAACwB,aAAa,CAAC,GAAG,EAAE,EAAE,GAAG0B,UAAU,CAAC9B,MAAM,CAAC;IAC/C,OAAO,IAAI,CAACqC,OAAO,CAACzD,IAAI,EAAEqD,IAAI,CAAC;EACjC;EAEAb,eAAeA,CAACkB,IAAY,EAAE;IAC5B,MAAMC,oBAAoB,GAAG,CAACC,MAAM,CAACF,IAAI,CAAC,GAAGE,MAAM,CAAC,WAAW,CAAC,IAAIA,MAAM,CAAC,QAAQ,CAAC;IAEpF,MAAMC,EAAE,GAAGC,MAAM,CAACH,oBAAoB,GAAGC,MAAM,CAAC,UAAU,CAAC,CAAC;IAC5D,MAAMG,EAAE,GAAGD,MAAM,CAAEH,oBAAoB,IAAIC,MAAM,CAAC,EAAE,CAAC,GAAIA,MAAM,CAAC,UAAU,CAAC,CAAC;IAE5E,MAAMI,MAAM,GAAGvB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9BsB,MAAM,CAACxC,aAAa,CAACqC,EAAE,EAAE,CAAC,CAAC;IAC3BG,MAAM,CAACxC,aAAa,CAACuC,EAAE,EAAE,CAAC,CAAC;IAC3B,OAAOC,MAAM;EACf;EAEAhC,YAAYA,CAACvB,MAAc,EAAEuC,IAAY,EAAEpC,QAAgB,EAAEqC,WAAmB,EAAEE,WAAmB,EAAE;IACrG,MAAME,IAAI,GAAG,IAAI,CAACC,QAAQ,CAAC7C,MAAM,EAAEuC,IAAI,EAAEpC,QAAQ,CAAC;IAClD,MAAMZ,IAAI,GAAGyC,MAAM,CAACC,KAAK,CAACO,WAAW,CAAC7B,MAAM,GAAG+B,WAAW,CAAC/B,MAAM,EAAE,CAAC,CAAC;IAErE6B,WAAW,CAACO,IAAI,CAACxD,IAAI,CAAC;IACtBmD,WAAW,CAACK,IAAI,CAACxD,IAAI,EAAEiD,WAAW,CAAC7B,MAAM,EAAE,CAAC,EAAE+B,WAAW,CAAC/B,MAAM,CAAC;IAEjE,MAAM6C,OAAO,GAAG,IAAI,CAACR,OAAO,CAACzD,IAAI,EAAEqD,IAAI,CAAC;IACxC,MAAMa,QAAQ,GAAGzB,MAAM,CAACC,KAAK,CAACuB,OAAO,CAAC7C,MAAM,GAAG+B,WAAW,CAAC/B,MAAM,EAAE,CAAC,CAAC;IAErE6C,OAAO,CAACT,IAAI,CAACU,QAAQ,CAAC;IACtBf,WAAW,CAACK,IAAI,CAACU,QAAQ,EAAED,OAAO,CAAC7C,MAAM,EAAE,CAAC,EAAE+B,WAAW,CAAC/B,MAAM,CAAC;IAEjE,OAAO8C,QAAQ;EACjB;EAEAZ,QAAQA,CAAC7C,MAAc,EAAEuC,IAAY,EAAEpC,QAAgB,EAAE;IACvD,MAAMyC,IAAI,GAAG,IAAI,CAACc,MAAM,CAACvD,QAAQ,CAAC;IAClC,MAAMwD,QAAQ,GAAG3B,MAAM,CAAC4B,IAAI,CAACrB,IAAI,CAACsB,WAAW,CAAC,CAAC,GAAG7D,MAAM,CAAC6D,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC;IAC/E,OAAO,IAAI,CAACb,OAAO,CAACW,QAAQ,EAAEf,IAAI,CAAC;EACrC;EAEAc,MAAMA,CAACI,IAAY,EAAE;IACnB,MAAMC,aAAa,GAAG/B,MAAM,CAAC4B,IAAI,CAACE,IAAI,EAAE,MAAM,CAAC;IAC/C,OAAO9B,MAAM,CAAC4B,IAAI,CAACI,aAAG,CAACC,WAAW,CAACF,aAAa,CAAC,CAAC;EACpD;EAEAf,OAAOA,CAACzD,IAAY,EAAE2E,GAAW,EAAE;IACjC,OAAOvG,MAAM,CAACwG,UAAU,CAAC,KAAK,EAAED,GAAG,CAAC,CAACE,MAAM,CAAC7E,IAAI,CAAC,CAAC8E,MAAM,CAAC,CAAC;EAC5D;AACF;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAnG,OAAA,GAEcgB,mBAAmB;AAClCoF,MAAM,CAACD,OAAO,GAAGnF,mBAAmB"}