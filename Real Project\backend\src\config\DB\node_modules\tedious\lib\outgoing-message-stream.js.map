{"version": 3, "file": "outgoing-message-stream.js", "names": ["_bl", "_interopRequireDefault", "require", "_stream", "_packet", "obj", "__esModule", "default", "OutgoingMessageStream", "Duplex", "constructor", "debug", "packetSize", "writableObjectMode", "bl", "BufferList", "on", "push", "_write", "message", "_encoding", "callback", "length", "HEADER_LENGTH", "packetNumber", "currentMessage", "data", "ignore", "append", "slice", "consume", "packet", "Packet", "type", "packetId", "resetConnection", "addData", "buffer", "pause", "last", "undefined", "_read", "_size", "resume", "_default", "exports", "module"], "sources": ["../src/outgoing-message-stream.ts"], "sourcesContent": ["import Buffer<PERSON>ist from 'bl';\nimport { Duplex } from 'stream';\n\nimport Debug from './debug';\nimport Message from './message';\nimport { Packet, HEADER_LENGTH } from './packet';\n\nclass OutgoingMessageStream extends Duplex {\n  declare packetSize: number;\n  declare debug: Debug;\n  declare bl: any;\n\n  declare currentMessage: Message | undefined;\n\n  constructor(debug: Debug, { packetSize }: { packetSize: number }) {\n    super({ writableObjectMode: true });\n\n    this.packetSize = packetSize;\n    this.debug = debug;\n    this.bl = new BufferList();\n\n    // When the writable side is ended, push `null`\n    // to also end the readable side.\n    this.on('finish', () => {\n      this.push(null);\n    });\n  }\n\n  _write(message: Message, _encoding: string, callback: (err?: Error | null) => void) {\n    const length = this.packetSize - HEADER_LENGTH;\n    let packetNumber = 0;\n\n    this.currentMessage = message;\n    this.currentMessage.on('data', (data: Buffer) => {\n      if (message.ignore) {\n        return;\n      }\n\n      this.bl.append(data);\n\n      while (this.bl.length > length) {\n        const data = this.bl.slice(0, length);\n        this.bl.consume(length);\n\n        // TODO: Get rid of creating `Packet` instances here.\n        const packet = new Packet(message.type);\n        packet.packetId(packetNumber += 1);\n        packet.resetConnection(message.resetConnection);\n        packet.addData(data);\n\n        this.debug.packet('Sent', packet);\n        this.debug.data(packet);\n\n        if (this.push(packet.buffer) === false) {\n          message.pause();\n        }\n      }\n    });\n\n    this.currentMessage.on('end', () => {\n      const data = this.bl.slice();\n      this.bl.consume(data.length);\n\n      // TODO: Get rid of creating `Packet` instances here.\n      const packet = new Packet(message.type);\n      packet.packetId(packetNumber += 1);\n      packet.resetConnection(message.resetConnection);\n      packet.last(true);\n      packet.ignore(message.ignore);\n      packet.addData(data);\n\n      this.debug.packet('Sent', packet);\n      this.debug.data(packet);\n\n      this.push(packet.buffer);\n\n      this.currentMessage = undefined;\n\n      callback();\n    });\n  }\n\n  _read(_size: number) {\n    // If we do have a message, resume it and get data flowing.\n    // Otherwise, there is nothing to do.\n    if (this.currentMessage) {\n      this.currentMessage.resume();\n    }\n  }\n}\n\nexport default OutgoingMessageStream;\nmodule.exports = OutgoingMessageStream;\n"], "mappings": ";;;;;;AAAA,IAAAA,GAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAIA,IAAAE,OAAA,GAAAF,OAAA;AAAiD,SAAAD,uBAAAI,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEjD,MAAMG,qBAAqB,SAASC,cAAM,CAAC;EAOzCC,WAAWA,CAACC,KAAY,EAAE;IAAEC;EAAmC,CAAC,EAAE;IAChE,KAAK,CAAC;MAAEC,kBAAkB,EAAE;IAAK,CAAC,CAAC;IAEnC,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACG,EAAE,GAAG,IAAIC,WAAU,CAAC,CAAC;;IAE1B;IACA;IACA,IAAI,CAACC,EAAE,CAAC,QAAQ,EAAE,MAAM;MACtB,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEAC,MAAMA,CAACC,OAAgB,EAAEC,SAAiB,EAAEC,QAAsC,EAAE;IAClF,MAAMC,MAAM,GAAG,IAAI,CAACV,UAAU,GAAGW,qBAAa;IAC9C,IAAIC,YAAY,GAAG,CAAC;IAEpB,IAAI,CAACC,cAAc,GAAGN,OAAO;IAC7B,IAAI,CAACM,cAAc,CAACT,EAAE,CAAC,MAAM,EAAGU,IAAY,IAAK;MAC/C,IAAIP,OAAO,CAACQ,MAAM,EAAE;QAClB;MACF;MAEA,IAAI,CAACb,EAAE,CAACc,MAAM,CAACF,IAAI,CAAC;MAEpB,OAAO,IAAI,CAACZ,EAAE,CAACQ,MAAM,GAAGA,MAAM,EAAE;QAC9B,MAAMI,IAAI,GAAG,IAAI,CAACZ,EAAE,CAACe,KAAK,CAAC,CAAC,EAAEP,MAAM,CAAC;QACrC,IAAI,CAACR,EAAE,CAACgB,OAAO,CAACR,MAAM,CAAC;;QAEvB;QACA,MAAMS,MAAM,GAAG,IAAIC,cAAM,CAACb,OAAO,CAACc,IAAI,CAAC;QACvCF,MAAM,CAACG,QAAQ,CAACV,YAAY,IAAI,CAAC,CAAC;QAClCO,MAAM,CAACI,eAAe,CAAChB,OAAO,CAACgB,eAAe,CAAC;QAC/CJ,MAAM,CAACK,OAAO,CAACV,IAAI,CAAC;QAEpB,IAAI,CAACf,KAAK,CAACoB,MAAM,CAAC,MAAM,EAAEA,MAAM,CAAC;QACjC,IAAI,CAACpB,KAAK,CAACe,IAAI,CAACK,MAAM,CAAC;QAEvB,IAAI,IAAI,CAACd,IAAI,CAACc,MAAM,CAACM,MAAM,CAAC,KAAK,KAAK,EAAE;UACtClB,OAAO,CAACmB,KAAK,CAAC,CAAC;QACjB;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACb,cAAc,CAACT,EAAE,CAAC,KAAK,EAAE,MAAM;MAClC,MAAMU,IAAI,GAAG,IAAI,CAACZ,EAAE,CAACe,KAAK,CAAC,CAAC;MAC5B,IAAI,CAACf,EAAE,CAACgB,OAAO,CAACJ,IAAI,CAACJ,MAAM,CAAC;;MAE5B;MACA,MAAMS,MAAM,GAAG,IAAIC,cAAM,CAACb,OAAO,CAACc,IAAI,CAAC;MACvCF,MAAM,CAACG,QAAQ,CAACV,YAAY,IAAI,CAAC,CAAC;MAClCO,MAAM,CAACI,eAAe,CAAChB,OAAO,CAACgB,eAAe,CAAC;MAC/CJ,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC;MACjBR,MAAM,CAACJ,MAAM,CAACR,OAAO,CAACQ,MAAM,CAAC;MAC7BI,MAAM,CAACK,OAAO,CAACV,IAAI,CAAC;MAEpB,IAAI,CAACf,KAAK,CAACoB,MAAM,CAAC,MAAM,EAAEA,MAAM,CAAC;MACjC,IAAI,CAACpB,KAAK,CAACe,IAAI,CAACK,MAAM,CAAC;MAEvB,IAAI,CAACd,IAAI,CAACc,MAAM,CAACM,MAAM,CAAC;MAExB,IAAI,CAACZ,cAAc,GAAGe,SAAS;MAE/BnB,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC;EACJ;EAEAoB,KAAKA,CAACC,KAAa,EAAE;IACnB;IACA;IACA,IAAI,IAAI,CAACjB,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACkB,MAAM,CAAC,CAAC;IAC9B;EACF;AACF;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAtC,OAAA,GAEcC,qBAAqB;AACpCsC,MAAM,CAACD,OAAO,GAAGrC,qBAAqB"}