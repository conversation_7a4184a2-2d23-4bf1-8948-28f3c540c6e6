{"version": 3, "file": "prelogin-payload.js", "names": ["_sprintfJs", "require", "_writableTrackingBuffer", "_interopRequireDefault", "obj", "__esModule", "default", "optionBufferSize", "TOKEN", "VERSION", "ENCRYPTION", "INSTOPT", "THREADID", "MARS", "FEDAUTHREQUIRED", "TERMINATOR", "ENCRYPT", "OFF", "ON", "NOT_SUP", "REQ", "encryptByValue", "name", "value", "marsByValue", "PreloginPayload", "constructor", "bufferOrOptions", "encrypt", "version", "major", "minor", "build", "subbuild", "<PERSON><PERSON><PERSON>", "data", "options", "createOptions", "extractOptions", "createVersionOption", "createEncryptionOption", "createInstanceOption", "createThreadIdOption", "createMarsOption", "createFedAuthOption", "length", "i", "len", "option", "alloc", "optionOffset", "optionDataOffset", "j", "writeUInt8", "token", "writeUInt16BE", "copy", "buffer", "WritableTrackingBuffer", "writeUInt32BE", "offset", "dataOffset", "readUInt16BE", "dataLength", "extractVersion", "extractEncryption", "extractInstance", "extractThreadId", "extractMars", "extractFedAuth", "readUInt8", "encryption", "encryptionString", "instance", "threadId", "readUInt32BE", "mars", "marsString", "fedAuthRequired", "toString", "indent", "sprintf", "_default", "exports", "module"], "sources": ["../src/prelogin-payload.ts"], "sourcesContent": ["import { sprintf } from 'sprintf-js';\n\nimport WritableTrackingBuffer from './tracking-buffer/writable-tracking-buffer';\n\nconst optionBufferSize = 20;\n\nconst TOKEN = {\n  VERSION: 0x00,\n  ENCRYPTION: 0x01,\n  INSTOPT: 0x02,\n  THREADID: 0x03,\n  MARS: 0x04,\n  FEDAUTHREQUIRED: 0x06,\n  TERMINATOR: 0xFF\n};\n\nconst ENCRYPT: { [key: string]: number } = {\n  OFF: 0x00,\n  ON: 0x01,\n  NOT_SUP: 0x02,\n  REQ: 0x03\n};\n\nconst encryptByValue: { [key: number]: string } = {};\n\nfor (const name in ENCRYPT) {\n  const value = ENCRYPT[name];\n  encryptByValue[value] = name;\n}\n\nconst MARS: { [key: string]: number } = {\n  OFF: 0x00,\n  ON: 0x01\n};\n\nconst marsByValue: { [key: number]: string } = {};\n\nfor (const name in MARS) {\n  const value = MARS[name];\n  marsByValue[value] = name;\n}\n\ninterface Options {\n  encrypt: boolean;\n  version: {\n    major: number;\n    minor: number;\n    build: number;\n    subbuild: number;\n  };\n}\n\n/*\n  s2.2.6.4\n */\nclass PreloginPayload {\n  declare data: Buffer;\n  declare options: Options;\n\n  declare version: {\n    major: number;\n    minor: number;\n    build: number;\n    subbuild: number;\n  };\n\n  declare encryption: number;\n  declare encryptionString: string;\n\n  declare instance: number;\n\n  declare threadId: number;\n\n  declare mars: number;\n  declare marsString: string;\n  declare fedAuthRequired: number;\n\n  constructor(bufferOrOptions: Buffer | Options = { encrypt: false, version: { major: 0, minor: 0, build: 0, subbuild: 0 } }) {\n    if (bufferOrOptions instanceof Buffer) {\n      this.data = bufferOrOptions;\n      this.options = { encrypt: false, version: { major: 0, minor: 0, build: 0, subbuild: 0 } };\n    } else {\n      this.options = bufferOrOptions;\n      this.createOptions();\n    }\n    this.extractOptions();\n  }\n\n  createOptions() {\n    const options = [\n      this.createVersionOption(),\n      this.createEncryptionOption(),\n      this.createInstanceOption(),\n      this.createThreadIdOption(),\n      this.createMarsOption(),\n      this.createFedAuthOption()\n    ];\n\n    let length = 0;\n    for (let i = 0, len = options.length; i < len; i++) {\n      const option = options[i];\n      length += 5 + option.data.length;\n    }\n    length++; // terminator\n    this.data = Buffer.alloc(length, 0);\n    let optionOffset = 0;\n    let optionDataOffset = 5 * options.length + 1;\n\n    for (let j = 0, len = options.length; j < len; j++) {\n      const option = options[j];\n      this.data.writeUInt8(option.token, optionOffset + 0);\n      this.data.writeUInt16BE(optionDataOffset, optionOffset + 1);\n      this.data.writeUInt16BE(option.data.length, optionOffset + 3);\n      optionOffset += 5;\n      option.data.copy(this.data, optionDataOffset);\n      optionDataOffset += option.data.length;\n    }\n\n    this.data.writeUInt8(TOKEN.TERMINATOR, optionOffset);\n  }\n\n  createVersionOption() {\n    const buffer = new WritableTrackingBuffer(optionBufferSize);\n    buffer.writeUInt8(this.options.version.major);\n    buffer.writeUInt8(this.options.version.minor);\n    buffer.writeUInt16BE(this.options.version.build);\n    buffer.writeUInt16BE(this.options.version.subbuild);\n    return {\n      token: TOKEN.VERSION,\n      data: buffer.data\n    };\n  }\n\n  createEncryptionOption() {\n    const buffer = new WritableTrackingBuffer(optionBufferSize);\n    if (this.options.encrypt) {\n      buffer.writeUInt8(ENCRYPT.ON);\n    } else {\n      buffer.writeUInt8(ENCRYPT.NOT_SUP);\n    }\n    return {\n      token: TOKEN.ENCRYPTION,\n      data: buffer.data\n    };\n  }\n\n  createInstanceOption() {\n    const buffer = new WritableTrackingBuffer(optionBufferSize);\n    buffer.writeUInt8(0x00);\n    return {\n      token: TOKEN.INSTOPT,\n      data: buffer.data\n    };\n  }\n\n  createThreadIdOption() {\n    const buffer = new WritableTrackingBuffer(optionBufferSize);\n    buffer.writeUInt32BE(0x00);\n    return {\n      token: TOKEN.THREADID,\n      data: buffer.data\n    };\n  }\n\n  createMarsOption() {\n    const buffer = new WritableTrackingBuffer(optionBufferSize);\n    buffer.writeUInt8(MARS.OFF);\n    return {\n      token: TOKEN.MARS,\n      data: buffer.data\n    };\n  }\n\n  createFedAuthOption() {\n    const buffer = new WritableTrackingBuffer(optionBufferSize);\n    buffer.writeUInt8(0x01);\n    return {\n      token: TOKEN.FEDAUTHREQUIRED,\n      data: buffer.data\n    };\n  }\n\n  extractOptions() {\n    let offset = 0;\n    while (this.data[offset] !== TOKEN.TERMINATOR) {\n      let dataOffset = this.data.readUInt16BE(offset + 1);\n      const dataLength = this.data.readUInt16BE(offset + 3);\n      switch (this.data[offset]) {\n        case TOKEN.VERSION:\n          this.extractVersion(dataOffset);\n          break;\n        case TOKEN.ENCRYPTION:\n          this.extractEncryption(dataOffset);\n          break;\n        case TOKEN.INSTOPT:\n          this.extractInstance(dataOffset);\n          break;\n        case TOKEN.THREADID:\n          if (dataLength > 0) {\n            this.extractThreadId(dataOffset);\n          }\n          break;\n        case TOKEN.MARS:\n          this.extractMars(dataOffset);\n          break;\n        case TOKEN.FEDAUTHREQUIRED:\n          this.extractFedAuth(dataOffset);\n          break;\n      }\n      offset += 5;\n      dataOffset += dataLength;\n    }\n  }\n\n  extractVersion(offset: number) {\n    this.version = {\n      major: this.data.readUInt8(offset + 0),\n      minor: this.data.readUInt8(offset + 1),\n      build: this.data.readUInt16BE(offset + 2),\n      subbuild: this.data.readUInt16BE(offset + 4)\n    };\n  }\n\n  extractEncryption(offset: number) {\n    this.encryption = this.data.readUInt8(offset);\n    this.encryptionString = encryptByValue[this.encryption];\n  }\n\n  extractInstance(offset: number) {\n    this.instance = this.data.readUInt8(offset);\n  }\n\n  extractThreadId(offset: number) {\n    this.threadId = this.data.readUInt32BE(offset);\n  }\n\n  extractMars(offset: number) {\n    this.mars = this.data.readUInt8(offset);\n    this.marsString = marsByValue[this.mars];\n  }\n\n  extractFedAuth(offset: number) {\n    this.fedAuthRequired = this.data.readUInt8(offset);\n  }\n\n  toString(indent = '') {\n    return indent + 'PreLogin - ' + sprintf(\n      'version:%d.%d.%d.%d, encryption:0x%02X(%s), instopt:0x%02X, threadId:0x%08X, mars:0x%02X(%s)',\n      this.version.major, this.version.minor, this.version.build, this.version.subbuild,\n      this.encryption ? this.encryption : 0,\n      this.encryptionString ? this.encryptionString : '',\n      this.instance ? this.instance : 0,\n      this.threadId ? this.threadId : 0,\n      this.mars ? this.mars : 0,\n      this.marsString ? this.marsString : ''\n    );\n  }\n}\n\nexport default PreloginPayload;\nmodule.exports = PreloginPayload;\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,uBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAgF,SAAAE,uBAAAC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEhF,MAAMG,gBAAgB,GAAG,EAAE;AAE3B,MAAMC,KAAK,GAAG;EACZC,OAAO,EAAE,IAAI;EACbC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,IAAI;EACbC,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,IAAI;EACVC,eAAe,EAAE,IAAI;EACrBC,UAAU,EAAE;AACd,CAAC;AAED,MAAMC,OAAkC,GAAG;EACzCC,GAAG,EAAE,IAAI;EACTC,EAAE,EAAE,IAAI;EACRC,OAAO,EAAE,IAAI;EACbC,GAAG,EAAE;AACP,CAAC;AAED,MAAMC,cAAyC,GAAG,CAAC,CAAC;AAEpD,KAAK,MAAMC,IAAI,IAAIN,OAAO,EAAE;EAC1B,MAAMO,KAAK,GAAGP,OAAO,CAACM,IAAI,CAAC;EAC3BD,cAAc,CAACE,KAAK,CAAC,GAAGD,IAAI;AAC9B;AAEA,MAAMT,IAA+B,GAAG;EACtCI,GAAG,EAAE,IAAI;EACTC,EAAE,EAAE;AACN,CAAC;AAED,MAAMM,WAAsC,GAAG,CAAC,CAAC;AAEjD,KAAK,MAAMF,IAAI,IAAIT,IAAI,EAAE;EACvB,MAAMU,KAAK,GAAGV,IAAI,CAACS,IAAI,CAAC;EACxBE,WAAW,CAACD,KAAK,CAAC,GAAGD,IAAI;AAC3B;AAYA;AACA;AACA;AACA,MAAMG,eAAe,CAAC;EAsBpBC,WAAWA,CAACC,eAAiC,GAAG;IAAEC,OAAO,EAAE,KAAK;IAAEC,OAAO,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAE;EAAE,CAAC,EAAE;IAC1H,IAAIN,eAAe,YAAYO,MAAM,EAAE;MACrC,IAAI,CAACC,IAAI,GAAGR,eAAe;MAC3B,IAAI,CAACS,OAAO,GAAG;QAAER,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;UAAEC,KAAK,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAE;MAAE,CAAC;IAC3F,CAAC,MAAM;MACL,IAAI,CAACG,OAAO,GAAGT,eAAe;MAC9B,IAAI,CAACU,aAAa,CAAC,CAAC;IACtB;IACA,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB;EAEAD,aAAaA,CAAA,EAAG;IACd,MAAMD,OAAO,GAAG,CACd,IAAI,CAACG,mBAAmB,CAAC,CAAC,EAC1B,IAAI,CAACC,sBAAsB,CAAC,CAAC,EAC7B,IAAI,CAACC,oBAAoB,CAAC,CAAC,EAC3B,IAAI,CAACC,oBAAoB,CAAC,CAAC,EAC3B,IAAI,CAACC,gBAAgB,CAAC,CAAC,EACvB,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAC3B;IAED,IAAIC,MAAM,GAAG,CAAC;IACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGX,OAAO,CAACS,MAAM,EAAEC,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAClD,MAAME,MAAM,GAAGZ,OAAO,CAACU,CAAC,CAAC;MACzBD,MAAM,IAAI,CAAC,GAAGG,MAAM,CAACb,IAAI,CAACU,MAAM;IAClC;IACAA,MAAM,EAAE,CAAC,CAAC;IACV,IAAI,CAACV,IAAI,GAAGD,MAAM,CAACe,KAAK,CAACJ,MAAM,EAAE,CAAC,CAAC;IACnC,IAAIK,YAAY,GAAG,CAAC;IACpB,IAAIC,gBAAgB,GAAG,CAAC,GAAGf,OAAO,CAACS,MAAM,GAAG,CAAC;IAE7C,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEL,GAAG,GAAGX,OAAO,CAACS,MAAM,EAAEO,CAAC,GAAGL,GAAG,EAAEK,CAAC,EAAE,EAAE;MAClD,MAAMJ,MAAM,GAAGZ,OAAO,CAACgB,CAAC,CAAC;MACzB,IAAI,CAACjB,IAAI,CAACkB,UAAU,CAACL,MAAM,CAACM,KAAK,EAAEJ,YAAY,GAAG,CAAC,CAAC;MACpD,IAAI,CAACf,IAAI,CAACoB,aAAa,CAACJ,gBAAgB,EAAED,YAAY,GAAG,CAAC,CAAC;MAC3D,IAAI,CAACf,IAAI,CAACoB,aAAa,CAACP,MAAM,CAACb,IAAI,CAACU,MAAM,EAAEK,YAAY,GAAG,CAAC,CAAC;MAC7DA,YAAY,IAAI,CAAC;MACjBF,MAAM,CAACb,IAAI,CAACqB,IAAI,CAAC,IAAI,CAACrB,IAAI,EAAEgB,gBAAgB,CAAC;MAC7CA,gBAAgB,IAAIH,MAAM,CAACb,IAAI,CAACU,MAAM;IACxC;IAEA,IAAI,CAACV,IAAI,CAACkB,UAAU,CAAC7C,KAAK,CAACO,UAAU,EAAEmC,YAAY,CAAC;EACtD;EAEAX,mBAAmBA,CAAA,EAAG;IACpB,MAAMkB,MAAM,GAAG,IAAIC,+BAAsB,CAACnD,gBAAgB,CAAC;IAC3DkD,MAAM,CAACJ,UAAU,CAAC,IAAI,CAACjB,OAAO,CAACP,OAAO,CAACC,KAAK,CAAC;IAC7C2B,MAAM,CAACJ,UAAU,CAAC,IAAI,CAACjB,OAAO,CAACP,OAAO,CAACE,KAAK,CAAC;IAC7C0B,MAAM,CAACF,aAAa,CAAC,IAAI,CAACnB,OAAO,CAACP,OAAO,CAACG,KAAK,CAAC;IAChDyB,MAAM,CAACF,aAAa,CAAC,IAAI,CAACnB,OAAO,CAACP,OAAO,CAACI,QAAQ,CAAC;IACnD,OAAO;MACLqB,KAAK,EAAE9C,KAAK,CAACC,OAAO;MACpB0B,IAAI,EAAEsB,MAAM,CAACtB;IACf,CAAC;EACH;EAEAK,sBAAsBA,CAAA,EAAG;IACvB,MAAMiB,MAAM,GAAG,IAAIC,+BAAsB,CAACnD,gBAAgB,CAAC;IAC3D,IAAI,IAAI,CAAC6B,OAAO,CAACR,OAAO,EAAE;MACxB6B,MAAM,CAACJ,UAAU,CAACrC,OAAO,CAACE,EAAE,CAAC;IAC/B,CAAC,MAAM;MACLuC,MAAM,CAACJ,UAAU,CAACrC,OAAO,CAACG,OAAO,CAAC;IACpC;IACA,OAAO;MACLmC,KAAK,EAAE9C,KAAK,CAACE,UAAU;MACvByB,IAAI,EAAEsB,MAAM,CAACtB;IACf,CAAC;EACH;EAEAM,oBAAoBA,CAAA,EAAG;IACrB,MAAMgB,MAAM,GAAG,IAAIC,+BAAsB,CAACnD,gBAAgB,CAAC;IAC3DkD,MAAM,CAACJ,UAAU,CAAC,IAAI,CAAC;IACvB,OAAO;MACLC,KAAK,EAAE9C,KAAK,CAACG,OAAO;MACpBwB,IAAI,EAAEsB,MAAM,CAACtB;IACf,CAAC;EACH;EAEAO,oBAAoBA,CAAA,EAAG;IACrB,MAAMe,MAAM,GAAG,IAAIC,+BAAsB,CAACnD,gBAAgB,CAAC;IAC3DkD,MAAM,CAACE,aAAa,CAAC,IAAI,CAAC;IAC1B,OAAO;MACLL,KAAK,EAAE9C,KAAK,CAACI,QAAQ;MACrBuB,IAAI,EAAEsB,MAAM,CAACtB;IACf,CAAC;EACH;EAEAQ,gBAAgBA,CAAA,EAAG;IACjB,MAAMc,MAAM,GAAG,IAAIC,+BAAsB,CAACnD,gBAAgB,CAAC;IAC3DkD,MAAM,CAACJ,UAAU,CAACxC,IAAI,CAACI,GAAG,CAAC;IAC3B,OAAO;MACLqC,KAAK,EAAE9C,KAAK,CAACK,IAAI;MACjBsB,IAAI,EAAEsB,MAAM,CAACtB;IACf,CAAC;EACH;EAEAS,mBAAmBA,CAAA,EAAG;IACpB,MAAMa,MAAM,GAAG,IAAIC,+BAAsB,CAACnD,gBAAgB,CAAC;IAC3DkD,MAAM,CAACJ,UAAU,CAAC,IAAI,CAAC;IACvB,OAAO;MACLC,KAAK,EAAE9C,KAAK,CAACM,eAAe;MAC5BqB,IAAI,EAAEsB,MAAM,CAACtB;IACf,CAAC;EACH;EAEAG,cAAcA,CAAA,EAAG;IACf,IAAIsB,MAAM,GAAG,CAAC;IACd,OAAO,IAAI,CAACzB,IAAI,CAACyB,MAAM,CAAC,KAAKpD,KAAK,CAACO,UAAU,EAAE;MAC7C,IAAI8C,UAAU,GAAG,IAAI,CAAC1B,IAAI,CAAC2B,YAAY,CAACF,MAAM,GAAG,CAAC,CAAC;MACnD,MAAMG,UAAU,GAAG,IAAI,CAAC5B,IAAI,CAAC2B,YAAY,CAACF,MAAM,GAAG,CAAC,CAAC;MACrD,QAAQ,IAAI,CAACzB,IAAI,CAACyB,MAAM,CAAC;QACvB,KAAKpD,KAAK,CAACC,OAAO;UAChB,IAAI,CAACuD,cAAc,CAACH,UAAU,CAAC;UAC/B;QACF,KAAKrD,KAAK,CAACE,UAAU;UACnB,IAAI,CAACuD,iBAAiB,CAACJ,UAAU,CAAC;UAClC;QACF,KAAKrD,KAAK,CAACG,OAAO;UAChB,IAAI,CAACuD,eAAe,CAACL,UAAU,CAAC;UAChC;QACF,KAAKrD,KAAK,CAACI,QAAQ;UACjB,IAAImD,UAAU,GAAG,CAAC,EAAE;YAClB,IAAI,CAACI,eAAe,CAACN,UAAU,CAAC;UAClC;UACA;QACF,KAAKrD,KAAK,CAACK,IAAI;UACb,IAAI,CAACuD,WAAW,CAACP,UAAU,CAAC;UAC5B;QACF,KAAKrD,KAAK,CAACM,eAAe;UACxB,IAAI,CAACuD,cAAc,CAACR,UAAU,CAAC;UAC/B;MACJ;MACAD,MAAM,IAAI,CAAC;MACXC,UAAU,IAAIE,UAAU;IAC1B;EACF;EAEAC,cAAcA,CAACJ,MAAc,EAAE;IAC7B,IAAI,CAAC/B,OAAO,GAAG;MACbC,KAAK,EAAE,IAAI,CAACK,IAAI,CAACmC,SAAS,CAACV,MAAM,GAAG,CAAC,CAAC;MACtC7B,KAAK,EAAE,IAAI,CAACI,IAAI,CAACmC,SAAS,CAACV,MAAM,GAAG,CAAC,CAAC;MACtC5B,KAAK,EAAE,IAAI,CAACG,IAAI,CAAC2B,YAAY,CAACF,MAAM,GAAG,CAAC,CAAC;MACzC3B,QAAQ,EAAE,IAAI,CAACE,IAAI,CAAC2B,YAAY,CAACF,MAAM,GAAG,CAAC;IAC7C,CAAC;EACH;EAEAK,iBAAiBA,CAACL,MAAc,EAAE;IAChC,IAAI,CAACW,UAAU,GAAG,IAAI,CAACpC,IAAI,CAACmC,SAAS,CAACV,MAAM,CAAC;IAC7C,IAAI,CAACY,gBAAgB,GAAGnD,cAAc,CAAC,IAAI,CAACkD,UAAU,CAAC;EACzD;EAEAL,eAAeA,CAACN,MAAc,EAAE;IAC9B,IAAI,CAACa,QAAQ,GAAG,IAAI,CAACtC,IAAI,CAACmC,SAAS,CAACV,MAAM,CAAC;EAC7C;EAEAO,eAAeA,CAACP,MAAc,EAAE;IAC9B,IAAI,CAACc,QAAQ,GAAG,IAAI,CAACvC,IAAI,CAACwC,YAAY,CAACf,MAAM,CAAC;EAChD;EAEAQ,WAAWA,CAACR,MAAc,EAAE;IAC1B,IAAI,CAACgB,IAAI,GAAG,IAAI,CAACzC,IAAI,CAACmC,SAAS,CAACV,MAAM,CAAC;IACvC,IAAI,CAACiB,UAAU,GAAGrD,WAAW,CAAC,IAAI,CAACoD,IAAI,CAAC;EAC1C;EAEAP,cAAcA,CAACT,MAAc,EAAE;IAC7B,IAAI,CAACkB,eAAe,GAAG,IAAI,CAAC3C,IAAI,CAACmC,SAAS,CAACV,MAAM,CAAC;EACpD;EAEAmB,QAAQA,CAACC,MAAM,GAAG,EAAE,EAAE;IACpB,OAAOA,MAAM,GAAG,aAAa,GAAG,IAAAC,kBAAO,EACrC,8FAA8F,EAC9F,IAAI,CAACpD,OAAO,CAACC,KAAK,EAAE,IAAI,CAACD,OAAO,CAACE,KAAK,EAAE,IAAI,CAACF,OAAO,CAACG,KAAK,EAAE,IAAI,CAACH,OAAO,CAACI,QAAQ,EACjF,IAAI,CAACsC,UAAU,GAAG,IAAI,CAACA,UAAU,GAAG,CAAC,EACrC,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,GAAG,EAAE,EAClD,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,CAAC,EACjC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,CAAC,EACjC,IAAI,CAACE,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,CAAC,EACzB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,GAAG,EACtC,CAAC;EACH;AACF;AAAC,IAAAK,QAAA,GAAAC,OAAA,CAAA7E,OAAA,GAEcmB,eAAe;AAC9B2D,MAAM,CAACD,OAAO,GAAG1D,eAAe"}