"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _writableTrackingBuffer = _interopRequireDefault(require("./tracking-buffer/writable-tracking-buffer"));
var _allHeaders = require("./all-headers");
var _errors = require("./errors");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
// const OPTION = {
//   WITH_RECOMPILE: 0x01,
//   NO_METADATA: 0x02,
//   REUSE_METADATA: 0x04
// };

const STATUS = {
  BY_REF_VALUE: 0x01,
  DEFAULT_VALUE: 0x02
};

/*
  s2.2.6.5
 */
class RpcRequestPayload {
  constructor(procedure, parameters, txnDescriptor, options, collation) {
    this.procedure = procedure;
    this.parameters = parameters;
    this.options = options;
    this.txnDescriptor = txnDescriptor;
    this.collation = collation;
  }
  [Symbol.iterator]() {
    return this.generateData();
  }
  *generateData() {
    const buffer = new _writableTrackingBuffer.default(500);
    if (this.options.tdsVersion >= '7_2') {
      const outstandingRequestCount = 1;
      (0, _allHeaders.writeToTrackingBuffer)(buffer, this.txnDescriptor, outstandingRequestCount);
    }
    if (typeof this.procedure === 'string') {
      buffer.writeUsVarchar(this.procedure);
    } else {
      buffer.writeUShort(0xFFFF);
      buffer.writeUShort(this.procedure);
    }
    const optionFlags = 0;
    buffer.writeUInt16LE(optionFlags);
    yield buffer.data;
    const parametersLength = this.parameters.length;
    for (let i = 0; i < parametersLength; i++) {
      yield* this.generateParameterData(this.parameters[i]);
    }
  }
  toString(indent = '') {
    return indent + ('RPC Request - ' + this.procedure);
  }
  *generateParameterData(parameter) {
    const buffer = new _writableTrackingBuffer.default(1 + 2 + Buffer.byteLength(parameter.name, 'ucs-2') + 1);
    if (parameter.name) {
      buffer.writeBVarchar('@' + parameter.name);
    } else {
      buffer.writeBVarchar('');
    }
    let statusFlags = 0;
    if (parameter.output) {
      statusFlags |= STATUS.BY_REF_VALUE;
    }
    buffer.writeUInt8(statusFlags);
    yield buffer.data;
    const param = {
      value: parameter.value
    };
    const type = parameter.type;
    if ((type.id & 0x30) === 0x20) {
      if (parameter.length) {
        param.length = parameter.length;
      } else if (type.resolveLength) {
        param.length = type.resolveLength(parameter);
      }
    }
    if (parameter.precision) {
      param.precision = parameter.precision;
    } else if (type.resolvePrecision) {
      param.precision = type.resolvePrecision(parameter);
    }
    if (parameter.scale) {
      param.scale = parameter.scale;
    } else if (type.resolveScale) {
      param.scale = type.resolveScale(parameter);
    }
    if (this.collation) {
      param.collation = this.collation;
    }
    yield type.generateTypeInfo(param, this.options);
    yield type.generateParameterLength(param, this.options);
    try {
      yield* type.generateParameterData(param, this.options);
    } catch (error) {
      throw new _errors.InputError(`Input parameter '${parameter.name}' could not be validated`, {
        cause: error
      });
    }
  }
}
var _default = exports.default = RpcRequestPayload;
module.exports = RpcRequestPayload;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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