{"version": 3, "file": "rpcrequest-payload.js", "names": ["_writableTrackingBuffer", "_interopRequireDefault", "require", "_allHeaders", "_errors", "obj", "__esModule", "default", "STATUS", "BY_REF_VALUE", "DEFAULT_VALUE", "RpcRequestPayload", "constructor", "procedure", "parameters", "txnDescriptor", "options", "collation", "Symbol", "iterator", "generateData", "buffer", "WritableTrackingBuffer", "tdsVersion", "outstandingRequestCount", "writeToTrackingBuffer", "writeUsVarchar", "writeUShort", "optionFlags", "writeUInt16LE", "data", "<PERSON><PERSON><PERSON>th", "length", "i", "generateParameterData", "toString", "indent", "parameter", "<PERSON><PERSON><PERSON>", "byteLength", "name", "writeBVarchar", "statusFlags", "output", "writeUInt8", "param", "value", "type", "id", "<PERSON><PERSON><PERSON><PERSON>", "precision", "resolvePrecision", "scale", "resolveScale", "generateTypeInfo", "generateParameterLength", "error", "InputError", "cause", "_default", "exports", "module"], "sources": ["../src/rpcrequest-payload.ts"], "sourcesContent": ["import WritableTracking<PERSON>uffer from './tracking-buffer/writable-tracking-buffer';\nimport { writeToTrackingBuffer } from './all-headers';\nimport { type Parameter, type ParameterData } from './data-type';\nimport { type InternalConnectionOptions } from './connection';\nimport { Collation } from './collation';\nimport { InputError } from './errors';\n\n// const OPTION = {\n//   WITH_RECOMPILE: 0x01,\n//   NO_METADATA: 0x02,\n//   REUSE_METADATA: 0x04\n// };\n\nconst STATUS = {\n  BY_REF_VALUE: 0x01,\n  DEFAULT_VALUE: 0x02\n};\n\n/*\n  s2.2.6.5\n */\nclass RpcRequestPayload implements Iterable<Buffer> {\n  declare procedure: string | number;\n  declare parameters: Parameter[];\n\n  declare options: InternalConnectionOptions;\n  declare txnDescriptor: Buffer;\n  declare collation: Collation | undefined;\n\n  constructor(procedure: string | number, parameters: Parameter[], txnDescriptor: <PERSON>uffer, options: InternalConnectionOptions, collation: Collation | undefined) {\n    this.procedure = procedure;\n    this.parameters = parameters;\n    this.options = options;\n    this.txnDescriptor = txnDescriptor;\n    this.collation = collation;\n  }\n\n  [Symbol.iterator]() {\n    return this.generateData();\n  }\n\n  * generateData() {\n    const buffer = new WritableTrackingBuffer(500);\n    if (this.options.tdsVersion >= '7_2') {\n      const outstandingRequestCount = 1;\n      writeToTrackingBuffer(buffer, this.txnDescriptor, outstandingRequestCount);\n    }\n\n    if (typeof this.procedure === 'string') {\n      buffer.writeUsVarchar(this.procedure);\n    } else {\n      buffer.writeUShort(0xFFFF);\n      buffer.writeUShort(this.procedure);\n    }\n\n    const optionFlags = 0;\n    buffer.writeUInt16LE(optionFlags);\n    yield buffer.data;\n\n    const parametersLength = this.parameters.length;\n    for (let i = 0; i < parametersLength; i++) {\n      yield * this.generateParameterData(this.parameters[i]);\n    }\n  }\n\n  toString(indent = '') {\n    return indent + ('RPC Request - ' + this.procedure);\n  }\n\n  * generateParameterData(parameter: Parameter) {\n    const buffer = new WritableTrackingBuffer(1 + 2 + Buffer.byteLength(parameter.name, 'ucs-2') + 1);\n\n    if (parameter.name) {\n      buffer.writeBVarchar('@' + parameter.name);\n    } else {\n      buffer.writeBVarchar('');\n    }\n\n    let statusFlags = 0;\n    if (parameter.output) {\n      statusFlags |= STATUS.BY_REF_VALUE;\n    }\n    buffer.writeUInt8(statusFlags);\n\n    yield buffer.data;\n\n    const param: ParameterData = { value: parameter.value };\n\n    const type = parameter.type;\n\n    if ((type.id & 0x30) === 0x20) {\n      if (parameter.length) {\n        param.length = parameter.length;\n      } else if (type.resolveLength) {\n        param.length = type.resolveLength(parameter);\n      }\n    }\n\n    if (parameter.precision) {\n      param.precision = parameter.precision;\n    } else if (type.resolvePrecision) {\n      param.precision = type.resolvePrecision(parameter);\n    }\n\n    if (parameter.scale) {\n      param.scale = parameter.scale;\n    } else if (type.resolveScale) {\n      param.scale = type.resolveScale(parameter);\n    }\n\n    if (this.collation) {\n      param.collation = this.collation;\n    }\n\n    yield type.generateTypeInfo(param, this.options);\n    yield type.generateParameterLength(param, this.options);\n    try {\n      yield * type.generateParameterData(param, this.options);\n    } catch (error) {\n      throw new InputError(`Input parameter '${parameter.name}' could not be validated`, { cause: error });\n    }\n  }\n}\n\nexport default RpcRequestPayload;\nmodule.exports = RpcRequestPayload;\n"], "mappings": ";;;;;;AAAA,IAAAA,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAIA,IAAAE,OAAA,GAAAF,OAAA;AAAsC,SAAAD,uBAAAI,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEtC;AACA;AACA;AACA;AACA;;AAEA,MAAMG,MAAM,GAAG;EACbC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE;AACjB,CAAC;;AAED;AACA;AACA;AACA,MAAMC,iBAAiB,CAA6B;EAQlDC,WAAWA,CAACC,SAA0B,EAAEC,UAAuB,EAAEC,aAAqB,EAAEC,OAAkC,EAAEC,SAAgC,EAAE;IAC5J,IAAI,CAACJ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACE,SAAS,GAAGA,SAAS;EAC5B;EAEA,CAACC,MAAM,CAACC,QAAQ,IAAI;IAClB,OAAO,IAAI,CAACC,YAAY,CAAC,CAAC;EAC5B;EAEA,CAAEA,YAAYA,CAAA,EAAG;IACf,MAAMC,MAAM,GAAG,IAAIC,+BAAsB,CAAC,GAAG,CAAC;IAC9C,IAAI,IAAI,CAACN,OAAO,CAACO,UAAU,IAAI,KAAK,EAAE;MACpC,MAAMC,uBAAuB,GAAG,CAAC;MACjC,IAAAC,iCAAqB,EAACJ,MAAM,EAAE,IAAI,CAACN,aAAa,EAAES,uBAAuB,CAAC;IAC5E;IAEA,IAAI,OAAO,IAAI,CAACX,SAAS,KAAK,QAAQ,EAAE;MACtCQ,MAAM,CAACK,cAAc,CAAC,IAAI,CAACb,SAAS,CAAC;IACvC,CAAC,MAAM;MACLQ,MAAM,CAACM,WAAW,CAAC,MAAM,CAAC;MAC1BN,MAAM,CAACM,WAAW,CAAC,IAAI,CAACd,SAAS,CAAC;IACpC;IAEA,MAAMe,WAAW,GAAG,CAAC;IACrBP,MAAM,CAACQ,aAAa,CAACD,WAAW,CAAC;IACjC,MAAMP,MAAM,CAACS,IAAI;IAEjB,MAAMC,gBAAgB,GAAG,IAAI,CAACjB,UAAU,CAACkB,MAAM;IAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,gBAAgB,EAAEE,CAAC,EAAE,EAAE;MACzC,OAAQ,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACpB,UAAU,CAACmB,CAAC,CAAC,CAAC;IACxD;EACF;EAEAE,QAAQA,CAACC,MAAM,GAAG,EAAE,EAAE;IACpB,OAAOA,MAAM,IAAI,gBAAgB,GAAG,IAAI,CAACvB,SAAS,CAAC;EACrD;EAEA,CAAEqB,qBAAqBA,CAACG,SAAoB,EAAE;IAC5C,MAAMhB,MAAM,GAAG,IAAIC,+BAAsB,CAAC,CAAC,GAAG,CAAC,GAAGgB,MAAM,CAACC,UAAU,CAACF,SAAS,CAACG,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAEjG,IAAIH,SAAS,CAACG,IAAI,EAAE;MAClBnB,MAAM,CAACoB,aAAa,CAAC,GAAG,GAAGJ,SAAS,CAACG,IAAI,CAAC;IAC5C,CAAC,MAAM;MACLnB,MAAM,CAACoB,aAAa,CAAC,EAAE,CAAC;IAC1B;IAEA,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIL,SAAS,CAACM,MAAM,EAAE;MACpBD,WAAW,IAAIlC,MAAM,CAACC,YAAY;IACpC;IACAY,MAAM,CAACuB,UAAU,CAACF,WAAW,CAAC;IAE9B,MAAMrB,MAAM,CAACS,IAAI;IAEjB,MAAMe,KAAoB,GAAG;MAAEC,KAAK,EAAET,SAAS,CAACS;IAAM,CAAC;IAEvD,MAAMC,IAAI,GAAGV,SAAS,CAACU,IAAI;IAE3B,IAAI,CAACA,IAAI,CAACC,EAAE,GAAG,IAAI,MAAM,IAAI,EAAE;MAC7B,IAAIX,SAAS,CAACL,MAAM,EAAE;QACpBa,KAAK,CAACb,MAAM,GAAGK,SAAS,CAACL,MAAM;MACjC,CAAC,MAAM,IAAIe,IAAI,CAACE,aAAa,EAAE;QAC7BJ,KAAK,CAACb,MAAM,GAAGe,IAAI,CAACE,aAAa,CAACZ,SAAS,CAAC;MAC9C;IACF;IAEA,IAAIA,SAAS,CAACa,SAAS,EAAE;MACvBL,KAAK,CAACK,SAAS,GAAGb,SAAS,CAACa,SAAS;IACvC,CAAC,MAAM,IAAIH,IAAI,CAACI,gBAAgB,EAAE;MAChCN,KAAK,CAACK,SAAS,GAAGH,IAAI,CAACI,gBAAgB,CAACd,SAAS,CAAC;IACpD;IAEA,IAAIA,SAAS,CAACe,KAAK,EAAE;MACnBP,KAAK,CAACO,KAAK,GAAGf,SAAS,CAACe,KAAK;IAC/B,CAAC,MAAM,IAAIL,IAAI,CAACM,YAAY,EAAE;MAC5BR,KAAK,CAACO,KAAK,GAAGL,IAAI,CAACM,YAAY,CAAChB,SAAS,CAAC;IAC5C;IAEA,IAAI,IAAI,CAACpB,SAAS,EAAE;MAClB4B,KAAK,CAAC5B,SAAS,GAAG,IAAI,CAACA,SAAS;IAClC;IAEA,MAAM8B,IAAI,CAACO,gBAAgB,CAACT,KAAK,EAAE,IAAI,CAAC7B,OAAO,CAAC;IAChD,MAAM+B,IAAI,CAACQ,uBAAuB,CAACV,KAAK,EAAE,IAAI,CAAC7B,OAAO,CAAC;IACvD,IAAI;MACF,OAAQ+B,IAAI,CAACb,qBAAqB,CAACW,KAAK,EAAE,IAAI,CAAC7B,OAAO,CAAC;IACzD,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACd,MAAM,IAAIC,kBAAU,CAAE,oBAAmBpB,SAAS,CAACG,IAAK,0BAAyB,EAAE;QAAEkB,KAAK,EAAEF;MAAM,CAAC,CAAC;IACtG;EACF;AACF;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAArD,OAAA,GAEcI,iBAAiB;AAChCkD,MAAM,CAACD,OAAO,GAAGjD,iBAAiB"}