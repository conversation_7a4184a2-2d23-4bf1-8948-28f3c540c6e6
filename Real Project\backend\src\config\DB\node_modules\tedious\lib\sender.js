"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.sendInParallel = sendInParallel;
exports.sendMessage = sendMessage;
var _dgram = _interopRequireDefault(require("dgram"));
var _net = _interopRequireDefault(require("net"));
var _nodeUrl = _interopRequireDefault(require("node:url"));
var _abortError = _interopRequireDefault(require("./errors/abort-error"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
async function sendInParallel(addresses, port, request, signal) {
  if (signal.aborted) {
    throw new _abortError.default();
  }
  return await new Promise((resolve, reject) => {
    const sockets = [];
    let errorCount = 0;
    const onError = err => {
      errorCount++;
      if (errorCount === addresses.length) {
        signal.removeEventListener('abort', onAbort);
        clearSockets();
        reject(err);
      }
    };
    const onMessage = message => {
      signal.removeEventListener('abort', onAbort);
      clearSockets();
      resolve(message);
    };
    const onAbort = () => {
      clearSockets();
      reject(new _abortError.default());
    };
    const clearSockets = () => {
      for (const socket of sockets) {
        socket.removeListener('error', onError);
        socket.removeListener('message', onMessage);
        socket.close();
      }
    };
    signal.addEventListener('abort', onAbort, {
      once: true
    });
    for (let j = 0; j < addresses.length; j++) {
      const udpType = addresses[j].family === 6 ? 'udp6' : 'udp4';
      const socket = _dgram.default.createSocket(udpType);
      sockets.push(socket);
      socket.on('error', onError);
      socket.on('message', onMessage);
      socket.send(request, 0, request.length, port, addresses[j].address);
    }
  });
}
async function sendMessage(host, port, lookup, signal, request) {
  if (signal.aborted) {
    throw new _abortError.default();
  }
  let addresses;
  if (_net.default.isIP(host)) {
    addresses = [{
      address: host,
      family: _net.default.isIPv6(host) ? 6 : 4
    }];
  } else {
    addresses = await new Promise((resolve, reject) => {
      const onAbort = () => {
        reject(new _abortError.default());
      };
      const domainInASCII = _nodeUrl.default.domainToASCII(host);
      lookup(domainInASCII === '' ? host : domainInASCII, {
        all: true
      }, (err, addresses) => {
        signal.removeEventListener('abort', onAbort);
        err ? reject(err) : resolve(addresses);
      });
    });
  }
  return await sendInParallel(addresses, port, request, signal);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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