{"version": 3, "file": "sender.js", "names": ["_dgram", "_interopRequireDefault", "require", "_net", "_nodeUrl", "_abortError", "obj", "__esModule", "default", "sendInParallel", "addresses", "port", "request", "signal", "aborted", "AbortError", "Promise", "resolve", "reject", "sockets", "errorCount", "onError", "err", "length", "removeEventListener", "onAbort", "clearSockets", "onMessage", "message", "socket", "removeListener", "close", "addEventListener", "once", "j", "udpType", "family", "dgram", "createSocket", "push", "on", "send", "address", "sendMessage", "host", "lookup", "net", "isIP", "isIPv6", "domainInASCII", "url", "domainToASCII", "all"], "sources": ["../src/sender.ts"], "sourcesContent": ["import dgram from 'dgram';\nimport dns from 'dns';\nimport net from 'net';\nimport url from 'node:url';\n\nimport AbortError from './errors/abort-error';\n\ntype LookupFunction = (hostname: string, options: dns.LookupAllOptions, callback: (err: NodeJS.ErrnoException | null, addresses: dns.LookupAddress[]) => void) => void;\n\nexport async function sendInParallel(addresses: dns.LookupAddress[], port: number, request: Buffer, signal: AbortSignal) {\n  if (signal.aborted) {\n    throw new AbortError();\n  }\n\n  return await new Promise<Buffer>((resolve, reject) => {\n    const sockets: dgram.Socket[] = [];\n\n    let errorCount = 0;\n\n    const onError = (err: Error) => {\n      errorCount++;\n\n      if (errorCount === addresses.length) {\n        signal.removeEventListener('abort', onAbort);\n        clearSockets();\n\n        reject(err);\n      }\n    };\n\n    const onMessage = (message: <PERSON>uffer) => {\n      signal.removeEventListener('abort', onAbort);\n      clearSockets();\n\n      resolve(message);\n    };\n\n    const onAbort = () => {\n      clearSockets();\n\n      reject(new AbortError());\n    };\n\n    const clearSockets = () => {\n      for (const socket of sockets) {\n        socket.removeListener('error', onError);\n        socket.removeListener('message', onMessage);\n        socket.close();\n      }\n    };\n\n    signal.addEventListener('abort', onAbort, { once: true });\n\n    for (let j = 0; j < addresses.length; j++) {\n      const udpType = addresses[j].family === 6 ? 'udp6' : 'udp4';\n\n      const socket = dgram.createSocket(udpType);\n      sockets.push(socket);\n      socket.on('error', onError);\n      socket.on('message', onMessage);\n      socket.send(request, 0, request.length, port, addresses[j].address);\n    }\n  });\n}\n\nexport async function sendMessage(host: string, port: number, lookup: LookupFunction, signal: AbortSignal, request: Buffer) {\n  if (signal.aborted) {\n    throw new AbortError();\n  }\n\n  let addresses: dns.LookupAddress[];\n\n  if (net.isIP(host)) {\n    addresses = [\n      { address: host, family: net.isIPv6(host) ? 6 : 4 }\n    ];\n  } else {\n    addresses = await new Promise<dns.LookupAddress[]>((resolve, reject) => {\n      const onAbort = () => {\n        reject(new AbortError());\n      };\n\n      const domainInASCII = url.domainToASCII(host);\n      lookup(domainInASCII === '' ? host : domainInASCII, { all: true }, (err, addresses) => {\n        signal.removeEventListener('abort', onAbort);\n\n        err ? reject(err) : resolve(addresses);\n      });\n    });\n  }\n\n  return await sendInParallel(addresses, port, request, signal);\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,IAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,QAAA,GAAAH,sBAAA,CAAAC,OAAA;AAEA,IAAAG,WAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAA8C,SAAAD,uBAAAK,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAIvC,eAAeG,cAAcA,CAACC,SAA8B,EAAEC,IAAY,EAAEC,OAAe,EAAEC,MAAmB,EAAE;EACvH,IAAIA,MAAM,CAACC,OAAO,EAAE;IAClB,MAAM,IAAIC,mBAAU,CAAC,CAAC;EACxB;EAEA,OAAO,MAAM,IAAIC,OAAO,CAAS,CAACC,OAAO,EAAEC,MAAM,KAAK;IACpD,MAAMC,OAAuB,GAAG,EAAE;IAElC,IAAIC,UAAU,GAAG,CAAC;IAElB,MAAMC,OAAO,GAAIC,GAAU,IAAK;MAC9BF,UAAU,EAAE;MAEZ,IAAIA,UAAU,KAAKV,SAAS,CAACa,MAAM,EAAE;QACnCV,MAAM,CAACW,mBAAmB,CAAC,OAAO,EAAEC,OAAO,CAAC;QAC5CC,YAAY,CAAC,CAAC;QAEdR,MAAM,CAACI,GAAG,CAAC;MACb;IACF,CAAC;IAED,MAAMK,SAAS,GAAIC,OAAe,IAAK;MACrCf,MAAM,CAACW,mBAAmB,CAAC,OAAO,EAAEC,OAAO,CAAC;MAC5CC,YAAY,CAAC,CAAC;MAEdT,OAAO,CAACW,OAAO,CAAC;IAClB,CAAC;IAED,MAAMH,OAAO,GAAGA,CAAA,KAAM;MACpBC,YAAY,CAAC,CAAC;MAEdR,MAAM,CAAC,IAAIH,mBAAU,CAAC,CAAC,CAAC;IAC1B,CAAC;IAED,MAAMW,YAAY,GAAGA,CAAA,KAAM;MACzB,KAAK,MAAMG,MAAM,IAAIV,OAAO,EAAE;QAC5BU,MAAM,CAACC,cAAc,CAAC,OAAO,EAAET,OAAO,CAAC;QACvCQ,MAAM,CAACC,cAAc,CAAC,SAAS,EAAEH,SAAS,CAAC;QAC3CE,MAAM,CAACE,KAAK,CAAC,CAAC;MAChB;IACF,CAAC;IAEDlB,MAAM,CAACmB,gBAAgB,CAAC,OAAO,EAAEP,OAAO,EAAE;MAAEQ,IAAI,EAAE;IAAK,CAAC,CAAC;IAEzD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,SAAS,CAACa,MAAM,EAAEW,CAAC,EAAE,EAAE;MACzC,MAAMC,OAAO,GAAGzB,SAAS,CAACwB,CAAC,CAAC,CAACE,MAAM,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM;MAE3D,MAAMP,MAAM,GAAGQ,cAAK,CAACC,YAAY,CAACH,OAAO,CAAC;MAC1ChB,OAAO,CAACoB,IAAI,CAACV,MAAM,CAAC;MACpBA,MAAM,CAACW,EAAE,CAAC,OAAO,EAAEnB,OAAO,CAAC;MAC3BQ,MAAM,CAACW,EAAE,CAAC,SAAS,EAAEb,SAAS,CAAC;MAC/BE,MAAM,CAACY,IAAI,CAAC7B,OAAO,EAAE,CAAC,EAAEA,OAAO,CAACW,MAAM,EAAEZ,IAAI,EAAED,SAAS,CAACwB,CAAC,CAAC,CAACQ,OAAO,CAAC;IACrE;EACF,CAAC,CAAC;AACJ;AAEO,eAAeC,WAAWA,CAACC,IAAY,EAAEjC,IAAY,EAAEkC,MAAsB,EAAEhC,MAAmB,EAAED,OAAe,EAAE;EAC1H,IAAIC,MAAM,CAACC,OAAO,EAAE;IAClB,MAAM,IAAIC,mBAAU,CAAC,CAAC;EACxB;EAEA,IAAIL,SAA8B;EAElC,IAAIoC,YAAG,CAACC,IAAI,CAACH,IAAI,CAAC,EAAE;IAClBlC,SAAS,GAAG,CACV;MAAEgC,OAAO,EAAEE,IAAI;MAAER,MAAM,EAAEU,YAAG,CAACE,MAAM,CAACJ,IAAI,CAAC,GAAG,CAAC,GAAG;IAAE,CAAC,CACpD;EACH,CAAC,MAAM;IACLlC,SAAS,GAAG,MAAM,IAAIM,OAAO,CAAsB,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtE,MAAMO,OAAO,GAAGA,CAAA,KAAM;QACpBP,MAAM,CAAC,IAAIH,mBAAU,CAAC,CAAC,CAAC;MAC1B,CAAC;MAED,MAAMkC,aAAa,GAAGC,gBAAG,CAACC,aAAa,CAACP,IAAI,CAAC;MAC7CC,MAAM,CAACI,aAAa,KAAK,EAAE,GAAGL,IAAI,GAAGK,aAAa,EAAE;QAAEG,GAAG,EAAE;MAAK,CAAC,EAAE,CAAC9B,GAAG,EAAEZ,SAAS,KAAK;QACrFG,MAAM,CAACW,mBAAmB,CAAC,OAAO,EAAEC,OAAO,CAAC;QAE5CH,GAAG,GAAGJ,MAAM,CAACI,GAAG,CAAC,GAAGL,OAAO,CAACP,SAAS,CAAC;MACxC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA,OAAO,MAAMD,cAAc,CAACC,SAAS,EAAEC,IAAI,EAAEC,OAAO,EAAEC,MAAM,CAAC;AAC/D"}