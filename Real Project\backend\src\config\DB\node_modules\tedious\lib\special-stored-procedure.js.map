{"version": 3, "file": "special-stored-procedure.js", "names": ["procedures", "Sp_Cursor", "Sp_CursorOpen", "Sp_CursorPrepare", "Sp_CursorExecute", "Sp_CursorPrepExec", "Sp_CursorUnprepare", "Sp_CursorFetch", "Sp_CursorOption", "Sp_CursorClose", "Sp_ExecuteSql", "Sp_Prepare", "Sp_Execute", "Sp_PrepExec", "Sp_PrepExecRpc", "Sp_Unprepare", "_default", "exports", "default", "module"], "sources": ["../src/special-stored-procedure.ts"], "sourcesContent": ["const procedures = {\n  Sp_Cursor: 1,\n  Sp_<PERSON>ursorOpen: 2,\n  Sp_CursorPrepare: 3,\n  Sp_CursorExecute: 4,\n  Sp_CursorPrepExec: 5,\n  Sp_CursorUnprepare: 6,\n  Sp_<PERSON>ursorFetch: 7,\n  Sp_CursorOption: 8,\n  Sp_CursorClose: 9,\n  Sp_ExecuteSql: 10,\n  Sp_Prepare: 11,\n  Sp_Execute: 12,\n  Sp_PrepExec: 13,\n  Sp_PrepExecRpc: 14,\n  Sp_Unprepare: 15\n};\n\nexport default procedures;\nmodule.exports = procedures;\n"], "mappings": ";;;;;;AAAA,MAAMA,UAAU,GAAG;EACjBC,SAAS,EAAE,CAAC;EACZC,aAAa,EAAE,CAAC;EAChBC,gBAAgB,EAAE,CAAC;EACnBC,gBAAgB,EAAE,CAAC;EACnBC,iBAAiB,EAAE,CAAC;EACpBC,kBAAkB,EAAE,CAAC;EACrBC,cAAc,EAAE,CAAC;EACjBC,eAAe,EAAE,CAAC;EAClBC,cAAc,EAAE,CAAC;EACjBC,aAAa,EAAE,EAAE;EACjBC,UAAU,EAAE,EAAE;EACdC,UAAU,EAAE,EAAE;EACdC,WAAW,EAAE,EAAE;EACfC,cAAc,EAAE,EAAE;EAClBC,YAAY,EAAE;AAChB,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEalB,UAAU;AACzBmB,MAAM,CAACF,OAAO,GAAGjB,UAAU"}