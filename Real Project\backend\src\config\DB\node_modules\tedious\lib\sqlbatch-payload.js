"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _writableTrackingBuffer = _interopRequireDefault(require("./tracking-buffer/writable-tracking-buffer"));
var _allHeaders = require("./all-headers");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
/*
  s2.2.6.6
 */
class SqlBatchPayload {
  constructor(sqlText, txnDescriptor, options) {
    this.sqlText = sqlText;
    this.txnDescriptor = txnDescriptor;
    this.options = options;
  }
  *[Symbol.iterator]() {
    if (this.options.tdsVersion >= '7_2') {
      const buffer = new _writableTrackingBuffer.default(18, 'ucs2');
      const outstandingRequestCount = 1;
      (0, _allHeaders.writeToTrackingBuffer)(buffer, this.txnDescriptor, outstandingRequestCount);
      yield buffer.data;
    }
    yield Buffer.from(this.sqlText, 'ucs2');
  }
  toString(indent = '') {
    return indent + ('SQL Batch - ' + this.sqlText);
  }
}
var _default = exports.default = SqlBatchPayload;
module.exports = SqlBatchPayload;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfd3JpdGFibGVUcmFja2luZ0J1ZmZlciIsIl9pbnRlcm9wUmVxdWlyZURlZmF1bHQiLCJyZXF1aXJlIiwiX2FsbEhlYWRlcnMiLCJvYmoiLCJfX2VzTW9kdWxlIiwiZGVmYXVsdCIsIlNxbEJhdGNoUGF5bG9hZCIsImNvbnN0cnVjdG9yIiwic3FsVGV4dCIsInR4bkRlc2NyaXB0b3IiLCJvcHRpb25zIiwiU3ltYm9sIiwiaXRlcmF0b3IiLCJ0ZHNWZXJzaW9uIiwiYnVmZmVyIiwiV3JpdGFibGVUcmFja2luZ0J1ZmZlciIsIm91dHN0YW5kaW5nUmVxdWVzdENvdW50Iiwid3JpdGVUb1RyYWNraW5nQnVmZmVyIiwiZGF0YSIsIkJ1ZmZlciIsImZyb20iLCJ0b1N0cmluZyIsImluZGVudCIsIl9kZWZhdWx0IiwiZXhwb3J0cyIsIm1vZHVsZSJdLCJzb3VyY2VzIjpbIi4uL3NyYy9zcWxiYXRjaC1wYXlsb2FkLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBXcml0YWJsZVRyYWNraW5nQnVmZmVyIGZyb20gJy4vdHJhY2tpbmctYnVmZmVyL3dyaXRhYmxlLXRyYWNraW5nLWJ1ZmZlcic7XG5pbXBvcnQgeyB3cml0ZVRvVHJhY2tpbmdCdWZmZXIgfSBmcm9tICcuL2FsbC1oZWFkZXJzJztcblxuLypcbiAgczIuMi42LjZcbiAqL1xuY2xhc3MgU3FsQmF0Y2hQYXlsb2FkIGltcGxlbWVudHMgSXRlcmFibGU8QnVmZmVyPiB7XG4gIGRlY2xhcmUgc3FsVGV4dDogc3RyaW5nO1xuICBkZWNsYXJlIHR4bkRlc2NyaXB0b3I6IEJ1ZmZlcjtcbiAgZGVjbGFyZSBvcHRpb25zOiB7IHRkc1ZlcnNpb246IHN0cmluZyB9O1xuXG4gIGNvbnN0cnVjdG9yKHNxbFRleHQ6IHN0cmluZywgdHhuRGVzY3JpcHRvcjogQnVmZmVyLCBvcHRpb25zOiB7IHRkc1ZlcnNpb246IHN0cmluZyB9KSB7XG4gICAgdGhpcy5zcWxUZXh0ID0gc3FsVGV4dDtcbiAgICB0aGlzLnR4bkRlc2NyaXB0b3IgPSB0eG5EZXNjcmlwdG9yO1xuICAgIHRoaXMub3B0aW9ucyA9IG9wdGlvbnM7XG4gIH1cblxuICAqW1N5bWJvbC5pdGVyYXRvcl0oKSB7XG4gICAgaWYgKHRoaXMub3B0aW9ucy50ZHNWZXJzaW9uID49ICc3XzInKSB7XG4gICAgICBjb25zdCBidWZmZXIgPSBuZXcgV3JpdGFibGVUcmFja2luZ0J1ZmZlcigxOCwgJ3VjczInKTtcbiAgICAgIGNvbnN0IG91dHN0YW5kaW5nUmVxdWVzdENvdW50ID0gMTtcblxuICAgICAgd3JpdGVUb1RyYWNraW5nQnVmZmVyKGJ1ZmZlciwgdGhpcy50eG5EZXNjcmlwdG9yLCBvdXRzdGFuZGluZ1JlcXVlc3RDb3VudCk7XG5cbiAgICAgIHlpZWxkIGJ1ZmZlci5kYXRhO1xuICAgIH1cblxuICAgIHlpZWxkIEJ1ZmZlci5mcm9tKHRoaXMuc3FsVGV4dCwgJ3VjczInKTtcbiAgfVxuXG4gIHRvU3RyaW5nKGluZGVudCA9ICcnKSB7XG4gICAgcmV0dXJuIGluZGVudCArICgnU1FMIEJhdGNoIC0gJyArIHRoaXMuc3FsVGV4dCk7XG4gIH1cbn1cblxuZXhwb3J0IGRlZmF1bHQgU3FsQmF0Y2hQYXlsb2FkO1xubW9kdWxlLmV4cG9ydHMgPSBTcWxCYXRjaFBheWxvYWQ7XG4iXSwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLElBQUFBLHVCQUFBLEdBQUFDLHNCQUFBLENBQUFDLE9BQUE7QUFDQSxJQUFBQyxXQUFBLEdBQUFELE9BQUE7QUFBc0QsU0FBQUQsdUJBQUFHLEdBQUEsV0FBQUEsR0FBQSxJQUFBQSxHQUFBLENBQUFDLFVBQUEsR0FBQUQsR0FBQSxLQUFBRSxPQUFBLEVBQUFGLEdBQUE7QUFFdEQ7QUFDQTtBQUNBO0FBQ0EsTUFBTUcsZUFBZSxDQUE2QjtFQUtoREMsV0FBV0EsQ0FBQ0MsT0FBZSxFQUFFQyxhQUFxQixFQUFFQyxPQUErQixFQUFFO0lBQ25GLElBQUksQ0FBQ0YsT0FBTyxHQUFHQSxPQUFPO0lBQ3RCLElBQUksQ0FBQ0MsYUFBYSxHQUFHQSxhQUFhO0lBQ2xDLElBQUksQ0FBQ0MsT0FBTyxHQUFHQSxPQUFPO0VBQ3hCO0VBRUEsRUFBRUMsTUFBTSxDQUFDQyxRQUFRLElBQUk7SUFDbkIsSUFBSSxJQUFJLENBQUNGLE9BQU8sQ0FBQ0csVUFBVSxJQUFJLEtBQUssRUFBRTtNQUNwQyxNQUFNQyxNQUFNLEdBQUcsSUFBSUMsK0JBQXNCLENBQUMsRUFBRSxFQUFFLE1BQU0sQ0FBQztNQUNyRCxNQUFNQyx1QkFBdUIsR0FBRyxDQUFDO01BRWpDLElBQUFDLGlDQUFxQixFQUFDSCxNQUFNLEVBQUUsSUFBSSxDQUFDTCxhQUFhLEVBQUVPLHVCQUF1QixDQUFDO01BRTFFLE1BQU1GLE1BQU0sQ0FBQ0ksSUFBSTtJQUNuQjtJQUVBLE1BQU1DLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLElBQUksQ0FBQ1osT0FBTyxFQUFFLE1BQU0sQ0FBQztFQUN6QztFQUVBYSxRQUFRQSxDQUFDQyxNQUFNLEdBQUcsRUFBRSxFQUFFO0lBQ3BCLE9BQU9BLE1BQU0sSUFBSSxjQUFjLEdBQUcsSUFBSSxDQUFDZCxPQUFPLENBQUM7RUFDakQ7QUFDRjtBQUFDLElBQUFlLFFBQUEsR0FBQUMsT0FBQSxDQUFBbkIsT0FBQSxHQUVjQyxlQUFlO0FBQzlCbUIsTUFBTSxDQUFDRCxPQUFPLEdBQUdsQixlQUFlIn0=