{"version": 3, "file": "tedious.js", "names": ["_bulkLoad", "_interopRequireDefault", "require", "_connection", "_request", "_library", "_errors", "_dataType", "_transaction", "_tdsVersions", "obj", "__esModule", "default", "library", "exports", "name", "connect", "config", "connectListener", "connection", "Connection"], "sources": ["../src/tedious.ts"], "sourcesContent": ["import BulkLoad from './bulk-load';\nimport Connection, { type ConnectionAuthentication, type ConnectionConfiguration, type ConnectionOptions } from './connection';\nimport Request from './request';\nimport { name } from './library';\n\nimport { ConnectionError, RequestError } from './errors';\n\nimport { TYPES } from './data-type';\nimport { ISOLATION_LEVEL } from './transaction';\nimport { versions as TDS_VERSION } from './tds-versions';\n\nconst library = { name: name };\n\nexport function connect(config: ConnectionConfiguration, connectListener?: (err?: Error) => void) {\n  const connection = new Connection(config);\n  connection.connect(connectListener);\n  return connection;\n}\n\nexport {\n  BulkLoad,\n  Connection,\n  Request,\n  library,\n  ConnectionError,\n  RequestError,\n  TYPES,\n  ISOLATION_LEVEL,\n  TDS_VERSION\n};\n\nexport type {\n  ConnectionAuthentication,\n  ConnectionConfiguration,\n  ConnectionOptions\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,QAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAEA,IAAAI,OAAA,GAAAJ,OAAA;AAEA,IAAAK,SAAA,GAAAL,OAAA;AACA,IAAAM,YAAA,GAAAN,OAAA;AACA,IAAAO,YAAA,GAAAP,OAAA;AAAyD,SAAAD,uBAAAS,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEzD,MAAMG,OAAO,GAAAC,OAAA,CAAAD,OAAA,GAAG;EAAEE,IAAI,EAAEA;AAAK,CAAC;AAEvB,SAASC,OAAOA,CAACC,MAA+B,EAAEC,eAAuC,EAAE;EAChG,MAAMC,UAAU,GAAG,IAAIC,mBAAU,CAACH,MAAM,CAAC;EACzCE,UAAU,CAACH,OAAO,CAACE,eAAe,CAAC;EACnC,OAAOC,UAAU;AACnB"}