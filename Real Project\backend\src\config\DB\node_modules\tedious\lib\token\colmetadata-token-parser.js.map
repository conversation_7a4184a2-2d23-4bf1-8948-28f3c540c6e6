{"version": 3, "file": "colmetadata-token-parser.js", "names": ["_metadataParser", "require", "_token", "_helpers", "readTableName", "buf", "offset", "metadata", "options", "type", "hasTableName", "Result", "undefined", "tdsVersion", "readUsVarChar", "numberOfTableNameParts", "value", "readUInt8", "tableName", "i", "tableNamePart", "push", "readColumnName", "index", "colName", "readBVarChar", "columnNameReplacer", "camelCaseColumns", "replace", "s", "toLowerCase", "readColumn", "readMetadata", "userType", "flags", "collation", "precision", "scale", "udtInfo", "dataLength", "schema", "colMetadataParser", "parser", "columnCount", "readUInt16LE", "buffer", "position", "err", "NotEnoughDataError", "waitForChunk", "columns", "column", "ColMetadataToken", "_default", "exports", "default", "module"], "sources": ["../../src/token/colmetadata-token-parser.ts"], "sourcesContent": ["import { readMetadata, type Metadata } from '../metadata-parser';\n\nimport Parser, { type ParserOptions } from './stream-parser';\nimport { ColMetadataToken } from './token';\nimport { NotEnoughDataError, Result, readBVarChar, readUInt16LE, readUInt8, readUsVarChar } from './helpers';\n\nexport interface ColumnMetadata extends Metadata {\n  /**\n   * The column's name。\n   */\n  colName: string;\n\n  tableName?: string | string[] | undefined;\n}\n\nfunction readTableName(buf: Buffer, offset: number, metadata: Metadata, options: ParserOptions): Result<string | string[] | undefined> {\n  if (!metadata.type.hasTableName) {\n    return new Result(undefined, offset);\n  }\n\n  if (options.tdsVersion < '7_2') {\n    return readUsVarChar(buf, offset);\n  }\n\n  let numberOfTableNameParts;\n  ({ offset, value: numberOfTableNameParts } = readUInt8(buf, offset));\n\n  const tableName: string[] = [];\n  for (let i = 0; i < numberOfTableNameParts; i++) {\n    let tableNamePart;\n    ({ offset, value: tableNamePart } = readUsVarChar(buf, offset));\n\n    tableName.push(tableNamePart);\n  }\n\n  return new Result(tableName, offset);\n}\n\nfunction readColumnName(buf: Buffer, offset: number, index: number, metadata: Metadata, options: ParserOptions): Result<string> {\n  let colName;\n  ({ offset, value: colName } = readBVarChar(buf, offset));\n\n  if (options.columnNameReplacer) {\n    return new Result(options.columnNameReplacer(colName, index, metadata), offset);\n  } else if (options.camelCaseColumns) {\n    return new Result(colName.replace(/^[A-Z]/, function(s) {\n      return s.toLowerCase();\n    }), offset);\n  } else {\n    return new Result(colName, offset);\n  }\n}\n\nfunction readColumn(buf: Buffer, offset: number, options: ParserOptions, index: number) {\n  let metadata;\n  ({ offset, value: metadata } = readMetadata(buf, offset, options));\n\n  let tableName;\n  ({ offset, value: tableName } = readTableName(buf, offset, metadata, options));\n\n  let colName;\n  ({ offset, value: colName } = readColumnName(buf, offset, index, metadata, options));\n\n  return new Result({\n    userType: metadata.userType,\n    flags: metadata.flags,\n    type: metadata.type,\n    collation: metadata.collation,\n    precision: metadata.precision,\n    scale: metadata.scale,\n    udtInfo: metadata.udtInfo,\n    dataLength: metadata.dataLength,\n    schema: metadata.schema,\n    colName: colName,\n    tableName: tableName\n  }, offset);\n}\n\nasync function colMetadataParser(parser: Parser): Promise<ColMetadataToken> {\n  let columnCount;\n\n  while (true) {\n    let offset;\n\n    try {\n      ({ offset, value: columnCount } = readUInt16LE(parser.buffer, parser.position));\n    } catch (err) {\n      if (err instanceof NotEnoughDataError) {\n        await parser.waitForChunk();\n        continue;\n      }\n\n      throw err;\n    }\n\n    parser.position = offset;\n    break;\n  }\n\n  const columns: ColumnMetadata[] = [];\n  for (let i = 0; i < columnCount; i++) {\n    while (true) {\n      let column: ColumnMetadata;\n      let offset;\n\n      try {\n        ({ offset, value: column } = readColumn(parser.buffer, parser.position, parser.options, i));\n      } catch (err: any) {\n        if (err instanceof NotEnoughDataError) {\n          await parser.waitForChunk();\n          continue;\n        }\n\n        throw err;\n      }\n\n      parser.position = offset;\n      columns.push(column);\n\n      break;\n    }\n  }\n\n  return new ColMetadataToken(columns);\n}\n\nexport default colMetadataParser;\nmodule.exports = colMetadataParser;\n"], "mappings": ";;;;;;AAAA,IAAAA,eAAA,GAAAC,OAAA;AAGA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAWA,SAASG,aAAaA,CAACC,GAAW,EAAEC,MAAc,EAAEC,QAAkB,EAAEC,OAAsB,EAAyC;EACrI,IAAI,CAACD,QAAQ,CAACE,IAAI,CAACC,YAAY,EAAE;IAC/B,OAAO,IAAIC,eAAM,CAACC,SAAS,EAAEN,MAAM,CAAC;EACtC;EAEA,IAAIE,OAAO,CAACK,UAAU,GAAG,KAAK,EAAE;IAC9B,OAAO,IAAAC,sBAAa,EAACT,GAAG,EAAEC,MAAM,CAAC;EACnC;EAEA,IAAIS,sBAAsB;EAC1B,CAAC;IAAET,MAAM;IAAEU,KAAK,EAAED;EAAuB,CAAC,GAAG,IAAAE,kBAAS,EAACZ,GAAG,EAAEC,MAAM,CAAC;EAEnE,MAAMY,SAAmB,GAAG,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,sBAAsB,EAAEI,CAAC,EAAE,EAAE;IAC/C,IAAIC,aAAa;IACjB,CAAC;MAAEd,MAAM;MAAEU,KAAK,EAAEI;IAAc,CAAC,GAAG,IAAAN,sBAAa,EAACT,GAAG,EAAEC,MAAM,CAAC;IAE9DY,SAAS,CAACG,IAAI,CAACD,aAAa,CAAC;EAC/B;EAEA,OAAO,IAAIT,eAAM,CAACO,SAAS,EAAEZ,MAAM,CAAC;AACtC;AAEA,SAASgB,cAAcA,CAACjB,GAAW,EAAEC,MAAc,EAAEiB,KAAa,EAAEhB,QAAkB,EAAEC,OAAsB,EAAkB;EAC9H,IAAIgB,OAAO;EACX,CAAC;IAAElB,MAAM;IAAEU,KAAK,EAAEQ;EAAQ,CAAC,GAAG,IAAAC,qBAAY,EAACpB,GAAG,EAAEC,MAAM,CAAC;EAEvD,IAAIE,OAAO,CAACkB,kBAAkB,EAAE;IAC9B,OAAO,IAAIf,eAAM,CAACH,OAAO,CAACkB,kBAAkB,CAACF,OAAO,EAAED,KAAK,EAAEhB,QAAQ,CAAC,EAAED,MAAM,CAAC;EACjF,CAAC,MAAM,IAAIE,OAAO,CAACmB,gBAAgB,EAAE;IACnC,OAAO,IAAIhB,eAAM,CAACa,OAAO,CAACI,OAAO,CAAC,QAAQ,EAAE,UAASC,CAAC,EAAE;MACtD,OAAOA,CAAC,CAACC,WAAW,CAAC,CAAC;IACxB,CAAC,CAAC,EAAExB,MAAM,CAAC;EACb,CAAC,MAAM;IACL,OAAO,IAAIK,eAAM,CAACa,OAAO,EAAElB,MAAM,CAAC;EACpC;AACF;AAEA,SAASyB,UAAUA,CAAC1B,GAAW,EAAEC,MAAc,EAAEE,OAAsB,EAAEe,KAAa,EAAE;EACtF,IAAIhB,QAAQ;EACZ,CAAC;IAAED,MAAM;IAAEU,KAAK,EAAET;EAAS,CAAC,GAAG,IAAAyB,4BAAY,EAAC3B,GAAG,EAAEC,MAAM,EAAEE,OAAO,CAAC;EAEjE,IAAIU,SAAS;EACb,CAAC;IAAEZ,MAAM;IAAEU,KAAK,EAAEE;EAAU,CAAC,GAAGd,aAAa,CAACC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EAE7E,IAAIgB,OAAO;EACX,CAAC;IAAElB,MAAM;IAAEU,KAAK,EAAEQ;EAAQ,CAAC,GAAGF,cAAc,CAACjB,GAAG,EAAEC,MAAM,EAAEiB,KAAK,EAAEhB,QAAQ,EAAEC,OAAO,CAAC;EAEnF,OAAO,IAAIG,eAAM,CAAC;IAChBsB,QAAQ,EAAE1B,QAAQ,CAAC0B,QAAQ;IAC3BC,KAAK,EAAE3B,QAAQ,CAAC2B,KAAK;IACrBzB,IAAI,EAAEF,QAAQ,CAACE,IAAI;IACnB0B,SAAS,EAAE5B,QAAQ,CAAC4B,SAAS;IAC7BC,SAAS,EAAE7B,QAAQ,CAAC6B,SAAS;IAC7BC,KAAK,EAAE9B,QAAQ,CAAC8B,KAAK;IACrBC,OAAO,EAAE/B,QAAQ,CAAC+B,OAAO;IACzBC,UAAU,EAAEhC,QAAQ,CAACgC,UAAU;IAC/BC,MAAM,EAAEjC,QAAQ,CAACiC,MAAM;IACvBhB,OAAO,EAAEA,OAAO;IAChBN,SAAS,EAAEA;EACb,CAAC,EAAEZ,MAAM,CAAC;AACZ;AAEA,eAAemC,iBAAiBA,CAACC,MAAc,EAA6B;EAC1E,IAAIC,WAAW;EAEf,OAAO,IAAI,EAAE;IACX,IAAIrC,MAAM;IAEV,IAAI;MACF,CAAC;QAAEA,MAAM;QAAEU,KAAK,EAAE2B;MAAY,CAAC,GAAG,IAAAC,qBAAY,EAACF,MAAM,CAACG,MAAM,EAAEH,MAAM,CAACI,QAAQ,CAAC;IAChF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ,IAAIA,GAAG,YAAYC,2BAAkB,EAAE;QACrC,MAAMN,MAAM,CAACO,YAAY,CAAC,CAAC;QAC3B;MACF;MAEA,MAAMF,GAAG;IACX;IAEAL,MAAM,CAACI,QAAQ,GAAGxC,MAAM;IACxB;EACF;EAEA,MAAM4C,OAAyB,GAAG,EAAE;EACpC,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,WAAW,EAAExB,CAAC,EAAE,EAAE;IACpC,OAAO,IAAI,EAAE;MACX,IAAIgC,MAAsB;MAC1B,IAAI7C,MAAM;MAEV,IAAI;QACF,CAAC;UAAEA,MAAM;UAAEU,KAAK,EAAEmC;QAAO,CAAC,GAAGpB,UAAU,CAACW,MAAM,CAACG,MAAM,EAAEH,MAAM,CAACI,QAAQ,EAAEJ,MAAM,CAAClC,OAAO,EAAEW,CAAC,CAAC;MAC5F,CAAC,CAAC,OAAO4B,GAAQ,EAAE;QACjB,IAAIA,GAAG,YAAYC,2BAAkB,EAAE;UACrC,MAAMN,MAAM,CAACO,YAAY,CAAC,CAAC;UAC3B;QACF;QAEA,MAAMF,GAAG;MACX;MAEAL,MAAM,CAACI,QAAQ,GAAGxC,MAAM;MACxB4C,OAAO,CAAC7B,IAAI,CAAC8B,MAAM,CAAC;MAEpB;IACF;EACF;EAEA,OAAO,IAAIC,uBAAgB,CAACF,OAAO,CAAC;AACtC;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcd,iBAAiB;AAChCe,MAAM,CAACF,OAAO,GAAGb,iBAAiB"}