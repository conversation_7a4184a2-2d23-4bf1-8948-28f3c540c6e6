import { type ParserOptions } from './stream-parser';
import { DoneToken, DoneInProcToken, DoneProcToken } from './token';
import { Result } from './helpers';
export declare function doneParser(buf: Buffer, offset: number, options: ParserOptions): Result<DoneToken>;
export declare function doneInProcParser(buf: Buffer, offset: number, options: ParserOptions): Result<DoneInProcToken>;
export declare function doneProcParser(buf: Buffer, offset: number, options: ParserOptions): Result<DoneProcToken>;
