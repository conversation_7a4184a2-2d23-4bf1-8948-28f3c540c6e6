"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.doneInProcParser = doneInProcParser;
exports.doneParser = doneParser;
exports.doneProcParser = doneProcParser;
var _token = require("./token");
var _helpers = require("./helpers");
// s2.2.7.5/6/7

const STATUS = {
  MORE: 0x0001,
  ERROR: 0x0002,
  // This bit is not yet in use by SQL Server, so is not exposed in the returned token
  INXACT: 0x0004,
  COUNT: 0x0010,
  ATTN: 0x0020,
  SRVERROR: 0x0100
};
function readToken(buf, offset, options) {
  let status;
  ({
    offset,
    value: status
  } = (0, _helpers.readUInt16LE)(buf, offset));
  const more = !!(status & STATUS.MORE);
  const sqlError = !!(status & STATUS.ERROR);
  const rowCountValid = !!(status & STATUS.COUNT);
  const attention = !!(status & STATUS.ATTN);
  const serverError = !!(status & STATUS.SRVERROR);
  let curCmd;
  ({
    offset,
    value: curCmd
  } = (0, _helpers.readUInt16LE)(buf, offset));
  let rowCount;
  ({
    offset,
    value: rowCount
  } = (options.tdsVersion < '7_2' ? _helpers.readUInt32LE : _helpers.readBigUInt64LE)(buf, offset));
  return new _helpers.Result({
    more: more,
    sqlError: sqlError,
    attention: attention,
    serverError: serverError,
    rowCount: rowCountValid ? Number(rowCount) : undefined,
    curCmd: curCmd
  }, offset);
}
function doneParser(buf, offset, options) {
  let value;
  ({
    offset,
    value
  } = readToken(buf, offset, options));
  return new _helpers.Result(new _token.DoneToken(value), offset);
}
function doneInProcParser(buf, offset, options) {
  let value;
  ({
    offset,
    value
  } = readToken(buf, offset, options));
  return new _helpers.Result(new _token.DoneInProcToken(value), offset);
}
function doneProcParser(buf, offset, options) {
  let value;
  ({
    offset,
    value
  } = readToken(buf, offset, options));
  return new _helpers.Result(new _token.DoneProcToken(value), offset);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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