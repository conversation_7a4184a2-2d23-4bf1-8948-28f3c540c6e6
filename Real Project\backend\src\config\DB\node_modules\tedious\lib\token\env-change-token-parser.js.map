{"version": 3, "file": "env-change-token-parser.js", "names": ["_collation", "require", "_token", "_helpers", "types", "name", "event", "_readNewAndOldValue", "buf", "offset", "length", "type", "newValue", "value", "readBVarChar", "oldValue", "Result", "PacketSizeEnvChangeToken", "parseInt", "DatabaseEnvChangeToken", "LanguageEnvChangeToken", "CharsetEnvChangeToken", "DatabaseMirroringPartnerEnvChangeToken", "Error", "readBVarByte", "newCollation", "Collation", "fromBuffer", "undefined", "oldCollation", "CollationChangeToken", "BeginTransactionEnvChangeToken", "CommitTransactionEnvChangeToken", "RollbackTransactionEnvChangeToken", "ResetConnectionEnvChangeToken", "routePacket", "readUsVarByte", "protocol", "readUInt8", "port", "readUInt16LE", "serverLen", "server", "toString", "RoutingEnvChangeToken", "console", "error", "env<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_options", "token<PERSON><PERSON>th", "NotEnoughDataError", "typeNumber", "_default", "exports", "default", "module"], "sources": ["../../src/token/env-change-token-parser.ts"], "sourcesContent": ["import { type ParserOptions } from './stream-parser';\nimport { Collation } from '../collation';\n\nimport {\n  DatabaseEnvChangeToken,\n  LanguageEnvChangeToken,\n  CharsetEnvChangeToken,\n  PacketSizeEnvChangeToken,\n  BeginTransactionEnvChangeToken,\n  CommitTransactionEnvChangeToken,\n  RollbackTransactionEnvChangeToken,\n  DatabaseMirroringPartnerEnvChangeToken,\n  ResetConnectionEnvChangeToken,\n  RoutingEnvChangeToken,\n  CollationChangeToken,\n  type EnvChangeToken\n} from './token';\n\nimport { NotEnoughDataError, readBVarByte, readBVarChar, readUInt16LE, readUInt8, readUsVarByte, Result } from './helpers';\n\nconst types: { [key: number]: { name: string, event?: string }} = {\n  1: {\n    name: 'DATABASE',\n    event: 'databaseChange'\n  },\n  2: {\n    name: 'LANGUAGE',\n    event: 'languageChange'\n  },\n  3: {\n    name: 'CHARSET',\n    event: 'charsetChange'\n  },\n  4: {\n    name: 'PACKET_SIZE',\n    event: 'packetSizeChange'\n  },\n  7: {\n    name: 'SQL_COLLATION',\n    event: 'sqlCollationChange'\n  },\n  8: {\n    name: 'BEGIN_TXN',\n    event: 'beginTransaction'\n  },\n  9: {\n    name: 'COMMIT_TXN',\n    event: 'commitTransaction'\n  },\n  10: {\n    name: 'ROLLBACK_TXN',\n    event: 'rollbackTransaction'\n  },\n  13: {\n    name: 'DATABASE_MIRRORING_PARTNER',\n    event: 'partnerNode'\n  },\n  17: {\n    name: 'TXN_ENDED'\n  },\n  18: {\n    name: 'RESET_CONNECTION',\n    event: 'resetConnection'\n  },\n  20: {\n    name: 'ROUTING_CHANGE',\n    event: 'routingChange'\n  }\n};\n\nfunction _readNewAndOldValue(buf: Buffer, offset: number, length: number, type: { name: string, event?: string }): Result<EnvChangeToken | undefined> {\n  switch (type.name) {\n    case 'DATABASE':\n    case 'LANGUAGE':\n    case 'CHARSET':\n    case 'PACKET_SIZE':\n    case 'DATABASE_MIRRORING_PARTNER': {\n      let newValue;\n      ({ offset, value: newValue } = readBVarChar(buf, offset));\n\n      let oldValue;\n      ({ offset, value: oldValue } = readBVarChar(buf, offset));\n\n      switch (type.name) {\n        case 'PACKET_SIZE':\n          return new Result(new PacketSizeEnvChangeToken(parseInt(newValue), parseInt(oldValue)), offset);\n\n        case 'DATABASE':\n          return new Result(new DatabaseEnvChangeToken(newValue, oldValue), offset);\n\n        case 'LANGUAGE':\n          return new Result(new LanguageEnvChangeToken(newValue, oldValue), offset);\n\n        case 'CHARSET':\n          return new Result(new CharsetEnvChangeToken(newValue, oldValue), offset);\n\n        case 'DATABASE_MIRRORING_PARTNER':\n          return new Result(new DatabaseMirroringPartnerEnvChangeToken(newValue, oldValue), offset);\n      }\n\n      throw new Error('unreachable');\n    }\n\n    case 'SQL_COLLATION':\n    case 'BEGIN_TXN':\n    case 'COMMIT_TXN':\n    case 'ROLLBACK_TXN':\n    case 'RESET_CONNECTION': {\n      let newValue;\n      ({ offset, value: newValue } = readBVarByte(buf, offset));\n\n      let oldValue;\n      ({ offset, value: oldValue } = readBVarByte(buf, offset));\n\n      switch (type.name) {\n        case 'SQL_COLLATION': {\n          const newCollation = newValue.length ? Collation.fromBuffer(newValue) : undefined;\n          const oldCollation = oldValue.length ? Collation.fromBuffer(oldValue) : undefined;\n\n          return new Result(new CollationChangeToken(newCollation, oldCollation), offset);\n        }\n\n        case 'BEGIN_TXN':\n          return new Result(new BeginTransactionEnvChangeToken(newValue, oldValue), offset);\n\n        case 'COMMIT_TXN':\n          return new Result(new CommitTransactionEnvChangeToken(newValue, oldValue), offset);\n\n        case 'ROLLBACK_TXN':\n          return new Result(new RollbackTransactionEnvChangeToken(newValue, oldValue), offset);\n\n        case 'RESET_CONNECTION':\n          return new Result(new ResetConnectionEnvChangeToken(newValue, oldValue), offset);\n      }\n\n      throw new Error('unreachable');\n    }\n\n    case 'ROUTING_CHANGE': {\n      let routePacket;\n      ({ offset, value: routePacket } = readUsVarByte(buf, offset));\n\n      let oldValue;\n      ({ offset, value: oldValue } = readUsVarByte(buf, offset));\n\n      // Routing Change:\n      // Byte 1: Protocol (must be 0)\n      // Bytes 2-3 (USHORT): Port number\n      // Bytes 4-5 (USHORT): Length of server data in unicode (2byte chars)\n      // Bytes 6-*: Server name in unicode characters\n      const protocol = routePacket.readUInt8(0);\n      if (protocol !== 0) {\n        throw new Error('Unknown protocol byte in routing change event');\n      }\n\n      const port = routePacket.readUInt16LE(1);\n      const serverLen = routePacket.readUInt16LE(3);\n      // 2 bytes per char, starting at offset 5\n      const server = routePacket.toString('ucs2', 5, 5 + (serverLen * 2));\n\n      const newValue = {\n        protocol: protocol,\n        port: port,\n        server: server\n      };\n\n      return new Result(new RoutingEnvChangeToken(newValue, oldValue), offset);\n    }\n\n    default: {\n      console.error('Tedious > Unsupported ENVCHANGE type ' + type.name);\n\n      // skip unknown bytes\n      return new Result(undefined, offset + length - 1);\n    }\n  }\n}\n\nfunction envChangeParser(buf: Buffer, offset: number, _options: ParserOptions): Result<EnvChangeToken | undefined> {\n  let tokenLength;\n  ({ offset, value: tokenLength } = readUInt16LE(buf, offset));\n\n  if (buf.length < offset + tokenLength) {\n    throw new NotEnoughDataError(offset + tokenLength);\n  }\n\n  let typeNumber;\n  ({ offset, value: typeNumber } = readUInt8(buf, offset));\n\n  const type = types[typeNumber];\n\n  if (!type) {\n    console.error('Tedious > Unsupported ENVCHANGE type ' + typeNumber);\n    return new Result(undefined, offset + tokenLength - 1);\n  }\n\n  return _readNewAndOldValue(buf, offset, tokenLength, type);\n}\n\nexport default envChangeParser;\nmodule.exports = envChangeParser;\n"], "mappings": ";;;;;;AACA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAeA,IAAAE,QAAA,GAAAF,OAAA;AAEA,MAAMG,KAAyD,GAAG;EAChE,CAAC,EAAE;IACDC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE;EACT,CAAC;EACD,CAAC,EAAE;IACDD,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE;EACT,CAAC;EACD,CAAC,EAAE;IACDD,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE;EACT,CAAC;EACD,CAAC,EAAE;IACDD,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;EACT,CAAC;EACD,CAAC,EAAE;IACDD,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;EACT,CAAC;EACD,CAAC,EAAE;IACDD,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;EACT,CAAC;EACD,CAAC,EAAE;IACDD,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;EACT,CAAC;EACD,EAAE,EAAE;IACFD,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;EACT,CAAC;EACD,EAAE,EAAE;IACFD,IAAI,EAAE,4BAA4B;IAClCC,KAAK,EAAE;EACT,CAAC;EACD,EAAE,EAAE;IACFD,IAAI,EAAE;EACR,CAAC;EACD,EAAE,EAAE;IACFA,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC;EACD,EAAE,EAAE;IACFD,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,SAASC,mBAAmBA,CAACC,GAAW,EAAEC,MAAc,EAAEC,MAAc,EAAEC,IAAsC,EAAsC;EACpJ,QAAQA,IAAI,CAACN,IAAI;IACf,KAAK,UAAU;IACf,KAAK,UAAU;IACf,KAAK,SAAS;IACd,KAAK,aAAa;IAClB,KAAK,4BAA4B;MAAE;QACjC,IAAIO,QAAQ;QACZ,CAAC;UAAEH,MAAM;UAAEI,KAAK,EAAED;QAAS,CAAC,GAAG,IAAAE,qBAAY,EAACN,GAAG,EAAEC,MAAM,CAAC;QAExD,IAAIM,QAAQ;QACZ,CAAC;UAAEN,MAAM;UAAEI,KAAK,EAAEE;QAAS,CAAC,GAAG,IAAAD,qBAAY,EAACN,GAAG,EAAEC,MAAM,CAAC;QAExD,QAAQE,IAAI,CAACN,IAAI;UACf,KAAK,aAAa;YAChB,OAAO,IAAIW,eAAM,CAAC,IAAIC,+BAAwB,CAACC,QAAQ,CAACN,QAAQ,CAAC,EAAEM,QAAQ,CAACH,QAAQ,CAAC,CAAC,EAAEN,MAAM,CAAC;UAEjG,KAAK,UAAU;YACb,OAAO,IAAIO,eAAM,CAAC,IAAIG,6BAAsB,CAACP,QAAQ,EAAEG,QAAQ,CAAC,EAAEN,MAAM,CAAC;UAE3E,KAAK,UAAU;YACb,OAAO,IAAIO,eAAM,CAAC,IAAII,6BAAsB,CAACR,QAAQ,EAAEG,QAAQ,CAAC,EAAEN,MAAM,CAAC;UAE3E,KAAK,SAAS;YACZ,OAAO,IAAIO,eAAM,CAAC,IAAIK,4BAAqB,CAACT,QAAQ,EAAEG,QAAQ,CAAC,EAAEN,MAAM,CAAC;UAE1E,KAAK,4BAA4B;YAC/B,OAAO,IAAIO,eAAM,CAAC,IAAIM,6CAAsC,CAACV,QAAQ,EAAEG,QAAQ,CAAC,EAAEN,MAAM,CAAC;QAC7F;QAEA,MAAM,IAAIc,KAAK,CAAC,aAAa,CAAC;MAChC;IAEA,KAAK,eAAe;IACpB,KAAK,WAAW;IAChB,KAAK,YAAY;IACjB,KAAK,cAAc;IACnB,KAAK,kBAAkB;MAAE;QACvB,IAAIX,QAAQ;QACZ,CAAC;UAAEH,MAAM;UAAEI,KAAK,EAAED;QAAS,CAAC,GAAG,IAAAY,qBAAY,EAAChB,GAAG,EAAEC,MAAM,CAAC;QAExD,IAAIM,QAAQ;QACZ,CAAC;UAAEN,MAAM;UAAEI,KAAK,EAAEE;QAAS,CAAC,GAAG,IAAAS,qBAAY,EAAChB,GAAG,EAAEC,MAAM,CAAC;QAExD,QAAQE,IAAI,CAACN,IAAI;UACf,KAAK,eAAe;YAAE;cACpB,MAAMoB,YAAY,GAAGb,QAAQ,CAACF,MAAM,GAAGgB,oBAAS,CAACC,UAAU,CAACf,QAAQ,CAAC,GAAGgB,SAAS;cACjF,MAAMC,YAAY,GAAGd,QAAQ,CAACL,MAAM,GAAGgB,oBAAS,CAACC,UAAU,CAACZ,QAAQ,CAAC,GAAGa,SAAS;cAEjF,OAAO,IAAIZ,eAAM,CAAC,IAAIc,2BAAoB,CAACL,YAAY,EAAEI,YAAY,CAAC,EAAEpB,MAAM,CAAC;YACjF;UAEA,KAAK,WAAW;YACd,OAAO,IAAIO,eAAM,CAAC,IAAIe,qCAA8B,CAACnB,QAAQ,EAAEG,QAAQ,CAAC,EAAEN,MAAM,CAAC;UAEnF,KAAK,YAAY;YACf,OAAO,IAAIO,eAAM,CAAC,IAAIgB,sCAA+B,CAACpB,QAAQ,EAAEG,QAAQ,CAAC,EAAEN,MAAM,CAAC;UAEpF,KAAK,cAAc;YACjB,OAAO,IAAIO,eAAM,CAAC,IAAIiB,wCAAiC,CAACrB,QAAQ,EAAEG,QAAQ,CAAC,EAAEN,MAAM,CAAC;UAEtF,KAAK,kBAAkB;YACrB,OAAO,IAAIO,eAAM,CAAC,IAAIkB,oCAA6B,CAACtB,QAAQ,EAAEG,QAAQ,CAAC,EAAEN,MAAM,CAAC;QACpF;QAEA,MAAM,IAAIc,KAAK,CAAC,aAAa,CAAC;MAChC;IAEA,KAAK,gBAAgB;MAAE;QACrB,IAAIY,WAAW;QACf,CAAC;UAAE1B,MAAM;UAAEI,KAAK,EAAEsB;QAAY,CAAC,GAAG,IAAAC,sBAAa,EAAC5B,GAAG,EAAEC,MAAM,CAAC;QAE5D,IAAIM,QAAQ;QACZ,CAAC;UAAEN,MAAM;UAAEI,KAAK,EAAEE;QAAS,CAAC,GAAG,IAAAqB,sBAAa,EAAC5B,GAAG,EAAEC,MAAM,CAAC;;QAEzD;QACA;QACA;QACA;QACA;QACA,MAAM4B,QAAQ,GAAGF,WAAW,CAACG,SAAS,CAAC,CAAC,CAAC;QACzC,IAAID,QAAQ,KAAK,CAAC,EAAE;UAClB,MAAM,IAAId,KAAK,CAAC,+CAA+C,CAAC;QAClE;QAEA,MAAMgB,IAAI,GAAGJ,WAAW,CAACK,YAAY,CAAC,CAAC,CAAC;QACxC,MAAMC,SAAS,GAAGN,WAAW,CAACK,YAAY,CAAC,CAAC,CAAC;QAC7C;QACA,MAAME,MAAM,GAAGP,WAAW,CAACQ,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,GAAIF,SAAS,GAAG,CAAE,CAAC;QAEnE,MAAM7B,QAAQ,GAAG;UACfyB,QAAQ,EAAEA,QAAQ;UAClBE,IAAI,EAAEA,IAAI;UACVG,MAAM,EAAEA;QACV,CAAC;QAED,OAAO,IAAI1B,eAAM,CAAC,IAAI4B,4BAAqB,CAAChC,QAAQ,EAAEG,QAAQ,CAAC,EAAEN,MAAM,CAAC;MAC1E;IAEA;MAAS;QACPoC,OAAO,CAACC,KAAK,CAAC,uCAAuC,GAAGnC,IAAI,CAACN,IAAI,CAAC;;QAElE;QACA,OAAO,IAAIW,eAAM,CAACY,SAAS,EAAEnB,MAAM,GAAGC,MAAM,GAAG,CAAC,CAAC;MACnD;EACF;AACF;AAEA,SAASqC,eAAeA,CAACvC,GAAW,EAAEC,MAAc,EAAEuC,QAAuB,EAAsC;EACjH,IAAIC,WAAW;EACf,CAAC;IAAExC,MAAM;IAAEI,KAAK,EAAEoC;EAAY,CAAC,GAAG,IAAAT,qBAAY,EAAChC,GAAG,EAAEC,MAAM,CAAC;EAE3D,IAAID,GAAG,CAACE,MAAM,GAAGD,MAAM,GAAGwC,WAAW,EAAE;IACrC,MAAM,IAAIC,2BAAkB,CAACzC,MAAM,GAAGwC,WAAW,CAAC;EACpD;EAEA,IAAIE,UAAU;EACd,CAAC;IAAE1C,MAAM;IAAEI,KAAK,EAAEsC;EAAW,CAAC,GAAG,IAAAb,kBAAS,EAAC9B,GAAG,EAAEC,MAAM,CAAC;EAEvD,MAAME,IAAI,GAAGP,KAAK,CAAC+C,UAAU,CAAC;EAE9B,IAAI,CAACxC,IAAI,EAAE;IACTkC,OAAO,CAACC,KAAK,CAAC,uCAAuC,GAAGK,UAAU,CAAC;IACnE,OAAO,IAAInC,eAAM,CAACY,SAAS,EAAEnB,MAAM,GAAGwC,WAAW,GAAG,CAAC,CAAC;EACxD;EAEA,OAAO1C,mBAAmB,CAACC,GAAG,EAAEC,MAAM,EAAEwC,WAAW,EAAEtC,IAAI,CAAC;AAC5D;AAAC,IAAAyC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcP,eAAe;AAC9BQ,MAAM,CAACF,OAAO,GAAGN,eAAe"}