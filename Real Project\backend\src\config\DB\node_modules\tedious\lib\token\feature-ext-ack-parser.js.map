{"version": 3, "file": "feature-ext-ack-parser.js", "names": ["_helpers", "require", "_token", "FEATURE_ID", "SESSIONRECOVERY", "FEDAUTH", "COLUMNENCRYPTION", "GLOBALTRANSACTIONS", "AZURESQLSUPPORT", "UTF8_SUPPORT", "TERMINATOR", "featureExtAckParser", "buf", "offset", "_options", "fedAuth", "utf8Support", "featureId", "value", "readUInt8", "Result", "FeatureExtAckToken", "featureAckDataLen", "readUInt32LE", "length", "NotEnoughDataError", "featureData", "slice", "_default", "exports", "default", "module"], "sources": ["../../src/token/feature-ext-ack-parser.ts"], "sourcesContent": ["import { NotEnoughDataError, readUInt32LE, readUInt8, Result } from './helpers';\nimport { type ParserOptions } from './stream-parser';\n\nimport { FeatureExtAckToken } from './token';\n\nconst FEATURE_ID = {\n  SESSIONRECOVERY: 0x01,\n  FEDAUTH: 0x02,\n  COLUMNENCRYPTION: 0x04,\n  GLOBALTRANSACTIONS: 0x05,\n  AZURESQLSUPPORT: 0x08,\n  UTF8_SUPPORT: 0x0A,\n  TERMINATOR: 0xFF\n};\n\nfunction featureExtAckParser(buf: Buffer, offset: number, _options: ParserOptions): Result<FeatureExtAckToken> {\n  let fedAuth: Buffer | undefined;\n  let utf8Support: boolean | undefined;\n\n  while (true) {\n    let featureId;\n    ({ value: featureId, offset } = readUInt8(buf, offset));\n\n    if (featureId === FEATURE_ID.TERMINATOR) {\n      return new Result(new FeatureExtAckToken(fedAuth, utf8Support), offset);\n    }\n\n    let featureAckDataLen;\n    ({ value: featureAckDataLen, offset } = readUInt32LE(buf, offset));\n\n    if (buf.length < offset + featureAckDataLen) {\n      throw new NotEnoughDataError(offset + featureAckDataLen);\n    }\n\n    const featureData = buf.slice(offset, offset + featureAckDataLen);\n    offset += featureAckDataLen;\n\n    switch (featureId) {\n      case FEATURE_ID.FEDAUTH:\n        fedAuth = featureData;\n        break;\n      case FEATURE_ID.UTF8_SUPPORT:\n        utf8Support = !!featureData[0];\n        break;\n    }\n  }\n}\n\nexport default featureExtAckParser;\nmodule.exports = featureExtAckParser;\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAGA,IAAAC,MAAA,GAAAD,OAAA;AAEA,MAAME,UAAU,GAAG;EACjBC,eAAe,EAAE,IAAI;EACrBC,OAAO,EAAE,IAAI;EACbC,gBAAgB,EAAE,IAAI;EACtBC,kBAAkB,EAAE,IAAI;EACxBC,eAAe,EAAE,IAAI;EACrBC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE;AACd,CAAC;AAED,SAASC,mBAAmBA,CAACC,GAAW,EAAEC,MAAc,EAAEC,QAAuB,EAA8B;EAC7G,IAAIC,OAA2B;EAC/B,IAAIC,WAAgC;EAEpC,OAAO,IAAI,EAAE;IACX,IAAIC,SAAS;IACb,CAAC;MAAEC,KAAK,EAAED,SAAS;MAAEJ;IAAO,CAAC,GAAG,IAAAM,kBAAS,EAACP,GAAG,EAAEC,MAAM,CAAC;IAEtD,IAAII,SAAS,KAAKd,UAAU,CAACO,UAAU,EAAE;MACvC,OAAO,IAAIU,eAAM,CAAC,IAAIC,yBAAkB,CAACN,OAAO,EAAEC,WAAW,CAAC,EAAEH,MAAM,CAAC;IACzE;IAEA,IAAIS,iBAAiB;IACrB,CAAC;MAAEJ,KAAK,EAAEI,iBAAiB;MAAET;IAAO,CAAC,GAAG,IAAAU,qBAAY,EAACX,GAAG,EAAEC,MAAM,CAAC;IAEjE,IAAID,GAAG,CAACY,MAAM,GAAGX,MAAM,GAAGS,iBAAiB,EAAE;MAC3C,MAAM,IAAIG,2BAAkB,CAACZ,MAAM,GAAGS,iBAAiB,CAAC;IAC1D;IAEA,MAAMI,WAAW,GAAGd,GAAG,CAACe,KAAK,CAACd,MAAM,EAAEA,MAAM,GAAGS,iBAAiB,CAAC;IACjET,MAAM,IAAIS,iBAAiB;IAE3B,QAAQL,SAAS;MACf,KAAKd,UAAU,CAACE,OAAO;QACrBU,OAAO,GAAGW,WAAW;QACrB;MACF,KAAKvB,UAAU,CAACM,YAAY;QAC1BO,WAAW,GAAG,CAAC,CAACU,WAAW,CAAC,CAAC,CAAC;QAC9B;IACJ;EACF;AACF;AAAC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcnB,mBAAmB;AAClCoB,MAAM,CAACF,OAAO,GAAGlB,mBAAmB"}