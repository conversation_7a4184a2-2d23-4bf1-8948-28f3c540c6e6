"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _helpers = require("./helpers");
var _token = require("./token");
const FEDAUTHINFOID = {
  STSURL: 0x01,
  SPN: 0x02
};
function readFedAuthInfo(data) {
  let offset = 0;
  let spn, stsurl;
  const countOfInfoIDs = data.readUInt32LE(offset);
  offset += 4;
  for (let i = 0; i < countOfInfoIDs; i++) {
    const fedauthInfoID = data.readUInt8(offset);
    offset += 1;
    const fedAuthInfoDataLen = data.readUInt32LE(offset);
    offset += 4;
    const fedAuthInfoDataOffset = data.readUInt32LE(offset);
    offset += 4;
    switch (fedauthInfoID) {
      case FEDAUTHINFOID.SPN:
        spn = data.toString('ucs2', fedAuthInfoDataOffset, fedAuthInfoDataOffset + fedAuthInfoDataLen);
        break;
      case FEDAUTHINFOID.STSURL:
        stsurl = data.toString('ucs2', fedAuthInfoDataOffset, fedAuthInfoDataOffset + fedAuthInfoDataLen);
        break;

      // ignoring unknown fedauthinfo options
      default:
        break;
    }
  }
  return {
    spn,
    stsurl
  };
}
function fedAuthInfoParser(buf, offset, _options) {
  let tokenLength;
  ({
    offset,
    value: tokenLength
  } = (0, _helpers.readUInt32LE)(buf, offset));
  if (buf.length < offset + tokenLength) {
    throw new _helpers.NotEnoughDataError(offset + tokenLength);
  }
  const data = buf.slice(offset, offset + tokenLength);
  offset += tokenLength;
  const {
    spn,
    stsurl
  } = readFedAuthInfo(data);
  return new _helpers.Result(new _token.FedAuthInfoToken(spn, stsurl), offset);
}
var _default = exports.default = fedAuthInfoParser;
module.exports = fedAuthInfoParser;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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