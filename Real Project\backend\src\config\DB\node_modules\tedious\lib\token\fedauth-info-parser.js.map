{"version": 3, "file": "fedauth-info-parser.js", "names": ["_helpers", "require", "_token", "FEDAUTHINFOID", "STSURL", "SPN", "readFedAuthInfo", "data", "offset", "spn", "<PERSON>url", "countOfInfoIDs", "readUInt32LE", "i", "fedauthInfoID", "readUInt8", "fedAuthInfoDataLen", "fedAuthInfoDataOffset", "toString", "fedAuthInfoParser", "buf", "_options", "token<PERSON><PERSON>th", "value", "length", "NotEnoughDataError", "slice", "Result", "FedAuthInfoToken", "_default", "exports", "default", "module"], "sources": ["../../src/token/fedauth-info-parser.ts"], "sourcesContent": ["import { NotEnoughDataError, readUInt32LE, Result } from './helpers';\nimport { type ParserOptions } from './stream-parser';\nimport { FedAuthInfoToken } from './token';\n\nconst FEDAUTHINFOID = {\n  STSURL: 0x01,\n  SPN: 0x02\n};\n\nfunction readFedAuthInfo(data: Buffer): { spn: string | undefined, stsurl: string | undefined } {\n  let offset = 0;\n  let spn, stsurl;\n\n  const countOfInfoIDs = data.readUInt32LE(offset);\n  offset += 4;\n\n  for (let i = 0; i < countOfInfoIDs; i++) {\n    const fedauthInfoID = data.readUInt8(offset);\n    offset += 1;\n\n    const fedAuthInfoDataLen = data.readUInt32LE(offset);\n    offset += 4;\n\n    const fedAuthInfoDataOffset = data.readUInt32LE(offset);\n    offset += 4;\n\n    switch (fedauthInfoID) {\n      case FEDAUTHINFOID.SPN:\n        spn = data.toString('ucs2', fedAuthInfoDataOffset, fedAuthInfoDataOffset + fedAuthInfoDataLen);\n        break;\n\n      case FEDAUTHINFOID.STSURL:\n        stsurl = data.toString('ucs2', fedAuthInfoDataOffset, fedAuthInfoDataOffset + fedAuthInfoDataLen);\n        break;\n\n      // ignoring unknown fedauthinfo options\n      default:\n        break;\n    }\n  }\n\n  return { spn, stsurl };\n}\n\nfunction fedAuthInfoParser(buf: Buffer, offset: number, _options: ParserOptions): Result<FedAuthInfoToken> {\n  let tokenLength;\n  ({ offset, value: tokenLength } = readUInt32LE(buf, offset));\n\n  if (buf.length < offset + tokenLength) {\n    throw new NotEnoughDataError(offset + tokenLength);\n  }\n\n  const data = buf.slice(offset, offset + tokenLength);\n  offset += tokenLength;\n\n  const { spn, stsurl } = readFedAuthInfo(data);\n  return new Result(new FedAuthInfoToken(spn, stsurl), offset);\n}\n\nexport default fedAuthInfoParser;\nmodule.exports = fedAuthInfoParser;\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAEA,MAAME,aAAa,GAAG;EACpBC,MAAM,EAAE,IAAI;EACZC,GAAG,EAAE;AACP,CAAC;AAED,SAASC,eAAeA,CAACC,IAAY,EAA2D;EAC9F,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,GAAG,EAAEC,MAAM;EAEf,MAAMC,cAAc,GAAGJ,IAAI,CAACK,YAAY,CAACJ,MAAM,CAAC;EAChDA,MAAM,IAAI,CAAC;EAEX,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,cAAc,EAAEE,CAAC,EAAE,EAAE;IACvC,MAAMC,aAAa,GAAGP,IAAI,CAACQ,SAAS,CAACP,MAAM,CAAC;IAC5CA,MAAM,IAAI,CAAC;IAEX,MAAMQ,kBAAkB,GAAGT,IAAI,CAACK,YAAY,CAACJ,MAAM,CAAC;IACpDA,MAAM,IAAI,CAAC;IAEX,MAAMS,qBAAqB,GAAGV,IAAI,CAACK,YAAY,CAACJ,MAAM,CAAC;IACvDA,MAAM,IAAI,CAAC;IAEX,QAAQM,aAAa;MACnB,KAAKX,aAAa,CAACE,GAAG;QACpBI,GAAG,GAAGF,IAAI,CAACW,QAAQ,CAAC,MAAM,EAAED,qBAAqB,EAAEA,qBAAqB,GAAGD,kBAAkB,CAAC;QAC9F;MAEF,KAAKb,aAAa,CAACC,MAAM;QACvBM,MAAM,GAAGH,IAAI,CAACW,QAAQ,CAAC,MAAM,EAAED,qBAAqB,EAAEA,qBAAqB,GAAGD,kBAAkB,CAAC;QACjG;;MAEF;MACA;QACE;IACJ;EACF;EAEA,OAAO;IAAEP,GAAG;IAAEC;EAAO,CAAC;AACxB;AAEA,SAASS,iBAAiBA,CAACC,GAAW,EAAEZ,MAAc,EAAEa,QAAuB,EAA4B;EACzG,IAAIC,WAAW;EACf,CAAC;IAAEd,MAAM;IAAEe,KAAK,EAAED;EAAY,CAAC,GAAG,IAAAV,qBAAY,EAACQ,GAAG,EAAEZ,MAAM,CAAC;EAE3D,IAAIY,GAAG,CAACI,MAAM,GAAGhB,MAAM,GAAGc,WAAW,EAAE;IACrC,MAAM,IAAIG,2BAAkB,CAACjB,MAAM,GAAGc,WAAW,CAAC;EACpD;EAEA,MAAMf,IAAI,GAAGa,GAAG,CAACM,KAAK,CAAClB,MAAM,EAAEA,MAAM,GAAGc,WAAW,CAAC;EACpDd,MAAM,IAAIc,WAAW;EAErB,MAAM;IAAEb,GAAG;IAAEC;EAAO,CAAC,GAAGJ,eAAe,CAACC,IAAI,CAAC;EAC7C,OAAO,IAAIoB,eAAM,CAAC,IAAIC,uBAAgB,CAACnB,GAAG,EAAEC,MAAM,CAAC,EAAEF,MAAM,CAAC;AAC9D;AAAC,IAAAqB,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcZ,iBAAiB;AAChCa,MAAM,CAACF,OAAO,GAAGX,iBAAiB"}