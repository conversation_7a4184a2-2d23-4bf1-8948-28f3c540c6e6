{"version": 3, "file": "handler.js", "names": ["_request", "_interopRequireDefault", "require", "_errors", "obj", "__esModule", "default", "UnexpectedTokenError", "Error", "constructor", "handler", "token", "name", "exports", "TokenHandler", "onInfoMessage", "onErrorMessage", "onSSPI", "onDatabaseChange", "onLanguageChange", "onCharsetChange", "onSqlCollationChange", "onRoutingChange", "onPacketSizeChange", "onResetConnection", "onBeginTransaction", "onCommitTransaction", "onRollbackTransaction", "onFedAuthInfo", "onFeatureExtAck", "onLoginAck", "onColMetadata", "onOrder", "onRow", "onReturnStatus", "onReturnValue", "onDoneProc", "onDoneInProc", "onDone", "onDatabaseMirroringPartner", "InitialSqlTokenHandler", "connection", "emit", "newValue", "databaseCollation", "messageIo", "packetSize", "transactionDescriptors", "push", "inTransaction", "length", "close", "Login7TokenHandler", "loginAckReceived", "error", "ConnectionError", "message", "isLogin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transientErrorLookup", "isTransientError", "number", "curTransient<PERSON><PERSON>ry<PERSON>ount", "config", "options", "maxRetriesOnTransientErrors", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loginError", "ntlmpacket", "ntlmpacketBuffer", "fedAuthInfoToken", "authentication", "type", "fedAuth", "undefined", "utf8Support", "tdsVersion", "interface", "server", "split", "routingData", "port", "RequestTokenHandler", "request", "errors", "canceled", "RequestError", "state", "class", "serverName", "procName", "lineNumber", "Request", "AggregateError", "useColumnNames", "columns", "Object", "create", "j", "len", "col", "colName", "orderColumns", "rowCollectionOnRequestCompletion", "rows", "rowCollectionOnDone", "rst", "procReturnStatusValue", "value", "paramName", "metadata", "sqlError", "rowCount", "more", "AttentionTokenHandler", "attentionReceived", "attention"], "sources": ["../../src/token/handler.ts"], "sourcesContent": ["import Connection from '../connection';\nimport Request from '../request';\nimport { ConnectionError, RequestError } from '../errors';\nimport { type ColumnMetadata } from './colmetadata-token-parser';\nimport {\n  BeginTransactionEnvChangeToken,\n  CharsetEnvChangeToken,\n  CollationChangeToken,\n  ColMetadataToken,\n  CommitTransactionEnvChangeToken,\n  DatabaseEnvChangeToken,\n  DatabaseMirroringPartnerEnvChangeToken,\n  DoneInProcToken,\n  DoneProcToken,\n  DoneToken,\n  ErrorMessageToken,\n  FeatureExtAckToken,\n  FedAuthInfoToken,\n  InfoMessageToken,\n  LanguageEnvChangeToken,\n  LoginAckToken,\n  NBCRowToken,\n  OrderToken,\n  PacketSizeEnvChangeToken,\n  ResetConnectionEnvChangeToken,\n  ReturnStatusToken,\n  ReturnValueToken,\n  RollbackTransactionEnvChangeToken,\n  RoutingEnvChangeToken,\n  RowToken,\n  SSPIToken,\n  Token\n} from './token';\nimport BulkLoad from '../bulk-load';\n\nexport class UnexpectedTokenError extends Error {\n  constructor(handler: TokenHand<PERSON>, token: Token) {\n    super('Unexpected token `' + token.name + '` in `' + handler.constructor.name + '`');\n  }\n}\n\nexport class TokenHandler {\n  onInfoMessage(token: InfoMessageToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onErrorMessage(token: ErrorMessageToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onSSPI(token: SSPIToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onDatabaseChange(token: DatabaseEnvChangeToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onLanguageChange(token: LanguageEnvChangeToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onCharsetChange(token: CharsetEnvChangeToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onSqlCollationChange(token: CollationChangeToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onRoutingChange(token: RoutingEnvChangeToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onPacketSizeChange(token: PacketSizeEnvChangeToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onResetConnection(token: ResetConnectionEnvChangeToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onBeginTransaction(token: BeginTransactionEnvChangeToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onCommitTransaction(token: CommitTransactionEnvChangeToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onRollbackTransaction(token: RollbackTransactionEnvChangeToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onFedAuthInfo(token: FedAuthInfoToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onFeatureExtAck(token: FeatureExtAckToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onLoginAck(token: LoginAckToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onColMetadata(token: ColMetadataToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onOrder(token: OrderToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onRow(token: RowToken | NBCRowToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onReturnStatus(token: ReturnStatusToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onReturnValue(token: ReturnValueToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onDoneProc(token: DoneProcToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onDoneInProc(token: DoneInProcToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onDone(token: DoneToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n\n  onDatabaseMirroringPartner(token: DatabaseMirroringPartnerEnvChangeToken) {\n    throw new UnexpectedTokenError(this, token);\n  }\n}\n\n/**\n * A handler for tokens received in the response message to the initial SQL Batch request\n * that sets up different connection settings.\n */\nexport class InitialSqlTokenHandler extends TokenHandler {\n  declare connection: Connection;\n\n  constructor(connection: Connection) {\n    super();\n\n    this.connection = connection;\n  }\n\n  onInfoMessage(token: InfoMessageToken) {\n    this.connection.emit('infoMessage', token);\n  }\n\n  onErrorMessage(token: ErrorMessageToken) {\n    this.connection.emit('errorMessage', token);\n  }\n\n  onDatabaseChange(token: DatabaseEnvChangeToken) {\n    this.connection.emit('databaseChange', token.newValue);\n  }\n\n  onLanguageChange(token: LanguageEnvChangeToken) {\n    this.connection.emit('languageChange', token.newValue);\n  }\n\n  onCharsetChange(token: CharsetEnvChangeToken) {\n    this.connection.emit('charsetChange', token.newValue);\n  }\n\n  onSqlCollationChange(token: CollationChangeToken) {\n    this.connection.databaseCollation = token.newValue;\n  }\n\n  onPacketSizeChange(token: PacketSizeEnvChangeToken) {\n    this.connection.messageIo.packetSize(token.newValue);\n  }\n\n  onBeginTransaction(token: BeginTransactionEnvChangeToken) {\n    this.connection.transactionDescriptors.push(token.newValue);\n    this.connection.inTransaction = true;\n  }\n\n  onCommitTransaction(token: CommitTransactionEnvChangeToken) {\n    this.connection.transactionDescriptors.length = 1;\n    this.connection.inTransaction = false;\n  }\n\n  onRollbackTransaction(token: RollbackTransactionEnvChangeToken) {\n    this.connection.transactionDescriptors.length = 1;\n    // An outermost transaction was rolled back. Reset the transaction counter\n    this.connection.inTransaction = false;\n    this.connection.emit('rollbackTransaction');\n  }\n\n  onColMetadata(token: ColMetadataToken) {\n    this.connection.emit('error', new Error(\"Received 'columnMetadata' when no sqlRequest is in progress\"));\n    this.connection.close();\n  }\n\n  onOrder(token: OrderToken) {\n    this.connection.emit('error', new Error(\"Received 'order' when no sqlRequest is in progress\"));\n    this.connection.close();\n  }\n\n  onRow(token: RowToken | NBCRowToken) {\n    this.connection.emit('error', new Error(\"Received 'row' when no sqlRequest is in progress\"));\n    this.connection.close();\n  }\n\n  onReturnStatus(token: ReturnStatusToken) {\n    // Do nothing\n  }\n\n  onReturnValue(token: ReturnValueToken) {\n    // Do nothing\n  }\n\n  onDoneProc(token: DoneProcToken) {\n    // Do nothing\n  }\n\n  onDoneInProc(token: DoneInProcToken) {\n    // Do nothing\n  }\n\n  onDone(token: DoneToken) {\n    // Do nothing\n  }\n\n  onResetConnection(token: ResetConnectionEnvChangeToken) {\n    this.connection.emit('resetConnection');\n  }\n}\n\n/**\n * A handler for tokens received in the response message to a Login7 message.\n */\nexport class Login7TokenHandler extends TokenHandler {\n  declare connection: Connection;\n\n  declare fedAuthInfoToken: FedAuthInfoToken | undefined;\n  declare routingData: { server: string, port: number } | undefined;\n\n  declare loginAckReceived: boolean;\n\n  constructor(connection: Connection) {\n    super();\n    this.loginAckReceived = false;\n    this.connection = connection;\n  }\n\n  onInfoMessage(token: InfoMessageToken) {\n    this.connection.emit('infoMessage', token);\n  }\n\n  onErrorMessage(token: ErrorMessageToken) {\n    this.connection.emit('errorMessage', token);\n\n    const error = new ConnectionError(token.message, 'ELOGIN');\n\n    const isLoginErrorTransient = this.connection.transientErrorLookup.isTransientError(token.number);\n    if (isLoginErrorTransient && this.connection.curTransientRetryCount !== this.connection.config.options.maxRetriesOnTransientErrors) {\n      error.isTransient = true;\n    }\n\n    this.connection.loginError = error;\n  }\n\n  onSSPI(token: SSPIToken) {\n    if (token.ntlmpacket) {\n      this.connection.ntlmpacket = token.ntlmpacket;\n      this.connection.ntlmpacketBuffer = token.ntlmpacketBuffer;\n    }\n  }\n\n  onDatabaseChange(token: DatabaseEnvChangeToken) {\n    this.connection.emit('databaseChange', token.newValue);\n  }\n\n  onLanguageChange(token: LanguageEnvChangeToken) {\n    this.connection.emit('languageChange', token.newValue);\n  }\n\n  onCharsetChange(token: CharsetEnvChangeToken) {\n    this.connection.emit('charsetChange', token.newValue);\n  }\n\n  onSqlCollationChange(token: CollationChangeToken) {\n    this.connection.databaseCollation = token.newValue;\n  }\n\n  onFedAuthInfo(token: FedAuthInfoToken) {\n    this.fedAuthInfoToken = token;\n  }\n\n  onFeatureExtAck(token: FeatureExtAckToken) {\n    const { authentication } = this.connection.config;\n\n    if (authentication.type === 'azure-active-directory-password' || authentication.type === 'azure-active-directory-access-token' || authentication.type === 'azure-active-directory-msi-vm' || authentication.type === 'azure-active-directory-msi-app-service' || authentication.type === 'azure-active-directory-service-principal-secret' || authentication.type === 'azure-active-directory-default') {\n      if (token.fedAuth === undefined) {\n        this.connection.loginError = new ConnectionError('Did not receive Active Directory authentication acknowledgement');\n      } else if (token.fedAuth.length !== 0) {\n        this.connection.loginError = new ConnectionError(`Active Directory authentication acknowledgment for ${authentication.type} authentication method includes extra data`);\n      }\n    } else if (token.fedAuth === undefined && token.utf8Support === undefined) {\n      this.connection.loginError = new ConnectionError('Received acknowledgement for unknown feature');\n    } else if (token.fedAuth) {\n      this.connection.loginError = new ConnectionError('Did not request Active Directory authentication, but received the acknowledgment');\n    }\n  }\n\n  onLoginAck(token: LoginAckToken) {\n    if (!token.tdsVersion) {\n      // unsupported TDS version\n      this.connection.loginError = new ConnectionError('Server responded with unknown TDS version.', 'ETDS');\n      return;\n    }\n\n    if (!token.interface) {\n      // unsupported interface\n      this.connection.loginError = new ConnectionError('Server responded with unsupported interface.', 'EINTERFACENOTSUPP');\n      return;\n    }\n\n    // use negotiated version\n    this.connection.config.options.tdsVersion = token.tdsVersion;\n\n    this.loginAckReceived = true;\n  }\n\n  onRoutingChange(token: RoutingEnvChangeToken) {\n    // Removes instance name attached to the redirect url. E.g., redirect.db.net\\instance1 --> redirect.db.net\n    const [ server ] = token.newValue.server.split('\\\\');\n\n    this.routingData = {\n      server, port: token.newValue.port\n    };\n  }\n\n  onDoneInProc(token: DoneInProcToken) {\n    // Do nothing\n  }\n\n  onDone(token: DoneToken) {\n    // Do nothing\n  }\n\n  onPacketSizeChange(token: PacketSizeEnvChangeToken) {\n    this.connection.messageIo.packetSize(token.newValue);\n  }\n\n  onDatabaseMirroringPartner(token: DatabaseMirroringPartnerEnvChangeToken) {\n    // Do nothing\n  }\n}\n\n/**\n * A handler for tokens received in the response message to a RPC Request,\n * a SQL Batch Request, a Bulk Load BCP Request or a Transaction Manager Request.\n */\nexport class RequestTokenHandler extends TokenHandler {\n  declare connection: Connection;\n  declare request: Request | BulkLoad;\n  declare errors: RequestError[];\n\n  constructor(connection: Connection, request: Request | BulkLoad) {\n    super();\n\n    this.connection = connection;\n    this.request = request;\n    this.errors = [];\n  }\n\n  onInfoMessage(token: InfoMessageToken) {\n    this.connection.emit('infoMessage', token);\n  }\n\n  onErrorMessage(token: ErrorMessageToken) {\n    this.connection.emit('errorMessage', token);\n\n    if (!this.request.canceled) {\n      const error = new RequestError(token.message, 'EREQUEST');\n\n      error.number = token.number;\n      error.state = token.state;\n      error.class = token.class;\n      error.serverName = token.serverName;\n      error.procName = token.procName;\n      error.lineNumber = token.lineNumber;\n      this.errors.push(error);\n      this.request.error = error;\n      if (this.request instanceof Request && this.errors.length > 1) {\n        this.request.error = new AggregateError(this.errors);\n      }\n    }\n  }\n\n  onDatabaseChange(token: DatabaseEnvChangeToken) {\n    this.connection.emit('databaseChange', token.newValue);\n  }\n\n  onLanguageChange(token: LanguageEnvChangeToken) {\n    this.connection.emit('languageChange', token.newValue);\n  }\n\n  onCharsetChange(token: CharsetEnvChangeToken) {\n    this.connection.emit('charsetChange', token.newValue);\n  }\n\n  onSqlCollationChange(token: CollationChangeToken) {\n    this.connection.databaseCollation = token.newValue;\n  }\n\n  onPacketSizeChange(token: PacketSizeEnvChangeToken) {\n    this.connection.messageIo.packetSize(token.newValue);\n  }\n\n  onBeginTransaction(token: BeginTransactionEnvChangeToken) {\n    this.connection.transactionDescriptors.push(token.newValue);\n    this.connection.inTransaction = true;\n  }\n\n  onCommitTransaction(token: CommitTransactionEnvChangeToken) {\n    this.connection.transactionDescriptors.length = 1;\n    this.connection.inTransaction = false;\n  }\n\n  onRollbackTransaction(token: RollbackTransactionEnvChangeToken) {\n    this.connection.transactionDescriptors.length = 1;\n    // An outermost transaction was rolled back. Reset the transaction counter\n    this.connection.inTransaction = false;\n    this.connection.emit('rollbackTransaction');\n  }\n\n  onColMetadata(token: ColMetadataToken) {\n    if (!this.request.canceled) {\n      if (this.connection.config.options.useColumnNames) {\n        const columns: { [key: string]: ColumnMetadata } = Object.create(null);\n\n        for (let j = 0, len = token.columns.length; j < len; j++) {\n          const col = token.columns[j];\n          if (columns[col.colName] == null) {\n            columns[col.colName] = col;\n          }\n        }\n\n        this.request.emit('columnMetadata', columns);\n      } else {\n        this.request.emit('columnMetadata', token.columns);\n      }\n    }\n  }\n\n  onOrder(token: OrderToken) {\n    if (!this.request.canceled) {\n      this.request.emit('order', token.orderColumns);\n    }\n  }\n\n  onRow(token: RowToken | NBCRowToken) {\n    if (!this.request.canceled) {\n      if (this.connection.config.options.rowCollectionOnRequestCompletion) {\n        this.request.rows!.push(token.columns);\n      }\n\n      if (this.connection.config.options.rowCollectionOnDone) {\n        this.request.rst!.push(token.columns);\n      }\n\n      this.request.emit('row', token.columns);\n    }\n  }\n\n  onReturnStatus(token: ReturnStatusToken) {\n    if (!this.request.canceled) {\n      // Keep value for passing in 'doneProc' event.\n      this.connection.procReturnStatusValue = token.value;\n    }\n  }\n\n  onReturnValue(token: ReturnValueToken) {\n    if (!this.request.canceled) {\n      this.request.emit('returnValue', token.paramName, token.value, token.metadata);\n    }\n  }\n\n  onDoneProc(token: DoneProcToken) {\n    if (!this.request.canceled) {\n      if (token.sqlError && !this.request.error) {\n        // check if the DONE_ERROR flags was set, but an ERROR token was not sent.\n        this.request.error = new RequestError('An unknown error has occurred.', 'UNKNOWN');\n      }\n\n      this.request.emit('doneProc', token.rowCount, token.more, this.connection.procReturnStatusValue, this.request.rst);\n\n      this.connection.procReturnStatusValue = undefined;\n\n      if (token.rowCount !== undefined) {\n        this.request.rowCount! += token.rowCount;\n      }\n\n      if (this.connection.config.options.rowCollectionOnDone) {\n        this.request.rst = [];\n      }\n    }\n  }\n\n  onDoneInProc(token: DoneInProcToken) {\n    if (!this.request.canceled) {\n      this.request.emit('doneInProc', token.rowCount, token.more, this.request.rst);\n\n      if (token.rowCount !== undefined) {\n        this.request.rowCount! += token.rowCount;\n      }\n\n      if (this.connection.config.options.rowCollectionOnDone) {\n        this.request.rst = [];\n      }\n    }\n  }\n\n  onDone(token: DoneToken) {\n    if (!this.request.canceled) {\n      if (token.sqlError && !this.request.error) {\n        // check if the DONE_ERROR flags was set, but an ERROR token was not sent.\n        this.request.error = new RequestError('An unknown error has occurred.', 'UNKNOWN');\n      }\n\n      this.request.emit('done', token.rowCount, token.more, this.request.rst);\n\n      if (token.rowCount !== undefined) {\n        this.request.rowCount! += token.rowCount;\n      }\n\n      if (this.connection.config.options.rowCollectionOnDone) {\n        this.request.rst = [];\n      }\n    }\n  }\n\n  onResetConnection(token: ResetConnectionEnvChangeToken) {\n    this.connection.emit('resetConnection');\n  }\n}\n\n/**\n * A handler for the attention acknowledgement message.\n *\n * This message only contains a `DONE` token that acknowledges\n * that the attention message was received by the server.\n */\nexport class AttentionTokenHandler extends TokenHandler {\n  declare connection: Connection;\n  declare request: Request | BulkLoad;\n\n  /**\n   * Returns whether an attention acknowledgement was received.\n   */\n  declare attentionReceived: boolean;\n\n  constructor(connection: Connection, request: Request | BulkLoad) {\n    super();\n\n    this.connection = connection;\n    this.request = request;\n\n    this.attentionReceived = false;\n  }\n\n  onDone(token: DoneToken) {\n    if (token.attention) {\n      this.attentionReceived = true;\n    }\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAA0D,SAAAD,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAiCnD,MAAMG,oBAAoB,SAASC,KAAK,CAAC;EAC9CC,WAAWA,CAACC,OAAqB,EAAEC,KAAY,EAAE;IAC/C,KAAK,CAAC,oBAAoB,GAAGA,KAAK,CAACC,IAAI,GAAG,QAAQ,GAAGF,OAAO,CAACD,WAAW,CAACG,IAAI,GAAG,GAAG,CAAC;EACtF;AACF;AAACC,OAAA,CAAAN,oBAAA,GAAAA,oBAAA;AAEM,MAAMO,YAAY,CAAC;EACxBC,aAAaA,CAACJ,KAAuB,EAAE;IACrC,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAK,cAAcA,CAACL,KAAwB,EAAE;IACvC,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAM,MAAMA,CAACN,KAAgB,EAAE;IACvB,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAO,gBAAgBA,CAACP,KAA6B,EAAE;IAC9C,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAQ,gBAAgBA,CAACR,KAA6B,EAAE;IAC9C,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAS,eAAeA,CAACT,KAA4B,EAAE;IAC5C,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAU,oBAAoBA,CAACV,KAA2B,EAAE;IAChD,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAW,eAAeA,CAACX,KAA4B,EAAE;IAC5C,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAY,kBAAkBA,CAACZ,KAA+B,EAAE;IAClD,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAa,iBAAiBA,CAACb,KAAoC,EAAE;IACtD,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAc,kBAAkBA,CAACd,KAAqC,EAAE;IACxD,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAe,mBAAmBA,CAACf,KAAsC,EAAE;IAC1D,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAgB,qBAAqBA,CAAChB,KAAwC,EAAE;IAC9D,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAiB,aAAaA,CAACjB,KAAuB,EAAE;IACrC,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAkB,eAAeA,CAAClB,KAAyB,EAAE;IACzC,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAmB,UAAUA,CAACnB,KAAoB,EAAE;IAC/B,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAoB,aAAaA,CAACpB,KAAuB,EAAE;IACrC,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAqB,OAAOA,CAACrB,KAAiB,EAAE;IACzB,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAsB,KAAKA,CAACtB,KAA6B,EAAE;IACnC,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAuB,cAAcA,CAACvB,KAAwB,EAAE;IACvC,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAwB,aAAaA,CAACxB,KAAuB,EAAE;IACrC,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEAyB,UAAUA,CAACzB,KAAoB,EAAE;IAC/B,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEA0B,YAAYA,CAAC1B,KAAsB,EAAE;IACnC,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEA2B,MAAMA,CAAC3B,KAAgB,EAAE;IACvB,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;EAEA4B,0BAA0BA,CAAC5B,KAA6C,EAAE;IACxE,MAAM,IAAIJ,oBAAoB,CAAC,IAAI,EAAEI,KAAK,CAAC;EAC7C;AACF;;AAEA;AACA;AACA;AACA;AAHAE,OAAA,CAAAC,YAAA,GAAAA,YAAA;AAIO,MAAM0B,sBAAsB,SAAS1B,YAAY,CAAC;EAGvDL,WAAWA,CAACgC,UAAsB,EAAE;IAClC,KAAK,CAAC,CAAC;IAEP,IAAI,CAACA,UAAU,GAAGA,UAAU;EAC9B;EAEA1B,aAAaA,CAACJ,KAAuB,EAAE;IACrC,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,aAAa,EAAE/B,KAAK,CAAC;EAC5C;EAEAK,cAAcA,CAACL,KAAwB,EAAE;IACvC,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,cAAc,EAAE/B,KAAK,CAAC;EAC7C;EAEAO,gBAAgBA,CAACP,KAA6B,EAAE;IAC9C,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,gBAAgB,EAAE/B,KAAK,CAACgC,QAAQ,CAAC;EACxD;EAEAxB,gBAAgBA,CAACR,KAA6B,EAAE;IAC9C,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,gBAAgB,EAAE/B,KAAK,CAACgC,QAAQ,CAAC;EACxD;EAEAvB,eAAeA,CAACT,KAA4B,EAAE;IAC5C,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,eAAe,EAAE/B,KAAK,CAACgC,QAAQ,CAAC;EACvD;EAEAtB,oBAAoBA,CAACV,KAA2B,EAAE;IAChD,IAAI,CAAC8B,UAAU,CAACG,iBAAiB,GAAGjC,KAAK,CAACgC,QAAQ;EACpD;EAEApB,kBAAkBA,CAACZ,KAA+B,EAAE;IAClD,IAAI,CAAC8B,UAAU,CAACI,SAAS,CAACC,UAAU,CAACnC,KAAK,CAACgC,QAAQ,CAAC;EACtD;EAEAlB,kBAAkBA,CAACd,KAAqC,EAAE;IACxD,IAAI,CAAC8B,UAAU,CAACM,sBAAsB,CAACC,IAAI,CAACrC,KAAK,CAACgC,QAAQ,CAAC;IAC3D,IAAI,CAACF,UAAU,CAACQ,aAAa,GAAG,IAAI;EACtC;EAEAvB,mBAAmBA,CAACf,KAAsC,EAAE;IAC1D,IAAI,CAAC8B,UAAU,CAACM,sBAAsB,CAACG,MAAM,GAAG,CAAC;IACjD,IAAI,CAACT,UAAU,CAACQ,aAAa,GAAG,KAAK;EACvC;EAEAtB,qBAAqBA,CAAChB,KAAwC,EAAE;IAC9D,IAAI,CAAC8B,UAAU,CAACM,sBAAsB,CAACG,MAAM,GAAG,CAAC;IACjD;IACA,IAAI,CAACT,UAAU,CAACQ,aAAa,GAAG,KAAK;IACrC,IAAI,CAACR,UAAU,CAACC,IAAI,CAAC,qBAAqB,CAAC;EAC7C;EAEAX,aAAaA,CAACpB,KAAuB,EAAE;IACrC,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,OAAO,EAAE,IAAIlC,KAAK,CAAC,6DAA6D,CAAC,CAAC;IACvG,IAAI,CAACiC,UAAU,CAACU,KAAK,CAAC,CAAC;EACzB;EAEAnB,OAAOA,CAACrB,KAAiB,EAAE;IACzB,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,OAAO,EAAE,IAAIlC,KAAK,CAAC,oDAAoD,CAAC,CAAC;IAC9F,IAAI,CAACiC,UAAU,CAACU,KAAK,CAAC,CAAC;EACzB;EAEAlB,KAAKA,CAACtB,KAA6B,EAAE;IACnC,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,OAAO,EAAE,IAAIlC,KAAK,CAAC,kDAAkD,CAAC,CAAC;IAC5F,IAAI,CAACiC,UAAU,CAACU,KAAK,CAAC,CAAC;EACzB;EAEAjB,cAAcA,CAACvB,KAAwB,EAAE;IACvC;EAAA;EAGFwB,aAAaA,CAACxB,KAAuB,EAAE;IACrC;EAAA;EAGFyB,UAAUA,CAACzB,KAAoB,EAAE;IAC/B;EAAA;EAGF0B,YAAYA,CAAC1B,KAAsB,EAAE;IACnC;EAAA;EAGF2B,MAAMA,CAAC3B,KAAgB,EAAE;IACvB;EAAA;EAGFa,iBAAiBA,CAACb,KAAoC,EAAE;IACtD,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,iBAAiB,CAAC;EACzC;AACF;;AAEA;AACA;AACA;AAFA7B,OAAA,CAAA2B,sBAAA,GAAAA,sBAAA;AAGO,MAAMY,kBAAkB,SAAStC,YAAY,CAAC;EAQnDL,WAAWA,CAACgC,UAAsB,EAAE;IAClC,KAAK,CAAC,CAAC;IACP,IAAI,CAACY,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACZ,UAAU,GAAGA,UAAU;EAC9B;EAEA1B,aAAaA,CAACJ,KAAuB,EAAE;IACrC,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,aAAa,EAAE/B,KAAK,CAAC;EAC5C;EAEAK,cAAcA,CAACL,KAAwB,EAAE;IACvC,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,cAAc,EAAE/B,KAAK,CAAC;IAE3C,MAAM2C,KAAK,GAAG,IAAIC,uBAAe,CAAC5C,KAAK,CAAC6C,OAAO,EAAE,QAAQ,CAAC;IAE1D,MAAMC,qBAAqB,GAAG,IAAI,CAAChB,UAAU,CAACiB,oBAAoB,CAACC,gBAAgB,CAAChD,KAAK,CAACiD,MAAM,CAAC;IACjG,IAAIH,qBAAqB,IAAI,IAAI,CAAChB,UAAU,CAACoB,sBAAsB,KAAK,IAAI,CAACpB,UAAU,CAACqB,MAAM,CAACC,OAAO,CAACC,2BAA2B,EAAE;MAClIV,KAAK,CAACW,WAAW,GAAG,IAAI;IAC1B;IAEA,IAAI,CAACxB,UAAU,CAACyB,UAAU,GAAGZ,KAAK;EACpC;EAEArC,MAAMA,CAACN,KAAgB,EAAE;IACvB,IAAIA,KAAK,CAACwD,UAAU,EAAE;MACpB,IAAI,CAAC1B,UAAU,CAAC0B,UAAU,GAAGxD,KAAK,CAACwD,UAAU;MAC7C,IAAI,CAAC1B,UAAU,CAAC2B,gBAAgB,GAAGzD,KAAK,CAACyD,gBAAgB;IAC3D;EACF;EAEAlD,gBAAgBA,CAACP,KAA6B,EAAE;IAC9C,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,gBAAgB,EAAE/B,KAAK,CAACgC,QAAQ,CAAC;EACxD;EAEAxB,gBAAgBA,CAACR,KAA6B,EAAE;IAC9C,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,gBAAgB,EAAE/B,KAAK,CAACgC,QAAQ,CAAC;EACxD;EAEAvB,eAAeA,CAACT,KAA4B,EAAE;IAC5C,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,eAAe,EAAE/B,KAAK,CAACgC,QAAQ,CAAC;EACvD;EAEAtB,oBAAoBA,CAACV,KAA2B,EAAE;IAChD,IAAI,CAAC8B,UAAU,CAACG,iBAAiB,GAAGjC,KAAK,CAACgC,QAAQ;EACpD;EAEAf,aAAaA,CAACjB,KAAuB,EAAE;IACrC,IAAI,CAAC0D,gBAAgB,GAAG1D,KAAK;EAC/B;EAEAkB,eAAeA,CAAClB,KAAyB,EAAE;IACzC,MAAM;MAAE2D;IAAe,CAAC,GAAG,IAAI,CAAC7B,UAAU,CAACqB,MAAM;IAEjD,IAAIQ,cAAc,CAACC,IAAI,KAAK,iCAAiC,IAAID,cAAc,CAACC,IAAI,KAAK,qCAAqC,IAAID,cAAc,CAACC,IAAI,KAAK,+BAA+B,IAAID,cAAc,CAACC,IAAI,KAAK,wCAAwC,IAAID,cAAc,CAACC,IAAI,KAAK,iDAAiD,IAAID,cAAc,CAACC,IAAI,KAAK,gCAAgC,EAAE;MACtY,IAAI5D,KAAK,CAAC6D,OAAO,KAAKC,SAAS,EAAE;QAC/B,IAAI,CAAChC,UAAU,CAACyB,UAAU,GAAG,IAAIX,uBAAe,CAAC,iEAAiE,CAAC;MACrH,CAAC,MAAM,IAAI5C,KAAK,CAAC6D,OAAO,CAACtB,MAAM,KAAK,CAAC,EAAE;QACrC,IAAI,CAACT,UAAU,CAACyB,UAAU,GAAG,IAAIX,uBAAe,CAAE,sDAAqDe,cAAc,CAACC,IAAK,4CAA2C,CAAC;MACzK;IACF,CAAC,MAAM,IAAI5D,KAAK,CAAC6D,OAAO,KAAKC,SAAS,IAAI9D,KAAK,CAAC+D,WAAW,KAAKD,SAAS,EAAE;MACzE,IAAI,CAAChC,UAAU,CAACyB,UAAU,GAAG,IAAIX,uBAAe,CAAC,8CAA8C,CAAC;IAClG,CAAC,MAAM,IAAI5C,KAAK,CAAC6D,OAAO,EAAE;MACxB,IAAI,CAAC/B,UAAU,CAACyB,UAAU,GAAG,IAAIX,uBAAe,CAAC,kFAAkF,CAAC;IACtI;EACF;EAEAzB,UAAUA,CAACnB,KAAoB,EAAE;IAC/B,IAAI,CAACA,KAAK,CAACgE,UAAU,EAAE;MACrB;MACA,IAAI,CAAClC,UAAU,CAACyB,UAAU,GAAG,IAAIX,uBAAe,CAAC,4CAA4C,EAAE,MAAM,CAAC;MACtG;IACF;IAEA,IAAI,CAAC5C,KAAK,CAACiE,SAAS,EAAE;MACpB;MACA,IAAI,CAACnC,UAAU,CAACyB,UAAU,GAAG,IAAIX,uBAAe,CAAC,8CAA8C,EAAE,mBAAmB,CAAC;MACrH;IACF;;IAEA;IACA,IAAI,CAACd,UAAU,CAACqB,MAAM,CAACC,OAAO,CAACY,UAAU,GAAGhE,KAAK,CAACgE,UAAU;IAE5D,IAAI,CAACtB,gBAAgB,GAAG,IAAI;EAC9B;EAEA/B,eAAeA,CAACX,KAA4B,EAAE;IAC5C;IACA,MAAM,CAAEkE,MAAM,CAAE,GAAGlE,KAAK,CAACgC,QAAQ,CAACkC,MAAM,CAACC,KAAK,CAAC,IAAI,CAAC;IAEpD,IAAI,CAACC,WAAW,GAAG;MACjBF,MAAM;MAAEG,IAAI,EAAErE,KAAK,CAACgC,QAAQ,CAACqC;IAC/B,CAAC;EACH;EAEA3C,YAAYA,CAAC1B,KAAsB,EAAE;IACnC;EAAA;EAGF2B,MAAMA,CAAC3B,KAAgB,EAAE;IACvB;EAAA;EAGFY,kBAAkBA,CAACZ,KAA+B,EAAE;IAClD,IAAI,CAAC8B,UAAU,CAACI,SAAS,CAACC,UAAU,CAACnC,KAAK,CAACgC,QAAQ,CAAC;EACtD;EAEAJ,0BAA0BA,CAAC5B,KAA6C,EAAE;IACxE;EAAA;AAEJ;;AAEA;AACA;AACA;AACA;AAHAE,OAAA,CAAAuC,kBAAA,GAAAA,kBAAA;AAIO,MAAM6B,mBAAmB,SAASnE,YAAY,CAAC;EAKpDL,WAAWA,CAACgC,UAAsB,EAAEyC,OAA2B,EAAE;IAC/D,KAAK,CAAC,CAAC;IAEP,IAAI,CAACzC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACyC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAG,EAAE;EAClB;EAEApE,aAAaA,CAACJ,KAAuB,EAAE;IACrC,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,aAAa,EAAE/B,KAAK,CAAC;EAC5C;EAEAK,cAAcA,CAACL,KAAwB,EAAE;IACvC,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,cAAc,EAAE/B,KAAK,CAAC;IAE3C,IAAI,CAAC,IAAI,CAACuE,OAAO,CAACE,QAAQ,EAAE;MAC1B,MAAM9B,KAAK,GAAG,IAAI+B,oBAAY,CAAC1E,KAAK,CAAC6C,OAAO,EAAE,UAAU,CAAC;MAEzDF,KAAK,CAACM,MAAM,GAAGjD,KAAK,CAACiD,MAAM;MAC3BN,KAAK,CAACgC,KAAK,GAAG3E,KAAK,CAAC2E,KAAK;MACzBhC,KAAK,CAACiC,KAAK,GAAG5E,KAAK,CAAC4E,KAAK;MACzBjC,KAAK,CAACkC,UAAU,GAAG7E,KAAK,CAAC6E,UAAU;MACnClC,KAAK,CAACmC,QAAQ,GAAG9E,KAAK,CAAC8E,QAAQ;MAC/BnC,KAAK,CAACoC,UAAU,GAAG/E,KAAK,CAAC+E,UAAU;MACnC,IAAI,CAACP,MAAM,CAACnC,IAAI,CAACM,KAAK,CAAC;MACvB,IAAI,CAAC4B,OAAO,CAAC5B,KAAK,GAAGA,KAAK;MAC1B,IAAI,IAAI,CAAC4B,OAAO,YAAYS,gBAAO,IAAI,IAAI,CAACR,MAAM,CAACjC,MAAM,GAAG,CAAC,EAAE;QAC7D,IAAI,CAACgC,OAAO,CAAC5B,KAAK,GAAG,IAAIsC,cAAc,CAAC,IAAI,CAACT,MAAM,CAAC;MACtD;IACF;EACF;EAEAjE,gBAAgBA,CAACP,KAA6B,EAAE;IAC9C,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,gBAAgB,EAAE/B,KAAK,CAACgC,QAAQ,CAAC;EACxD;EAEAxB,gBAAgBA,CAACR,KAA6B,EAAE;IAC9C,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,gBAAgB,EAAE/B,KAAK,CAACgC,QAAQ,CAAC;EACxD;EAEAvB,eAAeA,CAACT,KAA4B,EAAE;IAC5C,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,eAAe,EAAE/B,KAAK,CAACgC,QAAQ,CAAC;EACvD;EAEAtB,oBAAoBA,CAACV,KAA2B,EAAE;IAChD,IAAI,CAAC8B,UAAU,CAACG,iBAAiB,GAAGjC,KAAK,CAACgC,QAAQ;EACpD;EAEApB,kBAAkBA,CAACZ,KAA+B,EAAE;IAClD,IAAI,CAAC8B,UAAU,CAACI,SAAS,CAACC,UAAU,CAACnC,KAAK,CAACgC,QAAQ,CAAC;EACtD;EAEAlB,kBAAkBA,CAACd,KAAqC,EAAE;IACxD,IAAI,CAAC8B,UAAU,CAACM,sBAAsB,CAACC,IAAI,CAACrC,KAAK,CAACgC,QAAQ,CAAC;IAC3D,IAAI,CAACF,UAAU,CAACQ,aAAa,GAAG,IAAI;EACtC;EAEAvB,mBAAmBA,CAACf,KAAsC,EAAE;IAC1D,IAAI,CAAC8B,UAAU,CAACM,sBAAsB,CAACG,MAAM,GAAG,CAAC;IACjD,IAAI,CAACT,UAAU,CAACQ,aAAa,GAAG,KAAK;EACvC;EAEAtB,qBAAqBA,CAAChB,KAAwC,EAAE;IAC9D,IAAI,CAAC8B,UAAU,CAACM,sBAAsB,CAACG,MAAM,GAAG,CAAC;IACjD;IACA,IAAI,CAACT,UAAU,CAACQ,aAAa,GAAG,KAAK;IACrC,IAAI,CAACR,UAAU,CAACC,IAAI,CAAC,qBAAqB,CAAC;EAC7C;EAEAX,aAAaA,CAACpB,KAAuB,EAAE;IACrC,IAAI,CAAC,IAAI,CAACuE,OAAO,CAACE,QAAQ,EAAE;MAC1B,IAAI,IAAI,CAAC3C,UAAU,CAACqB,MAAM,CAACC,OAAO,CAAC8B,cAAc,EAAE;QACjD,MAAMC,OAA0C,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;QAEtE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGvF,KAAK,CAACmF,OAAO,CAAC5C,MAAM,EAAE+C,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;UACxD,MAAME,GAAG,GAAGxF,KAAK,CAACmF,OAAO,CAACG,CAAC,CAAC;UAC5B,IAAIH,OAAO,CAACK,GAAG,CAACC,OAAO,CAAC,IAAI,IAAI,EAAE;YAChCN,OAAO,CAACK,GAAG,CAACC,OAAO,CAAC,GAAGD,GAAG;UAC5B;QACF;QAEA,IAAI,CAACjB,OAAO,CAACxC,IAAI,CAAC,gBAAgB,EAAEoD,OAAO,CAAC;MAC9C,CAAC,MAAM;QACL,IAAI,CAACZ,OAAO,CAACxC,IAAI,CAAC,gBAAgB,EAAE/B,KAAK,CAACmF,OAAO,CAAC;MACpD;IACF;EACF;EAEA9D,OAAOA,CAACrB,KAAiB,EAAE;IACzB,IAAI,CAAC,IAAI,CAACuE,OAAO,CAACE,QAAQ,EAAE;MAC1B,IAAI,CAACF,OAAO,CAACxC,IAAI,CAAC,OAAO,EAAE/B,KAAK,CAAC0F,YAAY,CAAC;IAChD;EACF;EAEApE,KAAKA,CAACtB,KAA6B,EAAE;IACnC,IAAI,CAAC,IAAI,CAACuE,OAAO,CAACE,QAAQ,EAAE;MAC1B,IAAI,IAAI,CAAC3C,UAAU,CAACqB,MAAM,CAACC,OAAO,CAACuC,gCAAgC,EAAE;QACnE,IAAI,CAACpB,OAAO,CAACqB,IAAI,CAAEvD,IAAI,CAACrC,KAAK,CAACmF,OAAO,CAAC;MACxC;MAEA,IAAI,IAAI,CAACrD,UAAU,CAACqB,MAAM,CAACC,OAAO,CAACyC,mBAAmB,EAAE;QACtD,IAAI,CAACtB,OAAO,CAACuB,GAAG,CAAEzD,IAAI,CAACrC,KAAK,CAACmF,OAAO,CAAC;MACvC;MAEA,IAAI,CAACZ,OAAO,CAACxC,IAAI,CAAC,KAAK,EAAE/B,KAAK,CAACmF,OAAO,CAAC;IACzC;EACF;EAEA5D,cAAcA,CAACvB,KAAwB,EAAE;IACvC,IAAI,CAAC,IAAI,CAACuE,OAAO,CAACE,QAAQ,EAAE;MAC1B;MACA,IAAI,CAAC3C,UAAU,CAACiE,qBAAqB,GAAG/F,KAAK,CAACgG,KAAK;IACrD;EACF;EAEAxE,aAAaA,CAACxB,KAAuB,EAAE;IACrC,IAAI,CAAC,IAAI,CAACuE,OAAO,CAACE,QAAQ,EAAE;MAC1B,IAAI,CAACF,OAAO,CAACxC,IAAI,CAAC,aAAa,EAAE/B,KAAK,CAACiG,SAAS,EAAEjG,KAAK,CAACgG,KAAK,EAAEhG,KAAK,CAACkG,QAAQ,CAAC;IAChF;EACF;EAEAzE,UAAUA,CAACzB,KAAoB,EAAE;IAC/B,IAAI,CAAC,IAAI,CAACuE,OAAO,CAACE,QAAQ,EAAE;MAC1B,IAAIzE,KAAK,CAACmG,QAAQ,IAAI,CAAC,IAAI,CAAC5B,OAAO,CAAC5B,KAAK,EAAE;QACzC;QACA,IAAI,CAAC4B,OAAO,CAAC5B,KAAK,GAAG,IAAI+B,oBAAY,CAAC,gCAAgC,EAAE,SAAS,CAAC;MACpF;MAEA,IAAI,CAACH,OAAO,CAACxC,IAAI,CAAC,UAAU,EAAE/B,KAAK,CAACoG,QAAQ,EAAEpG,KAAK,CAACqG,IAAI,EAAE,IAAI,CAACvE,UAAU,CAACiE,qBAAqB,EAAE,IAAI,CAACxB,OAAO,CAACuB,GAAG,CAAC;MAElH,IAAI,CAAChE,UAAU,CAACiE,qBAAqB,GAAGjC,SAAS;MAEjD,IAAI9D,KAAK,CAACoG,QAAQ,KAAKtC,SAAS,EAAE;QAChC,IAAI,CAACS,OAAO,CAAC6B,QAAQ,IAAKpG,KAAK,CAACoG,QAAQ;MAC1C;MAEA,IAAI,IAAI,CAACtE,UAAU,CAACqB,MAAM,CAACC,OAAO,CAACyC,mBAAmB,EAAE;QACtD,IAAI,CAACtB,OAAO,CAACuB,GAAG,GAAG,EAAE;MACvB;IACF;EACF;EAEApE,YAAYA,CAAC1B,KAAsB,EAAE;IACnC,IAAI,CAAC,IAAI,CAACuE,OAAO,CAACE,QAAQ,EAAE;MAC1B,IAAI,CAACF,OAAO,CAACxC,IAAI,CAAC,YAAY,EAAE/B,KAAK,CAACoG,QAAQ,EAAEpG,KAAK,CAACqG,IAAI,EAAE,IAAI,CAAC9B,OAAO,CAACuB,GAAG,CAAC;MAE7E,IAAI9F,KAAK,CAACoG,QAAQ,KAAKtC,SAAS,EAAE;QAChC,IAAI,CAACS,OAAO,CAAC6B,QAAQ,IAAKpG,KAAK,CAACoG,QAAQ;MAC1C;MAEA,IAAI,IAAI,CAACtE,UAAU,CAACqB,MAAM,CAACC,OAAO,CAACyC,mBAAmB,EAAE;QACtD,IAAI,CAACtB,OAAO,CAACuB,GAAG,GAAG,EAAE;MACvB;IACF;EACF;EAEAnE,MAAMA,CAAC3B,KAAgB,EAAE;IACvB,IAAI,CAAC,IAAI,CAACuE,OAAO,CAACE,QAAQ,EAAE;MAC1B,IAAIzE,KAAK,CAACmG,QAAQ,IAAI,CAAC,IAAI,CAAC5B,OAAO,CAAC5B,KAAK,EAAE;QACzC;QACA,IAAI,CAAC4B,OAAO,CAAC5B,KAAK,GAAG,IAAI+B,oBAAY,CAAC,gCAAgC,EAAE,SAAS,CAAC;MACpF;MAEA,IAAI,CAACH,OAAO,CAACxC,IAAI,CAAC,MAAM,EAAE/B,KAAK,CAACoG,QAAQ,EAAEpG,KAAK,CAACqG,IAAI,EAAE,IAAI,CAAC9B,OAAO,CAACuB,GAAG,CAAC;MAEvE,IAAI9F,KAAK,CAACoG,QAAQ,KAAKtC,SAAS,EAAE;QAChC,IAAI,CAACS,OAAO,CAAC6B,QAAQ,IAAKpG,KAAK,CAACoG,QAAQ;MAC1C;MAEA,IAAI,IAAI,CAACtE,UAAU,CAACqB,MAAM,CAACC,OAAO,CAACyC,mBAAmB,EAAE;QACtD,IAAI,CAACtB,OAAO,CAACuB,GAAG,GAAG,EAAE;MACvB;IACF;EACF;EAEAjF,iBAAiBA,CAACb,KAAoC,EAAE;IACtD,IAAI,CAAC8B,UAAU,CAACC,IAAI,CAAC,iBAAiB,CAAC;EACzC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AALA7B,OAAA,CAAAoE,mBAAA,GAAAA,mBAAA;AAMO,MAAMgC,qBAAqB,SAASnG,YAAY,CAAC;EAItD;AACF;AACA;;EAGEL,WAAWA,CAACgC,UAAsB,EAAEyC,OAA2B,EAAE;IAC/D,KAAK,CAAC,CAAC;IAEP,IAAI,CAACzC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACyC,OAAO,GAAGA,OAAO;IAEtB,IAAI,CAACgC,iBAAiB,GAAG,KAAK;EAChC;EAEA5E,MAAMA,CAAC3B,KAAgB,EAAE;IACvB,IAAIA,KAAK,CAACwG,SAAS,EAAE;MACnB,IAAI,CAACD,iBAAiB,GAAG,IAAI;IAC/B;EACF;AACF;AAACrG,OAAA,CAAAoG,qBAAA,GAAAA,qBAAA"}