"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Result = exports.NotEnoughDataError = void 0;
exports.readBVarByte = readBVarByte;
exports.readBVarChar = readBVarChar;
exports.readBigInt64LE = readBigInt64LE;
exports.readBigUInt64LE = readBigUInt64LE;
exports.readDoubleLE = readDoubleLE;
exports.readFloatLE = readFloatLE;
exports.readInt16LE = readInt16LE;
exports.readInt32LE = readInt32LE;
exports.readUInt16LE = readUInt16LE;
exports.readUInt24LE = readUInt24LE;
exports.readUInt32BE = readUInt32BE;
exports.readUInt32LE = readUInt32LE;
exports.readUInt40LE = readUInt40LE;
exports.readUInt8 = readUInt8;
exports.readUNumeric128LE = readUNumeric128LE;
exports.readUNumeric64LE = readUNumeric64LE;
exports.readUNumeric96LE = readUNumeric96LE;
exports.readUsVarByte = readUsVarByte;
exports.readUsVarChar = readUsVarChar;
class Result {
  constructor(value, offset) {
    this.value = value;
    this.offset = offset;
  }
}
exports.Result = Result;
class NotEnoughDataError extends Error {
  byteCount;
  constructor(byteCount) {
    super();
    this.byteCount = byteCount;
  }
}
exports.NotEnoughDataError = NotEnoughDataError;
function readUInt8(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 1) {
    throw new NotEnoughDataError(offset + 1);
  }
  return new Result(buf.readUInt8(offset), offset + 1);
}
function readUInt16LE(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 2) {
    throw new NotEnoughDataError(offset + 2);
  }
  return new Result(buf.readUInt16LE(offset), offset + 2);
}
function readInt16LE(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 2) {
    throw new NotEnoughDataError(offset + 2);
  }
  return new Result(buf.readInt16LE(offset), offset + 2);
}
function readUInt24LE(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 3) {
    throw new NotEnoughDataError(offset + 3);
  }
  return new Result(buf.readUIntLE(offset, 3), offset + 3);
}
function readUInt32LE(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 4) {
    throw new NotEnoughDataError(offset + 4);
  }
  return new Result(buf.readUInt32LE(offset), offset + 4);
}
function readUInt32BE(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 4) {
    throw new NotEnoughDataError(offset + 4);
  }
  return new Result(buf.readUInt32BE(offset), offset + 4);
}
function readUInt40LE(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 5) {
    throw new NotEnoughDataError(offset + 5);
  }
  return new Result(buf.readUIntLE(offset, 5), offset + 5);
}
function readInt32LE(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 4) {
    throw new NotEnoughDataError(offset + 4);
  }
  return new Result(buf.readInt32LE(offset), offset + 4);
}
function readBigUInt64LE(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 8) {
    throw new NotEnoughDataError(offset + 8);
  }
  return new Result(buf.readBigUInt64LE(offset), offset + 8);
}
function readBigInt64LE(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 8) {
    throw new NotEnoughDataError(offset + 8);
  }
  return new Result(buf.readBigInt64LE(offset), offset + 8);
}
function readFloatLE(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 4) {
    throw new NotEnoughDataError(offset + 4);
  }
  return new Result(buf.readFloatLE(offset), offset + 4);
}
function readDoubleLE(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 8) {
    throw new NotEnoughDataError(offset + 8);
  }
  return new Result(buf.readDoubleLE(offset), offset + 8);
}
function readBVarChar(buf, offset) {
  offset = +offset;
  let charCount;
  ({
    offset,
    value: charCount
  } = readUInt8(buf, offset));
  const byteLength = charCount * 2;
  if (buf.length < offset + byteLength) {
    throw new NotEnoughDataError(offset + byteLength);
  }
  return new Result(buf.toString('ucs2', offset, offset + byteLength), offset + byteLength);
}
function readBVarByte(buf, offset) {
  offset = +offset;
  let byteLength;
  ({
    offset,
    value: byteLength
  } = readUInt8(buf, offset));
  if (buf.length < offset + byteLength) {
    throw new NotEnoughDataError(offset + byteLength);
  }
  return new Result(buf.slice(offset, offset + byteLength), offset + byteLength);
}
function readUsVarChar(buf, offset) {
  offset = +offset;
  let charCount;
  ({
    offset,
    value: charCount
  } = readUInt16LE(buf, offset));
  const byteLength = charCount * 2;
  if (buf.length < offset + byteLength) {
    throw new NotEnoughDataError(offset + byteLength);
  }
  return new Result(buf.toString('ucs2', offset, offset + byteLength), offset + byteLength);
}
function readUsVarByte(buf, offset) {
  offset = +offset;
  let byteLength;
  ({
    offset,
    value: byteLength
  } = readUInt16LE(buf, offset));
  if (buf.length < offset + byteLength) {
    throw new NotEnoughDataError(offset + byteLength);
  }
  return new Result(buf.slice(offset, offset + byteLength), offset + byteLength);
}
function readUNumeric64LE(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 8) {
    throw new NotEnoughDataError(offset + 8);
  }
  const low = buf.readUInt32LE(offset);
  const high = buf.readUInt32LE(offset + 4);
  return new Result(0x100000000 * high + low, offset + 8);
}
function readUNumeric96LE(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 12) {
    throw new NotEnoughDataError(offset + 12);
  }
  const dword1 = buf.readUInt32LE(offset);
  const dword2 = buf.readUInt32LE(offset + 4);
  const dword3 = buf.readUInt32LE(offset + 8);
  return new Result(dword1 + 0x100000000 * dword2 + 0x100000000 * 0x100000000 * dword3, offset + 12);
}
function readUNumeric128LE(buf, offset) {
  offset = +offset;
  if (buf.length < offset + 16) {
    throw new NotEnoughDataError(offset + 16);
  }
  const dword1 = buf.readUInt32LE(offset);
  const dword2 = buf.readUInt32LE(offset + 4);
  const dword3 = buf.readUInt32LE(offset + 8);
  const dword4 = buf.readUInt32LE(offset + 12);
  return new Result(dword1 + 0x100000000 * dword2 + 0x100000000 * 0x100000000 * dword3 + 0x100000000 * 0x100000000 * 0x100000000 * dword4, offset + 16);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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