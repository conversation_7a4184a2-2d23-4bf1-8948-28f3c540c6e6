"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.errorParser = errorParser;
exports.infoParser = infoParser;
var _helpers = require("./helpers");
var _token = require("./token");
function readToken(buf, offset, options) {
  let tokenLength;
  ({
    offset,
    value: tokenLength
  } = (0, _helpers.readUInt16LE)(buf, offset));
  if (buf.length < tokenLength + offset) {
    throw new _helpers.NotEnoughDataError(tokenLength + offset);
  }
  let number;
  ({
    offset,
    value: number
  } = (0, _helpers.readUInt32LE)(buf, offset));
  let state;
  ({
    offset,
    value: state
  } = (0, _helpers.readUInt8)(buf, offset));
  let clazz;
  ({
    offset,
    value: clazz
  } = (0, _helpers.readUInt8)(buf, offset));
  let message;
  ({
    offset,
    value: message
  } = (0, _helpers.readUsVarChar)(buf, offset));
  let serverName;
  ({
    offset,
    value: serverName
  } = (0, _helpers.readBVarChar)(buf, offset));
  let procName;
  ({
    offset,
    value: procName
  } = (0, _helpers.readBVarChar)(buf, offset));
  let lineNumber;
  ({
    offset,
    value: lineNumber
  } = options.tdsVersion < '7_2' ? (0, _helpers.readUInt16LE)(buf, offset) : (0, _helpers.readUInt32LE)(buf, offset));
  return new _helpers.Result({
    'number': number,
    'state': state,
    'class': clazz,
    'message': message,
    'serverName': serverName,
    'procName': procName,
    'lineNumber': lineNumber
  }, offset);
}
function infoParser(buf, offset, options) {
  let data;
  ({
    offset,
    value: data
  } = readToken(buf, offset, options));
  return new _helpers.Result(new _token.InfoMessageToken(data), offset);
}
function errorParser(buf, offset, options) {
  let data;
  ({
    offset,
    value: data
  } = readToken(buf, offset, options));
  return new _helpers.Result(new _token.ErrorMessageToken(data), offset);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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