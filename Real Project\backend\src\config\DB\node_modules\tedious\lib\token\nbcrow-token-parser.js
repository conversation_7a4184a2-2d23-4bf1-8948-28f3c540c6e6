"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _token = require("./token");
var iconv = _interopRequireWildcard(require("iconv-lite"));
var _valueParser = require("../value-parser");
var _helpers = require("./helpers");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
// s2.2.7.13 (introduced in TDS 7.3.B)

async function nbcRowParser(parser) {
  const colMetadata = parser.colMetadata;
  const columns = [];
  const bitmap = [];
  const bitmapByteLength = Math.ceil(colMetadata.length / 8);
  while (parser.buffer.length - parser.position < bitmapByteLength) {
    await parser.waitForChunk();
  }
  const bytes = parser.buffer.slice(parser.position, parser.position + bitmapByteLength);
  parser.position += bitmapByteLength;
  for (let i = 0, len = bytes.length; i < len; i++) {
    const byte = bytes[i];
    bitmap.push(byte & 0b1 ? true : false);
    bitmap.push(byte & 0b10 ? true : false);
    bitmap.push(byte & 0b100 ? true : false);
    bitmap.push(byte & 0b1000 ? true : false);
    bitmap.push(byte & 0b10000 ? true : false);
    bitmap.push(byte & 0b100000 ? true : false);
    bitmap.push(byte & 0b1000000 ? true : false);
    bitmap.push(byte & 0b10000000 ? true : false);
  }
  for (let i = 0; i < colMetadata.length; i++) {
    const metadata = colMetadata[i];
    if (bitmap[i]) {
      columns.push({
        value: null,
        metadata
      });
      continue;
    }
    while (true) {
      if ((0, _valueParser.isPLPStream)(metadata)) {
        const chunks = await (0, _valueParser.readPLPStream)(parser);
        if (chunks === null) {
          columns.push({
            value: chunks,
            metadata
          });
        } else if (metadata.type.name === 'NVarChar' || metadata.type.name === 'Xml') {
          columns.push({
            value: Buffer.concat(chunks).toString('ucs2'),
            metadata
          });
        } else if (metadata.type.name === 'VarChar') {
          columns.push({
            value: iconv.decode(Buffer.concat(chunks), metadata.collation?.codepage ?? 'utf8'),
            metadata
          });
        } else if (metadata.type.name === 'VarBinary' || metadata.type.name === 'UDT') {
          columns.push({
            value: Buffer.concat(chunks),
            metadata
          });
        }
      } else {
        let result;
        try {
          result = (0, _valueParser.readValue)(parser.buffer, parser.position, metadata, parser.options);
        } catch (err) {
          if (err instanceof _helpers.NotEnoughDataError) {
            await parser.waitForChunk();
            continue;
          }
          throw err;
        }
        parser.position = result.offset;
        columns.push({
          value: result.value,
          metadata
        });
      }
      break;
    }
  }
  if (parser.options.useColumnNames) {
    const columnsMap = Object.create(null);
    columns.forEach(column => {
      const colName = column.metadata.colName;
      if (columnsMap[colName] == null) {
        columnsMap[colName] = column;
      }
    });
    return new _token.NBCRowToken(columnsMap);
  } else {
    return new _token.NBCRowToken(columns);
  }
}
var _default = exports.default = nbcRowParser;
module.exports = nbcRowParser;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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