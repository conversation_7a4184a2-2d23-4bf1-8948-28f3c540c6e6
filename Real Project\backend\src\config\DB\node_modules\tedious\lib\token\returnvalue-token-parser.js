"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _token = require("./token");
var _metadataParser = require("../metadata-parser");
var _valueParser = require("../value-parser");
var _helpers = require("./helpers");
var iconv = _interopRequireWildcard(require("iconv-lite"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
// s2.2.7.16

async function returnParser(parser) {
  let paramName;
  let paramOrdinal;
  let metadata;
  while (true) {
    const buf = parser.buffer;
    let offset = parser.position;
    try {
      ({
        offset,
        value: paramOrdinal
      } = (0, _helpers.readUInt16LE)(buf, offset));
      ({
        offset,
        value: paramName
      } = (0, _helpers.readBVarChar)(buf, offset));
      // status
      ({
        offset
      } = (0, _helpers.readUInt8)(buf, offset));
      ({
        offset,
        value: metadata
      } = (0, _metadataParser.readMetadata)(buf, offset, parser.options));
      if (paramName.charAt(0) === '@') {
        paramName = paramName.slice(1);
      }
    } catch (err) {
      if (err instanceof _helpers.NotEnoughDataError) {
        await parser.waitForChunk();
        continue;
      }
      throw err;
    }
    parser.position = offset;
    break;
  }
  let value;
  while (true) {
    const buf = parser.buffer;
    let offset = parser.position;
    if ((0, _valueParser.isPLPStream)(metadata)) {
      const chunks = await (0, _valueParser.readPLPStream)(parser);
      if (chunks === null) {
        value = chunks;
      } else if (metadata.type.name === 'NVarChar' || metadata.type.name === 'Xml') {
        value = Buffer.concat(chunks).toString('ucs2');
      } else if (metadata.type.name === 'VarChar') {
        value = iconv.decode(Buffer.concat(chunks), metadata.collation?.codepage ?? 'utf8');
      } else if (metadata.type.name === 'VarBinary' || metadata.type.name === 'UDT') {
        value = Buffer.concat(chunks);
      }
    } else {
      try {
        ({
          value,
          offset
        } = (0, _valueParser.readValue)(buf, offset, metadata, parser.options));
      } catch (err) {
        if (err instanceof _helpers.NotEnoughDataError) {
          await parser.waitForChunk();
          continue;
        }
        throw err;
      }
      parser.position = offset;
    }
    break;
  }
  return new _token.ReturnValueToken({
    paramOrdinal: paramOrdinal,
    paramName: paramName,
    metadata: metadata,
    value: value
  });
}
var _default = exports.default = returnParser;
module.exports = returnParser;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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