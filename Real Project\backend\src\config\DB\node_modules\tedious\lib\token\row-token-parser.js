"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _token = require("./token");
var iconv = _interopRequireWildcard(require("iconv-lite"));
var _valueParser = require("../value-parser");
var _helpers = require("./helpers");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
// s2.2.7.17

async function rowParser(parser) {
  const columns = [];
  for (const metadata of parser.colMetadata) {
    while (true) {
      if ((0, _valueParser.isPLPStream)(metadata)) {
        const chunks = await (0, _valueParser.readPLPStream)(parser);
        if (chunks === null) {
          columns.push({
            value: chunks,
            metadata
          });
        } else if (metadata.type.name === 'NVarChar' || metadata.type.name === 'Xml') {
          columns.push({
            value: Buffer.concat(chunks).toString('ucs2'),
            metadata
          });
        } else if (metadata.type.name === 'VarChar') {
          columns.push({
            value: iconv.decode(Buffer.concat(chunks), metadata.collation?.codepage ?? 'utf8'),
            metadata
          });
        } else if (metadata.type.name === 'VarBinary' || metadata.type.name === 'UDT') {
          columns.push({
            value: Buffer.concat(chunks),
            metadata
          });
        }
      } else {
        let result;
        try {
          result = (0, _valueParser.readValue)(parser.buffer, parser.position, metadata, parser.options);
        } catch (err) {
          if (err instanceof _helpers.NotEnoughDataError) {
            await parser.waitForChunk();
            continue;
          }
          throw err;
        }
        parser.position = result.offset;
        columns.push({
          value: result.value,
          metadata
        });
      }
      break;
    }
  }
  if (parser.options.useColumnNames) {
    const columnsMap = Object.create(null);
    columns.forEach(column => {
      const colName = column.metadata.colName;
      if (columnsMap[colName] == null) {
        columnsMap[colName] = column;
      }
    });
    return new _token.RowToken(columnsMap);
  } else {
    return new _token.RowToken(columns);
  }
}
var _default = exports.default = rowParser;
module.exports = rowParser;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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