{"version": 3, "file": "row-token-parser.js", "names": ["_token", "require", "iconv", "_interopRequireWildcard", "_valueParser", "_helpers", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "<PERSON><PERSON><PERSON><PERSON>", "parser", "columns", "metadata", "colMetadata", "isPLPStream", "chunks", "readPLPStream", "push", "value", "type", "name", "<PERSON><PERSON><PERSON>", "concat", "toString", "decode", "collation", "codepage", "result", "readValue", "buffer", "position", "options", "err", "NotEnoughDataError", "waitForChunk", "offset", "useColumnNames", "columnsMap", "create", "for<PERSON>ach", "column", "colName", "RowToken", "_default", "exports", "module"], "sources": ["../../src/token/row-token-parser.ts"], "sourcesContent": ["// s2.2.7.17\n\nimport Parser from './stream-parser';\nimport { type ColumnMetadata } from './colmetadata-token-parser';\n\nimport { RowToken } from './token';\nimport * as iconv from 'iconv-lite';\n\nimport { isPLPStream, readPLPStream, readValue } from '../value-parser';\nimport { NotEnoughDataError } from './helpers';\n\ninterface Column {\n  value: unknown;\n  metadata: ColumnMetadata;\n}\n\nasync function rowParser(parser: Parser): Promise<RowToken> {\n  const columns: Column[] = [];\n\n  for (const metadata of parser.colMetadata) {\n    while (true) {\n      if (isPLPStream(metadata)) {\n        const chunks = await readPLPStream(parser);\n\n        if (chunks === null) {\n          columns.push({ value: chunks, metadata });\n        } else if (metadata.type.name === 'NVarChar' || metadata.type.name === 'Xml') {\n          columns.push({ value: Buffer.concat(chunks).toString('ucs2'), metadata });\n        } else if (metadata.type.name === 'VarChar') {\n          columns.push({ value: iconv.decode(Buffer.concat(chunks), metadata.collation?.codepage ?? 'utf8'), metadata });\n        } else if (metadata.type.name === 'VarBinary' || metadata.type.name === 'UDT') {\n          columns.push({ value: Buffer.concat(chunks), metadata });\n        }\n      } else {\n        let result;\n        try {\n          result = readValue(parser.buffer, parser.position, metadata, parser.options);\n        } catch (err) {\n          if (err instanceof NotEnoughDataError) {\n            await parser.waitForChunk();\n            continue;\n          }\n\n          throw err;\n        }\n\n        parser.position = result.offset;\n        columns.push({ value: result.value, metadata });\n      }\n\n      break;\n    }\n  }\n\n  if (parser.options.useColumnNames) {\n    const columnsMap: { [key: string]: Column } = Object.create(null);\n\n    columns.forEach((column) => {\n      const colName = column.metadata.colName;\n      if (columnsMap[colName] == null) {\n        columnsMap[colName] = column;\n      }\n    });\n\n    return new RowToken(columnsMap);\n  } else {\n    return new RowToken(columns);\n  }\n}\n\nexport default rowParser;\nmodule.exports = rowParser;\n"], "mappings": ";;;;;;AAKA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,uBAAA,CAAAF,OAAA;AAEA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AAA+C,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAT/C;;AAgBA,eAAeY,SAASA,CAACC,MAAc,EAAqB;EAC1D,MAAMC,OAAiB,GAAG,EAAE;EAE5B,KAAK,MAAMC,QAAQ,IAAIF,MAAM,CAACG,WAAW,EAAE;IACzC,OAAO,IAAI,EAAE;MACX,IAAI,IAAAC,wBAAW,EAACF,QAAQ,CAAC,EAAE;QACzB,MAAMG,MAAM,GAAG,MAAM,IAAAC,0BAAa,EAACN,MAAM,CAAC;QAE1C,IAAIK,MAAM,KAAK,IAAI,EAAE;UACnBJ,OAAO,CAACM,IAAI,CAAC;YAAEC,KAAK,EAAEH,MAAM;YAAEH;UAAS,CAAC,CAAC;QAC3C,CAAC,MAAM,IAAIA,QAAQ,CAACO,IAAI,CAACC,IAAI,KAAK,UAAU,IAAIR,QAAQ,CAACO,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;UAC5ET,OAAO,CAACM,IAAI,CAAC;YAAEC,KAAK,EAAEG,MAAM,CAACC,MAAM,CAACP,MAAM,CAAC,CAACQ,QAAQ,CAAC,MAAM,CAAC;YAAEX;UAAS,CAAC,CAAC;QAC3E,CAAC,MAAM,IAAIA,QAAQ,CAACO,IAAI,CAACC,IAAI,KAAK,SAAS,EAAE;UAC3CT,OAAO,CAACM,IAAI,CAAC;YAAEC,KAAK,EAAElC,KAAK,CAACwC,MAAM,CAACH,MAAM,CAACC,MAAM,CAACP,MAAM,CAAC,EAAEH,QAAQ,CAACa,SAAS,EAAEC,QAAQ,IAAI,MAAM,CAAC;YAAEd;UAAS,CAAC,CAAC;QAChH,CAAC,MAAM,IAAIA,QAAQ,CAACO,IAAI,CAACC,IAAI,KAAK,WAAW,IAAIR,QAAQ,CAACO,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;UAC7ET,OAAO,CAACM,IAAI,CAAC;YAAEC,KAAK,EAAEG,MAAM,CAACC,MAAM,CAACP,MAAM,CAAC;YAAEH;UAAS,CAAC,CAAC;QAC1D;MACF,CAAC,MAAM;QACL,IAAIe,MAAM;QACV,IAAI;UACFA,MAAM,GAAG,IAAAC,sBAAS,EAAClB,MAAM,CAACmB,MAAM,EAAEnB,MAAM,CAACoB,QAAQ,EAAElB,QAAQ,EAAEF,MAAM,CAACqB,OAAO,CAAC;QAC9E,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZ,IAAIA,GAAG,YAAYC,2BAAkB,EAAE;YACrC,MAAMvB,MAAM,CAACwB,YAAY,CAAC,CAAC;YAC3B;UACF;UAEA,MAAMF,GAAG;QACX;QAEAtB,MAAM,CAACoB,QAAQ,GAAGH,MAAM,CAACQ,MAAM;QAC/BxB,OAAO,CAACM,IAAI,CAAC;UAAEC,KAAK,EAAES,MAAM,CAACT,KAAK;UAAEN;QAAS,CAAC,CAAC;MACjD;MAEA;IACF;EACF;EAEA,IAAIF,MAAM,CAACqB,OAAO,CAACK,cAAc,EAAE;IACjC,MAAMC,UAAqC,GAAGrC,MAAM,CAACsC,MAAM,CAAC,IAAI,CAAC;IAEjE3B,OAAO,CAAC4B,OAAO,CAAEC,MAAM,IAAK;MAC1B,MAAMC,OAAO,GAAGD,MAAM,CAAC5B,QAAQ,CAAC6B,OAAO;MACvC,IAAIJ,UAAU,CAACI,OAAO,CAAC,IAAI,IAAI,EAAE;QAC/BJ,UAAU,CAACI,OAAO,CAAC,GAAGD,MAAM;MAC9B;IACF,CAAC,CAAC;IAEF,OAAO,IAAIE,eAAQ,CAACL,UAAU,CAAC;EACjC,CAAC,MAAM;IACL,OAAO,IAAIK,eAAQ,CAAC/B,OAAO,CAAC;EAC9B;AACF;AAAC,IAAAgC,QAAA,GAAAC,OAAA,CAAAlD,OAAA,GAEce,SAAS;AACxBoC,MAAM,CAACD,OAAO,GAAGnC,SAAS"}