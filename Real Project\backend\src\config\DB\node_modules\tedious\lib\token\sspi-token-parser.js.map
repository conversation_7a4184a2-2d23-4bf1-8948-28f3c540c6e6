{"version": 3, "file": "sspi-token-parser.js", "names": ["_helpers", "require", "_token", "parseChallenge", "buffer", "challenge", "magic", "slice", "toString", "type", "readInt32LE", "domainLen", "readInt16LE", "domainMax", "domainOffset", "flags", "nonce", "zeroes", "targetLen", "targetMax", "targetOffset", "oddData", "domain", "target", "s<PERSON>i<PERSON><PERSON><PERSON>", "buf", "offset", "_options", "token<PERSON><PERSON>th", "value", "readUInt16LE", "length", "NotEnoughDataError", "data", "Result", "SSPIToken", "_default", "exports", "default", "module"], "sources": ["../../src/token/sspi-token-parser.ts"], "sourcesContent": ["import { NotEnoughDataError, readUInt16LE, Result } from './helpers';\nimport { type ParserOptions } from './stream-parser';\n\nimport { SSPIToken } from './token';\n\ninterface Data {\n  magic: string;\n  type: number;\n  domainLen: number;\n  domainMax: number;\n  domainOffset: number;\n  flags: number;\n  nonce: Buffer;\n  zeroes: Buffer;\n  targetLen: number;\n  targetMax: number;\n  targetOffset: number;\n  oddData: Buffer;\n  domain: string;\n  target: Buffer;\n}\n\nfunction parseChallenge(buffer: Buffer) {\n  const challenge: Partial<Data> = {};\n\n  challenge.magic = buffer.slice(0, 8).toString('utf8');\n  challenge.type = buffer.readInt32LE(8);\n  challenge.domainLen = buffer.readInt16LE(12);\n  challenge.domainMax = buffer.readInt16LE(14);\n  challenge.domainOffset = buffer.readInt32LE(16);\n  challenge.flags = buffer.readInt32LE(20);\n  challenge.nonce = buffer.slice(24, 32);\n  challenge.zeroes = buffer.slice(32, 40);\n  challenge.targetLen = buffer.readInt16LE(40);\n  challenge.targetMax = buffer.readInt16LE(42);\n  challenge.targetOffset = buffer.readInt32LE(44);\n  challenge.oddData = buffer.slice(48, 56);\n  challenge.domain = buffer.slice(56, 56 + challenge.domainLen).toString('ucs2');\n  challenge.target = buffer.slice(56 + challenge.domainLen, 56 + challenge.domainLen + challenge.targetLen);\n\n  return challenge as Data;\n}\n\nfunction sspiParser(buf: Buffer, offset: number, _options: ParserOptions): Result<SSPIToken> {\n  let tokenLength;\n  ({ offset, value: tokenLength } = readUInt16LE(buf, offset));\n\n  if (buf.length < offset + tokenLength) {\n    throw new NotEnoughDataError(offset + tokenLength);\n  }\n\n  const data = buf.slice(offset, offset + tokenLength);\n  offset += tokenLength;\n\n  return new Result(new SSPIToken(parseChallenge(data), data), offset);\n}\n\nexport default sspiParser;\nmodule.exports = sspiParser;\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAGA,IAAAC,MAAA,GAAAD,OAAA;AAmBA,SAASE,cAAcA,CAACC,MAAc,EAAE;EACtC,MAAMC,SAAwB,GAAG,CAAC,CAAC;EAEnCA,SAAS,CAACC,KAAK,GAAGF,MAAM,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC;EACrDH,SAAS,CAACI,IAAI,GAAGL,MAAM,CAACM,WAAW,CAAC,CAAC,CAAC;EACtCL,SAAS,CAACM,SAAS,GAAGP,MAAM,CAACQ,WAAW,CAAC,EAAE,CAAC;EAC5CP,SAAS,CAACQ,SAAS,GAAGT,MAAM,CAACQ,WAAW,CAAC,EAAE,CAAC;EAC5CP,SAAS,CAACS,YAAY,GAAGV,MAAM,CAACM,WAAW,CAAC,EAAE,CAAC;EAC/CL,SAAS,CAACU,KAAK,GAAGX,MAAM,CAACM,WAAW,CAAC,EAAE,CAAC;EACxCL,SAAS,CAACW,KAAK,GAAGZ,MAAM,CAACG,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;EACtCF,SAAS,CAACY,MAAM,GAAGb,MAAM,CAACG,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;EACvCF,SAAS,CAACa,SAAS,GAAGd,MAAM,CAACQ,WAAW,CAAC,EAAE,CAAC;EAC5CP,SAAS,CAACc,SAAS,GAAGf,MAAM,CAACQ,WAAW,CAAC,EAAE,CAAC;EAC5CP,SAAS,CAACe,YAAY,GAAGhB,MAAM,CAACM,WAAW,CAAC,EAAE,CAAC;EAC/CL,SAAS,CAACgB,OAAO,GAAGjB,MAAM,CAACG,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;EACxCF,SAAS,CAACiB,MAAM,GAAGlB,MAAM,CAACG,KAAK,CAAC,EAAE,EAAE,EAAE,GAAGF,SAAS,CAACM,SAAS,CAAC,CAACH,QAAQ,CAAC,MAAM,CAAC;EAC9EH,SAAS,CAACkB,MAAM,GAAGnB,MAAM,CAACG,KAAK,CAAC,EAAE,GAAGF,SAAS,CAACM,SAAS,EAAE,EAAE,GAAGN,SAAS,CAACM,SAAS,GAAGN,SAAS,CAACa,SAAS,CAAC;EAEzG,OAAOb,SAAS;AAClB;AAEA,SAASmB,UAAUA,CAACC,GAAW,EAAEC,MAAc,EAAEC,QAAuB,EAAqB;EAC3F,IAAIC,WAAW;EACf,CAAC;IAAEF,MAAM;IAAEG,KAAK,EAAED;EAAY,CAAC,GAAG,IAAAE,qBAAY,EAACL,GAAG,EAAEC,MAAM,CAAC;EAE3D,IAAID,GAAG,CAACM,MAAM,GAAGL,MAAM,GAAGE,WAAW,EAAE;IACrC,MAAM,IAAII,2BAAkB,CAACN,MAAM,GAAGE,WAAW,CAAC;EACpD;EAEA,MAAMK,IAAI,GAAGR,GAAG,CAAClB,KAAK,CAACmB,MAAM,EAAEA,MAAM,GAAGE,WAAW,CAAC;EACpDF,MAAM,IAAIE,WAAW;EAErB,OAAO,IAAIM,eAAM,CAAC,IAAIC,gBAAS,CAAChC,cAAc,CAAC8B,IAAI,CAAC,EAAEA,IAAI,CAAC,EAAEP,MAAM,CAAC;AACtE;AAAC,IAAAU,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcd,UAAU;AACzBe,MAAM,CAACF,OAAO,GAAGb,UAAU"}