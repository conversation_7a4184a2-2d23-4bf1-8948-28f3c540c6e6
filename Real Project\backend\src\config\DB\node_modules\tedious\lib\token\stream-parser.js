"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _token = require("./token");
var _colmetadataTokenParser = _interopRequireDefault(require("./colmetadata-token-parser"));
var _doneTokenParser = require("./done-token-parser");
var _envChangeTokenParser = _interopRequireDefault(require("./env-change-token-parser"));
var _infoerrorTokenParser = require("./infoerror-token-parser");
var _fedauthInfoParser = _interopRequireDefault(require("./fedauth-info-parser"));
var _featureExtAckParser = _interopRequireDefault(require("./feature-ext-ack-parser"));
var _loginackTokenParser = _interopRequireDefault(require("./loginack-token-parser"));
var _orderTokenParser = _interopRequireDefault(require("./order-token-parser"));
var _returnstatusTokenParser = _interopRequireDefault(require("./returnstatus-token-parser"));
var _returnvalueTokenParser = _interopRequireDefault(require("./returnvalue-token-parser"));
var _rowTokenParser = _interopRequireDefault(require("./row-token-parser"));
var _nbcrowTokenParser = _interopRequireDefault(require("./nbcrow-token-parser"));
var _sspiTokenParser = _interopRequireDefault(require("./sspi-token-parser"));
var _helpers = require("./helpers");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Parser {
  debug;
  colMetadata;
  options;
  iterator;
  buffer;
  position;
  static async *parseTokens(iterable, debug, options, colMetadata = []) {
    const parser = new Parser(iterable, debug, options);
    parser.colMetadata = colMetadata;
    while (true) {
      try {
        await parser.waitForChunk();
      } catch (err) {
        if (parser.position === parser.buffer.length) {
          return;
        }
        throw err;
      }
      while (parser.buffer.length >= parser.position + 1) {
        const type = parser.buffer.readUInt8(parser.position);
        parser.position += 1;
        const token = parser.readToken(type);
        if (token !== undefined) {
          yield token;
        }
      }
    }
  }
  readToken(type) {
    switch (type) {
      case _token.TYPE.DONE:
        {
          return this.readDoneToken();
        }
      case _token.TYPE.DONEPROC:
        {
          return this.readDoneProcToken();
        }
      case _token.TYPE.DONEINPROC:
        {
          return this.readDoneInProcToken();
        }
      case _token.TYPE.ERROR:
        {
          return this.readErrorToken();
        }
      case _token.TYPE.INFO:
        {
          return this.readInfoToken();
        }
      case _token.TYPE.ENVCHANGE:
        {
          return this.readEnvChangeToken();
        }
      case _token.TYPE.LOGINACK:
        {
          return this.readLoginAckToken();
        }
      case _token.TYPE.RETURNSTATUS:
        {
          return this.readReturnStatusToken();
        }
      case _token.TYPE.ORDER:
        {
          return this.readOrderToken();
        }
      case _token.TYPE.FEDAUTHINFO:
        {
          return this.readFedAuthInfoToken();
        }
      case _token.TYPE.SSPI:
        {
          return this.readSSPIToken();
        }
      case _token.TYPE.COLMETADATA:
        {
          return this.readColMetadataToken();
        }
      case _token.TYPE.RETURNVALUE:
        {
          return this.readReturnValueToken();
        }
      case _token.TYPE.ROW:
        {
          return this.readRowToken();
        }
      case _token.TYPE.NBCROW:
        {
          return this.readNbcRowToken();
        }
      case _token.TYPE.FEATUREEXTACK:
        {
          return this.readFeatureExtAckToken();
        }
      default:
        {
          throw new Error('Unknown type: ' + type);
        }
    }
  }
  readFeatureExtAckToken() {
    let result;
    try {
      result = (0, _featureExtAckParser.default)(this.buffer, this.position, this.options);
    } catch (err) {
      if (err instanceof _helpers.NotEnoughDataError) {
        return this.waitForChunk().then(() => {
          return this.readFeatureExtAckToken();
        });
      }
      throw err;
    }
    this.position = result.offset;
    return result.value;
  }
  async readNbcRowToken() {
    return await (0, _nbcrowTokenParser.default)(this);
  }
  async readReturnValueToken() {
    return await (0, _returnvalueTokenParser.default)(this);
  }
  async readColMetadataToken() {
    const token = await (0, _colmetadataTokenParser.default)(this);
    this.colMetadata = token.columns;
    return token;
  }
  readSSPIToken() {
    let result;
    try {
      result = (0, _sspiTokenParser.default)(this.buffer, this.position, this.options);
    } catch (err) {
      if (err instanceof _helpers.NotEnoughDataError) {
        return this.waitForChunk().then(() => {
          return this.readSSPIToken();
        });
      }
      throw err;
    }
    this.position = result.offset;
    return result.value;
  }
  readFedAuthInfoToken() {
    let result;
    try {
      result = (0, _fedauthInfoParser.default)(this.buffer, this.position, this.options);
    } catch (err) {
      if (err instanceof _helpers.NotEnoughDataError) {
        return this.waitForChunk().then(() => {
          return this.readFedAuthInfoToken();
        });
      }
      throw err;
    }
    this.position = result.offset;
    return result.value;
  }
  readOrderToken() {
    let result;
    try {
      result = (0, _orderTokenParser.default)(this.buffer, this.position, this.options);
    } catch (err) {
      if (err instanceof _helpers.NotEnoughDataError) {
        return this.waitForChunk().then(() => {
          return this.readOrderToken();
        });
      }
      throw err;
    }
    this.position = result.offset;
    return result.value;
  }
  readReturnStatusToken() {
    let result;
    try {
      result = (0, _returnstatusTokenParser.default)(this.buffer, this.position, this.options);
    } catch (err) {
      if (err instanceof _helpers.NotEnoughDataError) {
        return this.waitForChunk().then(() => {
          return this.readReturnStatusToken();
        });
      }
      throw err;
    }
    this.position = result.offset;
    return result.value;
  }
  readLoginAckToken() {
    let result;
    try {
      result = (0, _loginackTokenParser.default)(this.buffer, this.position, this.options);
    } catch (err) {
      if (err instanceof _helpers.NotEnoughDataError) {
        return this.waitForChunk().then(() => {
          return this.readLoginAckToken();
        });
      }
      throw err;
    }
    this.position = result.offset;
    return result.value;
  }
  readEnvChangeToken() {
    let result;
    try {
      result = (0, _envChangeTokenParser.default)(this.buffer, this.position, this.options);
    } catch (err) {
      if (err instanceof _helpers.NotEnoughDataError) {
        return this.waitForChunk().then(() => {
          return this.readEnvChangeToken();
        });
      }
      throw err;
    }
    this.position = result.offset;
    return result.value;
  }
  readRowToken() {
    return (0, _rowTokenParser.default)(this);
  }
  readInfoToken() {
    let result;
    try {
      result = (0, _infoerrorTokenParser.infoParser)(this.buffer, this.position, this.options);
    } catch (err) {
      if (err instanceof _helpers.NotEnoughDataError) {
        return this.waitForChunk().then(() => {
          return this.readInfoToken();
        });
      }
      throw err;
    }
    this.position = result.offset;
    return result.value;
  }
  readErrorToken() {
    let result;
    try {
      result = (0, _infoerrorTokenParser.errorParser)(this.buffer, this.position, this.options);
    } catch (err) {
      if (err instanceof _helpers.NotEnoughDataError) {
        return this.waitForChunk().then(() => {
          return this.readErrorToken();
        });
      }
      throw err;
    }
    this.position = result.offset;
    return result.value;
  }
  readDoneInProcToken() {
    let result;
    try {
      result = (0, _doneTokenParser.doneInProcParser)(this.buffer, this.position, this.options);
    } catch (err) {
      if (err instanceof _helpers.NotEnoughDataError) {
        return this.waitForChunk().then(() => {
          return this.readDoneInProcToken();
        });
      }
      throw err;
    }
    this.position = result.offset;
    return result.value;
  }
  readDoneProcToken() {
    let result;
    try {
      result = (0, _doneTokenParser.doneProcParser)(this.buffer, this.position, this.options);
    } catch (err) {
      if (err instanceof _helpers.NotEnoughDataError) {
        return this.waitForChunk().then(() => {
          return this.readDoneProcToken();
        });
      }
      throw err;
    }
    this.position = result.offset;
    return result.value;
  }
  readDoneToken() {
    let result;
    try {
      result = (0, _doneTokenParser.doneParser)(this.buffer, this.position, this.options);
    } catch (err) {
      if (err instanceof _helpers.NotEnoughDataError) {
        return this.waitForChunk().then(() => {
          return this.readDoneToken();
        });
      }
      throw err;
    }
    this.position = result.offset;
    return result.value;
  }
  constructor(iterable, debug, options) {
    this.debug = debug;
    this.colMetadata = [];
    this.options = options;
    this.iterator = (iterable[Symbol.asyncIterator] || iterable[Symbol.iterator]).call(iterable);
    this.buffer = Buffer.alloc(0);
    this.position = 0;
  }
  async waitForChunk() {
    const result = await this.iterator.next();
    if (result.done) {
      throw new Error('unexpected end of data');
    }
    if (this.position === this.buffer.length) {
      this.buffer = result.value;
    } else {
      this.buffer = Buffer.concat([this.buffer.slice(this.position), result.value]);
    }
    this.position = 0;
  }
}
var _default = exports.default = Parser;
module.exports = Parser;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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