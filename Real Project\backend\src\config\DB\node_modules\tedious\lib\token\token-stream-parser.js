"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Parser = void 0;
var _events = require("events");
var _streamParser = _interopRequireDefault(require("./stream-parser"));
var _stream = require("stream");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Parser extends _events.EventEmitter {
  constructor(message, debug, handler, options) {
    super();
    this.debug = debug;
    this.options = options;
    this.parser = _stream.Readable.from(_streamParser.default.parseTokens(message, this.debug, this.options));
    this.parser.on('data', token => {
      debug.token(token);
      handler[token.handlerName](token);
    });
    this.parser.on('drain', () => {
      this.emit('drain');
    });
    this.parser.on('end', () => {
      this.emit('end');
    });
  }
  pause() {
    return this.parser.pause();
  }
  resume() {
    return this.parser.resume();
  }
}
exports.Parser = Parser;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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