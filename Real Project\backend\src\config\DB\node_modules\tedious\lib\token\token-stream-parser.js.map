{"version": 3, "file": "token-stream-parser.js", "names": ["_events", "require", "_streamParser", "_interopRequireDefault", "_stream", "obj", "__esModule", "default", "<PERSON><PERSON><PERSON>", "EventEmitter", "constructor", "message", "debug", "handler", "options", "parser", "Readable", "from", "<PERSON><PERSON><PERSON><PERSON>", "parseTokens", "on", "token", "handler<PERSON>ame", "emit", "pause", "resume", "exports"], "sources": ["../../src/token/token-stream-parser.ts"], "sourcesContent": ["import { EventEmitter } from 'events';\nimport StreamParser, { type ParserOptions } from './stream-parser';\nimport Debug from '../debug';\nimport { Token } from './token';\nimport { Readable } from 'stream';\nimport Message from '../message';\nimport { To<PERSON>Handler } from './handler';\n\nexport class Parser extends EventEmitter {\n  declare debug: Debug;\n  declare options: ParserOptions;\n  declare parser: Readable;\n\n  constructor(message: Message, debug: Debug, handler: <PERSON><PERSON><PERSON><PERSON><PERSON>, options: ParserOptions) {\n    super();\n\n    this.debug = debug;\n    this.options = options;\n\n    this.parser = Readable.from(StreamParser.parseTokens(message, this.debug, this.options));\n    this.parser.on('data', (token: Token) => {\n      debug.token(token);\n      handler[token.handlerName as keyof TokenHandler](token as any);\n    });\n\n    this.parser.on('drain', () => {\n      this.emit('drain');\n    });\n\n    this.parser.on('end', () => {\n      this.emit('end');\n    });\n  }\n\n  declare on: (\n    ((event: 'end', listener: () => void) => this) &\n    ((event: string | symbol, listener: (...args: any[]) => void) => this)\n  );\n\n  pause() {\n    return this.parser.pause();\n  }\n\n  resume() {\n    return this.parser.resume();\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAC,sBAAA,CAAAF,OAAA;AAGA,IAAAG,OAAA,GAAAH,OAAA;AAAkC,SAAAE,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAI3B,MAAMG,MAAM,SAASC,oBAAY,CAAC;EAKvCC,WAAWA,CAACC,OAAgB,EAAEC,KAAY,EAAEC,OAAqB,EAAEC,OAAsB,EAAE;IACzF,KAAK,CAAC,CAAC;IAEP,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,OAAO,GAAGA,OAAO;IAEtB,IAAI,CAACC,MAAM,GAAGC,gBAAQ,CAACC,IAAI,CAACC,qBAAY,CAACC,WAAW,CAACR,OAAO,EAAE,IAAI,CAACC,KAAK,EAAE,IAAI,CAACE,OAAO,CAAC,CAAC;IACxF,IAAI,CAACC,MAAM,CAACK,EAAE,CAAC,MAAM,EAAGC,KAAY,IAAK;MACvCT,KAAK,CAACS,KAAK,CAACA,KAAK,CAAC;MAClBR,OAAO,CAACQ,KAAK,CAACC,WAAW,CAAuB,CAACD,KAAY,CAAC;IAChE,CAAC,CAAC;IAEF,IAAI,CAACN,MAAM,CAACK,EAAE,CAAC,OAAO,EAAE,MAAM;MAC5B,IAAI,CAACG,IAAI,CAAC,OAAO,CAAC;IACpB,CAAC,CAAC;IAEF,IAAI,CAACR,MAAM,CAACK,EAAE,CAAC,KAAK,EAAE,MAAM;MAC1B,IAAI,CAACG,IAAI,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC;EACJ;EAOAC,KAAKA,CAAA,EAAG;IACN,OAAO,IAAI,CAACT,MAAM,CAACS,KAAK,CAAC,CAAC;EAC5B;EAEAC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC;EAC7B;AACF;AAACC,OAAA,CAAAlB,MAAA,GAAAA,MAAA"}