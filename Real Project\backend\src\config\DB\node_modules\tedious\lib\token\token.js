"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Token = exports.TYPE = exports.SSPIToken = exports.RowToken = exports.RoutingEnvChangeToken = exports.RollbackTransactionEnvChangeToken = exports.ReturnValueToken = exports.ReturnStatusToken = exports.ResetConnectionEnvChangeToken = exports.PacketSizeEnvChangeToken = exports.OrderToken = exports.NBCRowToken = exports.LoginAckToken = exports.LanguageEnvChangeToken = exports.InfoMessageToken = exports.FedAuthInfoToken = exports.FeatureExtAckToken = exports.ErrorMessageToken = exports.DoneToken = exports.DoneProcToken = exports.DoneInProcToken = exports.DatabaseMirroringPartnerEnvChangeToken = exports.DatabaseEnvChangeToken = exports.CommitTransactionEnvChangeToken = exports.CollationChangeToken = exports.ColMetadataToken = exports.CharsetEnvChangeToken = exports.BeginTransactionEnvChangeToken = void 0;
const TYPE = exports.TYPE = {
  ALTMETADATA: 0x88,
  ALTROW: 0xD3,
  COLMETADATA: 0x81,
  COLINFO: 0xA5,
  DONE: 0xFD,
  DONEPROC: 0xFE,
  DONEINPROC: 0xFF,
  ENVCHANGE: 0xE3,
  ERROR: 0xAA,
  FEATUREEXTACK: 0xAE,
  FEDAUTHINFO: 0xEE,
  INFO: 0xAB,
  LOGINACK: 0xAD,
  NBCROW: 0xD2,
  OFFSET: 0x78,
  ORDER: 0xA9,
  RETURNSTATUS: 0x79,
  RETURNVALUE: 0xAC,
  ROW: 0xD1,
  SSPI: 0xED,
  TABNAME: 0xA4
};
class Token {
  constructor(name, handlerName) {
    this.name = name;
    this.handlerName = handlerName;
  }
}
exports.Token = Token;
class ColMetadataToken extends Token {
  constructor(columns) {
    super('COLMETADATA', 'onColMetadata');
    this.columns = columns;
  }
}
exports.ColMetadataToken = ColMetadataToken;
class DoneToken extends Token {
  constructor({
    more,
    sqlError,
    attention,
    serverError,
    rowCount,
    curCmd
  }) {
    super('DONE', 'onDone');
    this.more = more;
    this.sqlError = sqlError;
    this.attention = attention;
    this.serverError = serverError;
    this.rowCount = rowCount;
    this.curCmd = curCmd;
  }
}
exports.DoneToken = DoneToken;
class DoneInProcToken extends Token {
  constructor({
    more,
    sqlError,
    attention,
    serverError,
    rowCount,
    curCmd
  }) {
    super('DONEINPROC', 'onDoneInProc');
    this.more = more;
    this.sqlError = sqlError;
    this.attention = attention;
    this.serverError = serverError;
    this.rowCount = rowCount;
    this.curCmd = curCmd;
  }
}
exports.DoneInProcToken = DoneInProcToken;
class DoneProcToken extends Token {
  constructor({
    more,
    sqlError,
    attention,
    serverError,
    rowCount,
    curCmd
  }) {
    super('DONEPROC', 'onDoneProc');
    this.more = more;
    this.sqlError = sqlError;
    this.attention = attention;
    this.serverError = serverError;
    this.rowCount = rowCount;
    this.curCmd = curCmd;
  }
}
exports.DoneProcToken = DoneProcToken;
class DatabaseEnvChangeToken extends Token {
  constructor(newValue, oldValue) {
    super('ENVCHANGE', 'onDatabaseChange');
    this.type = 'DATABASE';
    this.newValue = newValue;
    this.oldValue = oldValue;
  }
}
exports.DatabaseEnvChangeToken = DatabaseEnvChangeToken;
class LanguageEnvChangeToken extends Token {
  constructor(newValue, oldValue) {
    super('ENVCHANGE', 'onLanguageChange');
    this.type = 'LANGUAGE';
    this.newValue = newValue;
    this.oldValue = oldValue;
  }
}
exports.LanguageEnvChangeToken = LanguageEnvChangeToken;
class CharsetEnvChangeToken extends Token {
  constructor(newValue, oldValue) {
    super('ENVCHANGE', 'onCharsetChange');
    this.type = 'CHARSET';
    this.newValue = newValue;
    this.oldValue = oldValue;
  }
}
exports.CharsetEnvChangeToken = CharsetEnvChangeToken;
class PacketSizeEnvChangeToken extends Token {
  constructor(newValue, oldValue) {
    super('ENVCHANGE', 'onPacketSizeChange');
    this.type = 'PACKET_SIZE';
    this.newValue = newValue;
    this.oldValue = oldValue;
  }
}
exports.PacketSizeEnvChangeToken = PacketSizeEnvChangeToken;
class BeginTransactionEnvChangeToken extends Token {
  constructor(newValue, oldValue) {
    super('ENVCHANGE', 'onBeginTransaction');
    this.type = 'BEGIN_TXN';
    this.newValue = newValue;
    this.oldValue = oldValue;
  }
}
exports.BeginTransactionEnvChangeToken = BeginTransactionEnvChangeToken;
class CommitTransactionEnvChangeToken extends Token {
  constructor(newValue, oldValue) {
    super('ENVCHANGE', 'onCommitTransaction');
    this.type = 'COMMIT_TXN';
    this.newValue = newValue;
    this.oldValue = oldValue;
  }
}
exports.CommitTransactionEnvChangeToken = CommitTransactionEnvChangeToken;
class RollbackTransactionEnvChangeToken extends Token {
  constructor(newValue, oldValue) {
    super('ENVCHANGE', 'onRollbackTransaction');
    this.type = 'ROLLBACK_TXN';
    this.newValue = newValue;
    this.oldValue = oldValue;
  }
}
exports.RollbackTransactionEnvChangeToken = RollbackTransactionEnvChangeToken;
class DatabaseMirroringPartnerEnvChangeToken extends Token {
  constructor(newValue, oldValue) {
    super('ENVCHANGE', 'onDatabaseMirroringPartner');
    this.type = 'DATABASE_MIRRORING_PARTNER';
    this.newValue = newValue;
    this.oldValue = oldValue;
  }
}
exports.DatabaseMirroringPartnerEnvChangeToken = DatabaseMirroringPartnerEnvChangeToken;
class ResetConnectionEnvChangeToken extends Token {
  constructor(newValue, oldValue) {
    super('ENVCHANGE', 'onResetConnection');
    this.type = 'RESET_CONNECTION';
    this.newValue = newValue;
    this.oldValue = oldValue;
  }
}
exports.ResetConnectionEnvChangeToken = ResetConnectionEnvChangeToken;
class CollationChangeToken extends Token {
  constructor(newValue, oldValue) {
    super('ENVCHANGE', 'onSqlCollationChange');
    this.type = 'SQL_COLLATION';
    this.newValue = newValue;
    this.oldValue = oldValue;
  }
}
exports.CollationChangeToken = CollationChangeToken;
class RoutingEnvChangeToken extends Token {
  constructor(newValue, oldValue) {
    super('ENVCHANGE', 'onRoutingChange');
    this.type = 'ROUTING_CHANGE';
    this.newValue = newValue;
    this.oldValue = oldValue;
  }
}
exports.RoutingEnvChangeToken = RoutingEnvChangeToken;
class FeatureExtAckToken extends Token {
  /** Value of UTF8_SUPPORT acknowledgement.
   *
   * undefined when UTF8_SUPPORT not included in token. */

  constructor(fedAuth, utf8Support) {
    super('FEATUREEXTACK', 'onFeatureExtAck');
    this.fedAuth = fedAuth;
    this.utf8Support = utf8Support;
  }
}
exports.FeatureExtAckToken = FeatureExtAckToken;
class FedAuthInfoToken extends Token {
  constructor(spn, stsurl) {
    super('FEDAUTHINFO', 'onFedAuthInfo');
    this.spn = spn;
    this.stsurl = stsurl;
  }
}
exports.FedAuthInfoToken = FedAuthInfoToken;
class InfoMessageToken extends Token {
  constructor({
    number,
    state,
    class: clazz,
    message,
    serverName,
    procName,
    lineNumber
  }) {
    super('INFO', 'onInfoMessage');
    this.number = number;
    this.state = state;
    this.class = clazz;
    this.message = message;
    this.serverName = serverName;
    this.procName = procName;
    this.lineNumber = lineNumber;
  }
}
exports.InfoMessageToken = InfoMessageToken;
class ErrorMessageToken extends Token {
  constructor({
    number,
    state,
    class: clazz,
    message,
    serverName,
    procName,
    lineNumber
  }) {
    super('ERROR', 'onErrorMessage');
    this.number = number;
    this.state = state;
    this.class = clazz;
    this.message = message;
    this.serverName = serverName;
    this.procName = procName;
    this.lineNumber = lineNumber;
  }
}
exports.ErrorMessageToken = ErrorMessageToken;
class LoginAckToken extends Token {
  constructor({
    interface: interfaze,
    tdsVersion,
    progName,
    progVersion
  }) {
    super('LOGINACK', 'onLoginAck');
    this.interface = interfaze;
    this.tdsVersion = tdsVersion;
    this.progName = progName;
    this.progVersion = progVersion;
  }
}
exports.LoginAckToken = LoginAckToken;
class NBCRowToken extends Token {
  constructor(columns) {
    super('NBCROW', 'onRow');
    this.columns = columns;
  }
}
exports.NBCRowToken = NBCRowToken;
class OrderToken extends Token {
  constructor(orderColumns) {
    super('ORDER', 'onOrder');
    this.orderColumns = orderColumns;
  }
}
exports.OrderToken = OrderToken;
class ReturnStatusToken extends Token {
  constructor(value) {
    super('RETURNSTATUS', 'onReturnStatus');
    this.value = value;
  }
}
exports.ReturnStatusToken = ReturnStatusToken;
class ReturnValueToken extends Token {
  constructor({
    paramOrdinal,
    paramName,
    metadata,
    value
  }) {
    super('RETURNVALUE', 'onReturnValue');
    this.paramOrdinal = paramOrdinal;
    this.paramName = paramName;
    this.metadata = metadata;
    this.value = value;
  }
}
exports.ReturnValueToken = ReturnValueToken;
class RowToken extends Token {
  constructor(columns) {
    super('ROW', 'onRow');
    this.columns = columns;
  }
}
exports.RowToken = RowToken;
class SSPIToken extends Token {
  constructor(ntlmpacket, ntlmpacketBuffer) {
    super('SSPICHALLENGE', 'onSSPI');
    this.ntlmpacket = ntlmpacket;
    this.ntlmpacketBuffer = ntlmpacketBuffer;
  }
}
exports.SSPIToken = SSPIToken;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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