"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Transaction = exports.OPERATION_TYPE = exports.ISOLATION_LEVEL = void 0;
exports.assertValidIsolationLevel = assertValidIsolationLevel;
exports.isolationLevelByValue = void 0;
var _writableTrackingBuffer = _interopRequireDefault(require("./tracking-buffer/writable-tracking-buffer"));
var _allHeaders = require("./all-headers");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
/*
  s2.2.6.8
 */

const OPERATION_TYPE = exports.OPERATION_TYPE = {
  TM_GET_DTC_ADDRESS: 0x00,
  TM_PROPAGATE_XACT: 0x01,
  TM_BEGIN_XACT: 0x05,
  TM_PROMOTE_XACT: 0x06,
  TM_COMMIT_XACT: 0x07,
  TM_ROLLBACK_XACT: 0x08,
  TM_SAVE_XACT: 0x09
};
const ISOLATION_LEVEL = exports.ISOLATION_LEVEL = {
  NO_CHANGE: 0x00,
  READ_UNCOMMITTED: 0x01,
  READ_COMMITTED: 0x02,
  REPEATABLE_READ: 0x03,
  SERIALIZABLE: 0x04,
  SNAPSHOT: 0x05
};
const isolationLevelByValue = exports.isolationLevelByValue = {};
for (const name in ISOLATION_LEVEL) {
  const value = ISOLATION_LEVEL[name];
  isolationLevelByValue[value] = name;
}
function assertValidIsolationLevel(isolationLevel, name) {
  if (typeof isolationLevel !== 'number') {
    throw new TypeError(`The "${name}" ${name.includes('.') ? 'property' : 'argument'} must be of type number. Received type ${typeof isolationLevel} (${isolationLevel})`);
  }
  if (!Number.isInteger(isolationLevel)) {
    throw new RangeError(`The value of "${name}" is out of range. It must be an integer. Received: ${isolationLevel}`);
  }
  if (!(isolationLevel >= 0 && isolationLevel <= 5)) {
    throw new RangeError(`The value of "${name}" is out of range. It must be >= 0 && <= 5. Received: ${isolationLevel}`);
  }
}
class Transaction {
  constructor(name, isolationLevel = ISOLATION_LEVEL.NO_CHANGE) {
    this.name = name;
    this.isolationLevel = isolationLevel;
    this.outstandingRequestCount = 1;
  }
  beginPayload(txnDescriptor) {
    const buffer = new _writableTrackingBuffer.default(100, 'ucs2');
    (0, _allHeaders.writeToTrackingBuffer)(buffer, txnDescriptor, this.outstandingRequestCount);
    buffer.writeUShort(OPERATION_TYPE.TM_BEGIN_XACT);
    buffer.writeUInt8(this.isolationLevel);
    buffer.writeUInt8(this.name.length * 2);
    buffer.writeString(this.name, 'ucs2');
    return {
      *[Symbol.iterator]() {
        yield buffer.data;
      },
      toString: () => {
        return 'Begin Transaction: name=' + this.name + ', isolationLevel=' + isolationLevelByValue[this.isolationLevel];
      }
    };
  }
  commitPayload(txnDescriptor) {
    const buffer = new _writableTrackingBuffer.default(100, 'ascii');
    (0, _allHeaders.writeToTrackingBuffer)(buffer, txnDescriptor, this.outstandingRequestCount);
    buffer.writeUShort(OPERATION_TYPE.TM_COMMIT_XACT);
    buffer.writeUInt8(this.name.length * 2);
    buffer.writeString(this.name, 'ucs2');
    // No fBeginXact flag, so no new transaction is started.
    buffer.writeUInt8(0);
    return {
      *[Symbol.iterator]() {
        yield buffer.data;
      },
      toString: () => {
        return 'Commit Transaction: name=' + this.name;
      }
    };
  }
  rollbackPayload(txnDescriptor) {
    const buffer = new _writableTrackingBuffer.default(100, 'ascii');
    (0, _allHeaders.writeToTrackingBuffer)(buffer, txnDescriptor, this.outstandingRequestCount);
    buffer.writeUShort(OPERATION_TYPE.TM_ROLLBACK_XACT);
    buffer.writeUInt8(this.name.length * 2);
    buffer.writeString(this.name, 'ucs2');
    // No fBeginXact flag, so no new transaction is started.
    buffer.writeUInt8(0);
    return {
      *[Symbol.iterator]() {
        yield buffer.data;
      },
      toString: () => {
        return 'Rollback Transaction: name=' + this.name;
      }
    };
  }
  savePayload(txnDescriptor) {
    const buffer = new _writableTrackingBuffer.default(100, 'ascii');
    (0, _allHeaders.writeToTrackingBuffer)(buffer, txnDescriptor, this.outstandingRequestCount);
    buffer.writeUShort(OPERATION_TYPE.TM_SAVE_XACT);
    buffer.writeUInt8(this.name.length * 2);
    buffer.writeString(this.name, 'ucs2');
    return {
      *[Symbol.iterator]() {
        yield buffer.data;
      },
      toString: () => {
        return 'Save Transaction: name=' + this.name;
      }
    };
  }
  isolationLevelToTSQL() {
    switch (this.isolationLevel) {
      case ISOLATION_LEVEL.READ_UNCOMMITTED:
        return 'READ UNCOMMITTED';
      case ISOLATION_LEVEL.READ_COMMITTED:
        return 'READ COMMITTED';
      case ISOLATION_LEVEL.REPEATABLE_READ:
        return 'REPEATABLE READ';
      case ISOLATION_LEVEL.SERIALIZABLE:
        return 'SERIALIZABLE';
      case ISOLATION_LEVEL.SNAPSHOT:
        return 'SNAPSHOT';
    }
    return '';
  }
}
exports.Transaction = Transaction;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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