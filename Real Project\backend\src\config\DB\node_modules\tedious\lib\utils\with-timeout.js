"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.withTimeout = withTimeout;
var _timeoutError = _interopRequireDefault(require("../errors/timeout-error"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
/**
 * Run the function `func` with an `AbortSignal` that will automatically abort after the time specified
 * by `timeout` or when the given `signal` is aborted.
 *
 * On timeout, the `timeoutSignal` will be aborted and a `TimeoutError` will be thrown.
 */
async function withTimeout(timeout, func, signal) {
  const timeoutController = new AbortController();
  const abortCurrentAttempt = () => {
    timeoutController.abort();
  };
  const timer = setTimeout(abortCurrentAttempt, timeout);
  signal?.addEventListener('abort', abortCurrentAttempt, {
    once: true
  });
  try {
    return await func(timeoutController.signal);
  } catch (err) {
    if (err instanceof Error && err.name === 'AbortError' && !(signal && signal.aborted)) {
      throw new _timeoutError.default();
    }
    throw err;
  } finally {
    signal?.removeEventListener('abort', abortCurrentAttempt);
    clearTimeout(timer);
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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