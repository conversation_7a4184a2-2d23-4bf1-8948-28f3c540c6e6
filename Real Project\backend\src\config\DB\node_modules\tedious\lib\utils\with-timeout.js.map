{"version": 3, "file": "with-timeout.js", "names": ["_timeoutError", "_interopRequireDefault", "require", "obj", "__esModule", "default", "withTimeout", "timeout", "func", "signal", "timeoutController", "AbortController", "abortCurrentAttempt", "abort", "timer", "setTimeout", "addEventListener", "once", "err", "Error", "name", "aborted", "TimeoutError", "removeEventListener", "clearTimeout"], "sources": ["../../src/utils/with-timeout.ts"], "sourcesContent": ["import TimeoutError from '../errors/timeout-error';\n\n/**\n * Run the function `func` with an `AbortSignal` that will automatically abort after the time specified\n * by `timeout` or when the given `signal` is aborted.\n *\n * On timeout, the `timeoutSignal` will be aborted and a `TimeoutError` will be thrown.\n */\nexport async function withTimeout<T>(timeout: number, func: (timeoutSignal: AbortSignal) => Promise<T>, signal?: AbortSignal): Promise<T> {\n  const timeoutController = new AbortController();\n  const abortCurrentAttempt = () => { timeoutController.abort(); };\n\n  const timer = setTimeout(abortCurrentAttempt, timeout);\n  signal?.addEventListener('abort', abortCurrentAttempt, { once: true });\n\n  try {\n    return await func(timeoutController.signal);\n  } catch (err) {\n    if (err instanceof Error && err.name === 'AbortError' && !(signal && signal.aborted)) {\n      throw new TimeoutError();\n    }\n\n    throw err;\n  } finally {\n    signal?.removeEventListener('abort', abortCurrentAttempt);\n    clearTimeout(timer);\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAmD,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEnD;AACA;AACA;AACA;AACA;AACA;AACO,eAAeG,WAAWA,CAAIC,OAAe,EAAEC,IAAgD,EAAEC,MAAoB,EAAc;EACxI,MAAMC,iBAAiB,GAAG,IAAIC,eAAe,CAAC,CAAC;EAC/C,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAAEF,iBAAiB,CAACG,KAAK,CAAC,CAAC;EAAE,CAAC;EAEhE,MAAMC,KAAK,GAAGC,UAAU,CAACH,mBAAmB,EAAEL,OAAO,CAAC;EACtDE,MAAM,EAAEO,gBAAgB,CAAC,OAAO,EAAEJ,mBAAmB,EAAE;IAAEK,IAAI,EAAE;EAAK,CAAC,CAAC;EAEtE,IAAI;IACF,OAAO,MAAMT,IAAI,CAACE,iBAAiB,CAACD,MAAM,CAAC;EAC7C,CAAC,CAAC,OAAOS,GAAG,EAAE;IACZ,IAAIA,GAAG,YAAYC,KAAK,IAAID,GAAG,CAACE,IAAI,KAAK,YAAY,IAAI,EAAEX,MAAM,IAAIA,MAAM,CAACY,OAAO,CAAC,EAAE;MACpF,MAAM,IAAIC,qBAAY,CAAC,CAAC;IAC1B;IAEA,MAAMJ,GAAG;EACX,CAAC,SAAS;IACRT,MAAM,EAAEc,mBAAmB,CAAC,OAAO,EAAEX,mBAAmB,CAAC;IACzDY,YAAY,CAACV,KAAK,CAAC;EACrB;AACF"}