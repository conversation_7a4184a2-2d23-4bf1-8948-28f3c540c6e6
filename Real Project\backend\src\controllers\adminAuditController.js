// src/controllers/adminAuditController.js
const { executeQuery } = require('../utils/dbUtils');
const ApiError = require('../utils/ApiError');
const catchAsync = require('../utils/catchAsync');
const logger = require('../utils/logger');

/**
 * Log an admin activity
 * @param {Object} data - Activity data
 * @param {number} data.userId - User ID who performed the action
 * @param {string} data.action - Action performed
 * @param {string} data.entityType - Type of entity affected
 * @param {number} data.entityId - ID of the entity affected
 * @param {string} data.description - Description of the activity
 * @param {string} data.ipAddress - IP address of the user
 * @param {string} data.userAgent - User agent of the user
 * @returns {Promise<Object>} - Created activity log
 */
const logActivity = async (data) => {
  try {
    const { userId, action, entityType, entityId, description, ipAddress, userAgent } = data;

    // Validate required fields
    if (!userId || !action || !entityType) {
      throw new Error('userId, action, and entityType are required');
    }

    // Insert activity log
    const query = `
      INSERT INTO AdminActivityLog (
        user_id,
        action,
        entity_type,
        entity_id,
        description,
        ip_address,
        user_agent
      )
      VALUES (
        @userId,
        @action,
        @entityType,
        @entityId,
        @description,
        @ipAddress,
        @userAgent
      );

      SELECT SCOPE_IDENTITY() AS log_id;
    `;

    const result = await executeQuery(query, {
      userId,
      action,
      entityType,
      entityId: entityId || null,
      description: description || null,
      ipAddress: ipAddress || null,
      userAgent: userAgent || null
    });

    if (!result.recordset || !result.recordset[0]) {
      throw new Error('Failed to log activity');
    }

    const logId = result.recordset[0].log_id;
    logger.info(`Admin activity logged: ${action} on ${entityType} by user ${userId}`);

    return { logId };
  } catch (error) {
    logger.error('Error logging admin activity:', error);
    // Don't throw error to prevent disrupting the main flow
    return { error: error.message };
  }
};

/**
 * Create a middleware to log admin activities
 * @param {Object} options - Options for the middleware
 * @param {string} options.action - Action being performed
 * @param {string} options.entityType - Type of entity being affected
 * @param {Function} options.getEntityId - Function to extract entity ID from request
 * @param {Function} options.getDescription - Function to generate description from request
 * @returns {Function} Express middleware
 */
const createAuditMiddleware = (options) => {
  const { action, entityType, getEntityId, getDescription } = options;

  return (req, res, next) => {
    // Store the original send function
    const originalSend = res.send;

    // Override the send function
    res.send = function (body) {
      // Restore the original send function
      res.send = originalSend;

      // Only log successful operations (2xx status codes)
      if (res.statusCode >= 200 && res.statusCode < 300) {
        const userId = req.user ? req.user.UserID : null;
        const entityId = getEntityId ? getEntityId(req) : null;
        const description = getDescription ? getDescription(req, body) : null;
        const ipAddress = req.ip;
        const userAgent = req.get('User-Agent');

        // Log the activity asynchronously (don't wait for it)
        logActivity({
          userId,
          action,
          entityType,
          entityId,
          description,
          ipAddress,
          userAgent
        }).catch(err => {
          logger.error('Error in audit middleware:', err);
        });
      }

      // Call the original send function
      return originalSend.call(this, body);
    };

    next();
  };
};

/**
 * Get audit logs with filtering and pagination
 * @route GET /api/admin/audit-logs
 */
const getAuditLogs = catchAsync(async (req, res) => {
  const {
    userId,
    action,
    entityType,
    entityId,
    startDate,
    endDate,
    page = 1,
    limit = 20,
    sortBy = 'created_at',
    sortOrder = 'desc'
  } = req.query;

  // Build the WHERE clause based on filters
  const conditions = [];
  const params = {};

  if (userId) {
    conditions.push('AL.user_id = @userId');
    params.userId = userId;
  }

  if (action) {
    conditions.push('AL.action = @action');
    params.action = action;
  }

  if (entityType) {
    conditions.push('AL.entity_type = @entityType');
    params.entityType = entityType;
  }

  if (entityId) {
    conditions.push('AL.entity_id = @entityId');
    params.entityId = entityId;
  }

  if (startDate) {
    conditions.push('AL.created_at >= @startDate');
    params.startDate = startDate;
  }

  if (endDate) {
    conditions.push('AL.created_at <= @endDate');
    params.endDate = endDate;
  }

  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

  // Validate sort parameters
  const allowedSortFields = ['created_at', 'user_id', 'action', 'entity_type', 'entity_id'];
  const allowedSortOrders = ['asc', 'desc'];

  const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
  const validSortOrder = allowedSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder : 'desc';

  // Calculate pagination
  const offset = (page - 1) * limit;
  params.offset = offset;
  params.limit = limit;

  // Get total count
  const countQuery = `
    SELECT COUNT(*) AS total
    FROM AdminActivityLog AL
    ${whereClause}
  `;

  const countResult = await executeQuery(countQuery, params);
  const total = countResult.recordset[0].total;

  // Get paginated results
  const query = `
    SELECT
      AL.log_id,
      AL.user_id,
      U.full_name AS user_name,
      U.email AS user_email,
      AL.action,
      AL.entity_type,
      AL.entity_id,
      AL.description,
      AL.ip_address,
      AL.user_agent,
      AL.created_at
    FROM AdminActivityLog AL
    JOIN Users U ON AL.user_id = U.user_id
    ${whereClause}
    ORDER BY AL.${validSortBy} ${validSortOrder}
    OFFSET @offset ROWS
    FETCH NEXT @limit ROWS ONLY
  `;

  const result = await executeQuery(query, params);

  // Calculate pagination metadata
  const totalPages = Math.ceil(total / limit);
  const hasNext = page < totalPages;
  const hasPrev = page > 1;

  res.json({
    success: true,
    data: result.recordset,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages,
      hasNext,
      hasPrev
    }
  });
});

/**
 * Get audit log actions for dropdown
 * @route GET /api/admin/audit-logs/actions
 */
const getAuditLogActions = catchAsync(async (req, res) => {
  const query = `
    SELECT DISTINCT action
    FROM AdminActivityLog
    ORDER BY action
  `;

  const result = await executeQuery(query);

  res.json({
    success: true,
    data: result.recordset.map(row => row.action)
  });
});

/**
 * Get audit log entity types for dropdown
 * @route GET /api/admin/audit-logs/entity-types
 */
const getAuditLogEntityTypes = catchAsync(async (req, res) => {
  const query = `
    SELECT DISTINCT entity_type
    FROM AdminActivityLog
    ORDER BY entity_type
  `;

  const result = await executeQuery(query);

  res.json({
    success: true,
    data: result.recordset.map(row => row.entity_type)
  });
});

module.exports = {
  logActivity,
  createAuditMiddleware,
  getAuditLogs,
  getAuditLogActions,
  getAuditLogEntityTypes
};
