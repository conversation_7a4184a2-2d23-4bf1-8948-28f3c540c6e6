// src/controllers/adminPermissionController.js
const { executeQuery } = require('../utils/dbUtils');
const ApiError = require('../utils/ApiError');
const catchAsync = require('../utils/catchAsync');
const logger = require('../utils/logger');
const { logActivity } = require('./adminAuditController');

/**
 * Get all permissions
 * @route GET /api/admin/permissions
 */
const getAllPermissions = catchAsync(async (req, res) => {
  const { category } = req.query;

  let query = `
    SELECT
      permission_id,
      permission_name,
      description,
      category,
      created_at
    FROM Permissions
  `;

  const params = {};

  if (category) {
    query += ` WHERE category = @category`;
    params.category = category;
  }

  query += ` ORDER BY category, permission_name`;

  const result = await executeQuery(query, params);

  // Group permissions by category
  const groupedPermissions = {};
  result.recordset.forEach(permission => {
    if (!groupedPermissions[permission.category]) {
      groupedPermissions[permission.category] = [];
    }
    groupedPermissions[permission.category].push(permission);
  });

  res.json({
    success: true,
    data: {
      permissions: result.recordset,
      groupedPermissions
    }
  });
});

/**
 * Get permission categories
 * @route GET /api/admin/permissions/categories
 */
const getPermissionCategories = catchAsync(async (req, res) => {
  const query = `
    SELECT DISTINCT category
    FROM Permissions
    ORDER BY category
  `;

  const result = await executeQuery(query);

  res.json({
    success: true,
    data: result.recordset.map(row => row.category)
  });
});

/**
 * Create a new permission
 * @route POST /api/admin/permissions
 */
const createPermission = catchAsync(async (req, res) => {
  const { permissionName, description, category } = req.body;

  // Validate required fields
  if (!permissionName || !category) {
    throw new ApiError(400, 'Permission name and category are required');
  }

  // Check if permission name already exists
  const checkQuery = `
    SELECT 1 FROM Permissions WHERE permission_name = @permissionName
  `;

  const checkResult = await executeQuery(checkQuery, { permissionName });

  if (checkResult.recordset && checkResult.recordset.length > 0) {
    throw new ApiError(400, 'Permission name already exists');
  }

  // Create the permission
  const query = `
    INSERT INTO Permissions (permission_name, description, category, created_at)
    VALUES (@permissionName, @description, @category, GETDATE());

    SELECT SCOPE_IDENTITY() AS permission_id;
  `;

  const result = await executeQuery(query, {
    permissionName,
    description: description || null,
    category
  });

  if (!result.recordset || !result.recordset[0]) {
    throw new ApiError(500, 'Failed to create permission');
  }

  const permissionId = result.recordset[0].permission_id;

  // Log the activity
  await logActivity({
    userId: req.user.UserID,
    action: 'create',
    entityType: 'permission',
    entityId: permissionId,
    description: `Created permission: ${permissionName}`,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.status(201).json({
    success: true,
    message: 'Permission created successfully',
    data: {
      permissionId,
      permissionName,
      description,
      category
    }
  });
});

/**
 * Update a permission
 * @route PUT /api/admin/permissions/:id
 */
const updatePermission = catchAsync(async (req, res) => {
  const { id } = req.params;
  const { permissionName, description, category } = req.body;

  // Validate required fields
  if (!permissionName || !category) {
    throw new ApiError(400, 'Permission name and category are required');
  }

  // Check if permission exists
  const checkPermissionQuery = `
    SELECT permission_name FROM Permissions WHERE permission_id = @id
  `;

  const checkPermissionResult = await executeQuery(checkPermissionQuery, { id });

  if (!checkPermissionResult.recordset || checkPermissionResult.recordset.length === 0) {
    throw new ApiError(404, 'Permission not found');
  }

  const existingPermission = checkPermissionResult.recordset[0];

  // Check if new permission name already exists (for another permission)
  if (permissionName !== existingPermission.permission_name) {
    const checkNameQuery = `
      SELECT 1 FROM Permissions WHERE permission_name = @permissionName AND permission_id <> @id
    `;

    const checkNameResult = await executeQuery(checkNameQuery, { permissionName, id });

    if (checkNameResult.recordset && checkNameResult.recordset.length > 0) {
      throw new ApiError(400, 'Permission name already exists');
    }
  }

  // Update the permission
  const query = `
    UPDATE Permissions
    SET
      permission_name = @permissionName,
      description = @description,
      category = @category
    WHERE permission_id = @id;
  `;

  await executeQuery(query, {
    id,
    permissionName,
    description: description || null,
    category
  });

  // Log the activity
  await logActivity({
    userId: req.user.UserID,
    action: 'update',
    entityType: 'permission',
    entityId: id,
    description: `Updated permission: ${permissionName}`,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.json({
    success: true,
    message: 'Permission updated successfully',
    data: {
      permissionId: id,
      permissionName,
      description,
      category
    }
  });
});

/**
 * Delete a permission
 * @route DELETE /api/admin/permissions/:id
 */
const deletePermission = catchAsync(async (req, res) => {
  const { id } = req.params;

  // Check if permission exists
  const checkPermissionQuery = `
    SELECT permission_name FROM Permissions WHERE permission_id = @id
  `;

  const checkPermissionResult = await executeQuery(checkPermissionQuery, { id });

  if (!checkPermissionResult.recordset || checkPermissionResult.recordset.length === 0) {
    throw new ApiError(404, 'Permission not found');
  }

  const existingPermission = checkPermissionResult.recordset[0];

  // Check if permission is assigned to any roles
  const checkRolesQuery = `
    SELECT COUNT(*) AS role_count FROM AdminRolePermissions WHERE permission_id = @id
  `;

  const checkRolesResult = await executeQuery(checkRolesQuery, { id });

  if (checkRolesResult.recordset[0].role_count > 0) {
    throw new ApiError(400, 'Cannot delete permission assigned to roles');
  }

  // Delete the permission
  const query = `
    DELETE FROM Permissions WHERE permission_id = @id
  `;

  await executeQuery(query, { id });

  // Log the activity
  await logActivity({
    userId: req.user.UserID,
    action: 'delete',
    entityType: 'permission',
    entityId: id,
    description: `Deleted permission: ${existingPermission.permission_name}`,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.json({
    success: true,
    message: 'Permission deleted successfully'
  });
});

/**
 * Get permissions for a specific role
 * @route GET /api/admin/permissions/role/:roleId
 */
const getPermissionsByRole = catchAsync(async (req, res) => {
  const { roleId } = req.params;

  // Check if role exists
  const checkRoleQuery = `
    SELECT role_name FROM AdminRoles WHERE role_id = @roleId
  `;

  const checkRoleResult = await executeQuery(checkRoleQuery, { roleId });

  if (!checkRoleResult.recordset || checkRoleResult.recordset.length === 0) {
    throw new ApiError(404, 'Role not found');
  }

  // Get permissions assigned to this role
  const query = `
    SELECT
      P.permission_id,
      P.permission_name,
      P.description,
      P.category,
      CASE WHEN ARP.permission_id IS NOT NULL THEN 1 ELSE 0 END AS is_assigned
    FROM Permissions P
    LEFT JOIN AdminRolePermissions ARP ON P.permission_id = ARP.permission_id AND ARP.role_id = @roleId
    ORDER BY P.category, P.permission_name
  `;

  const result = await executeQuery(query, { roleId });

  // Group permissions by category
  const groupedPermissions = {};
  result.recordset.forEach(permission => {
    if (!groupedPermissions[permission.category]) {
      groupedPermissions[permission.category] = [];
    }
    groupedPermissions[permission.category].push(permission);
  });

  res.json({
    success: true,
    data: {
      roleName: checkRoleResult.recordset[0].role_name,
      permissions: result.recordset,
      groupedPermissions
    }
  });
});

module.exports = {
  getAllPermissions,
  getPermissionCategories,
  createPermission,
  updatePermission,
  deletePermission,
  getPermissionsByRole
};
