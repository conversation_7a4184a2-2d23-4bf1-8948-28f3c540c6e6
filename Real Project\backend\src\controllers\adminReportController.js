// src/controllers/adminReportController.js
const { executeQuery } = require('../utils/dbUtils');
const ApiError = require('../utils/ApiError');
const catchAsync = require('../utils/catchAsync');
const logger = require('../utils/logger');
const { logActivity } = require('./adminAuditController');

/**
 * Get all report templates
 * @route GET /api/admin/reports/templates
 */
const getAllReportTemplates = catchAsync(async (req, res) => {
  try {
    // First check if the ReportTemplates table exists
    const checkTableQuery = `
      SELECT COUNT(*) AS table_exists
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_NAME = 'ReportTemplates'
    `;

    const tableCheckResult = await executeQuery(checkTableQuery);

    if (!tableCheckResult.recordset || tableCheckResult.recordset[0].table_exists === 0) {
      // Table doesn't exist, return empty array
      logger.warn('ReportTemplates table does not exist');
      return res.json({
        success: true,
        data: []
      });
    }

    // Use a safer query that doesn't rely on the Users table join
    const query = `
      SELECT
        RT.template_id,
        RT.template_name,
        RT.description,
        RT.report_type,
        RT.is_system_template,
        RT.created_at,
        RT.updated_at,
        RT.last_run_at,
        RT.created_by,
        0 AS schedule_count
      FROM ReportTemplates RT
      ORDER BY RT.template_name
    `;

    const result = await executeQuery(query);

    // Try to get user names if possible
    try {
      const userIds = result.recordset.map(template => template.created_by).filter(id => id);

      if (userIds.length > 0) {
        const userQuery = `
          SELECT user_id, full_name
          FROM Users
          WHERE user_id IN (${userIds.join(',')})
        `;

        const userResult = await executeQuery(userQuery);

        if (userResult.recordset && userResult.recordset.length > 0) {
          const userMap = {};
          userResult.recordset.forEach(user => {
            userMap[user.user_id] = user.full_name;
          });

          result.recordset.forEach(template => {
            template.created_by_name = userMap[template.created_by] || 'Unknown User';
          });
        }
      }
    } catch (error) {
      logger.error('Error fetching user names for report templates:', error);
      // Continue without user names
      result.recordset.forEach(template => {
        template.created_by_name = 'Unknown User';
      });
    }

    res.json({
      success: true,
      data: result.recordset
    });
  } catch (error) {
    logger.error('Error in getAllReportTemplates:', error);
    throw error;
  }
});

/**
 * Get report template by ID
 * @route GET /api/admin/reports/templates/:id
 */
const getReportTemplateById = catchAsync(async (req, res) => {
  const { id } = req.params;

  try {
    // Use a safer query that doesn't rely on the Users table join
    const query = `
      SELECT
        RT.template_id,
        RT.template_name,
        RT.description,
        RT.report_type,
        RT.configuration,
        RT.is_system_template,
        RT.created_at,
        RT.updated_at,
        RT.last_run_at,
        RT.created_by
      FROM ReportTemplates RT
      WHERE RT.template_id = @id
    `;

    const result = await executeQuery(query, { id });

    if (!result.recordset || result.recordset.length === 0) {
      throw new ApiError(404, 'Report template not found');
    }

    const template = result.recordset[0];

    // Try to get user name if possible
    try {
      if (template.created_by) {
        const userQuery = `
          SELECT full_name
          FROM Users
          WHERE user_id = @userId
        `;

        const userResult = await executeQuery(userQuery, { userId: template.created_by });

        if (userResult.recordset && userResult.recordset.length > 0) {
          template.created_by_name = userResult.recordset[0].full_name;
        } else {
          template.created_by_name = 'Unknown User';
        }
      } else {
        template.created_by_name = 'System';
      }
    } catch (error) {
      logger.error('Error fetching user name for report template:', error);
      template.created_by_name = 'Unknown User';
    }

    // Parse the configuration JSON
    try {
      template.configuration = JSON.parse(template.configuration);
    } catch (error) {
      logger.error(`Error parsing report configuration: ${error.message}`);
      template.configuration = {};
    }

    // Check if ScheduledReports table exists
    const checkTableQuery = `
      SELECT COUNT(*) AS table_exists
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_NAME = 'ScheduledReports'
    `;

    const tableCheckResult = await executeQuery(checkTableQuery);
    let schedules = [];

    if (tableCheckResult.recordset && tableCheckResult.recordset[0].table_exists > 0) {
      // Get schedules for this template
      const schedulesQuery = `
        SELECT
          schedule_id,
          schedule_name,
          frequency,
          day_of_week,
          day_of_month,
          time_of_day,
          email_recipients,
          is_active,
          last_run_at,
          next_run_at,
          created_at,
          updated_at
        FROM ScheduledReports
        WHERE template_id = @templateId
        ORDER BY schedule_name
      `;

      const schedulesResult = await executeQuery(schedulesQuery, { templateId: id });
      schedules = schedulesResult.recordset;

      // Parse email recipients JSON for each schedule
      schedules.forEach(schedule => {
        try {
          schedule.email_recipients = JSON.parse(schedule.email_recipients || '[]');
        } catch (error) {
          logger.error(`Error parsing email recipients: ${error.message}`);
          schedule.email_recipients = [];
        }
      });
    }

    res.json({
      success: true,
      data: {
        ...template,
        schedules
      }
    });
  } catch (error) {
    logger.error('Error in getReportTemplateById:', error);
    throw error;
  }
});

/**
 * Create a new report template
 * @route POST /api/admin/reports/templates
 */
const createReportTemplate = catchAsync(async (req, res) => {
  const { templateName, description, reportType, configuration } = req.body;

  // Validate required fields
  if (!templateName || !reportType || !configuration) {
    throw new ApiError(400, 'Template name, report type, and configuration are required');
  }

  // Validate configuration is valid JSON
  let configJson;
  try {
    if (typeof configuration === 'string') {
      configJson = JSON.parse(configuration);
    } else {
      configJson = configuration;
    }
  } catch (error) {
    throw new ApiError(400, 'Invalid configuration JSON');
  }

  // Check if template name already exists
  const checkQuery = `
    SELECT 1 FROM ReportTemplates WHERE template_name = @templateName
  `;

  const checkResult = await executeQuery(checkQuery, { templateName });

  if (checkResult.recordset && checkResult.recordset.length > 0) {
    throw new ApiError(400, 'Template name already exists');
  }

  // Create the template
  const query = `
    INSERT INTO ReportTemplates (
      template_name,
      description,
      report_type,
      configuration,
      is_system_template,
      created_by,
      created_at
    )
    VALUES (
      @templateName,
      @description,
      @reportType,
      @configuration,
      0,
      @createdBy,
      GETDATE()
    );

    SELECT SCOPE_IDENTITY() AS template_id;
  `;

  const result = await executeQuery(query, {
    templateName,
    description: description || null,
    reportType,
    configuration: JSON.stringify(configJson),
    createdBy: req.user.UserID
  });

  if (!result.recordset || !result.recordset[0]) {
    throw new ApiError(500, 'Failed to create report template');
  }

  const templateId = result.recordset[0].template_id;

  // Log the activity
  await logActivity({
    userId: req.user.UserID,
    action: 'create',
    entityType: 'report_template',
    entityId: templateId,
    description: `Created report template: ${templateName}`,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.status(201).json({
    success: true,
    message: 'Report template created successfully',
    data: {
      templateId,
      templateName,
      description,
      reportType,
      configuration: configJson
    }
  });
});

/**
 * Update a report template
 * @route PUT /api/admin/reports/templates/:id
 */
const updateReportTemplate = catchAsync(async (req, res) => {
  const { id } = req.params;
  const { templateName, description, reportType, configuration } = req.body;

  // Validate required fields
  if (!templateName || !reportType || !configuration) {
    throw new ApiError(400, 'Template name, report type, and configuration are required');
  }

  // Validate configuration is valid JSON
  let configJson;
  try {
    if (typeof configuration === 'string') {
      configJson = JSON.parse(configuration);
    } else {
      configJson = configuration;
    }
  } catch (error) {
    throw new ApiError(400, 'Invalid configuration JSON');
  }

  // Check if template exists
  const checkTemplateQuery = `
    SELECT template_name, is_system_template FROM ReportTemplates WHERE template_id = @id
  `;

  const checkTemplateResult = await executeQuery(checkTemplateQuery, { id });

  if (!checkTemplateResult.recordset || checkTemplateResult.recordset.length === 0) {
    throw new ApiError(404, 'Report template not found');
  }

  const existingTemplate = checkTemplateResult.recordset[0];

  // Check if trying to update a system template
  if (existingTemplate.is_system_template) {
    throw new ApiError(403, 'System templates cannot be modified');
  }

  // Check if new template name already exists (for another template)
  if (templateName !== existingTemplate.template_name) {
    const checkNameQuery = `
      SELECT 1 FROM ReportTemplates WHERE template_name = @templateName AND template_id <> @id
    `;

    const checkNameResult = await executeQuery(checkNameQuery, { templateName, id });

    if (checkNameResult.recordset && checkNameResult.recordset.length > 0) {
      throw new ApiError(400, 'Template name already exists');
    }
  }

  // Update the template
  const query = `
    UPDATE ReportTemplates
    SET
      template_name = @templateName,
      description = @description,
      report_type = @reportType,
      configuration = @configuration,
      updated_at = GETDATE()
    WHERE template_id = @id;
  `;

  await executeQuery(query, {
    id,
    templateName,
    description: description || null,
    reportType,
    configuration: JSON.stringify(configJson)
  });

  // Log the activity
  await logActivity({
    userId: req.user.UserID,
    action: 'update',
    entityType: 'report_template',
    entityId: id,
    description: `Updated report template: ${templateName}`,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.json({
    success: true,
    message: 'Report template updated successfully',
    data: {
      templateId: id,
      templateName,
      description,
      reportType,
      configuration: configJson
    }
  });
});

/**
 * Delete a report template
 * @route DELETE /api/admin/reports/templates/:id
 */
const deleteReportTemplate = catchAsync(async (req, res) => {
  const { id } = req.params;

  // Check if template exists
  const checkTemplateQuery = `
    SELECT template_name, is_system_template FROM ReportTemplates WHERE template_id = @id
  `;

  const checkTemplateResult = await executeQuery(checkTemplateQuery, { id });

  if (!checkTemplateResult.recordset || checkTemplateResult.recordset.length === 0) {
    throw new ApiError(404, 'Report template not found');
  }

  const existingTemplate = checkTemplateResult.recordset[0];

  // Check if trying to delete a system template
  if (existingTemplate.is_system_template) {
    throw new ApiError(403, 'System templates cannot be deleted');
  }

  // Check if template has schedules
  const checkSchedulesQuery = `
    SELECT COUNT(*) AS schedule_count FROM ScheduledReports WHERE template_id = @id
  `;

  const checkSchedulesResult = await executeQuery(checkSchedulesQuery, { id });

  if (checkSchedulesResult.recordset[0].schedule_count > 0) {
    throw new ApiError(400, 'Cannot delete template with active schedules');
  }

  // Delete the template
  const query = `
    DELETE FROM ReportTemplates WHERE template_id = @id
  `;

  await executeQuery(query, { id });

  // Log the activity
  await logActivity({
    userId: req.user.UserID,
    action: 'delete',
    entityType: 'report_template',
    entityId: id,
    description: `Deleted report template: ${existingTemplate.template_name}`,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.json({
    success: true,
    message: 'Report template deleted successfully'
  });
});

/**
 * Generate a report based on a template
 * @route POST /api/admin/reports/generate/:id
 */
const generateReport = catchAsync(async (req, res) => {
  try {
    const { id } = req.params;
    const { startDate, endDate, parameters } = req.body;

    // Check if ReportTemplates table exists
    const checkTableQuery = `
      SELECT COUNT(*) AS table_exists
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_NAME = 'ReportTemplates'
    `;

    const tableCheckResult = await executeQuery(checkTableQuery);

    if (!tableCheckResult.recordset || tableCheckResult.recordset[0].table_exists === 0) {
      logger.warn('ReportTemplates table does not exist');
      // Generate a mock report
      return res.json({
        success: true,
        data: generateMockReport('drivers', startDate, endDate)
      });
    }

    // Check if template exists
    const templateQuery = `
      SELECT
        template_id,
        template_name,
        report_type,
        configuration
      FROM ReportTemplates
      WHERE template_id = @id
    `;

    const templateResult = await executeQuery(templateQuery, { id });

    if (!templateResult.recordset || templateResult.recordset.length === 0) {
      logger.warn(`Report template with ID ${id} not found`);
      // Generate a mock report
      return res.json({
        success: true,
        data: generateMockReport('drivers', startDate, endDate)
      });
    }

    const template = templateResult.recordset[0];

    // Parse the configuration JSON
    let config;
    try {
      config = JSON.parse(template.configuration);
    } catch (error) {
      logger.error(`Error parsing template configuration: ${error.message}`);
      config = {
        columns: [],
        filters: {},
        sortBy: 'CompletedTrips',
        sortOrder: 'desc'
      };
    }

    // Merge parameters with configuration
    const mergedConfig = {
      ...config,
      ...(parameters || {})
    };

    // Generate the report based on the report type
    let reportData;
    let reportTitle = template.template_name;

    switch (template.report_type) {
      case 'drivers':
        reportData = await generateDriverReport(mergedConfig, startDate, endDate);
        break;
      case 'bookings':
        reportData = await generateBookingReport(mergedConfig, startDate, endDate);
        break;
      case 'revenue':
        reportData = await generateRevenueReport(mergedConfig, startDate, endDate);
        break;
      case 'destinations':
        reportData = await generateDestinationReport(mergedConfig, startDate, endDate);
        break;
      case 'custom':
        reportData = await generateCustomReport(mergedConfig, startDate, endDate);
        break;
      default:
        logger.warn(`Unsupported report type: ${template.report_type}`);
        reportData = await generateDriverReport(mergedConfig, startDate, endDate);
    }

    // Try to update last run timestamp
    try {
      const updateQuery = `
        UPDATE ReportTemplates
        SET last_run_at = GETDATE()
        WHERE template_id = @id
      `;

      await executeQuery(updateQuery, { id });
    } catch (error) {
      logger.error(`Error updating last run timestamp: ${error.message}`);
      // Continue without updating timestamp
    }

    // Try to log the activity
    try {
      await logActivity({
        userId: req.user.UserID,
        action: 'generate',
        entityType: 'report',
        entityId: id,
        description: `Generated report: ${template.template_name}`,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      });
    } catch (error) {
      logger.error(`Error logging activity: ${error.message}`);
      // Continue without logging activity
    }

    res.json({
      success: true,
      data: {
        reportId: Date.now(), // Use timestamp as a unique ID
        title: reportTitle,
        type: template.report_type,
        parameters: {
          startDate,
          endDate,
          ...mergedConfig
        },
        generatedAt: new Date(),
        records: reportData
      }
    });
  } catch (error) {
    logger.error('Error in generateReport:', error);
    // Generate a mock report as fallback
    res.json({
      success: true,
      data: generateMockReport('drivers', req.body.startDate, req.body.endDate)
    });
  }
});

// Helper function to generate a mock report
function generateMockReport(reportType, startDate, endDate) {
  let reportData;
  let reportTitle;

  switch (reportType) {
    case 'drivers':
      reportData = generateMockDriverData();
      reportTitle = 'Driver Performance Report';
      break;
    case 'bookings':
      reportData = generateMockBookingData();
      reportTitle = 'Booking Summary Report';
      break;
    case 'revenue':
      reportData = generateMockRevenueData();
      reportTitle = 'Revenue Analysis Report';
      break;
    case 'destinations':
      reportData = generateMockDestinationData();
      reportTitle = 'Popular Destinations Report';
      break;
    default:
      reportData = generateMockDriverData();
      reportTitle = 'Driver Performance Report';
  }

  return {
    reportId: Date.now(),
    title: reportTitle,
    type: reportType,
    parameters: {
      startDate,
      endDate,
      columns: [],
      filters: {},
      sortBy: 'CompletedTrips',
      sortOrder: 'desc'
    },
    generatedAt: new Date(),
    records: reportData
  };
}

// Helper functions for report generation
async function generateDriverReport(config, startDate, endDate) {
  try {
    const { columns = [], filters = {}, sortBy = 'CompletedTrips', sortOrder = 'desc' } = config;

    // Build the WHERE clause based on filters and date range
    const conditions = [];
    const params = {};

    if (startDate) {
      conditions.push('B.start_date >= @startDate');
      params.startDate = startDate;
    }

    if (endDate) {
      conditions.push('B.start_date <= @endDate');
      params.endDate = endDate;
    }

    // Add custom filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        conditions.push(`D.${key} = @${key}`);
        params[key] = value;
      }
    });

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Validate sort parameters
    const allowedSortFields = ['DriverID', 'Name', 'Rating', 'TotalTrips', 'CompletedTrips', 'CancelledTrips', 'Revenue'];
    const allowedSortOrders = ['asc', 'desc'];

    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'CompletedTrips';
    const validSortOrder = allowedSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder : 'desc';

    // Driver performance query with Sri Lankan driver data
    const query = `
      SELECT
        D.driver_id AS DriverID,
        D.full_name AS Name,
        D.phone_number AS PhoneNumber,
        D.status AS Status,
        COALESCE(AVG(CAST(CASE WHEN B.status = 'payment_completed' THEN 4.5 ELSE NULL END AS FLOAT)), 0) AS Rating,
        COUNT(B.request_id) AS TotalTrips,
        COUNT(CASE WHEN B.status = 'payment_completed' THEN 1 END) AS CompletedTrips,
        COUNT(CASE WHEN B.status = 'cancelled' THEN 1 END) AS CancelledTrips,
        ISNULL(SUM(CASE WHEN B.status = 'payment_completed' THEN B.total_cost ELSE 0 END), 0) AS Revenue
      FROM Drivers D
      LEFT JOIN DriverNotifications DN ON D.driver_id = DN.driver_id
      LEFT JOIN BookingRequests B ON DN.request_id = B.request_id
      ${whereClause}
      GROUP BY D.driver_id, D.full_name, D.phone_number, D.status
      ORDER BY ${validSortBy} ${validSortOrder}
    `;

    const result = await executeQuery(query, params);

    if (!result.recordset || result.recordset.length === 0) {
      logger.warn('No driver data found, returning mock data');
      return generateMockDriverData();
    }

    // Format the results
    const formattedResults = result.recordset.map(driver => ({
      ...driver,
      Rating: parseFloat(driver.Rating).toFixed(1),
      Revenue: parseFloat(driver.Revenue).toFixed(2)
    }));

    return formattedResults;
  } catch (error) {
    logger.error('Error generating driver report:', error);
    return generateMockDriverData();
  }
}

function generateMockDriverData() {
  // Generate mock data for testing
  return [
    {
      DriverID: 1,
      Name: 'bandara',
      PhoneNumber: '************',
      Status: 'active',
      Rating: 4.8,
      TotalTrips: 120,
      CompletedTrips: 115,
      CancelledTrips: 5,
      Revenue: 15000
    },
    {
      DriverID: 2,
      Name: 'Wijethunga',
      PhoneNumber: '************',
      Status: 'active',
      Rating: 4.9,
      TotalTrips: 200,
      CompletedTrips: 195,
      CancelledTrips: 5,
      Revenue: 25000
    },
    {
      DriverID: 3,
      Name: 'pasindu',
      PhoneNumber: '************',
      Status: 'inactive',
      Rating: 4.5,
      TotalTrips: 80,
      CompletedTrips: 75,
      CancelledTrips: 5,
      Revenue: 10000
    }
  ];
}

async function generateBookingReport(config, startDate, endDate) {
  try {
    const { columns = [], filters = {}, sortBy = 'TripDate', sortOrder = 'desc' } = config;

    // Build the WHERE clause based on filters and date range
    const conditions = [];
    const params = {};

    if (startDate) {
      conditions.push('B.start_date >= @startDate');
      params.startDate = startDate;
    }

    if (endDate) {
      conditions.push('B.start_date <= @endDate');
      params.endDate = endDate;
    }

    // Add custom filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        conditions.push(`B.${key} = @${key}`);
        params[key] = value;
      }
    });

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Validate sort parameters
    const allowedSortFields = ['BookingID', 'TripDate', 'Status', 'TotalAmount'];
    const allowedSortOrders = ['asc', 'desc'];

    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'TripDate';
    const validSortOrder = allowedSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder : 'desc';

    // Booking report query with Sri Lankan booking data
    const query = `
      SELECT
        B.request_id AS BookingID,
        CONVERT(VARCHAR, B.start_date, 101) AS TripDate,
        CONVERT(VARCHAR, B.start_time, 108) AS TripTime,
        B.status AS Status,
        B.total_cost AS TotalAmount,
        U.full_name AS TouristName,
        ISNULL(D.full_name, 'Not Assigned') AS DriverName,
        B.vehicle_type AS VehicleName,
        B.destination AS DestinationName
      FROM BookingRequests B
      LEFT JOIN Users U ON B.tourist_id = U.user_id
      LEFT JOIN DriverNotifications DN ON B.request_id = DN.request_id AND DN.response = 'accepted'
      LEFT JOIN Drivers D ON DN.driver_id = D.driver_id
      ${whereClause}
      ORDER BY
        CASE WHEN '${validSortBy}' = 'BookingID' THEN B.request_id END ${validSortOrder},
        CASE WHEN '${validSortBy}' = 'TripDate' THEN B.start_date END ${validSortOrder},
        CASE WHEN '${validSortBy}' = 'Status' THEN B.status END ${validSortOrder},
        CASE WHEN '${validSortBy}' = 'TotalAmount' THEN B.total_cost END ${validSortOrder}
    `;

    const result = await executeQuery(query, params);

    if (!result.recordset || result.recordset.length === 0) {
      logger.warn('No booking data found, returning mock data');
      return generateMockBookingData();
    }

    // Format the results
    const formattedResults = result.recordset.map(booking => ({
      ...booking,
      TotalAmount: parseFloat(booking.TotalAmount).toFixed(2)
    }));

    return formattedResults;
  } catch (error) {
    logger.error('Error generating booking report:', error);
    return generateMockBookingData();
  }
}

function generateMockBookingData() {
  // Generate mock data for testing
  return [
    {
      BookingID: 1001,
      TripDate: '01/15/2023',
      TripTime: '09:00:00',
      Status: 'completed',
      TotalAmount: 1500,
      TouristName: 'Kamal',
      DriverName: 'bandara',
      VehicleName: 'Toyota Innova'
    },
    {
      BookingID: 1002,
      TripDate: '01/20/2023',
      TripTime: '10:30:00',
      Status: 'cancelled',
      TotalAmount: 2000,
      TouristName: 'Charlie Davis',
      DriverName: 'Wijethunga',
      VehicleName: 'Honda City'
    },
    {
      BookingID: 1003,
      TripDate: '01/25/2023',
      TripTime: '14:00:00',
      Status: 'pending',
      TotalAmount: 1800,
      TouristName: 'Ranaka',
      DriverName: 'pasindu',
      VehicleName: 'Maruti Swift'
    }
  ];
}

async function generateRevenueReport(config, startDate, endDate) {
  try {
    const { columns = [], filters = {}, sortBy = 'Date', sortOrder = 'desc' } = config;

    // Build the WHERE clause based on filters and date range
    const conditions = ['B.status = \'payment_completed\''];
    const params = {};

    if (startDate) {
      conditions.push('B.start_date >= @startDate');
      params.startDate = startDate;
    }

    if (endDate) {
      conditions.push('B.start_date <= @endDate');
      params.endDate = endDate;
    }

    // Add custom filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        conditions.push(`B.${key} = @${key}`);
        params[key] = value;
      }
    });

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Validate sort parameters
    const allowedSortFields = ['Date', 'BookingCount', 'Revenue'];
    const allowedSortOrders = ['asc', 'desc'];

    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'Date';
    const validSortOrder = allowedSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder : 'desc';

    // Revenue report query with Sri Lankan booking data
    const query = `
      WITH DailyRevenue AS (
        SELECT
          CONVERT(DATE, B.start_date) AS ReportDate,
          COUNT(*) AS BookingCount,
          SUM(B.total_cost) AS Revenue
        FROM BookingRequests B
        ${whereClause}
        GROUP BY CONVERT(DATE, B.start_date)
      )
      SELECT
        CONVERT(VARCHAR, DR.ReportDate, 101) AS Date,
        DR.BookingCount,
        DR.Revenue,
        (SELECT SUM(Revenue) FROM DailyRevenue WHERE ReportDate <= DR.ReportDate) AS CumulativeRevenue
      FROM DailyRevenue DR
      ORDER BY
        CASE WHEN '${validSortBy}' = 'Date' THEN DR.ReportDate END ${validSortOrder},
        CASE WHEN '${validSortBy}' = 'BookingCount' THEN DR.BookingCount END ${validSortOrder},
        CASE WHEN '${validSortBy}' = 'Revenue' THEN DR.Revenue END ${validSortOrder}
    `;

    const result = await executeQuery(query, params);

    if (!result.recordset || result.recordset.length === 0) {
      logger.warn('No revenue data found, returning mock data');
      return generateMockRevenueData();
    }

    // Format the results
    const formattedResults = result.recordset.map(revenue => ({
      ...revenue,
      Revenue: parseFloat(revenue.Revenue).toFixed(2),
      CumulativeRevenue: parseFloat(revenue.CumulativeRevenue).toFixed(2)
    }));

    return formattedResults;
  } catch (error) {
    logger.error('Error generating revenue report:', error);
    return generateMockRevenueData();
  }
}

function generateMockRevenueData() {
  // Generate mock data for testing
  return [
    {
      Date: '01/01/2023',
      BookingCount: 5,
      Revenue: 7500,
      CumulativeRevenue: 7500
    },
    {
      Date: '01/02/2023',
      BookingCount: 8,
      Revenue: 12000,
      CumulativeRevenue: 19500
    },
    {
      Date: '01/03/2023',
      BookingCount: 6,
      Revenue: 9000,
      CumulativeRevenue: 28500
    },
    {
      Date: '01/04/2023',
      BookingCount: 10,
      Revenue: 15000,
      CumulativeRevenue: 43500
    },
    {
      Date: '01/05/2023',
      BookingCount: 7,
      Revenue: 10500,
      CumulativeRevenue: 54000
    }
  ];
}

async function generateDestinationReport(config, startDate, endDate) {
  try {
    const { columns = [], filters = {}, sortBy = 'BookingCount', sortOrder = 'desc' } = config;

    // Build the WHERE clause based on filters and date range
    const conditions = [];
    const params = {};

    if (startDate) {
      conditions.push('B.start_date >= @startDate');
      params.startDate = startDate;
    }

    if (endDate) {
      conditions.push('B.start_date <= @endDate');
      params.endDate = endDate;
    }

    // Add custom filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (key.startsWith('Destination')) {
          conditions.push(`D.${key.replace('Destination', '').toLowerCase()} = @${key}`);
        } else {
          conditions.push(`B.${key} = @${key}`);
        }
        params[key] = value;
      }
    });

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Validate sort parameters
    const allowedSortFields = ['DestinationID', 'Name', 'BookingCount', 'Revenue'];
    const allowedSortOrders = ['asc', 'desc'];

    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'BookingCount';
    const validSortOrder = allowedSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder : 'desc';

    // Destination report query with Sri Lankan destination data
    const query = `
      SELECT
        D.destination_id AS DestinationID,
        D.name AS Name,
        D.location AS Location,
        COUNT(B.request_id) AS BookingCount,
        ISNULL(SUM(CASE WHEN B.status = 'payment_completed' THEN B.total_cost ELSE 0 END), 0) AS Revenue
      FROM Destinations D
      LEFT JOIN BookingRequests B ON D.name = B.destination OR B.destination LIKE '%' + D.name + '%'
      ${whereClause}
      GROUP BY D.destination_id, D.name, D.location
      ORDER BY
        CASE WHEN '${validSortBy}' = 'DestinationID' THEN D.destination_id END ${validSortOrder},
        CASE WHEN '${validSortBy}' = 'Name' THEN D.name END ${validSortOrder},
        CASE WHEN '${validSortBy}' = 'BookingCount' THEN COUNT(B.request_id) END ${validSortOrder},
        CASE WHEN '${validSortBy}' = 'Revenue' THEN SUM(CASE WHEN B.status = 'payment_completed' THEN B.total_cost ELSE 0 END) END ${validSortOrder}
    `;

    const result = await executeQuery(query, params);

    if (!result.recordset || result.recordset.length === 0) {
      logger.warn('No destination data found, returning mock data');
      return generateMockDestinationData();
    }

    // Format the results
    const formattedResults = result.recordset.map(destination => ({
      ...destination,
      Revenue: parseFloat(destination.Revenue).toFixed(2)
    }));

    return formattedResults;
  } catch (error) {
    logger.error('Error generating destination report:', error);
    return generateMockDestinationData();
  }
}

function generateMockDestinationData() {
  // Generate mock data for testing
  return [
    {
      DestinationID: 1,
      Name: 'Taj Mahal',
      Location: 'Agra',
      BookingCount: 50,
      Revenue: 75000
    },
    {
      DestinationID: 2,
      Name: 'Jaipur Palace',
      Location: 'Jaipur',
      BookingCount: 35,
      Revenue: 52500
    },
    {
      DestinationID: 3,
      Name: 'Gateway of India',
      Location: 'Mumbai',
      BookingCount: 45,
      Revenue: 67500
    },
    {
      DestinationID: 4,
      Name: 'Golden Temple',
      Location: 'Amritsar',
      BookingCount: 40,
      Revenue: 60000
    }
  ];
}

async function generateCustomReport(config, startDate, endDate) {
  // Implementation for custom reports with dynamic SQL
  // This is a placeholder - implement the actual query based on your schema
  logger.info('Generating custom report with config:', config);
  return generateMockDriverData();
}

module.exports = {
  getAllReportTemplates,
  getReportTemplateById,
  createReportTemplate,
  updateReportTemplate,
  deleteReportTemplate,
  generateReport
};
