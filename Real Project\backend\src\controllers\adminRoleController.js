// src/controllers/adminRoleController.js
const { executeQuery } = require('../utils/dbUtils');
const ApiError = require('../utils/ApiError');
const catchAsync = require('../utils/catchAsync');
const logger = require('../utils/logger');
const { logActivity } = require('./adminAuditController');

/**
 * Get all admin roles
 * @route GET /api/admin/roles
 */
const getAllRoles = catchAsync(async (req, res) => {
  const query = `
    SELECT
      AR.role_id,
      AR.role_name,
      AR.description,
      AR.is_system_role,
      AR.created_at,
      AR.updated_at,
      COUNT(AU.admin_user_id) AS user_count,
      COUNT(ARP.permission_id) AS permission_count
    FROM AdminRoles AR
    LEFT JOIN AdminUsers AU ON AR.role_id = AU.role_id
    LEFT JOIN AdminRolePermissions ARP ON AR.role_id = ARP.role_id
    GROUP BY
      AR.role_id,
      AR.role_name,
      AR.description,
      AR.is_system_role,
      AR.created_at,
      AR.updated_at
    ORDER BY AR.role_name
  `;

  const result = await executeQuery(query);

  res.json({
    success: true,
    data: result.recordset
  });
});

/**
 * Get role by ID
 * @route GET /api/admin/roles/:id
 */
const getRoleById = catchAsync(async (req, res) => {
  const { id } = req.params;

  // Get role details
  const roleQuery = `
    SELECT
      role_id,
      role_name,
      description,
      is_system_role,
      created_at,
      updated_at
    FROM AdminRoles
    WHERE role_id = @id
  `;

  const roleResult = await executeQuery(roleQuery, { id });

  if (!roleResult.recordset || roleResult.recordset.length === 0) {
    throw new ApiError(404, 'Role not found');
  }

  const role = roleResult.recordset[0];

  // Get permissions assigned to this role
  const permissionsQuery = `
    SELECT
      P.permission_id,
      P.permission_name,
      P.description,
      P.category
    FROM Permissions P
    JOIN AdminRolePermissions ARP ON P.permission_id = ARP.permission_id
    WHERE ARP.role_id = @roleId
    ORDER BY P.category, P.permission_name
  `;

  const permissionsResult = await executeQuery(permissionsQuery, { roleId: id });

  // Get users assigned to this role
  const usersQuery = `
    SELECT
      U.user_id,
      U.full_name,
      U.email,
      U.role,
      AU.created_at AS assigned_at
    FROM Users U
    JOIN AdminUsers AU ON U.user_id = AU.user_id
    WHERE AU.role_id = @roleId
    ORDER BY U.full_name
  `;

  const usersResult = await executeQuery(usersQuery, { roleId: id });

  res.json({
    success: true,
    data: {
      ...role,
      permissions: permissionsResult.recordset,
      users: usersResult.recordset
    }
  });
});

/**
 * Create a new role
 * @route POST /api/admin/roles
 */
const createRole = catchAsync(async (req, res) => {
  const { roleName, description } = req.body;

  // Validate required fields
  if (!roleName) {
    throw new ApiError(400, 'Role name is required');
  }

  // Check if role name already exists
  const checkQuery = `
    SELECT 1 FROM AdminRoles WHERE role_name = @roleName
  `;

  const checkResult = await executeQuery(checkQuery, { roleName });

  if (checkResult.recordset && checkResult.recordset.length > 0) {
    throw new ApiError(400, 'Role name already exists');
  }

  // Create the role
  const query = `
    INSERT INTO AdminRoles (role_name, description, is_system_role, created_at)
    VALUES (@roleName, @description, 0, GETDATE());

    SELECT SCOPE_IDENTITY() AS role_id;
  `;

  const result = await executeQuery(query, {
    roleName,
    description: description || null
  });

  if (!result.recordset || !result.recordset[0]) {
    throw new ApiError(500, 'Failed to create role');
  }

  const roleId = result.recordset[0].role_id;

  // Log the activity
  await logActivity({
    userId: req.user.UserID,
    action: 'create',
    entityType: 'admin_role',
    entityId: roleId,
    description: `Created admin role: ${roleName}`,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.status(201).json({
    success: true,
    message: 'Role created successfully',
    data: {
      roleId,
      roleName,
      description
    }
  });
});

/**
 * Update a role
 * @route PUT /api/admin/roles/:id
 */
const updateRole = catchAsync(async (req, res) => {
  const { id } = req.params;
  const { roleName, description } = req.body;

  // Validate required fields
  if (!roleName) {
    throw new ApiError(400, 'Role name is required');
  }

  // Check if role exists
  const checkRoleQuery = `
    SELECT role_name, is_system_role FROM AdminRoles WHERE role_id = @id
  `;

  const checkRoleResult = await executeQuery(checkRoleQuery, { id });

  if (!checkRoleResult.recordset || checkRoleResult.recordset.length === 0) {
    throw new ApiError(404, 'Role not found');
  }

  const existingRole = checkRoleResult.recordset[0];

  // Check if trying to update a system role
  if (existingRole.is_system_role) {
    throw new ApiError(403, 'System roles cannot be modified');
  }

  // Check if new role name already exists (for another role)
  if (roleName !== existingRole.role_name) {
    const checkNameQuery = `
      SELECT 1 FROM AdminRoles WHERE role_name = @roleName AND role_id <> @id
    `;

    const checkNameResult = await executeQuery(checkNameQuery, { roleName, id });

    if (checkNameResult.recordset && checkNameResult.recordset.length > 0) {
      throw new ApiError(400, 'Role name already exists');
    }
  }

  // Update the role
  const query = `
    UPDATE AdminRoles
    SET
      role_name = @roleName,
      description = @description,
      updated_at = GETDATE()
    WHERE role_id = @id;
  `;

  await executeQuery(query, {
    id,
    roleName,
    description: description || null
  });

  // Log the activity
  await logActivity({
    userId: req.user.UserID,
    action: 'update',
    entityType: 'admin_role',
    entityId: id,
    description: `Updated admin role: ${roleName}`,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.json({
    success: true,
    message: 'Role updated successfully',
    data: {
      roleId: id,
      roleName,
      description
    }
  });
});

/**
 * Delete a role
 * @route DELETE /api/admin/roles/:id
 */
const deleteRole = catchAsync(async (req, res) => {
  const { id } = req.params;

  // Check if role exists
  const checkRoleQuery = `
    SELECT role_name, is_system_role FROM AdminRoles WHERE role_id = @id
  `;

  const checkRoleResult = await executeQuery(checkRoleQuery, { id });

  if (!checkRoleResult.recordset || checkRoleResult.recordset.length === 0) {
    throw new ApiError(404, 'Role not found');
  }

  const existingRole = checkRoleResult.recordset[0];

  // Check if trying to delete a system role
  if (existingRole.is_system_role) {
    throw new ApiError(403, 'System roles cannot be deleted');
  }

  // Check if role has users assigned
  const checkUsersQuery = `
    SELECT COUNT(*) AS user_count FROM AdminUsers WHERE role_id = @id
  `;

  const checkUsersResult = await executeQuery(checkUsersQuery, { id });

  if (checkUsersResult.recordset[0].user_count > 0) {
    throw new ApiError(400, 'Cannot delete role with assigned users');
  }

  // Delete the role
  const query = `
    DELETE FROM AdminRoles WHERE role_id = @id
  `;

  await executeQuery(query, { id });

  // Log the activity
  await logActivity({
    userId: req.user.UserID,
    action: 'delete',
    entityType: 'admin_role',
    entityId: id,
    description: `Deleted admin role: ${existingRole.role_name}`,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.json({
    success: true,
    message: 'Role deleted successfully'
  });
});

/**
 * Assign permissions to a role
 * @route POST /api/admin/roles/:id/permissions
 */
const assignPermissions = catchAsync(async (req, res) => {
  const { id } = req.params;
  const { permissionIds } = req.body;

  // Validate required fields
  if (!Array.isArray(permissionIds)) {
    throw new ApiError(400, 'permissionIds must be an array');
  }

  // Check if role exists
  const checkRoleQuery = `
    SELECT role_name FROM AdminRoles WHERE role_id = @id
  `;

  const checkRoleResult = await executeQuery(checkRoleQuery, { id });

  if (!checkRoleResult.recordset || checkRoleResult.recordset.length === 0) {
    throw new ApiError(404, 'Role not found');
  }

  const roleName = checkRoleResult.recordset[0].role_name;

  // Start a transaction
  const transaction = new sql.Transaction();

  try {
    await transaction.begin();

    // Delete existing permissions for this role
    const deleteQuery = `
      DELETE FROM AdminRolePermissions WHERE role_id = @roleId
    `;

    await transaction.request()
      .input('roleId', sql.Int, id)
      .query(deleteQuery);

    // Insert new permissions
    if (permissionIds.length > 0) {
      // Create a table-valued parameter for bulk insert
      const tvp = new sql.Table();
      tvp.columns.add('role_id', sql.Int);
      tvp.columns.add('permission_id', sql.Int);

      permissionIds.forEach(permissionId => {
        tvp.rows.add(id, permissionId);
      });

      const insertQuery = `
        INSERT INTO AdminRolePermissions (role_id, permission_id)
        SELECT role_id, permission_id FROM @permissions
      `;

      await transaction.request()
        .input('permissions', tvp)
        .query(insertQuery);
    }

    await transaction.commit();

    // Log the activity
    await logActivity({
      userId: req.user.UserID,
      action: 'update',
      entityType: 'admin_role_permissions',
      entityId: id,
      description: `Updated permissions for role: ${roleName}`,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'Permissions assigned successfully'
    });
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
});

module.exports = {
  getAllRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  assignPermissions
};
