// src/controllers/bookingRequestController.js
const { executeQuery } = require('../config/DB/db');
const { catchAsync, ApiError } = require('../utils/errorHandler');
const { sendEmail } = require('../services/emailService');
const logger = require('../config/logger');
const { VEHICLE_RATES } = require('../../utils/constants');

/**
 * Create a new booking request
 * @route POST /api/booking-requests
 */
const createBookingRequest = catchAsync(async (req, res) => {
  const {
    origin,
    destination,
    waypoints,
    startDate,
    startTime,
    tripType,
    vehicleType,
    numTravelers,
    totalDistance,
    estimatedDuration,
    totalCost,
    driverAccommodation,
    specialRequests
  } = req.body;

  // Get tourist ID from authenticated user
  const touristId = req.user.UserID || req.user.user_id; // Support both column naming conventions

  if (!touristId) {
    throw new ApiError(403, 'Only tourists can create booking requests');
  }

  // Log the request body for debugging
  console.log('Booking request body:', req.body);

  // Validate required fields
  if (!origin || !destination || !startDate || !startTime || !vehicleType || !numTravelers) {
    console.log('Missing required fields:', {
      origin: !!origin,
      destination: !!destination,
      startDate: !!startDate,
      startTime: !!startTime,
      vehicleType: !!vehicleType,
      numTravelers: !!numTravelers
    });
    throw new ApiError(400, 'Required booking information is missing');
  }

  // Set default values for optional fields
  const parsedTotalDistance = totalDistance || 0;
  const parsedEstimatedDuration = estimatedDuration || 0;

  // Begin transaction
  const transaction = await executeQuery('BEGIN TRANSACTION');

  try {
    // Insert booking request
    const requestQuery = `
      INSERT INTO BookingRequests (
        tourist_id, origin, destination, waypoints, start_date, start_time,
        trip_type, vehicle_type, num_travelers, total_distance,
        estimated_duration, total_cost, driver_accommodation, special_requests
      )
      OUTPUT INSERTED.request_id
      VALUES (
        @touristId, @origin, @destination, @waypoints, @startDate, @startTime,
        @tripType, @vehicleType, @numTravelers, @totalDistance,
        @estimatedDuration, @totalCost, @driverAccommodation, @specialRequests
      )
    `;

    const requestResult = await executeQuery(requestQuery, {
      touristId,
      origin,
      destination,
      waypoints: JSON.stringify(waypoints || []),
      startDate,
      startTime,
      tripType,
      vehicleType,
      numTravelers,
      totalDistance: parsedTotalDistance,
      estimatedDuration: Math.round(parsedEstimatedDuration * 60), // Convert hours to minutes
      totalCost,
      driverAccommodation: driverAccommodation || 'paid',
      specialRequests: specialRequests || ''
    });

    if (!requestResult.recordset || !requestResult.recordset[0]) {
      throw new ApiError(500, 'Failed to create booking request');
    }

    const requestId = requestResult.recordset[0].request_id;

    // Commit transaction
    await executeQuery('COMMIT TRANSACTION');

    // Notify eligible drivers
    await exports.notifyEligibleDrivers(requestId, vehicleType);

    logger.info(`New booking request created: ${requestId}`);

    res.status(201).json({
      success: true,
      message: 'Booking request created successfully',
      data: { requestId }
    });
  } catch (error) {
    // Rollback transaction in case of error
    await executeQuery('ROLLBACK TRANSACTION');
    throw error;
  }
});

/**
 * Notify eligible drivers about a new booking request
 * @param {number} requestId - The booking request ID
 * @param {string} vehicleType - The vehicle type requested
 */
exports.notifyEligibleDrivers = async (requestId, vehicleType) => {
  try {
    // Get the booking request details
    const requestQuery = "SELECT * FROM BookingRequests WHERE request_id = @requestId";
    const requestResult = await executeQuery(requestQuery, { requestId });

    if (!requestResult.recordset || requestResult.recordset.length === 0) {
      throw new Error(`Booking request not found: ${requestId}`);
    }

    const bookingRequest = requestResult.recordset[0];

    // Find eligible drivers based on vehicle type
    const driversQuery = `
      SELECT d.driver_id, u.email, u.full_name
      FROM Drivers d
      JOIN Users u ON d.user_id = u.user_id
      JOIN Vehicles v ON d.driver_id = v.driver_id
      WHERE v.vehicle_type = @vehicleType AND d.status = 'Approved'
    `;

    let driversResult;
    try {
      driversResult = await executeQuery(driversQuery, { vehicleType });

      if (!driversResult.recordset || driversResult.recordset.length === 0) {
        logger.warn(`No eligible drivers found for vehicle type: ${vehicleType}`);
        return { success: false, error: 'No eligible drivers found' };
      }

      logger.info(`Found ${driversResult.recordset.length} eligible drivers for vehicle type: ${vehicleType}`);
    } catch (error) {
      logger.error(`Error finding eligible drivers: ${error.message}`);
      return { success: false, error: `Error finding eligible drivers: ${error.message}` };
    }

    // Create notifications and send emails to all eligible drivers
    const notifiedDrivers = [];

    for (const driver of driversResult.recordset) {
      logger.info(`Processing driver: ${driver.full_name} (${driver.email})`);

      // Check if notification already exists
      const existingNotificationQuery = `
        SELECT notification_id FROM DriverNotifications
        WHERE request_id = @requestId AND driver_id = @driverId
      `;

      const existingNotificationResult = await executeQuery(existingNotificationQuery, {
        requestId,
        driverId: driver.driver_id
      });

      if (existingNotificationResult.recordset && existingNotificationResult.recordset.length > 0) {
        logger.info(`Notification already exists for driver ${driver.driver_id}`);
        continue;
      }

      // Create notification record
      logger.info(`Creating notification for driver ${driver.driver_id}`);
      const notificationQuery = `
        INSERT INTO DriverNotifications
        (request_id, driver_id, response)
        OUTPUT INSERTED.notification_id
        VALUES (@requestId, @driverId, 'pending')
      `;

      const notificationResult = await executeQuery(notificationQuery, {
        requestId,
        driverId: driver.driver_id
      });

      const notificationId = notificationResult.recordset[0].notification_id;

      // Get tourist details for the email
      const touristQuery = `
        SELECT u.full_name
        FROM Users u
        JOIN BookingRequests br ON u.user_id = br.tourist_id
        WHERE br.request_id = @requestId
      `;

      const touristResult = await executeQuery(touristQuery, { requestId });
      const touristName = touristResult.recordset[0]?.full_name || 'Tourist';

      // Send email notification
      logger.info(`Sending email to driver ${driver.driver_id} (${driver.email})`);
      try {
        const emailResult = await sendDriverBookingRequestEmail(
          driver.email,
          driver.full_name,
          touristName,
          bookingRequest,
          requestId,
          driver.driver_id,
          notificationId
        );

        if (emailResult && emailResult.success) {
          logger.info(`Email sent successfully to driver ${driver.driver_id} (${driver.email})`);
        } else {
          logger.warn(`Failed to send email to driver ${driver.driver_id}: ${emailResult ? emailResult.error : 'Unknown error'}`);
        }
      } catch (error) {
        logger.error(`Error sending email to driver ${driver.driver_id}: ${error.message}`);
      }

      notifiedDrivers.push({
        driverId: driver.driver_id,
        email: driver.email,
        name: driver.full_name
      });
    }

    logger.info(`Notified ${notifiedDrivers.length} drivers about booking request ${requestId}`);
    return {
      success: true,
      notifiedDrivers: notifiedDrivers.length,
      drivers: notifiedDrivers
    };
  } catch (error) {
    logger.error(`Error notifying drivers: ${error.message}`);
    return { success: false, error: error.message };
  }
};

/**
 * Send email notification to driver about a new booking request
 */
const sendDriverBookingRequestEmail = async (
  driverEmail,
  driverName,
  touristName,
  bookingRequest,
  requestId,
  driverId,
  notificationId
) => {
  try {
    // Use the emailService function instead of implementing it here
    const emailService = require('../services/emailService');
    return await emailService.sendDriverBookingRequestEmail(
      driverEmail,
      driverName,
      bookingRequest,
      requestId,
      driverId,
      notificationId
    );
  } catch (error) {
    logger.error(`Error sending driver booking request email: ${error.message}`);
    return { success: false, error: error.message };
  }
};



/**
 * Get booking request status
 * @route GET /api/booking-requests/:requestId
 */
const getBookingRequestStatus = catchAsync(async (req, res) => {
  const { requestId } = req.params;
  const userId = req.user.UserID || req.user.user_id; // Support both column naming conventions

  console.log(`Getting booking request status for ID: ${requestId}, User ID: ${userId}`);

  // Try a simpler query first to check if the booking request exists
  const simpleQuery = `
    SELECT * FROM BookingRequests
    WHERE request_id = @requestId
  `;

  console.log(`Executing simple query to verify booking request exists: ${simpleQuery}`);
  console.log(`With parameters: requestId = ${requestId}`);

  const simpleResult = await executeQuery(simpleQuery, { requestId });

  console.log(`Simple query result count: ${simpleResult.recordset ? simpleResult.recordset.length : 0}`);

  if (!simpleResult.recordset || simpleResult.recordset.length === 0) {
    console.log(`No booking request found with ID: ${requestId} in simple query`);
    throw new ApiError(404, 'Booking request not found');
  }

  console.log(`Booking request found with simple query. Proceeding with full query.`);

  // Get the booking request with user details
  const requestQuery = `
    SELECT br.*, u.full_name as tourist_name
    FROM BookingRequests br
    JOIN Users u ON br.tourist_id = u.user_id
    WHERE br.request_id = @requestId
  `;

  console.log(`Executing full query: ${requestQuery}`);

  const requestResult = await executeQuery(requestQuery, { requestId });

  console.log(`Full query result count: ${requestResult.recordset ? requestResult.recordset.length : 0}`);

  if (!requestResult.recordset || requestResult.recordset.length === 0) {
    console.log(`No booking request found with ID: ${requestId} in full query. This suggests a JOIN issue.`);

    // Fall back to the simple query result if the JOIN fails
    console.log(`Using simple query result as fallback`);
    const request = simpleResult.recordset[0];

    // Get tourist name separately
    const touristQuery = `
      SELECT full_name FROM Users WHERE user_id = @touristId
    `;

    console.log(`Executing tourist query: ${touristQuery}`);
    console.log(`With parameters: touristId = ${request.tourist_id}`);

    const touristResult = await executeQuery(touristQuery, { touristId: request.tourist_id });

    if (touristResult.recordset && touristResult.recordset.length > 0) {
      request.tourist_name = touristResult.recordset[0].full_name;
      console.log(`Found tourist name: ${request.tourist_name}`);
    } else {
      request.tourist_name = 'Unknown Tourist';
      console.log(`Could not find tourist name for ID: ${request.tourist_id}`);
    }

    // Check if user is authorized to view this request
    const isTourist = request.tourist_id === userId;
    const isDriver = await isUserDriver(userId);

    if (!isTourist && !isDriver) {
      console.log(`User ${userId} is not authorized to view booking request ${requestId}`);
      throw new ApiError(403, 'You are not authorized to view this booking request');
    }

    // Get driver responses
    const responsesQuery = `
      SELECT dn.*, u.full_name as driver_name, v.vehicle_model, v.vehicle_color, v.license_plate
      FROM DriverNotifications dn
      JOIN Drivers d ON dn.driver_id = d.driver_id
      JOIN Users u ON d.user_id = u.user_id
      JOIN Vehicles v ON d.driver_id = v.driver_id
      WHERE dn.request_id = @requestId
    `;

    console.log(`Executing responses query: ${responsesQuery}`);

    const responsesResult = await executeQuery(responsesQuery, { requestId });
    const driverResponses = responsesResult.recordset || [];

    console.log(`Found ${driverResponses.length} driver responses`);

    res.status(200).json({
      success: true,
      data: {
        request,
        driverResponses
      }
    });

    return;
  }

  const request = requestResult.recordset[0];

  // Check if user is authorized to view this request
  const isTourist = request.tourist_id === userId;
  const isDriver = await isUserDriver(userId);

  if (!isTourist && !isDriver) {
    throw new ApiError(403, 'You are not authorized to view this booking request');
  }

  // Get driver responses
  const responsesQuery = `
    SELECT dn.*, u.full_name as driver_name, v.vehicle_model, v.vehicle_color, v.registration_number as license_plate
    FROM DriverNotifications dn
    JOIN Drivers d ON dn.driver_id = d.driver_id
    JOIN Users u ON d.user_id = u.user_id
    LEFT JOIN Vehicles v ON d.driver_id = v.driver_id
    WHERE dn.request_id = @requestId
  `;

  const responsesResult = await executeQuery(responsesQuery, { requestId });
  const driverResponses = responsesResult.recordset || [];

  // Get confirmed driver details if the booking status is 'driver_confirmed'
  let confirmedDriver = null;
  if (request.status === 'driver_confirmed') {
    console.log(`Booking is driver_confirmed. Checking for driver details...`);

    // First try to get driver details from assigned_driver_id if it exists
    if (request.assigned_driver_id) {
      console.log(`Using assigned_driver_id: ${request.assigned_driver_id}`);

      const driverQuery = `
        SELECT
          d.driver_id,
          u.full_name,
          u.phone,
          u.email,
          v.make_model,
          v.vehicle_type,
          v.registration_number as license_plate,
          'Not specified' as vehicle_color,
          v.seat_count,
          v.air_conditioned,
          v.vehicle_photo
        FROM Drivers d
        JOIN Users u ON d.user_id = u.user_id
        LEFT JOIN Vehicles v ON d.driver_id = v.driver_id
        WHERE d.driver_id = @driverId
      `;

      try {
        const driverResult = await executeQuery(driverQuery, { driverId: request.assigned_driver_id });

        if (driverResult.recordset && driverResult.recordset.length > 0) {
          confirmedDriver = driverResult.recordset[0];
          console.log(`Found confirmed driver details using assigned_driver_id:`, confirmedDriver);
        } else {
          console.log(`No driver details found for driver ID: ${request.assigned_driver_id}`);
        }
      } catch (error) {
        console.error(`Error fetching driver details: ${error.message}`);
      }
    }

    // If we still don't have driver details, check driver notifications
    if (!confirmedDriver) {
      console.log(`No driver details found using assigned_driver_id. Checking driver notifications...`);

      // Try to find the driver who accepted the booking from notifications
      const acceptedNotification = driverResponses.find(notification => notification.response === 'accepted');

      if (acceptedNotification) {
        console.log(`Found accepted notification from driver ID: ${acceptedNotification.driver_id}`);

        const driverQuery = `
          SELECT
            d.driver_id,
            u.full_name,
            u.phone,
            u.email,
            v.make_model,
            v.vehicle_type,
            v.registration_number as license_plate,
            'Not specified' as vehicle_color,
            v.seat_count,
            v.air_conditioned,
            v.vehicle_photo
          FROM Drivers d
          JOIN Users u ON d.user_id = u.user_id
          LEFT JOIN Vehicles v ON d.driver_id = v.driver_id
          WHERE d.driver_id = @driverId
        `;

        try {
          const driverResult = await executeQuery(driverQuery, { driverId: acceptedNotification.driver_id });

          if (driverResult.recordset && driverResult.recordset.length > 0) {
            confirmedDriver = driverResult.recordset[0];
            console.log(`Found confirmed driver details from notification:`, confirmedDriver);

            // Update the assigned_driver_id in the database if it's missing
            if (!request.assigned_driver_id) {
              const updateQuery = `
                UPDATE BookingRequests
                SET assigned_driver_id = @driverId
                WHERE request_id = @requestId AND (assigned_driver_id IS NULL OR assigned_driver_id = 0)
              `;

              try {
                await executeQuery(updateQuery, {
                  requestId,
                  driverId: acceptedNotification.driver_id
                });
                console.log(`Updated assigned_driver_id to ${acceptedNotification.driver_id} for request ${requestId}`);
              } catch (updateError) {
                console.error(`Error updating assigned_driver_id: ${updateError.message}`);
              }
            }
          } else {
            console.log(`No driver details found for notification driver ID: ${acceptedNotification.driver_id}`);
          }
        } catch (error) {
          console.error(`Error fetching driver details from notification: ${error.message}`);
        }
      } else {
        console.log(`No accepted notification found for request ${requestId}`);
      }
    }

    // If we still don't have driver details, try to find any driver who has responded
    if (!confirmedDriver && driverResponses && driverResponses.length > 0) {
      console.log(`No accepted notification found. Checking all driver responses...`);

      // Get the first driver response as a fallback
      const firstDriverResponse = driverResponses[0];

      if (firstDriverResponse && firstDriverResponse.driver_id) {
        console.log(`Using first driver response with driver ID: ${firstDriverResponse.driver_id}`);

        const driverQuery = `
          SELECT
            d.driver_id,
            u.full_name,
            u.phone,
            u.email,
            v.make_model,
            v.vehicle_type,
            v.registration_number as license_plate,
            'Not specified' as vehicle_color,
            v.seat_count,
            v.air_conditioned,
            v.vehicle_photo
          FROM Drivers d
          JOIN Users u ON d.user_id = u.user_id
          LEFT JOIN Vehicles v ON d.driver_id = v.driver_id
          WHERE d.driver_id = @driverId
        `;

        try {
          const driverResult = await executeQuery(driverQuery, { driverId: firstDriverResponse.driver_id });

          if (driverResult.recordset && driverResult.recordset.length > 0) {
            confirmedDriver = driverResult.recordset[0];
            console.log(`Found driver details from first response:`, confirmedDriver);
          }
        } catch (error) {
          console.error(`Error fetching driver details from first response: ${error.message}`);
        }
      }
    }
  }

  res.status(200).json({
    success: true,
    data: {
      request,
      driverResponses,
      confirmedDriver
    }
  });
});

/**
 * Cancel booking request
 * @route POST /api/booking-requests/:requestId/cancel
 */
const cancelBookingRequest = catchAsync(async (req, res) => {
  const { requestId } = req.params;
  const userId = req.user.UserID || req.user.user_id; // Support both column naming conventions

  // Get the booking request
  const requestQuery = "SELECT * FROM BookingRequests WHERE request_id = @requestId";
  const requestResult = await executeQuery(requestQuery, { requestId });

  if (!requestResult.recordset || requestResult.recordset.length === 0) {
    throw new ApiError(404, 'Booking request not found');
  }

  const request = requestResult.recordset[0];

  // Check if user is authorized to cancel this request
  if (request.tourist_id !== userId) {
    throw new ApiError(403, 'You are not authorized to cancel this booking request');
  }

  // Check if request can be cancelled
  if (request.status !== 'pending') {
    throw new ApiError(400, `Cannot cancel booking request with status: ${request.status}`);
  }

  // Update request status
  const updateQuery = `
    UPDATE BookingRequests
    SET status = 'cancelled', updated_at = GETDATE()
    WHERE request_id = @requestId
  `;

  await executeQuery(updateQuery, { requestId });

  // Update all pending driver notifications
  const updateNotificationsQuery = `
    UPDATE DriverNotifications
    SET response = 'cancelled', updated_at = GETDATE()
    WHERE request_id = @requestId AND response = 'pending'
  `;

  await executeQuery(updateNotificationsQuery, { requestId });

  res.status(200).json({
    success: true,
    message: 'Booking request cancelled successfully'
  });
});

/**
 * Check if a user is a driver
 * @param {number} userId - The user ID
 * @returns {Promise<boolean>} - True if user is a driver
 */
const isUserDriver = async (userId) => {
  const query = "SELECT driver_id FROM Drivers WHERE user_id = @userId";
  const result = await executeQuery(query, { userId });

  return result.recordset && result.recordset.length > 0;
};

/**
 * Respond to booking request (for drivers)
 * @route POST /api/booking-requests/response
 */
const respondToBookingRequest = catchAsync(async (req, res) => {
  const { notificationId, response } = req.body;
  const userId = req.user.UserID || req.user.user_id; // Support both column naming conventions

  logger.info(`Processing booking response: notificationId=${notificationId}, response=${response}, userId=${userId}`);

  // Validate response
  if (!['accepted', 'rejected'].includes(response)) {
    logger.warn(`Invalid response value: ${response}`);
    throw new ApiError(400, 'Invalid response. Must be "accepted" or "rejected"');
  }

  // Get driver ID from user ID
  const driverQuery = "SELECT driver_id FROM Drivers WHERE user_id = @userId";
  logger.debug(`Executing driver query: ${driverQuery} with userId=${userId}`);

  const driverResult = await executeQuery(driverQuery, { userId });

  if (!driverResult.recordset || driverResult.recordset.length === 0) {
    logger.warn(`User ${userId} is not a registered driver`);
    throw new ApiError(403, 'You must be a registered driver to respond to booking requests');
  }

  const driverId = driverResult.recordset[0].driver_id;
  logger.info(`Found driver ID: ${driverId} for user ${userId}`);

  // Get notification details
  const notificationQuery = `
    SELECT dn.*, br.request_id, br.status as request_status
    FROM DriverNotifications dn
    JOIN BookingRequests br ON dn.request_id = br.request_id
    WHERE dn.notification_id = @notificationId
  `;

  logger.debug(`Executing notification query: ${notificationQuery} with notificationId=${notificationId}`);
  const notificationResult = await executeQuery(notificationQuery, { notificationId });

  if (!notificationResult.recordset || notificationResult.recordset.length === 0) {
    logger.warn(`Notification not found: ${notificationId}`);
    throw new ApiError(404, 'Notification not found');
  }

  const notification = notificationResult.recordset[0];
  logger.info(`Found notification: ${JSON.stringify(notification)}`);

  // Check if driver is authorized to respond to this notification
  if (notification.driver_id !== driverId) {
    logger.warn(`Driver ${driverId} is not authorized to respond to notification ${notificationId} (belongs to driver ${notification.driver_id})`);
    throw new ApiError(403, 'You are not authorized to respond to this notification');
  }

  // Check if notification can be responded to
  if (notification.response !== 'pending') {
    logger.warn(`Cannot respond to notification with status: ${notification.response}`);
    throw new ApiError(400, `Cannot respond to notification with status: ${notification.response}`);
  }

  // Check if booking request is still pending
  if (notification.request_status !== 'pending') {
    logger.warn(`Cannot respond to booking request with status: ${notification.request_status}`);
    throw new ApiError(400, `Cannot respond to booking request with status: ${notification.request_status}`);
  }

  // Begin transaction
  logger.info('Beginning database transaction');
  try {
    await executeQuery('BEGIN TRANSACTION');
    logger.debug('Transaction started successfully');
  } catch (txError) {
    logger.error(`Error starting transaction: ${txError.message}`);
    throw new ApiError(500, 'Database error: Could not start transaction');
  }

  try {
    // Update notification response
    const updateNotificationQuery = `
      UPDATE DriverNotifications
      SET response = @response, response_at = GETDATE()
      WHERE notification_id = @notificationId
    `;

    logger.debug(`Executing update notification query: ${updateNotificationQuery}`);
    await executeQuery(updateNotificationQuery, { notificationId, response });
    logger.info(`Updated notification ${notificationId} with response: ${response}`);

    // If driver accepted, update booking request status and assign driver
    if (response === 'accepted') {
      // Check if assigned_driver_id column exists
      const checkColumnQuery = `
        SELECT COUNT(*) as column_exists
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'BookingRequests'
        AND COLUMN_NAME = 'assigned_driver_id'
      `;

      logger.debug(`Checking if assigned_driver_id column exists: ${checkColumnQuery}`);
      const columnCheckResult = await executeQuery(checkColumnQuery);
      const columnExists = columnCheckResult.recordset[0].column_exists > 0;
      logger.info(`assigned_driver_id column exists: ${columnExists}`);

      if (columnExists) {
        // Use assigned_driver_id column if it exists
        // First check if updated_at column exists
        const checkUpdatedAtQuery = `
          SELECT COUNT(*) as column_exists
          FROM INFORMATION_SCHEMA.COLUMNS
          WHERE TABLE_NAME = 'BookingRequests'
          AND COLUMN_NAME = 'updated_at'
        `;

        logger.debug(`Checking if updated_at column exists: ${checkUpdatedAtQuery}`);
        const updatedAtCheckResult = await executeQuery(checkUpdatedAtQuery);
        const updatedAtExists = updatedAtCheckResult.recordset[0].column_exists > 0;
        logger.info(`updated_at column exists: ${updatedAtExists}`);

        let updateRequestQuery;
        if (updatedAtExists) {
          updateRequestQuery = `
            UPDATE BookingRequests
            SET status = 'driver_confirmed', assigned_driver_id = @driverId, updated_at = GETDATE()
            WHERE request_id = @requestId
          `;
        } else {
          updateRequestQuery = `
            UPDATE BookingRequests
            SET status = 'driver_confirmed', assigned_driver_id = @driverId
            WHERE request_id = @requestId
          `;
        }

        logger.debug(`Executing update request query with driver assignment: ${updateRequestQuery}`);
        await executeQuery(updateRequestQuery, { requestId: notification.request_id, driverId });
        logger.info(`Updated booking request ${notification.request_id} with driver ${driverId}`);
      } else {
        // Fall back to just updating the status
        // Check if updated_at column exists
        const checkUpdatedAtQuery = `
          SELECT COUNT(*) as column_exists
          FROM INFORMATION_SCHEMA.COLUMNS
          WHERE TABLE_NAME = 'BookingRequests'
          AND COLUMN_NAME = 'updated_at'
        `;

        logger.debug(`Checking if updated_at column exists: ${checkUpdatedAtQuery}`);
        const updatedAtCheckResult = await executeQuery(checkUpdatedAtQuery);
        const updatedAtExists = updatedAtCheckResult.recordset[0].column_exists > 0;
        logger.info(`updated_at column exists: ${updatedAtExists}`);

        let updateRequestQuery;
        if (updatedAtExists) {
          updateRequestQuery = `
            UPDATE BookingRequests
            SET status = 'driver_confirmed', updated_at = GETDATE()
            WHERE request_id = @requestId
          `;
        } else {
          updateRequestQuery = `
            UPDATE BookingRequests
            SET status = 'driver_confirmed'
            WHERE request_id = @requestId
          `;
        }

        logger.debug(`Executing update request query without driver assignment: ${updateRequestQuery}`);
        await executeQuery(updateRequestQuery, { requestId: notification.request_id });
        logger.info(`Updated booking request ${notification.request_id} status to driver_confirmed`);
      }

      // Update all other pending notifications for this request to 'expired'
      const updateOtherNotificationsQuery = `
        UPDATE DriverNotifications
        SET response = 'expired', response_at = GETDATE()
        WHERE request_id = @requestId AND notification_id != @notificationId AND response = 'pending'
      `;

      logger.debug(`Updating other notifications to expired: ${updateOtherNotificationsQuery}`);
      await executeQuery(updateOtherNotificationsQuery, {
        requestId: notification.request_id,
        notificationId
      });
      logger.info(`Updated other notifications for request ${notification.request_id} to expired`);
    }

    // Commit transaction
    logger.info('Committing transaction');
    await executeQuery('COMMIT TRANSACTION');
    logger.debug('Transaction committed successfully');

    // Get updated notification details
    const updatedNotificationQuery = `
      SELECT dn.*, br.*, u.full_name as tourist_name
      FROM DriverNotifications dn
      JOIN BookingRequests br ON dn.request_id = br.request_id
      JOIN Users u ON br.tourist_id = u.user_id
      WHERE dn.notification_id = @notificationId
    `;

    logger.debug(`Getting updated notification details: ${updatedNotificationQuery}`);
    const updatedResult = await executeQuery(updatedNotificationQuery, { notificationId });

    if (!updatedResult.recordset || updatedResult.recordset.length === 0) {
      logger.warn(`Could not find updated notification details for ${notificationId}`);
      throw new ApiError(500, 'Could not retrieve updated notification details');
    }

    const updatedNotification = updatedResult.recordset[0];
    logger.info(`Retrieved updated notification details for ${notificationId}`);

    // If driver accepted, notify tourist and other drivers
    if (response === 'accepted') {
      logger.info('Driver accepted booking, sending notifications');

      // Get driver details
      const driverDetailsQuery = `
        SELECT u.full_name, u.email, u.phone, v.make_model as vehicle_model,
               'Not specified' as vehicle_color, v.registration_number as license_plate
        FROM Drivers d
        JOIN Users u ON d.user_id = u.user_id
        JOIN Vehicles v ON d.driver_id = v.driver_id
        WHERE d.driver_id = @driverId
      `;

      logger.debug(`Getting driver details: ${driverDetailsQuery}`);
      const driverDetailsResult = await executeQuery(driverDetailsQuery, { driverId });

      if (!driverDetailsResult.recordset || driverDetailsResult.recordset.length === 0) {
        logger.warn(`Could not find driver details for driver ${driverId}`);
        // Continue without driver details
      } else {
        const driverDetails = driverDetailsResult.recordset[0];
        logger.info(`Retrieved driver details for ${driverId}`);

        // Get tourist email
        const touristQuery = `
          SELECT u.email, u.full_name
          FROM Users u
          JOIN BookingRequests br ON u.user_id = br.tourist_id
          WHERE br.request_id = @requestId
        `;

        logger.debug(`Getting tourist details: ${touristQuery}`);
        const touristResult = await executeQuery(touristQuery, { requestId: notification.request_id });

        if (!touristResult.recordset || touristResult.recordset.length === 0) {
          logger.warn(`Could not find tourist details for request ${notification.request_id}`);
          // Continue without tourist details
        } else {
          const touristEmail = touristResult.recordset[0]?.email;
          const touristName = touristResult.recordset[0]?.full_name;
          logger.info(`Retrieved tourist details: ${touristName} (${touristEmail})`);

          // Send email to tourist
          try {
            await sendTouristDriverConfirmationEmail(
              touristEmail,
              touristName,
              driverDetails,
              updatedNotification
            );
            logger.info(`Sent confirmation email to tourist ${touristName}`);
          } catch (emailError) {
            logger.error(`Error sending email to tourist: ${emailError.message}`);
            // Continue execution even if email fails
          }
        }

        // Notify other drivers that the booking has been accepted
        try {
          const notificationService = require('../services/notificationService');
          await notificationService.notifyOtherDrivers(
            notification.request_id,
            driverId,
            updatedNotification
          );
          logger.info(`Notified other drivers about booking acceptance`);
        } catch (notifyError) {
          logger.error(`Error notifying other drivers: ${notifyError.message}`);
          // Continue execution even if notification fails
        }
      }
    }

    logger.info(`Successfully processed booking response: ${response} for notification ${notificationId}`);
    res.status(200).json({
      success: true,
      message: `Booking request ${response}`,
      data: {
        status: response,
        notification: updatedNotification
      }
    });
  } catch (error) {
    // Rollback transaction in case of error
    logger.error(`Error processing booking response: ${error.message}`);
    logger.error(`Stack trace: ${error.stack}`);

    try {
      logger.info('Rolling back transaction');
      await executeQuery('ROLLBACK TRANSACTION');
      logger.debug('Transaction rolled back successfully');
    } catch (rollbackError) {
      logger.error(`Error rolling back transaction: ${rollbackError.message}`);
      // Continue with the original error
    }

    throw new ApiError(500, `Database error: ${error.message}`);
  }
});

/**
 * Send email to tourist when a driver accepts their booking request
 */
const sendTouristDriverConfirmationEmail = async (
  touristEmail,
  touristName,
  driverDetails,
  bookingRequest
) => {
  try {
    // Format waypoints for display
    let waypointsHtml = '<li>No stops</li>';
    if (bookingRequest.waypoints) {
      try {
        const waypoints = JSON.parse(bookingRequest.waypoints);
        if (waypoints.length > 0) {
          waypointsHtml = waypoints.map(wp => `<li>${wp}</li>`).join('');
        }
      } catch (e) {
        logger.error(`Error parsing waypoints: ${e.message}`);
      }
    }

    // Format date and time
    const startDate = new Date(bookingRequest.start_date).toLocaleDateString();
    const startTime = bookingRequest.start_time;

    // Format duration
    const hours = Math.floor(bookingRequest.estimated_duration / 60);
    const minutes = bookingRequest.estimated_duration % 60;
    const durationText = hours > 0
      ? `${hours} hour${hours > 1 ? 's' : ''}${minutes > 0 ? ` ${minutes} minute${minutes > 1 ? 's' : ''}` : ''}`
      : `${minutes} minute${minutes > 1 ? 's' : ''}`;

    // Generate payment link
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const paymentLink = `${baseUrl}/payment/${bookingRequest.request_id}`;

    // Email subject and body
    const subject = 'Driver Confirmed for Your Trip - Siyoga Travels';
    const body = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #4a6ee0;">Driver Confirmed for Your Trip</h2>
        <p>Hello ${touristName},</p>
        <p>Good news! A driver has accepted your trip request. Here are the details:</p>

        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Trip Details</h3>
          <p><strong>Origin:</strong> ${bookingRequest.origin}</p>
          <p><strong>Destination:</strong> ${bookingRequest.destination}</p>

          <div style="margin: 10px 0;">
            <strong>Stops:</strong>
            <ul>
              ${waypointsHtml}
            </ul>
          </div>

          <p><strong>Start Date:</strong> ${startDate}</p>
          <p><strong>Start Time:</strong> ${startTime}</p>
          <p><strong>Trip Type:</strong> ${bookingRequest.trip_type === 'return' ? 'Return Trip' : 'One-way Trip'}</p>
          <p><strong>Vehicle Type:</strong> ${bookingRequest.vehicle_type}</p>
          <p><strong>Number of Travelers:</strong> ${bookingRequest.num_travelers}</p>
          <p><strong>Total Distance:</strong> ${bookingRequest.total_distance} km</p>
          <p><strong>Estimated Duration:</strong> ${durationText}</p>
        </div>

        <div style="background-color: #e8f0ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Driver Details</h3>
          <p><strong>Driver Name:</strong> ${driverDetails.full_name}</p>
          <p><strong>Phone:</strong> ${driverDetails.phone}</p>
          <p><strong>Vehicle:</strong> ${driverDetails.vehicle_model} (${driverDetails.vehicle_color})</p>
          <p><strong>License Plate:</strong> ${driverDetails.license_plate}</p>
        </div>

        <div style="background-color: #fff8e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Payment Information</h3>
          <p><strong>Total Cost:</strong> Rs. ${bookingRequest.total_cost.toLocaleString()}</p>
          <p><strong>Advance Payment (50%):</strong> Rs. ${Math.round(bookingRequest.total_cost * 0.5).toLocaleString()}</p>
        </div>

        <p>Please proceed with the payment to confirm your booking:</p>

        <div style="margin: 30px 0; text-align: center;">
          <a href="${paymentLink}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Proceed to Payment</a>
        </div>

        <p>Note: Your booking will be confirmed only after the advance payment is made.</p>

        <p>Thank you for choosing Siyoga Travels!</p>

        <p>Best regards,<br>Siyoga Travels Team</p>
      </div>
    `;

    // Send email
    await sendEmail({
      to: touristEmail,
      subject,
      html: body
    });

    logger.info(`Email sent to tourist ${touristName} (${touristEmail}) for booking request ${bookingRequest.request_id}`);
    return { success: true };
  } catch (error) {
    logger.error(`Error sending tourist driver confirmation email: ${error.message}`);
    return { success: false, error: error.message };
  }
};

/**
 * Get driver notification details
 * @route GET /api/booking-requests/notification/:notificationId
 */
const getDriverNotification = catchAsync(async (req, res) => {
  const { notificationId } = req.params;
  const userId = req.user.UserID || req.user.user_id; // Support both column naming conventions

  // Get driver ID from user ID
  const driverQuery = "SELECT driver_id FROM Drivers WHERE user_id = @userId";
  const driverResult = await executeQuery(driverQuery, { userId });

  if (!driverResult.recordset || driverResult.recordset.length === 0) {
    throw new ApiError(403, 'You must be a registered driver to view notifications');
  }

  const driverId = driverResult.recordset[0].driver_id;

  // Get notification details with booking request info
  const notificationQuery = `
    SELECT dn.*, br.*, u.full_name as tourist_name
    FROM DriverNotifications dn
    JOIN BookingRequests br ON dn.request_id = br.request_id
    JOIN Users u ON br.tourist_id = u.user_id
    WHERE dn.notification_id = @notificationId
  `;

  const notificationResult = await executeQuery(notificationQuery, { notificationId });

  if (!notificationResult.recordset || notificationResult.recordset.length === 0) {
    throw new ApiError(404, 'Notification not found');
  }

  const notification = notificationResult.recordset[0];

  // Check if driver is authorized to view this notification
  if (notification.driver_id !== driverId) {
    throw new ApiError(403, 'You are not authorized to view this notification');
  }

  // Parse waypoints if present
  if (notification.waypoints) {
    try {
      notification.waypoints = JSON.parse(notification.waypoints);
    } catch (e) {
      logger.error(`Error parsing waypoints: ${e.message}`);
      notification.waypoints = [];
    }
  } else {
    notification.waypoints = [];
  }

  res.status(200).json({
    success: true,
    data: notification
  });
});

/**
 * Update booking request status
 * @route PUT /api/booking-requests/:requestId/status
 */
const updateBookingRequestStatus = catchAsync(async (req, res) => {
  const { requestId } = req.params;
  const { status, paymentDetails } = req.body;
  const userId = req.user.UserID || req.user.user_id; // Support both column naming conventions

  logger.info(`Updating booking request status: requestId=${requestId}, status=${status}, userId=${userId}`);

  // Validate status
  const validStatuses = ['pending', 'driver_confirmed', 'payment_completed', 'cancelled'];
  if (!validStatuses.includes(status)) {
    logger.warn(`Invalid status value: ${status}`);
    throw new ApiError(400, `Invalid status. Must be one of: ${validStatuses.join(', ')}`);
  }

  // Get the booking request
  const requestQuery = "SELECT * FROM BookingRequests WHERE request_id = @requestId";
  const requestResult = await executeQuery(requestQuery, { requestId });

  if (!requestResult.recordset || requestResult.recordset.length === 0) {
    logger.warn(`Booking request not found: ${requestId}`);
    throw new ApiError(404, 'Booking request not found');
  }

  const request = requestResult.recordset[0];

  // Check if user is authorized to update this request
  const isTourist = request.tourist_id === userId;
  const isDriver = await isUserDriver(userId);

  if (!isTourist && !isDriver) {
    logger.warn(`User ${userId} is not authorized to update booking request ${requestId}`);
    throw new ApiError(403, 'You are not authorized to update this booking request');
  }

  // Begin transaction
  await executeQuery('BEGIN TRANSACTION');

  try {
    // Update request status
    const updateQuery = `
      UPDATE BookingRequests
      SET status = @status, updated_at = GETDATE()
      WHERE request_id = @requestId
    `;

    await executeQuery(updateQuery, { requestId, status });
    logger.info(`Updated booking request ${requestId} status to ${status}`);

    // If payment details are provided, create a payment record
    if (status === 'payment_completed' && paymentDetails) {
      logger.info(`Creating payment record for booking request ${requestId}`);

      // Get tourist ID from the request
      const touristId = request.tourist_id;

      // Create payment record
      const paymentQuery = `
        INSERT INTO Payments (
          booking_request_id, tourist_id, amount, payment_method,
          payment_type, transaction_id, status, created_at
        )
        VALUES (
          @requestId, @touristId, @amount, @paymentMethod,
          @paymentType, @transactionId, 'completed', GETDATE()
        )
      `;

      await executeQuery(paymentQuery, {
        requestId,
        touristId,
        amount: paymentDetails.amount || request.total_cost,
        paymentMethod: paymentDetails.method || 'card',
        paymentType: paymentDetails.type || 'full',
        transactionId: paymentDetails.transactionId || `trans_${Date.now()}`
      });

      logger.info(`Created payment record for booking request ${requestId}`);

      // Get tourist and driver details for email notifications
      const touristQuery = `
        SELECT u.full_name, u.email
        FROM Users u
        WHERE u.user_id = @touristId
      `;

      const touristResult = await executeQuery(touristQuery, { touristId });
      const touristDetails = touristResult.recordset[0];

      // Get driver details
      const driverQuery = `
        SELECT d.driver_id, u.full_name, u.email
        FROM DriverNotifications dn
        JOIN Drivers d ON dn.driver_id = d.driver_id
        JOIN Users u ON d.user_id = u.user_id
        WHERE dn.request_id = @requestId AND dn.response = 'accepted'
      `;

      const driverResult = await executeQuery(driverQuery, { requestId });
      const driverDetails = driverResult.recordset[0];

      // Send email notifications
      if (touristDetails) {
        try {
          await sendEmail({
            to: touristDetails.email,
            subject: 'Payment Confirmation - Siyoga Travels',
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #4a6ee0;">Payment Confirmation</h2>
                <p>Hello ${touristDetails.full_name},</p>
                <p>Your payment for booking #${requestId} has been successfully processed.</p>
                <p>Amount: Rs. ${paymentDetails.amount || request.total_cost}</p>
                <p>Transaction ID: ${paymentDetails.transactionId || 'N/A'}</p>
                <p>Your trip is now confirmed. You can view the details in your dashboard.</p>
                <p>Thank you for choosing Siyoga Travels!</p>
                <p>Best regards,<br>Siyoga Travels Team</p>
              </div>
            `
          });
          logger.info(`Sent payment confirmation email to tourist ${touristDetails.email}`);
        } catch (emailError) {
          logger.error(`Error sending payment confirmation email to tourist: ${emailError.message}`);
        }
      }

      if (driverDetails) {
        try {
          await sendEmail({
            to: driverDetails.email,
            subject: 'Trip Payment Confirmation - Siyoga Travels',
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #4a6ee0;">Trip Payment Confirmation</h2>
                <p>Hello ${driverDetails.full_name},</p>
                <p>The tourist has completed the payment for booking #${requestId}.</p>
                <p>Amount: Rs. ${paymentDetails.amount || request.total_cost}</p>
                <p>The trip is now confirmed. You can view the details in your dashboard.</p>
                <p>Thank you for your service with Siyoga Travels!</p>
                <p>Best regards,<br>Siyoga Travels Team</p>
              </div>
            `
          });
          logger.info(`Sent payment confirmation email to driver ${driverDetails.email}`);
        } catch (emailError) {
          logger.error(`Error sending payment confirmation email to driver: ${emailError.message}`);
        }
      }
    }

    // Commit transaction
    await executeQuery('COMMIT TRANSACTION');

    res.status(200).json({
      success: true,
      message: `Booking request status updated to ${status} successfully`,
      data: { requestId, status }
    });
  } catch (error) {
    // Rollback transaction in case of error
    await executeQuery('ROLLBACK TRANSACTION');
    logger.error(`Error updating booking request status: ${error.message}`);
    throw error;
  }
});

/**
 * Get booking requests for authenticated tourist using user ID directly
 * @route GET /api/booking-requests/tourist-direct
 */
const getTouristBookingRequestsDirect = catchAsync(async (req, res) => {
  // Get user ID from authenticated user
  const userId = req.user.UserID || req.user.user_id;

  logger.info(`DEBUGGING: getTouristBookingRequestsDirect called for userId: ${userId}`);
  logger.info(`DEBUGGING: User object: ${JSON.stringify(req.user)}`);

  if (!userId) {
    logger.error('DEBUGGING: No userId found in request');
    throw new ApiError(403, 'Not authorized as tourist');
  }

  // Query booking requests using user_id directly as tourist_id
  const query = `
    SELECT
      BR.request_id,
      BR.tourist_id,
      BR.origin,
      BR.destination,
      BR.waypoints,
      BR.start_date,
      BR.start_time,
      BR.trip_type,
      BR.vehicle_type,
      BR.num_travelers,
      BR.total_distance,
      BR.estimated_duration,
      BR.total_cost,
      BR.driver_accommodation,
      BR.special_requests,
      BR.status,
      BR.created_at,
      BR.assigned_driver_id,
      BR.updated_at,
      D.Name AS driver_name,
      D.PhoneNumber AS driver_phone,
      V.Make AS vehicle_make,
      V.Model AS vehicle_model,
      V.RegistrationNumber AS vehicle_registration
    FROM BookingRequests BR
    LEFT JOIN Drivers D ON BR.assigned_driver_id = D.DriverID
    LEFT JOIN Vehicles V ON D.DriverID = V.DriverID
    WHERE BR.tourist_id = @userId
    ORDER BY BR.created_at DESC
  `;

  const params = { userId };

  logger.info(`DEBUGGING: Executing query with params: ${JSON.stringify(params)}`);
  logger.info(`DEBUGGING: Query: ${query}`);

  const result = await executeQuery(query, params);

  logger.info(`DEBUGGING: Query result count: ${result.recordset ? result.recordset.length : 0}`);

  if (result.recordset && result.recordset.length > 0) {
    logger.info(`DEBUGGING: First booking record: ${JSON.stringify(result.recordset[0])}`);

    // Process waypoints JSON
    const bookingRequests = result.recordset.map(request => {
      try {
        if (request.waypoints) {
          request.waypoints = JSON.parse(request.waypoints);
        } else {
          request.waypoints = [];
        }
      } catch (e) {
        logger.error(`Error parsing waypoints for request ${request.request_id}: ${e.message}`);
        request.waypoints = [];
      }
      return request;
    });

    res.json({
      success: true,
      count: bookingRequests.length,
      data: bookingRequests
    });
  } else {
    logger.warn(`DEBUGGING: No booking requests found for user ID ${userId}`);
    res.json({
      success: true,
      count: 0,
      data: []
    });
  }
});

module.exports = {
  createBookingRequest,
  getBookingRequestStatus,
  cancelBookingRequest,
  respondToBookingRequest,
  getDriverNotification,
  updateBookingRequestStatus,
  getTouristBookingRequestsDirect
};
