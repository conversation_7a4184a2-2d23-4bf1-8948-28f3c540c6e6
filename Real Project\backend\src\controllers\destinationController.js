// src/controllers/destinationController.js
const { executeQuery } = require('../config/DB/db');
const { catchAsync, ApiError } = require('../utils/errorHandler');
const logger = require('../config/logger');
const path = require('path');
const fs = require('fs');

/**
 * Get all destinations
 * @route GET /api/destinations
 */
const getAllDestinations = catchAsync(async (req, res) => {
  const { search, status = 'Active' } = req.query;

  let query = `
    SELECT
      destination_id as DestinationID,
      name as Name,
      province as Province,
      region as Region,
      description as Description,
      image_url as ImageURL
    FROM Destinations
  `;

  const params = { status };

  // Add search filter if provided
  if (search) {
    query += ` WHERE (name LIKE @search OR province LIKE @search OR region LIKE @search)`;
    params.search = `%${search}%`;
  }

  query += ` ORDER BY name ASC`;

  const result = await executeQuery(query, params);
  const destinations = result.recordset;

  res.json({
    success: true,
    count: destinations.length,
    data: destinations
  });
});

/**
 * Get destination by ID
 * @route GET /api/destinations/:id
 */
const getDestinationById = catchAsync(async (req, res) => {
  const { id } = req.params;

  const query = `
    SELECT
      destination_id as DestinationID,
      name as Name,
      province as Province,
      region as Region,
      description as Description,
      image_url as ImageURL
    FROM Destinations
    WHERE destination_id = @id
  `;

  const result = await executeQuery(query, { id });

  if (!result.recordset || result.recordset.length === 0) {
    throw new ApiError(404, 'Destination not found');
  }

  const destination = result.recordset[0];

  res.json({
    success: true,
    data: destination
  });
});

/**
 * Create a new destination (admin only)
 * @route POST /api/destinations
 */
const createDestination = catchAsync(async (req, res) => {
  const { name, province, region, description, imageUrl } = req.body;

  // Validate required fields
  if (!name || !province) {
    throw new ApiError(400, 'Name and province are required');
  }

  const query = `
    INSERT INTO Destinations (name, province, region, description, image_url)
    VALUES (@name, @province, @region, @description, @imageUrl);

    SELECT SCOPE_IDENTITY() AS destination_id;
  `;

  const result = await executeQuery(query, {
    name,
    province,
    region: region || null,
    description: description || null,
    imageUrl: imageUrl || null
  });

  if (!result.recordset || !result.recordset[0]) {
    throw new ApiError(500, 'Failed to create destination');
  }

  const destinationId = result.recordset[0].destination_id;

  // Get the newly created destination
  const newDestination = await executeQuery(
    'SELECT * FROM Destinations WHERE destination_id = @id',
    { id: destinationId }
  );

  // Get the destination data
  let destination = { ...newDestination.recordset[0] };

  logger.info(`New destination created: ${name}`);

  res.status(201).json({
    success: true,
    message: 'Destination created successfully',
    data: destination
  });
});

/**
 * Update a destination (admin only)
 * @route PUT /api/destinations/:id
 */
const updateDestination = catchAsync(async (req, res) => {
  const { id } = req.params;
  const { name, province, region, description, imageUrl } = req.body;

  // Check if destination exists
  const checkQuery = 'SELECT 1 FROM Destinations WHERE destination_id = @id';
  const checkResult = await executeQuery(checkQuery, { id });

  if (!checkResult.recordset || checkResult.recordset.length === 0) {
    throw new ApiError(404, 'Destination not found');
  }

  // Build update query
  let updateQuery = 'UPDATE Destinations SET ';
  const params = { id };
  const updates = [];

  if (name) {
    updates.push('name = @name');
    params.name = name;
  }

  if (province) {
    updates.push('province = @province');
    params.province = province;
  }

  if (region) {
    updates.push('region = @region');
    params.region = region;
  }

  if (description !== undefined) {
    updates.push('description = @description');
    params.description = description;
  }

  if (imageUrl !== undefined) {
    updates.push('image_url = @imageUrl');
    params.imageUrl = imageUrl;
  }

  if (updates.length === 0) {
    throw new ApiError(400, 'No updates provided');
  }

  updateQuery += updates.join(', ');
  updateQuery += ' WHERE destination_id = @id';

  await executeQuery(updateQuery, params);

  // Get the updated destination
  const updatedDestination = await executeQuery(
    'SELECT * FROM Destinations WHERE destination_id = @id',
    { id }
  );

  // Get the destination data
  let destination = { ...updatedDestination.recordset[0] };

  logger.info(`Destination updated: ${id}`);

  res.json({
    success: true,
    message: 'Destination updated successfully',
    data: destination
  });
});

/**
 * Delete a destination (admin only)
 * @route DELETE /api/destinations/:id
 */
const deleteDestination = catchAsync(async (req, res) => {
  const { id } = req.params;

  // Check if destination exists
  const checkQuery = 'SELECT 1 FROM Destinations WHERE destination_id = @id';
  const checkResult = await executeQuery(checkQuery, { id });

  if (!checkResult.recordset || checkResult.recordset.length === 0) {
    throw new ApiError(404, 'Destination not found');
  }

  // Delete the destination
  await executeQuery('DELETE FROM Destinations WHERE destination_id = @id', { id });

  logger.info(`Destination deleted: ${id}`);

  res.json({
    success: true,
    message: 'Destination deleted successfully'
  });
});

module.exports = {
  getAllDestinations,
  getDestinationById,
  createDestination,
  updateDestination,
  deleteDestination
};