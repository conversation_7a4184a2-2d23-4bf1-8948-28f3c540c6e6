const sql = require('mssql/msnodesqlv8');

// DSN connection configuration
const config = {
  driver: "msnodesqlv8",
  connectionString: "DSN=TripBookingSystem;Trusted_Connection=Yes;"
};

/**
 * Controller for direct database access to booking requests
 */
const directBookingController = {
  /**
   * Get all booking requests for a tourist directly from the database
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getTouristBookings: async (req, res) => {
    try {
      // Get user ID from params or use hardcoded value as fallback
      const userId = req.params.userId || 72; // Use param or fallback to 72
      console.log(`Fetching all bookings for user ID: ${userId}`);

      // Connect to the database
      await sql.connect(config);
      console.log('Connected to database');

      // Query the database directly using the user_id (which is stored as tourist_id in BookingRequests)
      // Simplified query with only columns that are known to exist
      const query = `
        SELECT
          BR.request_id,
          BR.tourist_id,
          U.full_name AS tourist_name,
          BR.origin,
          BR.destination,
          BR.start_date,
          BR.start_time,
          BR.status,
          BR.total_cost,
          BR.total_distance,
          BR.vehicle_type,
          BR.assigned_driver_id,
          BR.created_at,
          BR.updated_at
        FROM BookingRequests BR
        JOIN Users U ON BR.tourist_id = U.user_id
        WHERE BR.tourist_id = @userId
        ORDER BY BR.start_date DESC
      `;

      // Create a request and add parameters
      const request = new sql.Request();
      request.input('userId', sql.Int, userId);

      // Execute the query
      const result = await request.query(query);

      if (!result || !result.recordset) {
        await sql.close();
        return res.status(500).json({
          success: false,
          message: 'Failed to fetch booking requests'
        });
      }

      // Log the raw data for debugging
      console.log(`Found ${result.recordset.length} bookings for user ID: ${userId}`);

      // Format dates for better readability
      const formattedBookings = result.recordset.map(booking => {
        // Format dates if they exist
        if (booking.start_date) {
          const date = new Date(booking.start_date);
          booking.formatted_date = date.toLocaleDateString();
        }

        if (booking.created_at) {
          const date = new Date(booking.created_at);
          booking.formatted_created_at = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        }

        if (booking.updated_at) {
          const date = new Date(booking.updated_at);
          booking.formatted_updated_at = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        }

        return booking;
      });

      // Close the connection
      await sql.close();
      console.log('Database connection closed');

      return res.json({
        success: true,
        message: 'Booking requests fetched successfully',
        data: formattedBookings
      });
    } catch (error) {
      console.error('Error fetching tourist bookings:', error);

      // Try to close the connection if it's open
      try {
        await sql.close();
      } catch (closeError) {
        console.error('Error closing connection:', closeError);
      }

      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  },

  /**
   * Get a specific booking request by ID directly from the database
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getBookingById: async (req, res) => {
    try {
      const requestId = req.params.requestId;

      if (!requestId) {
        return res.status(400).json({
          success: false,
          message: 'Request ID is required'
        });
      }

      console.log(`Fetching booking details for request ID: ${requestId}`);

      // Connect to the database
      await sql.connect(config);
      console.log('Connected to database');

      // Query the database directly with simplified query
      const query = `
        SELECT
          BR.request_id,
          BR.tourist_id,
          U.full_name AS tourist_name,
          BR.origin,
          BR.destination,
          BR.start_date,
          BR.start_time,
          BR.status,
          BR.total_cost,
          BR.total_distance,
          BR.vehicle_type,
          BR.assigned_driver_id,
          BR.created_at,
          BR.updated_at
        FROM BookingRequests BR
        JOIN Users U ON BR.tourist_id = U.user_id
        WHERE BR.request_id = @requestId
      `;

      // Create a request and add parameters
      const request = new sql.Request();
      request.input('requestId', sql.Int, requestId);

      // Execute the query
      const result = await request.query(query);

      if (!result || !result.recordset || result.recordset.length === 0) {
        await sql.close();
        return res.status(404).json({
          success: false,
          message: 'Booking request not found'
        });
      }

      // Format dates for better readability
      const booking = result.recordset[0];

      if (booking.start_date) {
        const date = new Date(booking.start_date);
        booking.formatted_date = date.toLocaleDateString();
      }

      if (booking.created_at) {
        const date = new Date(booking.created_at);
        booking.formatted_created_at = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
      }

      if (booking.updated_at) {
        const date = new Date(booking.updated_at);
        booking.formatted_updated_at = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
      }

      // Close the connection
      await sql.close();
      console.log('Database connection closed');

      return res.json({
        success: true,
        message: 'Booking request fetched successfully',
        data: booking
      });
    } catch (error) {
      console.error('Error fetching booking details:', error);

      // Try to close the connection if it's open
      try {
        await sql.close();
      } catch (closeError) {
        console.error('Error closing connection:', closeError);
      }

      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
};

module.exports = directBookingController;
