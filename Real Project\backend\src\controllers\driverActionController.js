// src/controllers/driverActionController.js
const { executeQuery } = require('../config/DB/db');
const ApiError = require('../utils/ApiError');
const catchAsync = require('../utils/catchAsync');
const logger = require('../config/logger');
const emailService = require('../services/emailService');

/**
 * Get booking details by ID
 * @route GET /api/driver/actions/booking/:requestId
 */
const getBookingDetails = catchAsync(async (req, res) => {
  const { requestId } = req.params;
  const userId = req.user.UserID || req.user.user_id;

  logger.info(`Getting booking details for request ID: ${requestId}, user ID: ${userId}`);

  // Get driver ID from user ID
  const driverQuery = `
    SELECT driver_id
    FROM Drivers
    WHERE user_id = @userId
  `;

  logger.debug(`Executing driver query: ${driverQuery}`);
  const driverResult = await executeQuery(driverQuery, { userId });

  if (!driverResult.recordset || driverResult.recordset.length === 0) {
    logger.warn(`User ${userId} is not authorized as driver`);
    throw new ApiError(403, 'Not authorized as driver');
  }

  const driverId = driverResult.recordset[0].driver_id;
  logger.info(`Found driver ID: ${driverId} for user ID: ${userId}`);

  // Get booking details
  const query = `
    SELECT
      br.request_id,
      br.origin,
      br.destination,
      br.waypoints,
      br.start_date,
      br.start_time,
      br.trip_type,
      br.vehicle_type,
      br.num_travelers,
      br.total_distance,
      br.estimated_duration,
      br.total_cost,
      br.driver_accommodation,
      br.special_requests,
      br.status,
      br.created_at,
      br.updated_at,
      u.full_name AS tourist_name,
      u.phone AS tourist_phone,
      u.email AS tourist_email
    FROM BookingRequests br
    JOIN Users u ON br.tourist_id = u.user_id
    WHERE br.request_id = @requestId
    AND (br.assigned_driver_id = @driverId
      OR br.request_id IN (
        SELECT request_id
        FROM DriverNotifications
        WHERE driver_id = @driverId AND response = 'accepted'
      )
    )
  `;

  logger.debug(`Executing booking query with requestId: ${requestId}, driverId: ${driverId}`);
  const result = await executeQuery(query, { requestId, driverId });

  if (!result.recordset || result.recordset.length === 0) {
    logger.warn(`Booking not found or driver ${driverId} not authorized to view booking ${requestId}`);
    throw new ApiError(404, 'Booking not found or you are not authorized to view it');
  }

  logger.info(`Found booking details for request ID: ${requestId}`);
  const booking = result.recordset[0];
  logger.debug(`Booking data: ${JSON.stringify(booking)}`);


  // Parse waypoints JSON
  try {
    if (booking.waypoints) {
      booking.waypoints = JSON.parse(booking.waypoints);
    } else {
      booking.waypoints = [];
    }
  } catch (e) {
    logger.error(`Error parsing waypoints for request ${requestId}: ${e.message}`);
    booking.waypoints = [];
  }

  res.json({
    success: true,
    data: booking
  });
});

/**
 * Send payment reminder to tourist
 * @route POST /api/driver/actions/remind/:requestId
 */
const sendPaymentReminder = catchAsync(async (req, res) => {
  const { requestId } = req.params;
  const userId = req.user.UserID || req.user.user_id;

  // Get driver ID and details
  const driverQuery = `
    SELECT d.driver_id, u.full_name AS driver_name, u.email AS driver_email
    FROM Drivers d
    JOIN Users u ON d.user_id = u.user_id
    WHERE d.user_id = @userId
  `;

  const driverResult = await executeQuery(driverQuery, { userId });

  if (!driverResult.recordset || driverResult.recordset.length === 0) {
    throw new ApiError(403, 'Not authorized as driver');
  }

  const driver = driverResult.recordset[0];

  // Get booking and tourist details
  const bookingQuery = `
    SELECT
      br.request_id,
      br.origin,
      br.destination,
      br.start_date,
      br.start_time,
      br.total_cost,
      br.status,
      u.full_name AS tourist_name,
      u.email AS tourist_email
    FROM BookingRequests br
    JOIN Users u ON br.tourist_id = u.user_id
    WHERE br.request_id = @requestId
    AND (br.assigned_driver_id = @driverId
      OR br.request_id IN (
        SELECT request_id
        FROM DriverNotifications
        WHERE driver_id = @driverId AND response = 'accepted'
      )
    )
  `;

  const bookingResult = await executeQuery(bookingQuery, {
    requestId,
    driverId: driver.driver_id
  });

  if (!bookingResult.recordset || bookingResult.recordset.length === 0) {
    throw new ApiError(404, 'Booking not found or you are not authorized to send a reminder');
  }

  const booking = bookingResult.recordset[0];

  // Check if booking status is 'driver_confirmed' (awaiting payment)
  if (booking.status !== 'driver_confirmed') {
    throw new ApiError(400, `Cannot send payment reminder for booking with status: ${booking.status}`);
  }

  // Send payment reminder email
  const subject = 'Payment Reminder - Siyoga Travels';
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4a6ee0;">Payment Reminder</h2>
      <p>Dear ${booking.tourist_name},</p>
      <p>This is a friendly reminder that your payment for the following trip is pending:</p>

      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <p><strong>Trip ID:</strong> ${booking.request_id}</p>
        <p><strong>From:</strong> ${booking.origin}</p>
        <p><strong>To:</strong> ${booking.destination}</p>
        <p><strong>Date:</strong> ${new Date(booking.start_date).toLocaleDateString()}</p>
        <p><strong>Time:</strong> ${booking.start_time}</p>
        <p><strong>Total Amount:</strong> LKR ${booking.total_cost.toLocaleString()}</p>
      </div>

      <p>Your driver (${driver.driver_name}) has confirmed the booking and is waiting for your payment to proceed.</p>

      <p>Please log in to your account to complete the payment and confirm your trip.</p>

      <div style="margin: 25px 0; text-align: center;">
        <a href="http://localhost:5173/payment/${booking.request_id}" style="background-color: #4a6ee0; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">
          Complete Payment
        </a>
      </div>

      <p>If you have any questions, please don't hesitate to contact us.</p>

      <p>Thank you for choosing Siyoga Travels!</p>

      <p>Best regards,<br>Siyoga Travels Team</p>
    </div>
  `;

  await emailService.sendEmail({
    to: booking.tourist_email,
    subject,
    html
  });

  // Log the reminder
  const logQuery = `
    INSERT INTO PaymentReminders (request_id, driver_id, sent_at)
    VALUES (@requestId, @driverId, GETDATE())
  `;

  try {
    await executeQuery(logQuery, {
      requestId,
      driverId: driver.driver_id
    });
  } catch (error) {
    logger.warn(`Failed to log payment reminder: ${error.message}`);
    // Continue even if logging fails
  }

  logger.info(`Payment reminder sent to ${booking.tourist_name} (${booking.tourist_email}) for booking ${requestId}`);

  res.json({
    success: true,
    message: `Payment reminder sent to ${booking.tourist_name}`
  });
});

/**
 * Cancel booking
 * @route POST /api/driver/actions/cancel/:requestId
 */
const cancelBooking = catchAsync(async (req, res) => {
  const { requestId } = req.params;
  const { reason } = req.body;
  const userId = req.user.UserID || req.user.user_id;

  if (!reason || reason.trim() === '') {
    throw new ApiError(400, 'Cancellation reason is required');
  }

  // Get driver ID and details
  const driverQuery = `
    SELECT d.driver_id, u.full_name AS driver_name, u.email AS driver_email
    FROM Drivers d
    JOIN Users u ON d.user_id = u.user_id
    WHERE d.user_id = @userId
  `;

  const driverResult = await executeQuery(driverQuery, { userId });

  if (!driverResult.recordset || driverResult.recordset.length === 0) {
    throw new ApiError(403, 'Not authorized as driver');
  }

  const driver = driverResult.recordset[0];

  // Get booking and tourist details
  const bookingQuery = `
    SELECT
      br.request_id,
      br.tourist_id,
      br.origin,
      br.destination,
      br.start_date,
      br.start_time,
      br.total_cost,
      br.status,
      u.full_name AS tourist_name,
      u.email AS tourist_email
    FROM BookingRequests br
    JOIN Users u ON br.tourist_id = u.user_id
    WHERE br.request_id = @requestId
    AND (br.assigned_driver_id = @driverId
      OR br.request_id IN (
        SELECT request_id
        FROM DriverNotifications
        WHERE driver_id = @driverId AND response = 'accepted'
      )
    )
  `;

  const bookingResult = await executeQuery(bookingQuery, {
    requestId,
    driverId: driver.driver_id
  });

  if (!bookingResult.recordset || bookingResult.recordset.length === 0) {
    throw new ApiError(404, 'Booking not found or you are not authorized to cancel it');
  }

  const booking = bookingResult.recordset[0];

  // Check if booking can be cancelled
  if (booking.status !== 'driver_confirmed' && booking.status !== 'pending') {
    throw new ApiError(400, `Cannot cancel booking with status: ${booking.status}`);
  }

  // Begin transaction
  await executeQuery('BEGIN TRANSACTION');

  try {
    // Update booking status
    const updateQuery = `
      UPDATE BookingRequests
      SET status = 'cancelled', updated_at = GETDATE()
      WHERE request_id = @requestId
    `;

    await executeQuery(updateQuery, { requestId });

    // Log cancellation
    const logQuery = `
      INSERT INTO BookingCancellations (request_id, cancelled_by, cancellation_reason, cancelled_at)
      VALUES (@requestId, 'driver', @reason, GETDATE())
    `;

    await executeQuery(logQuery, {
      requestId,
      reason
    });

    // Commit transaction
    await executeQuery('COMMIT TRANSACTION');

    // Send cancellation email to tourist
    const subject = 'Trip Cancelled by Driver - Siyoga Travels';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #e74c3c;">Trip Cancelled</h2>
        <p>Dear ${booking.tourist_name},</p>
        <p>We regret to inform you that your driver has cancelled the following trip:</p>

        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p><strong>Trip ID:</strong> ${booking.request_id}</p>
          <p><strong>From:</strong> ${booking.origin}</p>
          <p><strong>To:</strong> ${booking.destination}</p>
          <p><strong>Date:</strong> ${new Date(booking.start_date).toLocaleDateString()}</p>
          <p><strong>Time:</strong> ${booking.start_time}</p>
        </div>

        <p><strong>Reason for cancellation:</strong> ${reason}</p>

        <p>You can log in to your account to plan a new trip or contact our customer support if you need any assistance.</p>

        <p>We apologize for any inconvenience this may have caused.</p>

        <p>Thank you for your understanding.</p>

        <p>Best regards,<br>Siyoga Travels Team</p>
      </div>
    `;

    await emailService.sendEmail({
      to: booking.tourist_email,
      subject,
      html
    });

    logger.info(`Booking ${requestId} cancelled by driver ${driver.driver_id} (${driver.driver_name})`);

    res.json({
      success: true,
      message: 'Booking cancelled successfully'
    });
  } catch (error) {
    // Rollback transaction in case of error
    await executeQuery('ROLLBACK TRANSACTION');
    throw error;
  }
});

module.exports = {
  getBookingDetails,
  sendPaymentReminder,
  cancelBooking
};
