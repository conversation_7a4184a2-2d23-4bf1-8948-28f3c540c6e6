// src/controllers/driverBookingController.js
const { executeQuery } = require('../config/DB/db');
const ApiError = require('../utils/ApiError');
const catchAsync = require('../utils/catchAsync');
const logger = require('../config/logger');

/**
 * Get all booking requests for the authenticated driver
 * @route GET /api/driver/booking-requests
 */
const getDriverBookingRequests = catchAsync(async (req, res) => {
  // Get driver ID from authenticated user
  const userId = req.user.UserID || req.user.user_id;
  
  // Get driver ID from user ID
  const driverQuery = `
    SELECT driver_id 
    FROM Drivers 
    WHERE user_id = @userId
  `;
  
  const driverResult = await executeQuery(driverQuery, { userId });
  
  if (!driverResult.recordset || driverResult.recordset.length === 0) {
    throw new ApiError(403, 'Not authorized as driver');
  }
  
  const driverId = driverResult.recordset[0].driver_id;
  
  // Get booking requests where this driver is assigned
  const query = `
    SELECT 
      br.request_id,
      br.origin,
      br.destination,
      br.waypoints,
      br.start_date,
      br.start_time,
      br.trip_type,
      br.vehicle_type,
      br.num_travelers,
      br.total_distance,
      br.estimated_duration,
      br.total_cost,
      br.driver_accommodation,
      br.special_requests,
      br.status,
      br.created_at,
      br.updated_at,
      u.full_name AS tourist_name,
      u.phone AS tourist_phone,
      u.email AS tourist_email
    FROM BookingRequests br
    JOIN Users u ON br.tourist_id = u.user_id
    WHERE br.assigned_driver_id = @driverId
    OR br.request_id IN (
      SELECT request_id 
      FROM DriverNotifications 
      WHERE driver_id = @driverId AND response = 'accepted'
    )
    ORDER BY br.start_date DESC, br.start_time DESC
  `;
  
  const result = await executeQuery(query, { driverId });
  
  // Process waypoints JSON
  const bookingRequests = result.recordset.map(request => {
    try {
      if (request.waypoints) {
        request.waypoints = JSON.parse(request.waypoints);
      } else {
        request.waypoints = [];
      }
    } catch (e) {
      logger.error(`Error parsing waypoints for request ${request.request_id}: ${e.message}`);
      request.waypoints = [];
    }
    return request;
  });
  
  res.json({
    success: true,
    count: bookingRequests.length,
    data: bookingRequests
  });
});

/**
 * Get driver-confirmed booking requests
 * @route GET /api/driver/confirmed-bookings
 */
const getDriverConfirmedBookings = catchAsync(async (req, res) => {
  // Get driver ID from authenticated user
  const userId = req.user.UserID || req.user.user_id;
  
  // Get driver ID from user ID
  const driverQuery = `
    SELECT driver_id 
    FROM Drivers 
    WHERE user_id = @userId
  `;
  
  const driverResult = await executeQuery(driverQuery, { userId });
  
  if (!driverResult.recordset || driverResult.recordset.length === 0) {
    throw new ApiError(403, 'Not authorized as driver');
  }
  
  const driverId = driverResult.recordset[0].driver_id;
  
  // Get booking requests with status 'driver_confirmed'
  const query = `
    SELECT 
      br.request_id,
      br.origin,
      br.destination,
      br.waypoints,
      br.start_date,
      br.start_time,
      br.trip_type,
      br.vehicle_type,
      br.num_travelers,
      br.total_distance,
      br.estimated_duration,
      br.total_cost,
      br.driver_accommodation,
      br.special_requests,
      br.status,
      br.created_at,
      br.updated_at,
      u.full_name AS tourist_name,
      u.phone AS tourist_phone,
      u.email AS tourist_email
    FROM BookingRequests br
    JOIN Users u ON br.tourist_id = u.user_id
    WHERE (br.assigned_driver_id = @driverId OR br.request_id IN (
      SELECT request_id 
      FROM DriverNotifications 
      WHERE driver_id = @driverId AND response = 'accepted'
    ))
    AND br.status = 'driver_confirmed'
    ORDER BY br.start_date DESC, br.start_time DESC
  `;
  
  const result = await executeQuery(query, { driverId });
  
  // Process waypoints JSON
  const bookingRequests = result.recordset.map(request => {
    try {
      if (request.waypoints) {
        request.waypoints = JSON.parse(request.waypoints);
      } else {
        request.waypoints = [];
      }
    } catch (e) {
      logger.error(`Error parsing waypoints for request ${request.request_id}: ${e.message}`);
      request.waypoints = [];
    }
    return request;
  });
  
  res.json({
    success: true,
    count: bookingRequests.length,
    data: bookingRequests
  });
});

module.exports = {
  getDriverBookingRequests,
  getDriverConfirmedBookings
};
