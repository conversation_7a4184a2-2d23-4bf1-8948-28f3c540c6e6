// src/controllers/driverController.js
const { executeQuery } = require('../config/DB/db');
const bcrypt = require('bcryptjs');
const { catchAsync, ApiError } = require('../utils/errorHandler');
const logger = require('../config/logger');

/**
 * Get driver profile
 * @route GET /api/drivers/profile
 */
const getDriverProfile = catchAsync(async (req, res) => {
  // Get driver ID from authenticated user
  const driverId = req.user.RoleID;

  if (!driverId) {
    throw new ApiError(403, 'Not authorized as driver');
  }

  const query = `
    SELECT
      D.DriverID,
      D.Name,
      D.PhoneNumber,
      D.LicenseNumber,
      D.ProfilePicture,
      D.Status,
      D.Rating,
      D.TotalTrips,
      D.ReviewCount,
      D.Biography,
      D.ExperienceYears,
      U.Email,
      U.LastLoginAt
    FROM Drivers D
    JOIN Users U ON D.UserID = U.UserID
    WHERE D.DriverID = @driverId
  `;

  const result = await executeQuery(query, { driverId });

  if (!result.recordset || result.recordset.length === 0) {
    throw new ApiError(404, 'Driver profile not found');
  }

  const profile = result.recordset[0];

  // Get driver's vehicles
  const vehiclesQuery = `
    SELECT
      VehicleID,
      Type,
      Make,
      Model,
      Year,
      Capacity,
      Status
    FROM Vehicles
    WHERE DriverID = @driverId
  `;

  const vehiclesResult = await executeQuery(vehiclesQuery, { driverId });

  // Add vehicles to profile response
  profile.Vehicles = vehiclesResult.recordset;

  res.json({
    success: true,
    data: profile
  });
});

/**
 * Update driver profile
 * @route PUT /api/drivers/profile
 */
const updateDriverProfile = catchAsync(async (req, res) => {
  const {
    name,
    phoneNumber,
    biography,
    experienceYears,
    profilePicture
  } = req.body;

  // Get driver ID from authenticated user
  const driverId = req.user.RoleID;

  if (!driverId) {
    throw new ApiError(403, 'Not authorized as driver');
  }

  // Build update query
  let updateQuery = 'UPDATE Drivers SET ';
  const params = { driverId };
  const updates = [];

  if (name) {
    updates.push('Name = @name');
    params.name = name;
  }

  if (phoneNumber !== undefined) {
    updates.push('PhoneNumber = @phoneNumber');
    params.phoneNumber = phoneNumber;
  }

  if (biography !== undefined) {
    updates.push('Biography = @biography');
    params.biography = biography;
  }

  if (experienceYears !== undefined) {
    updates.push('ExperienceYears = @experienceYears');
    params.experienceYears = experienceYears;
  }

  if (profilePicture !== undefined) {
    updates.push('ProfilePicture = @profilePicture');
    params.profilePicture = profilePicture;
  }

  if (updates.length === 0) {
    throw new ApiError(400, 'No updates provided');
  }

  updateQuery += updates.join(', ');
  updateQuery += ', UpdatedAt = GETDATE() WHERE DriverID = @driverId';

  await executeQuery(updateQuery, params);

  // Get updated profile
  const updatedProfile = await executeQuery(`
    SELECT
      D.DriverID,
      D.Name,
      D.PhoneNumber,
      D.LicenseNumber,
      D.ProfilePicture,
      D.Status,
      D.Rating,
      D.Biography,
      D.ExperienceYears,
      U.Email
    FROM Drivers D
    JOIN Users U ON D.UserID = U.UserID
    WHERE D.DriverID = @driverId
  `, { driverId });

  const profile = updatedProfile.recordset[0];

  logger.info(`Driver profile updated: ${driverId}`);

  res.json({
    success: true,
    message: 'Profile updated successfully',
    data: profile
  });
});

/**
 * Change driver password
 * @route PUT /api/drivers/change-password
 */
const changeDriverPassword = catchAsync(async (req, res) => {
  const { currentPassword, newPassword } = req.body;

  if (!currentPassword || !newPassword) {
    throw new ApiError(400, 'Current password and new password are required');
  }

  // Validate password strength
  if (newPassword.length < 8) {
    throw new ApiError(400, 'Password must be at least 8 characters long');
  }

  // Get user ID from authenticated user
  const userId = req.user.UserID;

  // Get current password hash
  const userQuery = `
    SELECT Password
    FROM Users
    WHERE UserID = @userId
  `;

  const userResult = await executeQuery(userQuery, { userId });

  if (!userResult.recordset || userResult.recordset.length === 0) {
    throw new ApiError(404, 'User not found');
  }

  const user = userResult.recordset[0];

  // Verify current password
  const isMatch = await bcrypt.compare(currentPassword, user.Password);
  if (!isMatch) {
    throw new ApiError(401, 'Current password is incorrect');
  }

  // Hash new password
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(newPassword, salt);

  // Update password
  await executeQuery(`
    UPDATE Users
    SET Password = @password, UpdatedAt = GETDATE()
    WHERE UserID = @userId
  `, {
    userId,
    password: hashedPassword
  });

  logger.info(`Driver password changed: user ID ${userId}`);

  res.json({
    success: true,
    message: 'Password changed successfully'
  });
});

/**
 * Get driver trips (history)
 * @route GET /api/drivers/trips
 */
const getDriverTrips = catchAsync(async (req, res) => {
  // Get driver ID from authenticated user
  const driverId = req.user.RoleID;

  if (!driverId) {
    throw new ApiError(403, 'Not authorized as driver');
  }

  const { status, vehicleId } = req.query;

  let query = `
    SELECT
      B.BookingID,
      B.TripDate,
      B.TripTime,
      B.PickupLocation,
      B.DropoffLocation,
      B.Status AS BookingStatus,
      B.TotalAmount,
      B.CreatedAt,
      P.Status AS PaymentStatus,
      T.TouristID,
      T.Name AS TouristName,
      T.PhoneNumber AS TouristPhone,
      V.VehicleID,
      V.Make + ' ' + V.Model AS VehicleName,
      V.Type AS VehicleType,
      DS.Name AS DestinationName
    FROM Bookings B
    JOIN Tourists T ON B.TouristID = T.TouristID
    LEFT JOIN Payments P ON B.BookingID = P.BookingID
    JOIN Vehicles V ON B.VehicleID = V.VehicleID
    JOIN Drivers D ON V.DriverID = D.DriverID
    LEFT JOIN Destinations DS ON B.DestinationID = DS.DestinationID
    WHERE D.DriverID = @driverId
  `;

  const params = { driverId };

  // Add filters if provided
  if (status) {
    query += ` AND B.Status = @status`;
    params.status = status;
  }

  if (vehicleId) {
    query += ` AND V.VehicleID = @vehicleId`;
    params.vehicleId = vehicleId;
  }

  // Order by trip date
  query += ` ORDER BY B.TripDate DESC, B.TripTime DESC`;

  const result = await executeQuery(query, params);

  res.json({
    success: true,
    count: result.recordset.length,
    data: result.recordset
  });
});

/**
 * Get driver reviews
 * @route GET /api/drivers/reviews
 */
const getDriverReviews = catchAsync(async (req, res) => {
  // Get driver ID from authenticated user
  const driverId = req.user.RoleID;

  if (!driverId) {
    throw new ApiError(403, 'Not authorized as driver');
  }

  const query = `
    SELECT
      R.ReviewID,
      R.BookingID,
      R.Rating,
      R.Comment,
      R.CreatedAt,
      T.Name AS TouristName,
      V.Make + ' ' + V.Model AS VehicleName,
      V.Type AS VehicleType,
      B.TripDate
    FROM Reviews R
    JOIN Bookings B ON R.BookingID = B.BookingID
    JOIN Tourists T ON R.TouristID = T.TouristID
    JOIN Vehicles V ON R.VehicleID = V.VehicleID
    WHERE R.DriverID = @driverId
    ORDER BY R.CreatedAt DESC
  `;

  const result = await executeQuery(query, { driverId });

  res.json({
    success: true,
    count: result.recordset.length,
    data: result.recordset
  });
});

/**
 * Get driver earnings
 * @route GET /api/drivers/earnings
 */
const getDriverEarnings = catchAsync(async (req, res) => {
  // Get driver ID from authenticated user
  const driverId = req.user.RoleID;

  if (!driverId) {
    throw new ApiError(403, 'Not authorized as driver');
  }

  const { period, startDate, endDate } = req.query;

  try {
    // First try to get earnings from the new TripBookingSystem schema

    // Base query for total earnings - TripBookingSystem schema
    let totalQuery = `
      SELECT SUM(B.fare) AS TotalEarnings
      FROM Bookings B
      JOIN Vehicles V ON B.vehicle_id = V.vehicle_id
      JOIN Trips T ON B.trip_id = T.trip_id
      WHERE V.driver_id = @driverId
        AND B.status = 'completed'
        AND B.payment_status = 'completed'
    `;

    // Base query for periodic earnings - TripBookingSystem schema
    let periodicQuery = `
      SELECT
    `;

    // Define time period grouping for TripBookingSystem schema
    if (period === 'daily') {
      periodicQuery += `
        CAST(T.start_date AS DATE) AS Period,
        COUNT(*) AS TripCount,
        SUM(B.fare) AS Earnings
      FROM Bookings B
      JOIN Vehicles V ON B.vehicle_id = V.vehicle_id
      JOIN Trips T ON B.trip_id = T.trip_id
      WHERE V.driver_id = @driverId
        AND B.status = 'completed'
        AND B.payment_status = 'completed'
        AND T.start_date BETWEEN DATEADD(DAY, -30, GETDATE()) AND GETDATE()
      GROUP BY CAST(T.start_date AS DATE)
      ORDER BY CAST(T.start_date AS DATE) DESC
      `;
    } else if (period === 'weekly') {
      periodicQuery += `
        DATEPART(YEAR, T.start_date) AS Year,
        DATEPART(WEEK, T.start_date) AS Week,
        COUNT(*) AS TripCount,
        SUM(B.fare) AS Earnings
      FROM Bookings B
      JOIN Vehicles V ON B.vehicle_id = V.vehicle_id
      JOIN Trips T ON B.trip_id = T.trip_id
      WHERE V.driver_id = @driverId
        AND B.status = 'completed'
        AND B.payment_status = 'completed'
        AND T.start_date BETWEEN DATEADD(WEEK, -12, GETDATE()) AND GETDATE()
      GROUP BY DATEPART(YEAR, T.start_date), DATEPART(WEEK, T.start_date)
      ORDER BY DATEPART(YEAR, T.start_date) DESC, DATEPART(WEEK, T.start_date) DESC
      `;
    } else { // monthly (default)
      periodicQuery += `
        DATEPART(YEAR, T.start_date) AS Year,
        DATEPART(MONTH, T.start_date) AS Month,
        COUNT(*) AS TripCount,
        SUM(B.fare) AS Earnings
      FROM Bookings B
      JOIN Vehicles V ON B.vehicle_id = V.vehicle_id
      JOIN Trips T ON B.trip_id = T.trip_id
      WHERE V.driver_id = @driverId
        AND B.status = 'completed'
        AND B.payment_status = 'completed'
        AND T.start_date BETWEEN DATEADD(MONTH, -12, GETDATE()) AND GETDATE()
      GROUP BY DATEPART(YEAR, T.start_date), DATEPART(MONTH, T.start_date)
      ORDER BY DATEPART(YEAR, T.start_date) DESC, DATEPART(MONTH, T.start_date) DESC
      `;
    }

    // Custom date range filter for TripBookingSystem schema
    if (startDate && endDate) {
      totalQuery += ` AND T.start_date BETWEEN @startDate AND @endDate`;

      // Override periodic query with custom date range
      periodicQuery = `
        SELECT
        ${period === 'daily'
          ? 'CAST(T.start_date AS DATE) AS Period,'
          : period === 'weekly'
            ? 'DATEPART(YEAR, T.start_date) AS Year, DATEPART(WEEK, T.start_date) AS Week,'
            : 'DATEPART(YEAR, T.start_date) AS Year, DATEPART(MONTH, T.start_date) AS Month,'
        }
        COUNT(*) AS TripCount,
        SUM(B.fare) AS Earnings
      FROM Bookings B
      JOIN Vehicles V ON B.vehicle_id = V.vehicle_id
      JOIN Trips T ON B.trip_id = T.trip_id
      WHERE V.driver_id = @driverId
        AND B.status = 'completed'
        AND B.payment_status = 'completed'
        AND T.start_date BETWEEN @startDate AND @endDate
      GROUP BY ${period === 'daily'
        ? 'CAST(T.start_date AS DATE)'
        : period === 'weekly'
          ? 'DATEPART(YEAR, T.start_date), DATEPART(WEEK, T.start_date)'
          : 'DATEPART(YEAR, T.start_date), DATEPART(MONTH, T.start_date)'
      }
      ORDER BY ${period === 'daily'
        ? 'CAST(T.start_date AS DATE) DESC'
        : period === 'weekly'
          ? 'DATEPART(YEAR, T.start_date) DESC, DATEPART(WEEK, T.start_date) DESC'
          : 'DATEPART(YEAR, T.start_date) DESC, DATEPART(MONTH, T.start_date) DESC'
      }
      `;
    }

    const params = {
      driverId,
      startDate: startDate || null,
      endDate: endDate || null
    };

    // Execute queries for TripBookingSystem schema
    const totalResult = await executeQuery(totalQuery, params);
    const periodicResult = await executeQuery(periodicQuery, params);

    // Vehicle earnings breakdown for TripBookingSystem schema
    const vehicleQuery = `
      SELECT
        V.vehicle_id AS VehicleID,
        V.make_model AS VehicleName,
        V.vehicle_type AS VehicleType,
        COUNT(*) AS TripCount,
        SUM(B.fare) AS Earnings
      FROM Bookings B
      JOIN Vehicles V ON B.vehicle_id = V.vehicle_id
      JOIN Trips T ON B.trip_id = T.trip_id
      WHERE V.driver_id = @driverId
        AND B.status = 'completed'
        AND B.payment_status = 'completed'
        ${(startDate && endDate) ? 'AND T.start_date BETWEEN @startDate AND @endDate' : ''}
      GROUP BY V.vehicle_id, V.make_model, V.vehicle_type
      ORDER BY Earnings DESC
    `;

    const vehicleResult = await executeQuery(vehicleQuery, params);

    // Calculate summary earnings for dashboard display
    const todayQuery = `
      SELECT SUM(B.fare) AS TodayEarnings
      FROM Bookings B
      JOIN Vehicles V ON B.vehicle_id = V.vehicle_id
      JOIN Trips T ON B.trip_id = T.trip_id
      WHERE V.driver_id = @driverId
        AND B.status = 'completed'
        AND B.payment_status = 'completed'
        AND CAST(T.start_date AS DATE) = CAST(GETDATE() AS DATE)
    `;

    const thisWeekQuery = `
      SELECT SUM(B.fare) AS WeekEarnings
      FROM Bookings B
      JOIN Vehicles V ON B.vehicle_id = V.vehicle_id
      JOIN Trips T ON B.trip_id = T.trip_id
      WHERE V.driver_id = @driverId
        AND B.status = 'completed'
        AND B.payment_status = 'completed'
        AND DATEPART(YEAR, T.start_date) = DATEPART(YEAR, GETDATE())
        AND DATEPART(WEEK, T.start_date) = DATEPART(WEEK, GETDATE())
    `;

    const thisMonthQuery = `
      SELECT SUM(B.fare) AS MonthEarnings
      FROM Bookings B
      JOIN Vehicles V ON B.vehicle_id = V.vehicle_id
      JOIN Trips T ON B.trip_id = T.trip_id
      WHERE V.driver_id = @driverId
        AND B.status = 'completed'
        AND B.payment_status = 'completed'
        AND DATEPART(YEAR, T.start_date) = DATEPART(YEAR, GETDATE())
        AND DATEPART(MONTH, T.start_date) = DATEPART(MONTH, GETDATE())
    `;

    const todayResult = await executeQuery(todayQuery, params);
    const weekResult = await executeQuery(thisWeekQuery, params);
    const monthResult = await executeQuery(thisMonthQuery, params);

    // Return the earnings data
    res.json({
      success: true,
      data: {
        totalEarnings: totalResult.recordset[0]?.TotalEarnings || 0,
        periodicEarnings: periodicResult.recordset,
        vehicleEarnings: vehicleResult.recordset,
        today: todayResult.recordset[0]?.TodayEarnings || 0,
        thisWeek: weekResult.recordset[0]?.WeekEarnings || 0,
        thisMonth: monthResult.recordset[0]?.MonthEarnings || 0,
        total: totalResult.recordset[0]?.TotalEarnings || 0
      }
    });
  } catch (error) {
    logger.error(`Error fetching driver earnings from TripBookingSystem schema: ${error.message}`);

    // Fallback to mock data if database queries fail
    res.json({
      success: true,
      data: {
        totalEarnings: 0,
        periodicEarnings: [],
        vehicleEarnings: [],
        today: 0,
        thisWeek: 0,
        thisMonth: 0,
        total: 0
      }
    });
  }
});

/**
 * Get driver dashboard data
 * @route GET /api/drivers/dashboard
 */
const getDriverDashboard = catchAsync(async (req, res) => {
  // Get driver ID from authenticated user
  const driverId = req.user.RoleID;

  if (!driverId) {
    throw new ApiError(403, 'Not authorized as driver');
  }

  try {
    // Earnings summary - TripBookingSystem schema
    const earningsQuery = `
      SELECT
        SUM(CASE WHEN CAST(T.start_date AS DATE) = CAST(GETDATE() AS DATE) THEN B.fare ELSE 0 END) AS TodayEarnings,
        SUM(CASE WHEN DATEPART(YEAR, T.start_date) = DATEPART(YEAR, GETDATE())
                AND DATEPART(WEEK, T.start_date) = DATEPART(WEEK, GETDATE())
                THEN B.fare ELSE 0 END) AS WeekEarnings,
        SUM(CASE WHEN DATEPART(YEAR, T.start_date) = DATEPART(YEAR, GETDATE())
                AND DATEPART(MONTH, T.start_date) = DATEPART(MONTH, GETDATE())
                THEN B.fare ELSE 0 END) AS MonthEarnings,
        SUM(B.fare) AS TotalEarnings
      FROM Bookings B
      JOIN Vehicles V ON B.vehicle_id = V.vehicle_id
      JOIN Trips T ON B.trip_id = T.trip_id
      WHERE V.driver_id = @driverId
        AND B.status = 'completed'
        AND B.payment_status = 'completed'
    `;

    const earningsResult = await executeQuery(earningsQuery, { driverId });

    // Upcoming trips - TripBookingSystem schema
    const upcomingTripsQuery = `
      SELECT TOP 5
        B.booking_id AS BookingID,
        T.start_date AS TripDate,
        T.start_time AS TripTime,
        T.origin AS PickupLocation,
        TS.destination AS DropoffLocation,
        B.status AS Status,
        B.fare AS TotalAmount,
        B.payment_status AS PaymentStatus,
        U_traveler.full_name AS TouristName,
        U_traveler.phone AS TouristPhone,
        V.make_model AS VehicleName,
        D.name AS DestinationName
      FROM Bookings B
      JOIN Trips T ON B.trip_id = T.trip_id
      JOIN Users U_traveler ON T.traveler_id = U_traveler.user_id
      JOIN Vehicles V ON B.vehicle_id = V.vehicle_id
      LEFT JOIN TripStops TS ON T.trip_id = TS.trip_id
      LEFT JOIN Destinations D ON TS.destination_id = D.destination_id
      WHERE V.driver_id = @driverId
        AND B.status IN ('confirmed', 'pending')
        AND T.start_date >= CAST(GETDATE() AS Date)
      ORDER BY T.start_date ASC, T.start_time ASC
    `;

    const upcomingTripsResult = await executeQuery(upcomingTripsQuery, { driverId });

    // Recent bookings - TripBookingSystem schema
    const recentBookingsQuery = `
      SELECT TOP 5
        B.booking_id AS BookingID,
        T.start_date AS TripDate,
        B.status AS Status,
        B.fare AS TotalAmount,
        B.payment_status AS PaymentStatus,
        U_traveler.full_name AS TouristName,
        D.name AS DestinationName
      FROM Bookings B
      JOIN Trips T ON B.trip_id = T.trip_id
      JOIN Users U_traveler ON T.traveler_id = U_traveler.user_id
      JOIN Vehicles V ON B.vehicle_id = V.vehicle_id
      LEFT JOIN TripStops TS ON T.trip_id = TS.trip_id
      LEFT JOIN Destinations D ON TS.destination_id = D.destination_id
      WHERE V.driver_id = @driverId
      ORDER BY B.created_at DESC
    `;

    const recentBookingsResult = await executeQuery(recentBookingsQuery, { driverId });

    // Booking statistics - TripBookingSystem schema
    const statisticsQuery = `
      SELECT
        COUNT(CASE WHEN B.status = 'completed' THEN 1 END) AS CompletedTrips,
        COUNT(CASE WHEN B.status = 'cancelled' THEN 1 END) AS CancelledTrips,
        COUNT(CASE WHEN B.status = 'confirmed' AND T.start_date >= CAST(GETDATE() AS Date) THEN 1 END) AS UpcomingTrips,
        COUNT(*) AS TotalTrips,
        SUM(CASE WHEN B.status = 'completed' THEN B.fare ELSE 0 END) AS TotalEarnings
      FROM Bookings B
      JOIN Vehicles V ON B.vehicle_id = V.vehicle_id
      JOIN Trips T ON B.trip_id = T.trip_id
      WHERE V.driver_id = @driverId
    `;

    const statisticsResult = await executeQuery(statisticsQuery, { driverId });

    // Recent reviews - TripBookingSystem schema
    const reviewsQuery = `
      SELECT TOP 3
        R.review_id AS ReviewID,
        R.rating AS Rating,
        R.review_text AS Comment,
        R.created_at AS CreatedAt,
        U_traveler.full_name AS TouristName,
        T.start_date AS TripDate,
        V.make_model AS VehicleName
      FROM TripReviews R
      JOIN Users U_traveler ON R.traveler_id = U_traveler.user_id
      JOIN Trips T ON R.trip_id = T.trip_id
      JOIN Bookings B ON T.trip_id = B.trip_id
      JOIN Vehicles V ON B.vehicle_id = V.vehicle_id
      WHERE V.driver_id = @driverId
      ORDER BY R.created_at DESC
    `;

    const reviewsResult = await executeQuery(reviewsQuery, { driverId });

    // Vehicle status - TripBookingSystem schema
    const vehiclesQuery = `
      SELECT
        V.vehicle_id AS VehicleID,
        V.vehicle_type AS Type,
        V.make_model AS Make,
        '' AS Model,
        CASE WHEN V.verified = 1 THEN 'Active' ELSE 'Inactive' END AS Status
      FROM Vehicles V
      WHERE V.driver_id = @driverId
    `;

    const vehiclesResult = await executeQuery(vehiclesQuery, { driverId });

    // Get driver profile - TripBookingSystem schema
    const profileQuery = `
      SELECT
        D.driver_id AS DriverID,
        D.full_name AS Name,
        D.phone_number AS PhoneNumber,
        D.license_number AS LicenseNumber,
        D.profile_picture AS ProfilePicture,
        D.status AS Status,
        0 AS Rating,
        U.email AS Email
      FROM Drivers D
      JOIN Users U ON D.user_id = U.user_id
      WHERE D.driver_id = @driverId
    `;

    const profileResult = await executeQuery(profileQuery, { driverId });

    // Get pending booking requests - TripBookingSystem schema
    const pendingBookingsQuery = `
      SELECT
        BR.request_id AS RequestID,
        BR.origin AS PickupLocation,
        BR.destination AS DropoffLocation,
        BR.start_date AS TripDate,
        BR.start_time AS TripTime,
        BR.trip_type AS TripType,
        BR.vehicle_type AS VehicleType,
        BR.num_travelers AS NumTravelers,
        BR.total_distance AS TotalDistance,
        BR.estimated_duration AS EstimatedDuration,
        BR.total_cost AS TotalAmount,
        BR.status AS Status,
        BR.created_at AS CreatedAt,
        U.full_name AS TouristName,
        U.phone AS TouristPhone,
        U.email AS TouristEmail
      FROM BookingRequests BR
      JOIN Users U ON BR.tourist_id = U.user_id
      JOIN DriverNotifications DN ON BR.request_id = DN.request_id
      WHERE DN.driver_id = @driverId
        AND BR.status = 'pending'
        AND DN.response IS NULL
      ORDER BY BR.created_at DESC
    `;

    const pendingBookingsResult = await executeQuery(pendingBookingsQuery, { driverId });

    res.json({
      success: true,
      data: {
        earnings: {
          today: earningsResult.recordset[0]?.TodayEarnings || 0,
          thisWeek: earningsResult.recordset[0]?.WeekEarnings || 0,
          thisMonth: earningsResult.recordset[0]?.MonthEarnings || 0,
          total: earningsResult.recordset[0]?.TotalEarnings || 0
        },
        upcomingTrips: upcomingTripsResult.recordset,
        recentBookings: recentBookingsResult.recordset,
        statistics: statisticsResult.recordset[0] || {
          CompletedTrips: 0,
          CancelledTrips: 0,
          UpcomingTrips: 0,
          TotalTrips: 0,
          TotalEarnings: 0
        },
        recentReviews: reviewsResult.recordset,
        vehicles: vehiclesResult.recordset,
        profile: profileResult.recordset[0] || {},
        pendingBookings: pendingBookingsResult.recordset
      }
    });
  } catch (error) {
    logger.error(`Error fetching driver dashboard data: ${error.message}`);

    // Fallback to Sri Lankan mock data if database queries fail
    const sriLankanMockData = {
      earnings: {
        today: 5500,
        thisWeek: 27500,
        thisMonth: 112000,
        total: 345000
      },
      upcomingTrips: [
        {
          BookingID: 'BK-' + Math.floor(100000 + Math.random() * 900000),
          TripDate: new Date(Date.now() + 86400000 * 2).toISOString().split('T')[0], // 2 days from now
          TripTime: '09:00:00',
          PickupLocation: 'Colombo Fort Railway Station',
          DropoffLocation: 'Sigiriya Rock Fortress',
          Status: 'confirmed',
          TotalAmount: 12500,
          PaymentStatus: 'completed',
          TouristName: 'Dinesh Perera',
          TouristPhone: '************',
          VehicleName: 'Toyota Prius',
          DestinationName: 'Sigiriya'
        },
        {
          BookingID: 'BK-' + Math.floor(100000 + Math.random() * 900000),
          TripDate: new Date(Date.now() + 86400000 * 4).toISOString().split('T')[0], // 4 days from now
          TripTime: '08:30:00',
          PickupLocation: 'Bandaranaike International Airport',
          DropoffLocation: 'Ella',
          Status: 'confirmed',
          TotalAmount: 15000,
          PaymentStatus: 'completed',
          TouristName: 'Amali Fernando',
          TouristPhone: '************',
          VehicleName: 'Toyota Prius',
          DestinationName: 'Ella'
        },
        {
          BookingID: 'BK-' + Math.floor(100000 + Math.random() * 900000),
          TripDate: new Date(Date.now() + 86400000 * 7).toISOString().split('T')[0], // 7 days from now
          TripTime: '07:00:00',
          PickupLocation: 'Negombo Beach Hotel',
          DropoffLocation: 'Yala National Park',
          Status: 'confirmed',
          TotalAmount: 18000,
          PaymentStatus: 'completed',
          TouristName: 'Rohan Gunawardena',
          TouristPhone: '************',
          VehicleName: 'Toyota Prius',
          DestinationName: 'Yala National Park'
        }
      ],
      recentBookings: [
        {
          BookingID: 'BK-' + Math.floor(100000 + Math.random() * 900000),
          TripDate: new Date(Date.now() - 86400000 * 2).toISOString().split('T')[0], // 2 days ago
          Status: 'completed',
          TotalAmount: 8500,
          PaymentStatus: 'completed',
          TouristName: 'Chaminda Silva',
          DestinationName: 'Kandy'
        },
        {
          BookingID: 'BK-' + Math.floor(100000 + Math.random() * 900000),
          TripDate: new Date(Date.now() - 86400000 * 5).toISOString().split('T')[0], // 5 days ago
          Status: 'completed',
          TotalAmount: 12000,
          PaymentStatus: 'completed',
          TouristName: 'Priyanka Jayawardena',
          DestinationName: 'Nuwara Eliya'
        },
        {
          BookingID: 'BK-' + Math.floor(100000 + Math.random() * 900000),
          TripDate: new Date(Date.now() - 86400000 * 8).toISOString().split('T')[0], // 8 days ago
          Status: 'cancelled',
          TotalAmount: 9500,
          PaymentStatus: 'refunded',
          TouristName: 'Malith Dissanayake',
          DestinationName: 'Galle Fort'
        }
      ],
      statistics: {
        CompletedTrips: 24,
        CancelledTrips: 3,
        UpcomingTrips: 5,
        TotalTrips: 32,
        TotalEarnings: 345000
      },
      recentReviews: [
        {
          ReviewID: 'REV-' + Math.floor(100000 + Math.random() * 900000),
          Rating: 5,
          Comment: 'Excellent driver, very knowledgeable about Sri Lankan history and culture. Made our trip to Sigiriya memorable!',
          CreatedAt: new Date(Date.now() - 86400000 * 3).toISOString(), // 3 days ago
          TouristName: 'Dinesh Perera',
          TripDate: new Date(Date.now() - 86400000 * 4).toISOString().split('T')[0], // 4 days ago
          VehicleName: 'Toyota Prius'
        },
        {
          ReviewID: 'REV-' + Math.floor(100000 + Math.random() * 900000),
          Rating: 4,
          Comment: 'Very professional and punctual. Helped us navigate through the busy streets of Colombo with ease.',
          CreatedAt: new Date(Date.now() - 86400000 * 10).toISOString(), // 10 days ago
          TouristName: 'Amali Fernando',
          TripDate: new Date(Date.now() - 86400000 * 11).toISOString().split('T')[0], // 11 days ago
          VehicleName: 'Toyota Prius'
        }
      ],
      vehicles: [
        {
          VehicleID: 'V-' + Math.floor(100000 + Math.random() * 900000),
          Type: 'Sedan',
          Make: 'Toyota',
          Model: 'Prius',
          Status: 'Active',
          LicensePlate: 'CAR-1234',
          imageUrl: 'https://via.placeholder.com/300x200?text=Toyota+Prius'
        },
        {
          VehicleID: 'V-' + Math.floor(100000 + Math.random() * 900000),
          Type: 'SUV',
          Make: 'Mitsubishi',
          Model: 'Montero',
          Status: 'Active',
          LicensePlate: 'SUV-5678',
          imageUrl: 'https://via.placeholder.com/300x200?text=Mitsubishi+Montero'
        }
      ],
      profile: {
        DriverID: driverId,
        Name: 'Saman Kumara',
        PhoneNumber: '************',
        LicenseNumber: 'DL-987654321',
        ProfilePicture: 'https://via.placeholder.com/150',
        Status: 'Active',
        Rating: 4.8,
        Email: '<EMAIL>'
      },
      pendingBookings: [
        {
          RequestID: 'REQ-' + Math.floor(100000 + Math.random() * 900000),
          PickupLocation: 'Colombo Hilton Hotel',
          DropoffLocation: 'Anuradhapura Ancient City',
          TripDate: new Date(Date.now() + 86400000 * 3).toISOString().split('T')[0], // 3 days from now
          TripTime: '06:30:00',
          TripType: 'one-way',
          VehicleType: 'Sedan',
          NumTravelers: 3,
          TotalDistance: 200,
          EstimatedDuration: '4 hours',
          TotalAmount: 14000,
          Status: 'pending',
          CreatedAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
          TouristName: 'Lakshitha Bandara',
          TouristPhone: '************',
          TouristEmail: '<EMAIL>'
        }
      ]
    };

    res.json({
      success: true,
      data: sriLankanMockData
    });
  }
});

/**
 * Update driver availability
 * @route PUT /api/drivers/availability
 */
const updateDriverAvailability = catchAsync(async (req, res) => {
  const { isAvailable, unavailableDates } = req.body;

  // Get driver ID from authenticated user
  const driverId = req.user.RoleID;

  if (!driverId) {
    throw new ApiError(403, 'Not authorized as driver');
  }

  // Update driver availability status
  const updateQuery = `
    UPDATE Drivers
    SET
      IsAvailable = @isAvailable,
      UpdatedAt = GETDATE()
    WHERE DriverID = @driverId
  `;

  await executeQuery(updateQuery, {
    driverId,
    isAvailable: isAvailable !== undefined ? isAvailable : true
  });

  // If unavailable dates are provided, update them
  if (unavailableDates && Array.isArray(unavailableDates)) {
    // First, remove existing dates
    await executeQuery(`
      DELETE FROM DriverUnavailableDates
      WHERE DriverID = @driverId
    `, { driverId });

    // Then insert new dates
    for (const date of unavailableDates) {
      await executeQuery(`
        INSERT INTO DriverUnavailableDates (DriverID, UnavailableDate)
        VALUES (@driverId, @date)
      `, {
        driverId,
        date
      });
    }
  }

  logger.info(`Driver availability updated: ${driverId}`);

  res.json({
    success: true,
    message: 'Availability updated successfully'
  });
});

module.exports = {
  getDriverProfile,
  updateDriverProfile,
  changeDriverPassword,
  getDriverTrips,
  getDriverReviews,
  getDriverEarnings,
  getDriverDashboard,
  updateDriverAvailability
};