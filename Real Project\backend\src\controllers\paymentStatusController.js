// src/controllers/paymentStatusController.js
const { executeQuery } = require('../config/DB/db');
const ApiError = require('../utils/ApiError');
const logger = require('../config/logger');
const { sendEmail } = require('../services/emailService');
const catchAsync = require('../utils/catchAsync');

/**
 * Update booking payment status
 * @route PUT /api/bookings/:id/status
 */
const updateBookingPaymentStatus = catchAsync(async (req, res) => {
  const { id } = req.params;
  const { status, paymentDetails } = req.body;

  // Validate status
  if (!status) {
    throw new ApiError(400, 'Status is required');
  }

  // Check if booking exists
  const bookingQuery = `
    SELECT
      B.BookingID,
      B.TouristID,
      B.DriverID,
      B.Status AS CurrentStatus,
      B.TotalAmount,
      T.Email AS TouristEmail,
      T.Name AS TouristName,
      D.Email AS DriverEmail,
      D.Name AS DriverName,
      V.Make,
      V.Model,
      V.VehicleType,
      V.LicensePlate
    FROM Bookings B
    LEFT JOIN Tourists T ON B.TouristID = T.TouristID
    LEFT JOIN Drivers D ON B.DriverID = D.DriverID
    LEFT JOIN Vehicles V ON D.VehicleID = V.VehicleID
    WHERE B.BookingID = @id
  `;

  const bookingResult = await executeQuery(bookingQuery, { id });

  if (!bookingResult.recordset || bookingResult.recordset.length === 0) {
    throw new ApiError(404, 'Booking not found');
  }

  const booking = bookingResult.recordset[0];

  // Check if user is authorized (must be the tourist who made the booking)
  const isTourist = req.user.Role === 'Tourist' && req.user.RoleID === booking.TouristID;
  const isAdmin = req.user.Role === 'Admin';

  if (!isTourist && !isAdmin) {
    throw new ApiError(403, 'You do not have permission to update this booking');
  }

  // Begin transaction
  await executeQuery('BEGIN TRANSACTION');

  try {
    // Update booking status
    const updateBookingQuery = `
      UPDATE Bookings
      SET Status = @status, UpdatedAt = GETDATE()
      WHERE BookingID = @id
    `;

    await executeQuery(updateBookingQuery, { id, status });

    // Check if payment record exists
    const paymentQuery = `
      SELECT PaymentID, Status
      FROM Payments
      WHERE BookingID = @id
    `;

    const paymentResult = await executeQuery(paymentQuery, { id });

    if (paymentResult.recordset && paymentResult.recordset.length > 0) {
      // Update existing payment record
      const paymentId = paymentResult.recordset[0].PaymentID;

      const updatePaymentQuery = `
        UPDATE Payments
        SET Status = @status,
            Method = @method,
            TransactionID = @transactionId,
            ProcessedAt = GETDATE(),
            UpdatedAt = GETDATE()
        WHERE PaymentID = @paymentId
      `;

      await executeQuery(updatePaymentQuery, {
        paymentId,
        status: 'Completed',
        method: paymentDetails.method,
        transactionId: paymentDetails.transactionId
      });
    } else {
      // Create new payment record
      const createPaymentQuery = `
        INSERT INTO Payments (
          BookingID, Method, Amount, Status, TransactionID, ProcessedAt
        )
        VALUES (
          @id, @method, @amount, 'Completed', @transactionId, GETDATE()
        )
      `;

      await executeQuery(createPaymentQuery, {
        id,
        method: paymentDetails.method,
        amount: paymentDetails.amount || booking.TotalAmount,
        transactionId: paymentDetails.transactionId
      });
    }

    // Commit transaction
    await executeQuery('COMMIT TRANSACTION');

    // Send email notifications
    if (booking.TouristEmail) {
      await sendEmail({
        to: booking.TouristEmail,
        subject: 'Payment Confirmation - Siyoga Travels',
        text: `Dear ${booking.TouristName},

Thank you for your payment for booking #${id}. Your trip has been confirmed.

Payment Details:
- Amount: LKR ${paymentDetails.amount || booking.TotalAmount}
- Method: ${paymentDetails.method}
- Transaction ID: ${paymentDetails.transactionId}
- Date: ${new Date().toLocaleString()}

Driver Details:
- Name: ${booking.DriverName}
- Vehicle: ${booking.Make} ${booking.Model} (${booking.VehicleType})
- License Plate: ${booking.LicensePlate}

You can view your booking details in your dashboard.

Thank you for choosing Siyoga Travels!
`
      });
    }

    if (booking.DriverEmail) {
      await sendEmail({
        to: booking.DriverEmail,
        subject: 'Payment Received - Siyoga Travels',
        text: `Dear ${booking.DriverName},

A payment has been received for booking #${id}.

Payment Details:
- Amount: LKR ${paymentDetails.amount || booking.TotalAmount}
- Method: ${paymentDetails.method}
- Date: ${new Date().toLocaleString()}

The trip has been confirmed and added to your upcoming trips.

Thank you for your service!
`
      });
    }

    logger.info(`Booking payment status updated: ${id} -> ${status}`);

    res.json({
      success: true,
      message: `Booking payment status updated to ${status}`,
      data: {
        bookingId: id,
        status,
        paymentDetails: {
          method: paymentDetails.method,
          amount: paymentDetails.amount || booking.TotalAmount,
          transactionId: paymentDetails.transactionId,
          date: new Date().toISOString()
        }
      }
    });
  } catch (error) {
    // Rollback transaction in case of error
    await executeQuery('ROLLBACK TRANSACTION');
    throw error;
  }
});

module.exports = {
  updateBookingPaymentStatus
};
