// src/controllers/vehicleImageController.js
const { executeQuery } = require('../config/DB/db');
const { catchAsync, ApiError } = require('../utils/errorHandler');
const logger = require('../config/logger');

/**
 * Update vehicle image
 * @route POST /api/vehicles/:id/update-image
 */
const updateVehicleImage = catchAsync(async (req, res) => {
  const { id } = req.params;
  
  // Check if file was uploaded
  if (!req.file) {
    throw new ApiError(400, 'No image file uploaded');
  }
  
  // Get the relative path to the vehicle image
  const imageUrl = req.file.path.replace(/\\/g, '/').split('uploads/')[1];
  logger.info(`Vehicle image uploaded: ${imageUrl}`);
  
  // Check if vehicle exists
  const checkQuery = `
    SELECT vehicle_id, driver_id, vehicle_type, make_model, registration_number, vehicle_photo
    FROM Vehicles
    WHERE vehicle_id = @id
  `;
  
  const checkResult = await executeQuery(checkQuery, { id });
  
  if (!checkResult.recordset || checkResult.recordset.length === 0) {
    throw new ApiError(404, 'Vehicle not found');
  }
  
  // If user is authenticated and is a driver, check if they own the vehicle
  if (req.user && req.user.Role === 'driver') {
    const driverId = req.user.RoleID;
    
    if (driverId && checkResult.recordset[0].driver_id !== driverId) {
      throw new ApiError(403, 'You do not have permission to update this vehicle');
    }
  }
  
  // Update the vehicle_photo field
  const updateQuery = `
    UPDATE Vehicles
    SET vehicle_photo = @imageUrl
    WHERE vehicle_id = @id;
    
    SELECT 
      vehicle_id,
      driver_id,
      vehicle_type,
      make_model,
      registration_number,
      vehicle_photo,
      insurance_expiry_date,
      seat_count,
      air_conditioned,
      verified
    FROM Vehicles
    WHERE vehicle_id = @id;
  `;
  
  const updateResult = await executeQuery(updateQuery, { id, imageUrl });
  
  if (!updateResult.recordset || updateResult.recordset.length === 0) {
    throw new ApiError(500, 'Failed to update vehicle image');
  }
  
  const updatedVehicle = updateResult.recordset[0];
  
  logger.info(`Vehicle image updated for vehicle ID ${id}: ${imageUrl}`);
  
  res.status(200).json({
    success: true,
    message: 'Vehicle image updated successfully',
    data: updatedVehicle
  });
});

module.exports = {
  updateVehicleImage
};
