// src/middleware/auth.js
const jwt = require('jsonwebtoken');
const { ApiError } = require('../utils/errorHandler');
const config = require('../config/config');
const { executeQuery } = require('../config/DB/db');

/**
 * Authentication middleware to protect routes
 * Verifies JWT token and sets user info in request object
 */
const authenticate = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;

    console.log('Authentication middleware - Headers:', req.headers);

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('Authentication failed: No Bearer token in Authorization header');
      throw new ApiError(401, 'Authentication required. Please login.');
    }

    // Extract token from header
    const token = authHeader.split(' ')[1];

    if (!token) {
      console.log('Authentication failed: Token is empty');
      throw new ApiError(401, 'Authentication token missing');
    }

    console.log('Token received:', token.substring(0, 15) + '...');

    // Verify token
    let decoded;
    try {
      decoded = jwt.verify(token, config.jwt.secret);
      console.log('Token verified successfully. Decoded payload:', decoded);
    } catch (tokenError) {
      console.log('Token verification failed:', tokenError.message);
      throw tokenError;
    }

    // Check if user exists in the database - using TripBookingSystem schema
    const query = `
      SELECT
        U.user_id AS UserID,
        U.email AS Email,
        U.role AS Role,
        U.full_name AS Name,
        CASE
          WHEN U.role = 'traveler' THEN T.tourist_id
          WHEN U.role = 'driver' THEN D.driver_id
          ELSE NULL
        END AS RoleID
      FROM Users U
      LEFT JOIN Tourists T ON U.user_id = T.user_id AND U.role = 'traveler'
      LEFT JOIN Drivers D ON U.user_id = D.user_id AND U.role = 'driver'
      WHERE U.user_id = @userId
    `;

    console.log('Querying user with ID:', decoded.userId);
    const result = await executeQuery(query, { userId: decoded.userId });

    if (!result.recordset || !result.recordset[0]) {
      console.log('User not found in database for ID:', decoded.userId);
      throw new ApiError(401, 'User not found');
    }

    // Add user info to request object
    req.user = result.recordset[0];

    // Log user info for debugging
    console.log('Authenticated user:', {
      id: req.user.UserID,
      email: req.user.Email,
      role: req.user.Role,
      roleId: req.user.RoleID
    });

    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return next(new ApiError(401, 'Token expired'));
    }

    if (error.name === 'JsonWebTokenError') {
      return next(new ApiError(401, 'Invalid token'));
    }

    next(error);
  }
};

/**
 * Role-based authorization middleware
 * @param {string|string[]} roles - Single role or array of allowed roles
 */
const authorize = (roles) => {
  // Convert string to array if a single role is provided
  const allowedRoles = Array.isArray(roles) ? roles : [roles];

  return (req, res, next) => {
    if (!req.user) {
      return next(new ApiError(401, 'Unauthorized'));
    }

    // Add debug logging
    console.log('Authorize middleware - User:', {
      id: req.user.UserID,
      role: req.user.Role,
      allowedRoles
    });

    // Case-insensitive role matching
    const userRole = req.user.Role ? req.user.Role.toLowerCase() : '';

    // Normalize allowed roles for comparison
    const normalizedAllowedRoles = allowedRoles.map(role => {
      // Handle common role variations
      if (role.toLowerCase() === 'tourist' || role.toLowerCase() === 'traveler') {
        return ['tourist', 'traveler'];
      }
      if (role.toLowerCase() === 'driver') {
        return ['driver'];
      }
      if (role.toLowerCase() === 'admin') {
        return ['admin'];
      }
      return [role.toLowerCase()];
    }).flat();

    console.log('Normalized allowed roles:', normalizedAllowedRoles);

    const hasMatchingRole = normalizedAllowedRoles.includes(userRole);

    if (allowedRoles.length && !hasMatchingRole) {
      console.log(`Access denied: User role "${req.user.Role}" not in allowed roles:`, normalizedAllowedRoles);
      return next(new ApiError(403, 'Forbidden: Insufficient privileges'));
    }

    next();
  };
};

/**
 * Tourist role check middleware
 */
const isTourist = (req, res, next) => {
  return authorize(['traveler', 'Tourist'])(req, res, next);
};

/**
 * Driver role check middleware
 */
const isDriver = (req, res, next) => {
  return authorize(['driver', 'Driver'])(req, res, next);
};

/**
 * Admin role check middleware
 */
const isAdmin = (req, res, next) => {
  return authorize(['admin', 'Admin'])(req, res, next);
};

module.exports = {
  authenticate,
  authorize,
  isTourist,
  isDriver,
  isAdmin
};