// src/middleware/permissions.js
const { executeQuery } = require('../utils/dbUtils');
const ApiError = require('../utils/ApiError');
const logger = require('../utils/logger');

/**
 * Check if user has the required permission
 * @param {string|string[]} requiredPermissions - Single permission or array of permissions (any one is sufficient)
 * @returns {Function} Express middleware
 */
const hasPermission = (requiredPermissions) => {
  // Convert string to array if a single permission is provided
  const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];

  return async (req, res, next) => {
    try {
      // If no user is authenticated, deny access
      if (!req.user) {
        return next(new ApiError(401, 'Authentication required'));
      }

      const userId = req.user.UserID;

      // Log the permission check
      logger.debug(`Checking permissions for user ${userId}: ${permissions.join(', ')}`);

      // Super admins bypass permission checks
      const isSuperAdminQuery = `
        SELECT 1
        FROM AdminUsers AU
        JOIN AdminRoles AR ON AU.role_id = AR.role_id
        WHERE AU.user_id = @userId AND AR.role_name = 'Super Admin'
      `;

      const superAdminResult = await executeQuery(isSuperAdminQuery, { userId });

      if (superAdminResult.recordset && superAdminResult.recordset.length > 0) {
        logger.debug(`User ${userId} is a Super Admin, bypassing permission check`);
        return next();
      }

      // Check if user has any of the required permissions
      const permissionQuery = `
        SELECT P.permission_name
        FROM Users U
        JOIN AdminUsers AU ON U.user_id = AU.user_id
        JOIN AdminRolePermissions ARP ON AU.role_id = ARP.role_id
        JOIN Permissions P ON ARP.permission_id = P.permission_id
        WHERE U.user_id = @userId AND P.permission_name IN (${permissions.map((_, i) => `@p${i}`).join(', ')})
      `;

      // Create parameters object with userId and all permissions
      const params = { userId };
      permissions.forEach((permission, index) => {
        params[`p${index}`] = permission;
      });

      const result = await executeQuery(permissionQuery, params);

      if (result.recordset && result.recordset.length > 0) {
        // User has at least one of the required permissions
        const grantedPermissions = result.recordset.map(row => row.permission_name);
        logger.debug(`User ${userId} has permissions: ${grantedPermissions.join(', ')}`);
        return next();
      }

      // If we get here, user doesn't have any of the required permissions
      logger.warn(`Permission denied for user ${userId}: ${permissions.join(', ')}`);
      return next(new ApiError(403, 'Insufficient permissions'));
    } catch (error) {
      logger.error('Error in permission middleware:', error);
      return next(new ApiError(500, 'Error checking permissions'));
    }
  };
};

/**
 * Check if user has permission to access a specific entity
 * @param {string} entityType - Type of entity (e.g., 'booking', 'driver')
 * @param {string} action - Action to perform (e.g., 'view', 'edit')
 * @param {Function} getEntityIdFn - Function to extract entity ID from request
 * @param {Function} checkOwnershipFn - Optional function to check if user owns the entity
 * @returns {Function} Express middleware
 */
const hasEntityPermission = (entityType, action, getEntityIdFn, checkOwnershipFn) => {
  return async (req, res, next) => {
    try {
      // If no user is authenticated, deny access
      if (!req.user) {
        return next(new ApiError(401, 'Authentication required'));
      }

      const userId = req.user.UserID;
      const entityId = getEntityIdFn(req);

      if (!entityId) {
        return next(new ApiError(400, 'Entity ID is required'));
      }

      // Log the entity permission check
      logger.debug(`Checking ${entityType}.${action} permission for user ${userId} on entity ${entityId}`);

      // Super admins bypass permission checks
      const isSuperAdminQuery = `
        SELECT 1
        FROM AdminUsers AU
        JOIN AdminRoles AR ON AU.role_id = AR.role_id
        WHERE AU.user_id = @userId AND AR.role_name = 'Super Admin'
      `;

      const superAdminResult = await executeQuery(isSuperAdminQuery, { userId });

      if (superAdminResult.recordset && superAdminResult.recordset.length > 0) {
        logger.debug(`User ${userId} is a Super Admin, bypassing entity permission check`);
        return next();
      }

      // Check if user has the required permission
      const permissionName = `${entityType}.${action}`;
      const permissionQuery = `
        SELECT 1
        FROM Users U
        JOIN AdminUsers AU ON U.user_id = AU.user_id
        JOIN AdminRolePermissions ARP ON AU.role_id = ARP.role_id
        JOIN Permissions P ON ARP.permission_id = P.permission_id
        WHERE U.user_id = @userId AND P.permission_name = @permissionName
      `;

      const permissionResult = await executeQuery(permissionQuery, {
        userId,
        permissionName
      });

      if (permissionResult.recordset && permissionResult.recordset.length > 0) {
        // User has the required permission
        logger.debug(`User ${userId} has permission ${permissionName}`);
        return next();
      }

      // If ownership check function is provided, check if user owns the entity
      if (checkOwnershipFn) {
        const isOwner = await checkOwnershipFn(req, userId, entityId);
        if (isOwner) {
          logger.debug(`User ${userId} owns entity ${entityType} ${entityId}, granting access`);
          return next();
        }
      }

      // If we get here, user doesn't have the required permission
      logger.warn(`Entity permission denied for user ${userId}: ${permissionName} on ${entityType} ${entityId}`);
      return next(new ApiError(403, 'Insufficient permissions'));
    } catch (error) {
      logger.error('Error in entity permission middleware:', error);
      return next(new ApiError(500, 'Error checking entity permissions'));
    }
  };
};

module.exports = {
  hasPermission,
  hasEntityPermission
};
