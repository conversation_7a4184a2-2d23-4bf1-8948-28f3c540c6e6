<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Response Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #4a6ee0;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4a6ee0;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #3a5ec0;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .email-preview {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 4px;
        }
        .email-buttons {
            margin-top: 20px;
        }
        .email-buttons a {
            display: inline-block;
            margin-right: 10px;
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .email-buttons a.reject {
            background-color: #f44336;
        }
    </style>
</head>
<body>
    <h1>Booking Response Test</h1>
    
    <div class="form-group">
        <label for="notificationId">Notification ID:</label>
        <input type="number" id="notificationId" placeholder="Enter notification ID">
    </div>
    
    <div class="form-group">
        <label for="baseUrl">Base URL:</label>
        <input type="text" id="baseUrl" value="http://localhost:5173" placeholder="Enter base URL">
    </div>
    
    <button id="generateLinks">Generate Email Links</button>
    
    <div class="email-preview" id="emailPreview" style="display: none;">
        <h2>Email Preview</h2>
        <p>Hello Driver,</p>
        <p>You have a new booking request. Please review the details and respond.</p>
        
        <div class="email-buttons">
            <a href="#" id="acceptLink" target="_blank">Accept Booking</a>
            <a href="#" id="rejectLink" class="reject" target="_blank">Reject Booking</a>
        </div>
        
        <p>Thank you,<br>Siyoga Travels Team</p>
    </div>
    
    <div class="form-group" style="margin-top: 40px;">
        <label for="testUrl">Test URL:</label>
        <input type="text" id="testUrl" placeholder="Enter URL to test">
    </div>
    
    <button id="testUrl">Test URL</button>
    
    <div class="result" id="result" style="display: none;"></div>
    
    <script>
        document.getElementById('generateLinks').addEventListener('click', function() {
            const notificationId = document.getElementById('notificationId').value;
            const baseUrl = document.getElementById('baseUrl').value;
            
            if (!notificationId) {
                alert('Please enter a notification ID');
                return;
            }
            
            // Generate links
            const acceptLink = `${baseUrl}/driver/booking-response-handler/${notificationId}/accept`;
            const rejectLink = `${baseUrl}/driver/booking-response-handler/${notificationId}/reject`;
            
            // Update email preview
            document.getElementById('acceptLink').href = acceptLink;
            document.getElementById('rejectLink').href = rejectLink;
            document.getElementById('emailPreview').style.display = 'block';
            
            // Show result
            document.getElementById('result').textContent = `Accept Link: ${acceptLink}\nReject Link: ${rejectLink}`;
            document.getElementById('result').style.display = 'block';
        });
        
        document.getElementById('testUrl').addEventListener('click', function() {
            const url = document.getElementById('testUrl').value;
            
            if (!url) {
                alert('Please enter a URL to test');
                return;
            }
            
            window.open(url, '_blank');
        });
    </script>
</body>
</html>
