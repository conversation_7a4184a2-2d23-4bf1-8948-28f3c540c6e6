<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Link Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #4a6ee0;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4a6ee0;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #3a5ec0;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .email-preview {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 4px;
        }
        .email-buttons {
            margin-top: 20px;
        }
        .email-buttons a {
            display: inline-block;
            margin-right: 10px;
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .email-buttons a.reject {
            background-color: #f44336;
        }
        .test-section {
            margin-top: 40px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <h1>Email Link Test</h1>
    
    <div class="form-group">
        <label for="notificationId">Notification ID:</label>
        <input type="number" id="notificationId" placeholder="Enter notification ID">
    </div>
    
    <div class="form-group">
        <label for="baseUrl">Base URL:</label>
        <input type="text" id="baseUrl" value="http://localhost:5173" placeholder="Enter base URL">
    </div>
    
    <button id="generateLinks">Generate Email Links</button>
    
    <div class="email-preview" id="emailPreview" style="display: none;">
        <h2>Email Preview</h2>
        <p>Hello Driver,</p>
        <p>You have a new booking request. Please review the details and respond.</p>
        
        <div class="email-buttons">
            <a href="#" id="acceptLink" target="_blank">Accept Booking</a>
            <a href="#" id="rejectLink" class="reject" target="_blank">Reject Booking</a>
        </div>
        
        <p>Thank you,<br>Siyoga Travels Team</p>
    </div>
    
    <div class="result" id="result" style="display: none;"></div>
    
    <div class="test-section">
        <h2>Test Problematic Links</h2>
        <p>Test links with template variables that weren't replaced:</p>
        
        <div class="form-group">
            <label for="testType">Test Type:</label>
            <select id="testType">
                <option value="handler">booking-response-handler</option>
                <option value="regular">booking-response</option>
            </select>
        </div>
        
        <button id="testTemplateVar">Test Template Variable Link</button>
        <button id="testValidLink">Test Valid Link</button>
        
        <div class="result" id="testResult" style="display: none;"></div>
    </div>
    
    <script>
        document.getElementById('generateLinks').addEventListener('click', function() {
            const notificationId = document.getElementById('notificationId').value;
            const baseUrl = document.getElementById('baseUrl').value;
            
            if (!notificationId) {
                alert('Please enter a notification ID');
                return;
            }
            
            // Generate links
            const acceptLink = `${baseUrl}/driver/booking-response-handler/${notificationId}/accept`;
            const rejectLink = `${baseUrl}/driver/booking-response-handler/${notificationId}/reject`;
            
            // Update email preview
            document.getElementById('acceptLink').href = acceptLink;
            document.getElementById('rejectLink').href = rejectLink;
            document.getElementById('emailPreview').style.display = 'block';
            
            // Show result
            document.getElementById('result').textContent = `Accept Link: ${acceptLink}\nReject Link: ${rejectLink}`;
            document.getElementById('result').style.display = 'block';
        });
        
        document.getElementById('testTemplateVar').addEventListener('click', function() {
            const baseUrl = document.getElementById('baseUrl').value;
            const testType = document.getElementById('testType').value;
            
            // Generate problematic link with template variable
            const testLink = `${baseUrl}/driver/${testType === 'handler' ? 'booking-response-handler' : 'booking-response'}/{{notification_id}}/accept`;
            
            // Show result
            document.getElementById('testResult').textContent = `Testing link with template variable: ${testLink}`;
            document.getElementById('testResult').style.display = 'block';
            
            // Open in new tab
            window.open(testLink, '_blank');
        });
        
        document.getElementById('testValidLink').addEventListener('click', function() {
            const notificationId = document.getElementById('notificationId').value || '1';
            const baseUrl = document.getElementById('baseUrl').value;
            const testType = document.getElementById('testType').value;
            
            if (!notificationId) {
                alert('Please enter a notification ID or a default of 1 will be used');
            }
            
            // Generate valid link
            const testLink = `${baseUrl}/driver/${testType === 'handler' ? 'booking-response-handler' : 'booking-response'}/${notificationId}/accept`;
            
            // Show result
            document.getElementById('testResult').textContent = `Testing valid link: ${testLink}`;
            document.getElementById('testResult').style.display = 'block';
            
            // Open in new tab
            window.open(testLink, '_blank');
        });
    </script>
</body>
</html>
