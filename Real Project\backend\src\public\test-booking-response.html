<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Response Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #4a6ee0;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .test-section {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }
        .test-description {
            margin-bottom: 15px;
        }
        code {
            background-color: #f0f0f0;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
        .note {
            background-color: #fffde7;
            border-left: 4px solid #ffd600;
            padding: 10px;
            margin-top: 15px;
        }
        button {
            background-color: #4a6ee0;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #3a5ec0;
        }
        #results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Booking Response Test</h1>
    
    <div class="test-section">
        <div class="test-title">Test 1: Email Links</div>
        <div class="test-description">
            The email sent to drivers should contain links in this format:
        </div>
        
        <code>http://localhost:5173/driver/booking-response/123/accept</code><br>
        <code>http://localhost:5173/driver/booking-response/123/reject</code>
        
        <div class="note">
            <strong>Note:</strong> The "123" should be replaced with the actual notification ID.
            If you see {{notification_id}} in the URL, it means the template variable isn't being replaced.
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 2: Frontend API Calls</div>
        <div class="test-description">
            When a driver clicks on the email link, the frontend should make these API calls:
        </div>
        
        <code>GET /api/booking-requests/notification/:notificationId</code><br>
        <code>POST /api/booking-requests/response</code>
        
        <div class="note">
            <strong>Note:</strong> Check the browser console for any errors in these API calls.
            The most common issue is using incorrect API endpoints.
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 3: Backend API</div>
        <div class="test-description">
            The backend API endpoints for booking responses:
        </div>
        
        <code>GET http://localhost:9876/api/booking-requests/notification/:notificationId</code><br>
        <code>POST http://localhost:9876/api/booking-requests/response</code>
        
        <div class="note">
            <strong>Note:</strong> These API endpoints should be working correctly if the backend is running on port 9876.
            You can test them using tools like Postman or curl.
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 4: Manual API Test</div>
        <div class="test-description">
            Test the API endpoints directly from this page:
        </div>
        
        <div style="margin: 15px 0;">
            <label for="notificationId">Notification ID:</label>
            <input type="number" id="notificationId" value="1" style="margin-left: 10px;">
        </div>
        
        <div style="margin: 15px 0;">
            <label for="token">Auth Token:</label>
            <input type="text" id="token" style="margin-left: 10px; width: 300px;" placeholder="Bearer token from localStorage">
        </div>
        
        <button onclick="testGetNotification()">Test Get Notification</button>
        <button onclick="testAcceptBooking()">Test Accept Booking</button>
        <button onclick="testRejectBooking()">Test Reject Booking</button>
        
        <div id="results"></div>
        
        <script>
            async function testGetNotification() {
                const notificationId = document.getElementById('notificationId').value;
                const token = document.getElementById('token').value;
                const resultsDiv = document.getElementById('results');
                
                resultsDiv.textContent = 'Loading...';
                
                try {
                    const response = await fetch(`http://localhost:9876/api/booking-requests/notification/${notificationId}`, {
                        headers: {
                            'Authorization': token
                        }
                    });
                    
                    const data = await response.json();
                    resultsDiv.textContent = JSON.stringify(data, null, 2);
                } catch (error) {
                    resultsDiv.textContent = `Error: ${error.message}`;
                }
            }
            
            async function testAcceptBooking() {
                await respondToBooking('accepted');
            }
            
            async function testRejectBooking() {
                await respondToBooking('rejected');
            }
            
            async function respondToBooking(response) {
                const notificationId = document.getElementById('notificationId').value;
                const token = document.getElementById('token').value;
                const resultsDiv = document.getElementById('results');
                
                resultsDiv.textContent = 'Loading...';
                
                try {
                    const response = await fetch(`http://localhost:9876/api/booking-requests/response`, {
                        method: 'POST',
                        headers: {
                            'Authorization': token,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            notificationId,
                            response
                        })
                    });
                    
                    const data = await response.json();
                    resultsDiv.textContent = JSON.stringify(data, null, 2);
                } catch (error) {
                    resultsDiv.textContent = `Error: ${error.message}`;
                }
            }
        </script>
    </div>
</body>
</html>
