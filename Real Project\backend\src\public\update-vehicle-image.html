<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Vehicle Image</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="number"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input[type="file"] {
            padding: 8px 0;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>Update Vehicle Image</h1>
    
    <div class="form-group">
        <label for="vehicleId">Vehicle ID:</label>
        <input type="number" id="vehicleId" name="vehicleId" required>
    </div>
    
    <div class="form-group">
        <label for="vehicleImage">Vehicle Image:</label>
        <input type="file" id="vehicleImage" name="vehicleImage" accept="image/*" required>
    </div>
    
    <button id="updateButton">Update Vehicle Image</button>
    
    <div id="result" class="result hidden">
        <h3>Result:</h3>
        <div id="resultContent"></div>
    </div>
    
    <script>
        document.getElementById('updateButton').addEventListener('click', async function() {
            const vehicleId = document.getElementById('vehicleId').value;
            const vehicleImage = document.getElementById('vehicleImage').files[0];
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            if (!vehicleId || !vehicleImage) {
                resultDiv.classList.remove('hidden');
                resultContent.innerHTML = '<p class="error">Please provide both Vehicle ID and Image</p>';
                return;
            }
            
            try {
                // Create form data
                const formData = new FormData();
                formData.append('vehicleId', vehicleId);
                formData.append('vehicleImage', vehicleImage);
                
                // Get token from localStorage (if authentication is required)
                const token = localStorage.getItem('token');
                
                // Determine API URL
                const apiPort = window.location.port === '5173' ? '9876' : '5000';
                const apiBaseUrl = window.location.hostname === 'localhost'
                    ? `http://${window.location.hostname}:${apiPort}`
                    : window.location.origin;
                
                const response = await fetch(`${apiBaseUrl}/api/vehicles/${vehicleId}/update-image`, {
                    method: 'POST',
                    headers: token ? {
                        'Authorization': `Bearer ${token}`
                    } : {},
                    body: formData
                });
                
                const data = await response.json();
                
                resultDiv.classList.remove('hidden');
                
                if (response.ok) {
                    resultContent.innerHTML = `
                        <p class="success">${data.message}</p>
                        <p>Vehicle ID: ${data.data.vehicle_id}</p>
                        <p>Image Path: ${data.data.vehicle_photo}</p>
                        ${data.data.vehicle_photo ? `<img src="${apiBaseUrl}/uploads/${data.data.vehicle_photo}" alt="Vehicle" style="max-width: 100%; max-height: 300px;">` : ''}
                    `;
                } else {
                    resultContent.innerHTML = `<p class="error">Error: ${data.message}</p>`;
                }
            } catch (error) {
                resultDiv.classList.remove('hidden');
                resultContent.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
