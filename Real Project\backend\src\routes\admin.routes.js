// src/routes/admin.routes.js
const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const adminRoleController = require('../controllers/adminRoleController');
const adminPermissionController = require('../controllers/adminPermissionController');
const adminAuditController = require('../controllers/adminAuditController');
const adminReportController = require('../controllers/adminReportController');
const { authenticate, authorize } = require('../middleware/auth');
const { hasPermission } = require('../middleware/permissions');

// Special route for creating an admin - no auth required
// This allows creating the first admin in the system
router.post('/create', adminController.createAdmin);

// All other routes require authentication and admin authorization
router.use(authenticate);
// Apply the authorize middleware to all routes
router.use((req, res, next) => {
  authorize('admin')(req, res, next);
});

// Dashboard
router.get('/dashboard', adminController.getDashboardStats);

// Drivers management
router.get('/drivers', hasPermission('driver.view'), adminController.getAllDrivers);
router.get('/drivers/:id', hasPermission('driver.view'), adminController.getDriverById);
router.patch('/drivers/:id/status', hasPermission('driver.approve'), adminController.updateDriverStatus);
router.post('/drivers/:id/status', hasPermission('driver.approve'), adminController.updateDriverStatus);
router.put('/drivers/:id/status', hasPermission('driver.approve'), adminController.updateDriverStatus);

// Tourists management
router.get('/tourists', hasPermission('tourist.view'), adminController.getAllTourists);
router.get('/tourists/:id', hasPermission('tourist.view'), adminController.getTouristById);

// Legacy Reports (for backward compatibility)
router.get('/reports', hasPermission('report.view'), adminController.generateReport);

// Admin user management
router.put('/change-password', adminController.changeAdminPassword);

// Role management
router.get('/roles', hasPermission('admin.role.view'), adminRoleController.getAllRoles);
router.get('/roles/:id', hasPermission('admin.role.view'), adminRoleController.getRoleById);
router.post('/roles', hasPermission('admin.role.create'), adminRoleController.createRole);
router.put('/roles/:id', hasPermission('admin.role.edit'), adminRoleController.updateRole);
router.delete('/roles/:id', hasPermission('admin.role.delete'), adminRoleController.deleteRole);
router.post('/roles/:id/permissions', hasPermission('admin.permission.assign'), adminRoleController.assignPermissions);

// Permission management
router.get('/permissions', hasPermission('admin.permission.view'), adminPermissionController.getAllPermissions);
router.get('/permissions/categories', hasPermission('admin.permission.view'), adminPermissionController.getPermissionCategories);
router.post('/permissions', hasPermission('admin.permission.assign'), adminPermissionController.createPermission);
router.put('/permissions/:id', hasPermission('admin.permission.assign'), adminPermissionController.updatePermission);
router.delete('/permissions/:id', hasPermission('admin.permission.assign'), adminPermissionController.deletePermission);
router.get('/permissions/role/:roleId', hasPermission('admin.permission.view'), adminPermissionController.getPermissionsByRole);

// Audit logs
router.get('/audit-logs', hasPermission('audit.view'), adminAuditController.getAuditLogs);
router.get('/audit-logs/actions', hasPermission('audit.view'), adminAuditController.getAuditLogActions);
router.get('/audit-logs/entity-types', hasPermission('audit.view'), adminAuditController.getAuditLogEntityTypes);

// Enhanced reporting
router.get('/reports/templates', hasPermission('report.view'), adminReportController.getAllReportTemplates);
router.get('/reports/templates/:id', hasPermission('report.view'), adminReportController.getReportTemplateById);
router.post('/reports/templates', hasPermission('report.create'), adminReportController.createReportTemplate);
router.put('/reports/templates/:id', hasPermission('report.create'), adminReportController.updateReportTemplate);
router.delete('/reports/templates/:id', hasPermission('report.create'), adminReportController.deleteReportTemplate);
router.post('/reports/generate/:id', hasPermission('report.view'), adminReportController.generateReport);

module.exports = router;