// src/routes/bookingRequest.routes.js
const express = require('express');
const router = express.Router();
const bookingRequestController = require('../controllers/bookingRequestController');
const { authenticate, authorize } = require('../middleware/auth');

// All routes require authentication
router.use(authenticate);

// Create a booking request (tourist only)
router.post('/', authorize(['traveler', 'Tourist']), bookingRequestController.createBookingRequest);

// Get booking request status
router.get('/:requestId', bookingRequestController.getBookingRequestStatus);

// Cancel booking request
router.post('/:requestId/cancel', authorize(['traveler', 'Tourist']), bookingRequestController.cancelBookingRequest);

// Respond to booking request (for drivers)
router.post('/response', authorize('Driver'), bookingRequestController.respondToBookingRequest);

// Get driver notification details
router.get('/notification/:notificationId', authorize('Driver'), bookingRequestController.getDriverNotification);

// Update booking request status
router.put('/:requestId/status', bookingRequestController.updateBookingRequestStatus);

// Get tourist booking requests using user ID directly
router.get('/tourist-direct', authorize(['traveler', 'Tourist']), bookingRequestController.getTouristBookingRequestsDirect);

module.exports = router;
