// Debug routes for troubleshooting specific issues
const express = require('express');
const router = express.Router();
const { executeQuery } = require('../config/DB/db');
const logger = require('../config/logger');

// Route to check notifications for a specific driver by email
router.get('/driver-notifications/:email', async (req, res) => {
  try {
    const { email } = req.params;
    logger.info(`Debug: Checking notifications for driver with email: ${email}`);

    // Step 1: Get driver ID from email
    const driverQuery = `
      SELECT
        U.user_id,
        U.email,
        U.role,
        U.full_name,
        D.driver_id,
        D.status
      FROM Users U
      JOIN Drivers D ON U.user_id = D.user_id
      WHERE U.email = @email
    `;

    const driverResult = await executeQuery(driverQuery, { email });

    if (!driverResult.recordset || driverResult.recordset.length === 0) {
      logger.warn(`Debug: No driver found with email: ${email}`);
      return res.status(404).json({
        success: false,
        message: `No driver found with email: ${email}`
      });
    }

    const driver = driverResult.recordset[0];
    logger.info(`Debug: Found driver: ${driver.full_name} (ID: ${driver.driver_id}, Status: ${driver.status})`);

    // Step 2: Get notifications for this driver
    const notificationsQuery = `
      SELECT
        DN.notification_id,
        DN.request_id,
        DN.sent_at,
        DN.response,
        DN.response_at,
        BR.origin,
        BR.destination,
        BR.start_date,
        BR.start_time,
        BR.trip_type,
        BR.vehicle_type,
        BR.num_travelers,
        BR.total_distance,
        BR.estimated_duration,
        BR.total_cost,
        BR.driver_accommodation,
        BR.special_requests,
        BR.status as request_status,
        U.full_name as tourist_name
      FROM DriverNotifications DN
      JOIN BookingRequests BR ON DN.request_id = BR.request_id
      JOIN Users U ON BR.tourist_id = U.user_id
      WHERE DN.driver_id = @driverId
      ORDER BY DN.sent_at DESC
    `;

    const notificationsResult = await executeQuery(notificationsQuery, { driverId: driver.driver_id });

    if (!notificationsResult.recordset || notificationsResult.recordset.length === 0) {
      logger.warn(`Debug: No notifications found for driver ID: ${driver.driver_id}`);
      return res.status(200).json({
        success: true,
        message: `No notifications found for driver ID: ${driver.driver_id}`,
        data: []
      });
    }

    logger.info(`Debug: Found ${notificationsResult.recordset.length} notifications for driver ID: ${driver.driver_id}`);

    // Count notifications by response status
    const pendingCount = notificationsResult.recordset.filter(n => n.response === 'pending').length;
    const acceptedCount = notificationsResult.recordset.filter(n => n.response === 'accepted').length;
    const rejectedCount = notificationsResult.recordset.filter(n => n.response === 'rejected').length;
    const expiredCount = notificationsResult.recordset.filter(n => n.response === 'expired').length;

    logger.info(`Debug: Notification status breakdown - Pending: ${pendingCount}, Accepted: ${acceptedCount}, Rejected: ${rejectedCount}, Expired: ${expiredCount}`);

    // Return the notifications
    return res.status(200).json({
      success: true,
      message: `Found ${notificationsResult.recordset.length} notifications for driver: ${driver.full_name}`,
      data: notificationsResult.recordset,
      stats: {
        pending: pendingCount,
        accepted: acceptedCount,
        rejected: rejectedCount,
        expired: expiredCount
      },
      driver: {
        id: driver.driver_id,
        name: driver.full_name,
        email: driver.email,
        status: driver.status
      }
    });

  } catch (error) {
    logger.error(`Debug: Error checking driver notifications: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: `Error checking driver notifications: ${error.message}`
    });
  }
});

// Route to test vehicle registration without authentication
router.post('/test-vehicle-registration', express.json(), async (req, res) => {
  try {
    logger.info('Debug: Testing vehicle registration without authentication');

    // Log the request body
    logger.info(`Debug: Vehicle registration request body: ${JSON.stringify(req.body)}`);

    // Check if all required fields are present
    const requiredFields = ['type', 'make', 'model', 'year', 'licensePlate', 'capacity', 'pricePerDay'];
    const missingFields = requiredFields.filter(field => !req.body[field]);

    if (missingFields.length > 0) {
      logger.warn(`Debug: Missing required fields: ${missingFields.join(', ')}`);
      return res.status(400).json({
        success: false,
        message: `Missing required fields: ${missingFields.join(', ')}`
      });
    }

    // Validate field types
    const validationErrors = [];

    // Check year is a number
    if (isNaN(parseInt(req.body.year))) {
      validationErrors.push('Year must be a number');
    }

    // Check capacity is a number
    if (isNaN(parseInt(req.body.capacity))) {
      validationErrors.push('Capacity must be a number');
    }

    // Check pricePerDay is a number
    if (isNaN(parseFloat(req.body.pricePerDay))) {
      validationErrors.push('Price per day must be a number');
    }

    if (validationErrors.length > 0) {
      logger.warn(`Debug: Validation errors: ${validationErrors.join(', ')}`);
      return res.status(400).json({
        success: false,
        message: `Validation errors: ${validationErrors.join(', ')}`
      });
    }

    // Log success
    logger.info('Debug: Vehicle registration test successful - all required fields present and valid');

    return res.status(200).json({
      success: true,
      message: 'Vehicle registration test successful',
      data: {
        receivedFields: Object.keys(req.body),
        requiredFields: requiredFields,
        allFieldsPresent: missingFields.length === 0,
        validationPassed: true
      }
    });

  } catch (error) {
    logger.error(`Debug: Error testing vehicle registration: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: `Error testing vehicle registration: ${error.message}`
    });
  }
});

// Route to check user role and RoleID by email
router.get('/user-role/:email', async (req, res) => {
  try {
    const { email } = req.params;
    logger.info(`Debug: Checking role and RoleID for user with email: ${email}`);

    // Get user info including role and RoleID
    const userQuery = `
      SELECT
        U.user_id AS UserID,
        U.email AS Email,
        U.role AS Role,
        U.full_name AS Name,
        CASE
          WHEN U.role = 'traveler' THEN T.tourist_id
          WHEN U.role = 'driver' THEN D.driver_id
          ELSE NULL
        END AS RoleID
      FROM Users U
      LEFT JOIN Tourists T ON U.user_id = T.user_id AND U.role = 'traveler'
      LEFT JOIN Drivers D ON U.user_id = D.user_id AND U.role = 'driver'
      WHERE U.email = @email
    `;

    const userResult = await executeQuery(userQuery, { email });

    if (!userResult.recordset || userResult.recordset.length === 0) {
      logger.warn(`Debug: No user found with email: ${email}`);
      return res.status(404).json({
        success: false,
        message: `No user found with email: ${email}`
      });
    }

    const user = userResult.recordset[0];
    logger.info(`Debug: Found user: ${user.Name} (ID: ${user.UserID}, Role: ${user.Role}, RoleID: ${user.RoleID})`);

    // If it's a driver, get additional driver info
    if (user.Role.toLowerCase() === 'driver') {
      const driverQuery = `
        SELECT
          D.driver_id,
          D.status,
          D.license_number,
          D.license_expiry_date,
          D.nic_number
        FROM Drivers D
        WHERE D.user_id = @userId
      `;

      const driverResult = await executeQuery(driverQuery, { userId: user.UserID });

      if (driverResult.recordset && driverResult.recordset.length > 0) {
        const driverInfo = driverResult.recordset[0];
        logger.info(`Debug: Driver details - Status: ${driverInfo.status}, License: ${driverInfo.license_number}`);

        return res.status(200).json({
          success: true,
          message: `Found user with email: ${email}`,
          user: {
            id: user.UserID,
            email: user.Email,
            name: user.Name,
            role: user.Role,
            roleId: user.RoleID
          },
          driverInfo: driverInfo
        });
      }
    }

    // Return the user info
    return res.status(200).json({
      success: true,
      message: `Found user with email: ${email}`,
      user: {
        id: user.UserID,
        email: user.Email,
        name: user.Name,
        role: user.Role,
        roleId: user.RoleID
      }
    });

  } catch (error) {
    logger.error(`Debug: Error checking user role: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: `Error checking user role: ${error.message}`
    });
  }
});

module.exports = router;
