const express = require('express');
const router = express.Router();
const directBookingController = require('../controllers/directBookingController');

// Public routes with no authentication
router.get('/tourist-direct/:userId', directBookingController.getTouristBookings);
router.get('/direct/:requestId', directBookingController.getBookingById);

// Add a test route that's completely public
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Public test endpoint is working',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
