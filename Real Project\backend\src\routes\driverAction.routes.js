// src/routes/driverAction.routes.js
const express = require('express');
const router = express.Router();
const driverActionController = require('../controllers/driverActionController');
const { authenticate, authorize } = require('../middleware/auth');

// All routes require authentication
router.use(authenticate);

// Get booking details
router.get('/booking/:requestId', authorize('Driver'), driverActionController.getBookingDetails);

// Send payment reminder to tourist
router.post('/remind/:requestId', authorize('Driver'), driverActionController.sendPaymentReminder);

// Cancel booking
router.post('/cancel/:requestId', authorize('Driver'), driverActionController.cancelBooking);

module.exports = router;
