// src/routes/driverBooking.routes.js
const express = require('express');
const router = express.Router();
const driverBookingController = require('../controllers/driverBookingController');
const { authenticate, authorize } = require('../middleware/auth');

// All routes require authentication
router.use(authenticate);

// Get all booking requests for the authenticated driver
router.get('/booking-requests', authorize('Driver'), driverBookingController.getDriverBookingRequests);

// Get driver-confirmed booking requests
router.get('/confirmed-bookings', authorize('Driver'), driverBookingController.getDriverConfirmedBookings);

module.exports = router;
