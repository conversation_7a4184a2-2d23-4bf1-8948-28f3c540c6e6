const express = require('express');
const router = express.Router();
const db = require('../config/DB/db');

// Test endpoint to check booking requests for a tourist
router.get('/booking-requests/:touristId', async (req, res) => {
  try {
    const touristId = req.params.touristId;
    console.log(`Checking booking requests for tourist ID: ${touristId}`);

    // Query to get all booking requests for this tourist
    const query = `
      SELECT
        br.request_id,
        br.tourist_id,
        t.full_name as tourist_name,
        br.origin,
        br.destination,
        br.start_date,
        br.start_time,
        br.status,
        br.assigned_driver_id,
        br.vehicle_type,
        br.total_cost,
        br.total_distance,
        d.full_name as driver_name,
        d.phone as driver_phone,
        d.email as driver_email,
        d.rating as driver_rating,
        v.make as vehicle_make,
        v.model as vehicle_model,
        v.type as vehicle_type,
        v.color as vehicle_color,
        v.license_plate
      FROM BookingRequests br
      LEFT JOIN Tourists t ON br.tourist_id = t.tourist_id
      LEFT JOIN Drivers d ON br.assigned_driver_id = d.driver_id
      LEFT JOIN Vehicles v ON d.vehicle_id = v.vehicle_id
      WHERE br.tourist_id = ?
      ORDER BY br.start_date DESC, br.start_time DESC
    `;

    const [bookings] = await db.execute(query, [touristId]);
    console.log(`Found ${bookings.length} booking requests for tourist ${touristId}`);

    return res.json({
      success: true,
      message: `Found ${bookings.length} booking requests for tourist ${touristId}`,
      data: bookings
    });
  } catch (error) {
    console.error('Error checking booking requests:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to check booking requests',
      error: error.message
    });
  }
});

// Test endpoint to get all tourists
router.get('/tourists', async (req, res) => {
  try {
    const [tourists] = await db.execute('SELECT tourist_id, full_name, email FROM Tourists');
    console.log(`Found ${tourists.length} tourists`);

    return res.json({
      success: true,
      message: `Found ${tourists.length} tourists`,
      data: tourists
    });
  } catch (error) {
    console.error('Error getting tourists:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get tourists',
      error: error.message
    });
  }
});

module.exports = router;
