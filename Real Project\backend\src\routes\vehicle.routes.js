// src/routes/vehicle.routes.js
const express = require('express');
const router = express.Router();
const vehicleController = require('../controllers/vehicleController');
const vehicleImageController = require('../controllers/vehicleImageController');
const { authenticate, authorize } = require('../middleware/auth');
const { upload } = require('../middleware/upload');

// Public routes
router.get('/', vehicleController.getAllVehicles);
router.get('/available', vehicleController.getAllVehicles); // For getting available vehicles
router.get('/:id', vehicleController.getVehicleById);
router.get('/:id/availability', vehicleController.checkAvailability);

// Public route for updating vehicle image (no authentication required)
router.post('/public/:id/update-image', upload.single('vehicleImage'), vehicleImageController.updateVehicleImage);

// Protected routes
router.use(authenticate);

// Driver routes
router.post('/', authorize('Driver'), upload.single('vehicleImage'), vehicleController.registerVehicle);
router.put('/:id', authorize('Driver'), upload.single('vehicleImage'), vehicleController.updateVehicle);
router.delete('/:id', authorize('Driver'), vehicleController.deleteVehicle);
router.get('/driver/vehicles', authorize('Driver'), vehicleController.getDriverVehicles);

// Vehicle image update route - can be used with or without authentication
router.post('/:id/update-image', upload.single('vehicleImage'), vehicleImageController.updateVehicleImage);

module.exports = router;