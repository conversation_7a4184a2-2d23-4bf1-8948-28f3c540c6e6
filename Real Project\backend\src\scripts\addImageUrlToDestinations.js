// src/scripts/addImageUrlToDestinations.js
const sql = require("mssql/msnodesqlv8");

// DSN connection configuration
const config = {
  driver: "msnodesqlv8",
  connectionString: "DSN=TripBookingSystem;Trusted_Connection=Yes;"
};

async function addImageUrlToDestinations() {
  try {
    console.log('Connecting to database...');
    const pool = new sql.ConnectionPool(config);
    await pool.connect();
    console.log('Connected to database successfully');

    // Check if image_url column exists
    const checkQuery = `
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Destinations' AND COLUMN_NAME = 'image_url')
      BEGIN
        ALTER TABLE Destinations
        ADD image_url NVARCHAR(MAX) NULL;
        
        SELECT 'Added image_url column to Destinations table' AS Result;
      END
      ELSE
      BEGIN
        SELECT 'image_url column already exists in Destinations table' AS Result;
      END
    `;

    console.log('Executing query...');
    const result = await pool.request().query(checkQuery);
    console.log('Result:', result.recordset[0].Result);

    // Close the connection
    await pool.close();
    console.log('Connection closed');
  } catch (error) {
    console.error('Error:', error);
  }
}

addImageUrlToDestinations();
