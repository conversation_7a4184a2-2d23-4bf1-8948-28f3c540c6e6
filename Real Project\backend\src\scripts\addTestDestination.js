// Script to add a test destination to the database
const sql = require("mssql/msnodesqlv8");

// DSN connection configuration
const config = {
  driver: "msnodesqlv8",
  connectionString: "DSN=TripBookingSystem;Trusted_Connection=Yes;"
};

async function addTestDestination() {
  try {
    console.log('Connecting to database...');
    const pool = new sql.ConnectionPool(config);
    await pool.connect();
    console.log('Connected to database successfully');

    // Insert a test destination
    const query = `
      INSERT INTO Destinations (name, province, region, description)
      VALUES ('Test Destination', 'Western', 'Western Coast', 'A beautiful test destination in Colombo');
    `;

    console.log('Executing query...');
    await pool.request().query(query);
    console.log('Test destination added successfully');

    // Close the connection
    await pool.close();
    console.log('Connection closed');
  } catch (error) {
    console.error('Error:', error);
  }
}

addTestDestination();
