// Script to check database for booking requests and driver notifications
const { executeQuery } = require('../config/DB/db');

async function checkDatabase() {
  console.log('Checking database...');
  
  try {
    // 1. Check if BookingRequests table exists
    console.log('\n--- CHECKING TABLES ---');
    const tablesQuery = `
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_TYPE = 'BASE TABLE'
      AND TABLE_NAME IN ('BookingRequests', 'DriverNotifications', 'EmailTemplates')
    `;
    
    const tablesResult = await executeQuery(tablesQuery);
    
    if (!tablesResult.recordset || tablesResult.recordset.length === 0) {
      console.log('Required tables not found');
    } else {
      console.log('Found tables:');
      for (const table of tablesResult.recordset) {
        console.log(`- ${table.TABLE_NAME}`);
      }
    }
    
    // 2. Check recent booking requests
    console.log('\n--- RECENT BOOKING REQUESTS ---');
    const bookingRequestsQuery = `
      SELECT TOP 5 request_id, tourist_id, origin, destination, vehicle_type, status, created_at
      FROM BookingRequests
      ORDER BY created_at DESC
    `;
    
    const bookingRequests = await executeQuery(bookingRequestsQuery);
    
    if (!bookingRequests.recordset || bookingRequests.recordset.length === 0) {
      console.log('No recent booking requests found');
    } else {
      console.log(`Found ${bookingRequests.recordset.length} recent booking requests`);
      console.log(JSON.stringify(bookingRequests.recordset, null, 2));
    }
    
    // 3. Check driver notifications
    console.log('\n--- DRIVER NOTIFICATIONS ---');
    const notificationsQuery = `
      SELECT TOP 10 notification_id, request_id, driver_id, response, sent_at
      FROM DriverNotifications
      ORDER BY sent_at DESC
    `;
    
    const notifications = await executeQuery(notificationsQuery);
    
    if (!notifications.recordset || notifications.recordset.length === 0) {
      console.log('No driver notifications found');
    } else {
      console.log(`Found ${notifications.recordset.length} driver notifications`);
      console.log(JSON.stringify(notifications.recordset, null, 2));
    }
    
    // 4. Check email templates
    console.log('\n--- EMAIL TEMPLATES ---');
    const templatesQuery = `
      SELECT template_id, template_name, subject
      FROM EmailTemplates
    `;
    
    const templates = await executeQuery(templatesQuery);
    
    if (!templates.recordset || templates.recordset.length === 0) {
      console.log('No email templates found');
    } else {
      console.log(`Found ${templates.recordset.length} email templates`);
      console.log(JSON.stringify(templates.recordset, null, 2));
    }
    
    // 5. Check drivers with vehicle type 'Cars'
    console.log('\n--- DRIVERS WITH CARS ---');
    const driversQuery = `
      SELECT d.driver_id, u.email, u.full_name, v.vehicle_type
      FROM Drivers d
      JOIN Users u ON d.user_id = u.user_id
      JOIN Vehicles v ON d.driver_id = v.driver_id
      WHERE v.vehicle_type = 'Cars'
    `;
    
    const drivers = await executeQuery(driversQuery);
    
    if (!drivers.recordset || drivers.recordset.length === 0) {
      console.log('No drivers with vehicle type "Cars" found');
    } else {
      console.log(`Found ${drivers.recordset.length} drivers with vehicle type "Cars"`);
      console.log(JSON.stringify(drivers.recordset, null, 2));
    }
    
  } catch (error) {
    console.error('Error checking database:', error);
  }
}

// Run the function
checkDatabase()
  .then(() => {
    console.log('\nCheck completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
