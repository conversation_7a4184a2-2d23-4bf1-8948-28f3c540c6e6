// <PERSON><PERSON>t to check if a driver with a specific email exists and has notifications
const { executeQuery } = require('../config/DB/db');
const logger = require('../config/logger');

// Email to check
const EMAIL_TO_CHECK = '<EMAIL>';

async function checkDriverNotifications() {
  try {
    console.log(`Checking driver information for email: ${EMAIL_TO_CHECK}`);
    
    // Step 1: Check if the user exists
    const userQuery = `
      SELECT 
        U.user_id,
        U.email,
        U.role,
        U.full_name,
        U.created_at
      FROM Users U
      WHERE U.email = @email
    `;
    
    const userResult = await executeQuery(userQuery, { email: EMAIL_TO_CHECK });
    
    if (!userResult.recordset || userResult.recordset.length === 0) {
      console.log(`No user found with email: ${EMAIL_TO_CHECK}`);
      return;
    }
    
    const user = userResult.recordset[0];
    console.log(`Found user: ${user.full_name} (ID: ${user.user_id}, Role: ${user.role})`);
    
    if (user.role !== 'driver') {
      console.log(`User with email ${EMAIL_TO_CHECK} exists but is not a driver (Role: ${user.role})`);
      return;
    }
    
    // Step 2: Get driver ID
    const driverQuery = `
      SELECT 
        D.driver_id,
        D.status,
        D.registration_date
      FROM Drivers D
      WHERE D.user_id = @userId
    `;
    
    const driverResult = await executeQuery(driverQuery, { userId: user.user_id });
    
    if (!driverResult.recordset || driverResult.recordset.length === 0) {
      console.log(`No driver record found for user ID: ${user.user_id}`);
      return;
    }
    
    const driver = driverResult.recordset[0];
    console.log(`Found driver record: ID: ${driver.driver_id}, Status: ${driver.status}`);
    
    if (driver.status !== 'Approved') {
      console.log(`Driver account is not approved (Status: ${driver.status}). This might be why they're not receiving notifications.`);
    }
    
    // Step 3: Check for notifications
    const notificationsQuery = `
      SELECT 
        DN.notification_id,
        DN.request_id,
        DN.sent_at,
        DN.response,
        DN.response_at,
        BR.origin,
        BR.destination,
        BR.status as request_status
      FROM DriverNotifications DN
      JOIN BookingRequests BR ON DN.request_id = BR.request_id
      WHERE DN.driver_id = @driverId
      ORDER BY DN.sent_at DESC
    `;
    
    const notificationsResult = await executeQuery(notificationsQuery, { driverId: driver.driver_id });
    
    if (!notificationsResult.recordset || notificationsResult.recordset.length === 0) {
      console.log(`No notifications found for driver ID: ${driver.driver_id}`);
      console.log('This could be because:');
      console.log('1. No booking requests have been sent to this driver');
      console.log('2. There might be an issue with the notification system');
      return;
    }
    
    console.log(`Found ${notificationsResult.recordset.length} notifications for driver ID: ${driver.driver_id}`);
    
    // Count notifications by response status
    const pendingCount = notificationsResult.recordset.filter(n => n.response === 'pending').length;
    const acceptedCount = notificationsResult.recordset.filter(n => n.response === 'accepted').length;
    const rejectedCount = notificationsResult.recordset.filter(n => n.response === 'rejected').length;
    const expiredCount = notificationsResult.recordset.filter(n => n.response === 'expired').length;
    
    console.log(`Notification status breakdown:`);
    console.log(`- Pending: ${pendingCount}`);
    console.log(`- Accepted: ${acceptedCount}`);
    console.log(`- Rejected: ${rejectedCount}`);
    console.log(`- Expired: ${expiredCount}`);
    
    // Show details of each notification
    console.log('\nNotification details:');
    notificationsResult.recordset.forEach((notification, index) => {
      console.log(`${index + 1}. Notification ID: ${notification.notification_id}`);
      console.log(`   Request ID: ${notification.request_id}`);
      console.log(`   Sent at: ${notification.sent_at}`);
      console.log(`   Response: ${notification.response}`);
      console.log(`   Response at: ${notification.response_at || 'N/A'}`);
      console.log(`   Trip: ${notification.origin} to ${notification.destination}`);
      console.log(`   Request status: ${notification.request_status}`);
      console.log('---');
    });
    
    // Step 4: Check for vehicles
    const vehiclesQuery = `
      SELECT 
        V.vehicle_id,
        V.vehicle_type,
        V.make_model,
        V.registration_number,
        V.verified
      FROM Vehicles V
      WHERE V.driver_id = @driverId
    `;
    
    const vehiclesResult = await executeQuery(vehiclesQuery, { driverId: driver.driver_id });
    
    if (!vehiclesResult.recordset || vehiclesResult.recordset.length === 0) {
      console.log(`No vehicles found for driver ID: ${driver.driver_id}`);
      console.log('Driver must have at least one verified vehicle to receive booking requests.');
      return;
    }
    
    console.log(`\nFound ${vehiclesResult.recordset.length} vehicles for driver ID: ${driver.driver_id}`);
    
    vehiclesResult.recordset.forEach((vehicle, index) => {
      console.log(`${index + 1}. Vehicle ID: ${vehicle.vehicle_id}`);
      console.log(`   Type: ${vehicle.vehicle_type}`);
      console.log(`   Make/Model: ${vehicle.make_model}`);
      console.log(`   Registration: ${vehicle.registration_number}`);
      console.log(`   Verified: ${vehicle.verified ? 'Yes' : 'No'}`);
      
      if (!vehicle.verified) {
        console.log('   WARNING: This vehicle is not verified. Driver will not receive booking requests for unverified vehicles.');
      }
    });
    
  } catch (error) {
    console.error('Error checking driver notifications:', error);
  }
}

// Run the function
checkDriverNotifications()
  .then(() => {
    console.log('Driver notification check completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Error running driver notification check:', error);
    process.exit(1);
  });
