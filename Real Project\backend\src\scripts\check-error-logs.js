// Script to check error logs
const fs = require('fs');
const path = require('path');

function checkErrorLogs() {
  console.log('Checking error logs...');
  
  try {
    // Check logs directory
    const logDir = path.join(__dirname, '../../logs');
    
    if (!fs.existsSync(logDir)) {
      console.log('Logs directory not found');
      return;
    }
    
    // Get all log files
    const logFiles = fs.readdirSync(logDir)
      .filter(file => file.endsWith('.log'))
      .sort((a, b) => {
        const statA = fs.statSync(path.join(logDir, a));
        const statB = fs.statSync(path.join(logDir, b));
        return statB.mtime.getTime() - statA.mtime.getTime();
      });
    
    if (logFiles.length === 0) {
      console.log('No log files found');
      return;
    }
    
    // Check the latest log file
    const latestLogFile = logFiles[0];
    console.log(`Checking latest log file: ${latestLogFile}`);
    
    const logContent = fs.readFileSync(path.join(logDir, latestLogFile), 'utf8');
    
    // Look for errors related to booking requests and emails
    const bookingRequestErrors = logContent.split('\n')
      .filter(line => 
        (line.includes('ERROR') || line.includes('Error') || line.includes('error')) &&
        (line.includes('booking') || line.includes('email') || line.includes('notification') || line.includes('driver'))
      );
    
    if (bookingRequestErrors.length === 0) {
      console.log('No booking request or email errors found in the latest log file');
    } else {
      console.log(`Found ${bookingRequestErrors.length} booking request or email errors in the latest log file:`);
      bookingRequestErrors.forEach((line, index) => console.log(`${index + 1}. ${line}`));
    }
    
    // Look for all errors
    const allErrors = logContent.split('\n')
      .filter(line => line.includes('ERROR') || line.includes('Error') || line.includes('error'));
    
    if (allErrors.length === 0) {
      console.log('No errors found in the latest log file');
    } else {
      console.log(`\nFound ${allErrors.length} total errors in the latest log file. Last 10 errors:`);
      allErrors.slice(-10).forEach((line, index) => console.log(`${index + 1}. ${line}`));
    }
    
    // Look for successful email sending
    const emailSentLines = logContent.split('\n')
      .filter(line => line.includes('Email sent'));
    
    if (emailSentLines.length === 0) {
      console.log('\nNo email sent logs found in the latest log file');
    } else {
      console.log(`\nFound ${emailSentLines.length} email sent logs in the latest log file. Last 5 emails:`);
      emailSentLines.slice(-5).forEach((line, index) => console.log(`${index + 1}. ${line}`));
    }
    
    // Look for notifyEligibleDrivers function calls
    const notifyDriversLines = logContent.split('\n')
      .filter(line => line.includes('notifyEligibleDrivers') || line.includes('Notified') || line.includes('eligible drivers'));
    
    if (notifyDriversLines.length === 0) {
      console.log('\nNo notifyEligibleDrivers logs found in the latest log file');
    } else {
      console.log(`\nFound ${notifyDriversLines.length} notifyEligibleDrivers logs in the latest log file:`);
      notifyDriversLines.forEach((line, index) => console.log(`${index + 1}. ${line}`));
    }
    
  } catch (error) {
    console.error('Error checking logs:', error);
  }
}

// Run the function
checkErrorLogs();
