// Script to check logs for booking request creation and email sending
const fs = require('fs');
const path = require('path');
const { executeQuery } = require('../config/DB/db');

async function checkBookingRequestFlow() {
  console.log('Checking booking request flow...');
  
  try {
    // 1. Check recent booking requests
    console.log('\n--- RECENT BOOKING REQUESTS ---');
    const bookingRequestsQuery = `
      SELECT TOP 5 *
      FROM BookingRequests
      ORDER BY created_at DESC
    `;
    
    const bookingRequests = await executeQuery(bookingRequestsQuery);
    
    if (!bookingRequests.recordset || bookingRequests.recordset.length === 0) {
      console.log('No recent booking requests found');
    } else {
      console.log(`Found ${bookingRequests.recordset.length} recent booking requests`);
      
      for (const request of bookingRequests.recordset) {
        console.log(`\nRequest ID: ${request.request_id}`);
        console.log(`Tourist ID: ${request.tourist_id}`);
        console.log(`Origin: ${request.origin}`);
        console.log(`Destination: ${request.destination}`);
        console.log(`Vehicle Type: ${request.vehicle_type}`);
        console.log(`Status: ${request.status}`);
        console.log(`Created At: ${request.created_at}`);
        
        // 2. Check driver notifications for this request
        console.log('\n--- DRIVER NOTIFICATIONS ---');
        const notificationsQuery = `
          SELECT dn.*, d.user_id, u.email, u.full_name
          FROM DriverNotifications dn
          JOIN Drivers d ON dn.driver_id = d.driver_id
          JOIN Users u ON d.user_id = u.user_id
          WHERE dn.request_id = @requestId
        `;
        
        const notifications = await executeQuery(notificationsQuery, { requestId: request.request_id });
        
        if (!notifications.recordset || notifications.recordset.length === 0) {
          console.log('No driver notifications found for this request');
          
          // Check eligible drivers
          console.log('\n--- ELIGIBLE DRIVERS ---');
          const driversQuery = `
            SELECT d.driver_id, u.email, u.full_name, v.vehicle_type
            FROM Drivers d
            JOIN Users u ON d.user_id = u.user_id
            JOIN Vehicles v ON d.driver_id = v.driver_id
            WHERE v.vehicle_type = @vehicleType AND d.status = 'Approved'
          `;
          
          const drivers = await executeQuery(driversQuery, { vehicleType: request.vehicle_type });
          
          if (!drivers.recordset || drivers.recordset.length === 0) {
            console.log(`No eligible drivers found for vehicle type: ${request.vehicle_type}`);
          } else {
            console.log(`Found ${drivers.recordset.length} eligible drivers for vehicle type: ${request.vehicle_type}`);
            for (const driver of drivers.recordset) {
              console.log(`Driver ID: ${driver.driver_id}, Name: ${driver.full_name}, Email: ${driver.email}`);
            }
          }
        } else {
          console.log(`Found ${notifications.recordset.length} driver notifications`);
          
          for (const notification of notifications.recordset) {
            console.log(`\nNotification ID: ${notification.notification_id}`);
            console.log(`Driver ID: ${notification.driver_id}`);
            console.log(`Driver Name: ${notification.full_name}`);
            console.log(`Driver Email: ${notification.email}`);
            console.log(`Response: ${notification.response}`);
            console.log(`Sent At: ${notification.sent_at}`);
          }
        }
      }
    }
    
    // 3. Check email templates
    console.log('\n--- EMAIL TEMPLATES ---');
    const templatesQuery = `
      SELECT *
      FROM EmailTemplates
      WHERE template_name LIKE '%driver%' OR template_name LIKE '%booking%'
    `;
    
    const templates = await executeQuery(templatesQuery);
    
    if (!templates.recordset || templates.recordset.length === 0) {
      console.log('No relevant email templates found');
    } else {
      console.log(`Found ${templates.recordset.length} relevant email templates`);
      
      for (const template of templates.recordset) {
        console.log(`\nTemplate ID: ${template.template_id}`);
        console.log(`Template Name: ${template.template_name}`);
        console.log(`Subject: ${template.subject}`);
      }
    }
    
    // 4. Check logs for errors
    console.log('\n--- LOG ERRORS ---');
    const logDir = path.join(__dirname, '../../logs');
    
    if (!fs.existsSync(logDir)) {
      console.log('Logs directory not found');
    } else {
      const logFiles = fs.readdirSync(logDir)
        .filter(file => file.endsWith('.log'))
        .sort((a, b) => {
          const statA = fs.statSync(path.join(logDir, a));
          const statB = fs.statSync(path.join(logDir, b));
          return statB.mtime.getTime() - statA.mtime.getTime();
        });
      
      if (logFiles.length === 0) {
        console.log('No log files found');
      } else {
        const latestLogFile = logFiles[0];
        console.log(`Checking latest log file: ${latestLogFile}`);
        
        const logContent = fs.readFileSync(path.join(logDir, latestLogFile), 'utf8');
        const errorLines = logContent.split('\n')
          .filter(line => line.includes('ERROR') || line.includes('Error') || line.includes('error'))
          .slice(-20); // Get last 20 error lines
        
        if (errorLines.length === 0) {
          console.log('No errors found in the latest log file');
        } else {
          console.log(`Found ${errorLines.length} error lines in the latest log file`);
          errorLines.forEach(line => console.log(line));
        }
      }
    }
    
  } catch (error) {
    console.error('Error checking booking request flow:', error);
  }
}

// Run the function
checkBookingRequestFlow()
  .then(() => {
    console.log('\nCheck completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
