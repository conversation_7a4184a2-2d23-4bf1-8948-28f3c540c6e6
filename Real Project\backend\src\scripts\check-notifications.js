// Script to check existing driver notifications
const { executeQuery } = require('../config/DB/db');
const logger = require('../config/logger');

async function checkNotifications() {
  console.log('Checking existing driver notifications...');
  
  try {
    // Get all notifications
    const notificationsQuery = `
      SELECT dn.*, br.origin, br.destination, br.status as request_status, 
             u.full_name as driver_name, u.email as driver_email
      FROM DriverNotifications dn
      JOIN BookingRequests br ON dn.request_id = br.request_id
      JOIN Drivers d ON dn.driver_id = d.driver_id
      JOIN Users u ON d.user_id = u.user_id
      ORDER BY dn.notification_id DESC
    `;
    
    const notificationsResult = await executeQuery(notificationsQuery);
    
    if (!notificationsResult.recordset || notificationsResult.recordset.length === 0) {
      console.log('No notifications found');
      return;
    }
    
    console.log(`Found ${notificationsResult.recordset.length} notifications:`);
    
    for (const notification of notificationsResult.recordset) {
      console.log(`\nNotification ID: ${notification.notification_id}`);
      console.log(`- Request ID: ${notification.request_id}`);
      console.log(`- Driver: ${notification.driver_name} (${notification.driver_email})`);
      console.log(`- Origin: ${notification.origin}`);
      console.log(`- Destination: ${notification.destination}`);
      console.log(`- Response: ${notification.response}`);
      console.log(`- Request Status: ${notification.request_status}`);
      console.log(`- Sent At: ${notification.sent_at}`);
      console.log(`- Response At: ${notification.response_at || 'N/A'}`);
    }
  } catch (error) {
    console.error('Error checking notifications:', error);
  }
}

// Run the function
checkNotifications()
  .then(() => {
    console.log('\nCheck completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
