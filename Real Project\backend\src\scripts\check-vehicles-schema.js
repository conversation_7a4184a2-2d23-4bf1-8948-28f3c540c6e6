// Script to check the Vehicles table schema
const { executeQuery } = require('../config/DB/db');
const logger = require('../config/logger');

async function checkVehiclesSchema() {
  console.log('Checking Vehicles table schema...');
  
  try {
    // Get table schema
    const schemaQuery = `
      SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'Vehicles'
      ORDER BY ORDINAL_POSITION
    `;
    
    const schemaResult = await executeQuery(schemaQuery);
    
    if (!schemaResult.recordset || schemaResult.recordset.length === 0) {
      console.log('No columns found for Vehicles table');
      return;
    }
    
    console.log(`Found ${schemaResult.recordset.length} columns in Vehicles table:`);
    
    for (const column of schemaResult.recordset) {
      console.log(`- ${column.COLUMN_NAME}: ${column.DATA_TYPE}${column.CHARACTER_MAXIMUM_LENGTH ? `(${column.CHARACTER_MAXIMUM_LENGTH})` : ''}`);
    }
    
    // Get sample data
    const dataQuery = `
      SELECT TOP 1 *
      FROM Vehicles
    `;
    
    const dataResult = await executeQuery(dataQuery);
    
    if (!dataResult.recordset || dataResult.recordset.length === 0) {
      console.log('\nNo data found in Vehicles table');
      return;
    }
    
    console.log('\nSample data from Vehicles table:');
    console.log(JSON.stringify(dataResult.recordset[0], null, 2));
  } catch (error) {
    console.error('Error checking Vehicles schema:', error);
  }
}

// Run the function
checkVehiclesSchema()
  .then(() => {
    console.log('\nCheck completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
