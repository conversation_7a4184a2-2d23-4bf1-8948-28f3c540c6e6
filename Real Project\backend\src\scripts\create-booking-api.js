// Script to create a booking request through the API endpoint
const axios = require('axios');
const { executeQuery } = require('../config/DB/db');
const logger = require('../config/logger');

// Get user token for authentication
async function getUserToken() {
  try {
    // Get a user with role 'traveler'
    const userQuery = `
      SELECT TOP 1 u.email, u.user_id
      FROM Users u
      WHERE u.role = 'traveler'
    `;
    
    const userResult = await executeQuery(userQuery);
    
    if (!userResult.recordset || userResult.recordset.length === 0) {
      console.log('No traveler user found');
      return null;
    }
    
    const user = userResult.recordset[0];
    console.log(`Found user: ${user.email} (ID: ${user.user_id})`);
    
    // Login to get token
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: user.email,
      password: 'password123' // Assuming this is the password for test users
    });
    
    if (!loginResponse.data || !loginResponse.data.token) {
      console.log('Failed to login');
      return null;
    }
    
    console.log('Login successful');
    return {
      token: loginResponse.data.token,
      userId: user.user_id
    };
  } catch (error) {
    console.error('Error getting user token:', error.message);
    return null;
  }
}

// Create booking request
async function createBookingRequest(token) {
  try {
    const bookingData = {
      origin: 'Colombo, Sri Lanka',
      destination: 'Galle, Sri Lanka',
      waypoints: ['Bentota, Sri Lanka'],
      startDate: '2025-01-15',
      startTime: '09:00',
      tripType: 'return',
      vehicleType: 'Cars',
      numTravelers: 3,
      totalDistance: 150,
      estimatedDuration: 3, // In hours
      totalCost: 15000,
      driverAccommodation: 'provided',
      specialRequests: 'Please bring water bottles'
    };
    
    console.log('Creating booking request with data:', bookingData);
    
    const response = await axios.post('http://localhost:5000/api/booking-requests', bookingData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.data || !response.data.success) {
      console.log('Failed to create booking request');
      return null;
    }
    
    console.log('Booking request created successfully');
    return response.data.data.requestId;
  } catch (error) {
    console.error('Error creating booking request:', error.response ? error.response.data : error.message);
    return null;
  }
}

// Check driver notifications
async function checkDriverNotifications(requestId) {
  try {
    const notificationsQuery = `
      SELECT dn.*, u.email, u.full_name
      FROM DriverNotifications dn
      JOIN Drivers d ON dn.driver_id = d.driver_id
      JOIN Users u ON d.user_id = u.user_id
      WHERE dn.request_id = @requestId
    `;
    
    const notificationsResult = await executeQuery(notificationsQuery, { requestId });
    
    if (!notificationsResult.recordset || notificationsResult.recordset.length === 0) {
      console.log('No driver notifications found for this booking request');
      return [];
    }
    
    console.log(`Found ${notificationsResult.recordset.length} driver notifications:`);
    for (const notification of notificationsResult.recordset) {
      console.log(`- Notification ID: ${notification.notification_id}, Driver: ${notification.full_name}, Email: ${notification.email}, Response: ${notification.response}`);
    }
    
    return notificationsResult.recordset;
  } catch (error) {
    console.error('Error checking driver notifications:', error.message);
    return [];
  }
}

// Main function
async function main() {
  try {
    // Get user token
    const userInfo = await getUserToken();
    
    if (!userInfo) {
      console.log('Failed to get user token');
      return;
    }
    
    // Create booking request
    const requestId = await createBookingRequest(userInfo.token);
    
    if (!requestId) {
      console.log('Failed to create booking request');
      return;
    }
    
    console.log(`Booking request created with ID: ${requestId}`);
    
    // Wait for notifications to be created
    console.log('Waiting for notifications to be created...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check driver notifications
    const notifications = await checkDriverNotifications(requestId);
    
    if (notifications.length === 0) {
      console.log('No driver notifications were created. This suggests an issue with the notifyEligibleDrivers function.');
    }
  } catch (error) {
    console.error('Error in main function:', error.message);
  }
}

// Run the main function
main()
  .then(() => {
    console.log('\nTest completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
