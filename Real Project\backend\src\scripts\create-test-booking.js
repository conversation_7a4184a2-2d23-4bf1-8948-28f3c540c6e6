// Script to create a test booking request
const { executeQuery } = require('../config/DB/db');
const logger = require('../config/logger');

async function createTestBooking() {
  console.log('Creating test booking request...');

  try {
    // Begin transaction
    await executeQuery('BEGIN TRANSACTION');

    // Create booking request
    const bookingQuery = `
      INSERT INTO BookingRequests (
        tourist_id, origin, destination, waypoints, start_date, start_time,
        trip_type, vehicle_type, num_travelers, total_distance,
        estimated_duration, total_cost, driver_accommodation, special_requests
      )
      OUTPUT INSERTED.request_id
      VALUES (
        @touristId, @origin, @destination, @waypoints, @startDate, @startTime,
        @tripType, @vehicleType, @numTravelers, @totalDistance,
        @estimatedDuration, @totalCost, @driverAccommodation, @specialRequests
      )
    `;

    const bookingParams = {
      touristId: 72, // Use an existing tourist ID
      origin: 'Colombo, Sri Lanka',
      destination: 'Galle, Sri Lanka',
      waypoints: JSON.stringify(['Bentota, Sri Lanka']),
      startDate: new Date('2025-01-15'),
      startTime: '09:00',
      tripType: 'return',
      vehicleType: 'Cars',
      numTravelers: 3,
      totalDistance: 150.0, // Use float
      estimatedDuration: 180,
      totalCost: 15000.00, // Use decimal
      driverAccommodation: 'provided',
      specialRequests: 'Please bring water bottles'
    };

    const bookingResult = await executeQuery(bookingQuery, bookingParams);

    if (!bookingResult.recordset || !bookingResult.recordset[0]) {
      console.log('Failed to create booking request');
      await executeQuery('ROLLBACK TRANSACTION');
      return;
    }

    const requestId = bookingResult.recordset[0].request_id;
    console.log(`Booking request created with ID: ${requestId}`);

    // Commit transaction
    await executeQuery('COMMIT TRANSACTION');

    // Now call the manual notify script
    console.log('\nNotifying drivers about the new booking request...');

    // Find eligible drivers
    const driversQuery = `
      SELECT d.driver_id, u.email, u.full_name
      FROM Drivers d
      JOIN Users u ON d.user_id = u.user_id
      JOIN Vehicles v ON d.driver_id = v.driver_id
      WHERE v.vehicle_type = @vehicleType AND d.status = 'Approved'
    `;

    const driversResult = await executeQuery(driversQuery, { vehicleType: bookingParams.vehicleType });

    if (!driversResult.recordset || driversResult.recordset.length === 0) {
      console.log(`No eligible drivers found for vehicle type: ${bookingParams.vehicleType}`);
      return;
    }

    console.log(`\nFound ${driversResult.recordset.length} eligible drivers:`);
    for (const driver of driversResult.recordset) {
      console.log(`- Driver ID: ${driver.driver_id}, Name: ${driver.full_name}, Email: ${driver.email}`);
    }

    // Get the booking request details
    const requestQuery = "SELECT * FROM BookingRequests WHERE request_id = @requestId";
    const requestResult = await executeQuery(requestQuery, { requestId });
    const bookingRequest = requestResult.recordset[0];

    // Get tourist details
    const touristQuery = `
      SELECT u.full_name
      FROM Users u
      JOIN BookingRequests br ON u.user_id = br.tourist_id
      WHERE br.request_id = @requestId
    `;

    const touristResult = await executeQuery(touristQuery, { requestId });
    const touristName = touristResult.recordset[0]?.full_name || 'Tourist';

    console.log(`\nTourist name: ${touristName}`);

    // Create notifications and send emails
    console.log('\nCreating notifications and sending emails:');

    const emailService = require('../services/emailService');

    for (const driver of driversResult.recordset) {
      console.log(`\nProcessing driver: ${driver.full_name} (${driver.email})`);

      // Create notification record
      console.log('Creating notification record...');
      const notificationQuery = `
        INSERT INTO DriverNotifications
        (request_id, driver_id, response)
        OUTPUT INSERTED.notification_id
        VALUES (@requestId, @driverId, 'pending')
      `;

      const notificationResult = await executeQuery(notificationQuery, {
        requestId,
        driverId: driver.driver_id
      });

      if (!notificationResult.recordset || !notificationResult.recordset[0]) {
        console.log(`Failed to create notification for driver ${driver.driver_id}`);
        continue;
      }

      const notificationId = notificationResult.recordset[0].notification_id;
      console.log(`Notification created with ID: ${notificationId}`);

      // Send email
      console.log('Sending email...');
      try {
        const result = await emailService.sendDriverBookingRequestEmail(
          driver.email,
          driver.full_name,
          bookingRequest,
          requestId,
          driver.driver_id,
          notificationId
        );

        if (result && result.success) {
          console.log('Email sent successfully');
        } else {
          console.log(`Failed to send email: ${result ? result.error : 'Unknown error'}`);
        }
      } catch (error) {
        console.log(`Error sending email: ${error.message}`);
      }
    }

    console.log(`\nTest booking request ${requestId} created and notifications sent`);

  } catch (error) {
    console.error('Error creating test booking:', error);
    await executeQuery('ROLLBACK TRANSACTION');
  }
}

// Run the function
createTestBooking()
  .then(() => {
    console.log('\nTest completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
