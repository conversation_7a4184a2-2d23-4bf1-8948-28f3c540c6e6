// Script to debug the email flow for a specific booking request
const { executeQuery } = require('../config/DB/db');
const emailService = require('../services/emailService');
const logger = require('../config/logger');

// Get booking request ID from command line
const requestId = process.argv[2] || 7; // Default to the most recent request ID

async function debugEmailFlow(requestId) {
  console.log(`Debugging email flow for booking request ID: ${requestId}`);
  
  try {
    // 1. Get booking request details
    console.log('\n--- BOOKING REQUEST DETAILS ---');
    const requestQuery = "SELECT * FROM BookingRequests WHERE request_id = @requestId";
    const requestResult = await executeQuery(requestQuery, { requestId });
    
    if (!requestResult.recordset || requestResult.recordset.length === 0) {
      console.log(`Booking request with ID ${requestId} not found`);
      return;
    }
    
    const bookingRequest = requestResult.recordset[0];
    console.log('Booking request found:');
    console.log(`- Request ID: ${bookingRequest.request_id}`);
    console.log(`- Tourist ID: ${bookingRequest.tourist_id}`);
    console.log(`- Origin: ${bookingRequest.origin}`);
    console.log(`- Destination: ${bookingRequest.destination}`);
    console.log(`- Vehicle Type: ${bookingRequest.vehicle_type}`);
    console.log(`- Status: ${bookingRequest.status}`);
    
    // 2. Find eligible drivers
    console.log('\n--- ELIGIBLE DRIVERS ---');
    const driversQuery = `
      SELECT d.driver_id, u.email, u.full_name
      FROM Drivers d
      JOIN Users u ON d.user_id = u.user_id
      JOIN Vehicles v ON d.driver_id = v.driver_id
      WHERE v.vehicle_type = @vehicleType AND d.status = 'Approved'
    `;
    
    const driversResult = await executeQuery(driversQuery, { vehicleType: bookingRequest.vehicle_type });
    
    if (!driversResult.recordset || driversResult.recordset.length === 0) {
      console.log(`No eligible drivers found for vehicle type: ${bookingRequest.vehicle_type}`);
      return;
    }
    
    console.log(`Found ${driversResult.recordset.length} eligible drivers:`);
    for (const driver of driversResult.recordset) {
      console.log(`- Driver ID: ${driver.driver_id}, Name: ${driver.full_name}, Email: ${driver.email}`);
    }
    
    // 3. Check existing notifications
    console.log('\n--- EXISTING NOTIFICATIONS ---');
    const notificationsQuery = `
      SELECT dn.*, u.email, u.full_name
      FROM DriverNotifications dn
      JOIN Drivers d ON dn.driver_id = d.driver_id
      JOIN Users u ON d.user_id = u.user_id
      WHERE dn.request_id = @requestId
    `;
    
    const notificationsResult = await executeQuery(notificationsQuery, { requestId });
    
    if (!notificationsResult.recordset || notificationsResult.recordset.length === 0) {
      console.log('No notifications found for this booking request');
      
      // 4. Create notifications and send emails
      console.log('\n--- CREATING NOTIFICATIONS AND SENDING EMAILS ---');
      
      for (const driver of driversResult.recordset) {
        console.log(`\nProcessing driver: ${driver.full_name} (${driver.email})`);
        
        // Create notification
        console.log('Creating notification...');
        const notificationQuery = `
          INSERT INTO DriverNotifications
          (request_id, driver_id, response)
          OUTPUT INSERTED.notification_id
          VALUES (@requestId, @driverId, 'pending')
        `;
        
        const notificationResult = await executeQuery(notificationQuery, {
          requestId,
          driverId: driver.driver_id
        });
        
        if (!notificationResult.recordset || !notificationResult.recordset[0]) {
          console.log('Failed to create notification');
          continue;
        }
        
        const notificationId = notificationResult.recordset[0].notification_id;
        console.log(`Notification created with ID: ${notificationId}`);
        
        // Get tourist details
        const touristQuery = `
          SELECT u.full_name
          FROM Users u
          JOIN BookingRequests br ON u.user_id = br.tourist_id
          WHERE br.request_id = @requestId
        `;
        
        const touristResult = await executeQuery(touristQuery, { requestId });
        const touristName = touristResult.recordset[0]?.full_name || 'Tourist';
        
        // Send email
        console.log('Sending email...');
        try {
          const result = await emailService.sendDriverBookingRequestEmail(
            driver.email,
            driver.full_name,
            bookingRequest,
            requestId,
            driver.driver_id,
            notificationId
          );
          
          if (result && result.success) {
            console.log('Email sent successfully');
          } else {
            console.log(`Failed to send email: ${result ? result.error : 'Unknown error'}`);
          }
        } catch (error) {
          console.log(`Error sending email: ${error.message}`);
        }
      }
    } else {
      console.log(`Found ${notificationsResult.recordset.length} notifications:`);
      for (const notification of notificationsResult.recordset) {
        console.log(`- Notification ID: ${notification.notification_id}, Driver: ${notification.full_name}, Response: ${notification.response}`);
      }
    }
    
  } catch (error) {
    console.error('Error debugging email flow:', error);
  }
}

// Run the function
debugEmailFlow(requestId)
  .then(() => {
    console.log('\nDebug completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
