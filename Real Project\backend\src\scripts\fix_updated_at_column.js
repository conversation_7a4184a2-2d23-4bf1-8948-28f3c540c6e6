// Script to ensure the updated_at column exists in BookingRequests table
const { executeQuery } = require('../config/DB/db');
const logger = require('../config/logger');

async function fixUpdatedAtColumn() {
  console.log('Checking and fixing updated_at column in BookingRequests table...');
  
  try {
    // Check if updated_at column exists
    const checkColumnQuery = `
      SELECT COUNT(*) as column_exists
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'BookingRequests'
      AND COLUMN_NAME = 'updated_at'
    `;
    
    const columnCheckResult = await executeQuery(checkColumnQuery);
    const columnExists = columnCheckResult.recordset[0].column_exists > 0;
    
    if (!columnExists) {
      console.log('Adding updated_at column to BookingRequests table...');
      
      // Add the column
      const addColumnQuery = `
        ALTER TABLE BookingRequests
        ADD updated_at DATETIME NULL
      `;
      
      await executeQuery(addColumnQuery);
      console.log('Column added successfully');
      
      // Update existing records to have current date
      const updateExistingQuery = `
        UPDATE BookingRequests
        SET updated_at = GETDATE()
        WHERE updated_at IS NULL
      `;
      
      await executeQuery(updateExistingQuery);
      console.log('Existing records updated with current date');
      
      // Add trigger to automatically update the column
      const dropTriggerIfExistsQuery = `
        IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'trg_BookingRequests_UpdatedAt')
        BEGIN
          DROP TRIGGER trg_BookingRequests_UpdatedAt
        END
      `;
      
      await executeQuery(dropTriggerIfExistsQuery);
      
      const createTriggerQuery = `
        CREATE TRIGGER trg_BookingRequests_UpdatedAt
        ON BookingRequests
        AFTER UPDATE
        AS
        BEGIN
          UPDATE BookingRequests
          SET updated_at = GETDATE()
          FROM BookingRequests br
          INNER JOIN inserted i ON br.request_id = i.request_id
        END
      `;
      
      await executeQuery(createTriggerQuery);
      console.log('Trigger created to automatically update updated_at column');
    } else {
      console.log('updated_at column already exists');
      
      // Check if trigger exists
      const checkTriggerQuery = `
        SELECT COUNT(*) as trigger_exists
        FROM sys.triggers
        WHERE name = 'trg_BookingRequests_UpdatedAt'
      `;
      
      const triggerCheckResult = await executeQuery(checkTriggerQuery);
      const triggerExists = triggerCheckResult.recordset[0].trigger_exists > 0;
      
      if (!triggerExists) {
        console.log('Creating trigger for updated_at column...');
        
        // Add trigger to automatically update the column
        const createTriggerQuery = `
          CREATE TRIGGER trg_BookingRequests_UpdatedAt
          ON BookingRequests
          AFTER UPDATE
          AS
          BEGIN
            UPDATE BookingRequests
            SET updated_at = GETDATE()
            FROM BookingRequests br
            INNER JOIN inserted i ON br.request_id = i.request_id
          END
        `;
        
        await executeQuery(createTriggerQuery);
        console.log('Trigger created to automatically update updated_at column');
      } else {
        console.log('Trigger for updated_at column already exists');
      }
    }
    
    console.log('Fix completed successfully');
  } catch (error) {
    console.error('Error fixing updated_at column:', error);
  }
}

// Execute the function if this script is run directly
if (require.main === module) {
  fixUpdatedAtColumn()
    .then(() => {
      console.log('Script completed');
      process.exit(0);
    })
    .catch(err => {
      console.error('Script failed:', err);
      process.exit(1);
    });
}

module.exports = { fixUpdatedAtColumn };
