// Script to list all vehicles in the database
const { executeQuery } = require('../config/DB/db');
const logger = require('../config/logger');

/**
 * Lists all vehicles in the database
 * @param {number} driverId - Optional driver ID to filter vehicles
 */
async function listVehicles(driverId) {
  try {
    console.log('Fetching vehicles from database...');
    
    let query = `
      SELECT 
        v.vehicle_id,
        v.driver_id,
        d.full_name AS driver_name,
        v.vehicle_type,
        v.make_model,
        v.registration_number,
        v.vehicle_photo,
        v.insurance_expiry_date,
        v.seat_count,
        v.air_conditioned,
        v.verified
      FROM Vehicles v
      JOIN Drivers d ON v.driver_id = d.driver_id
    `;
    
    const params = {};
    
    if (driverId) {
      query += ` WHERE v.driver_id = @driverId`;
      params.driverId = driverId;
      console.log(`Filtering by driver ID: ${driverId}`);
    }
    
    query += ` ORDER BY v.vehicle_id`;
    
    const result = await executeQuery(query, params);
    
    if (!result.recordset || result.recordset.length === 0) {
      console.log('No vehicles found');
      return;
    }
    
    console.log(`Found ${result.recordset.length} vehicles:`);
    console.log('-----------------------------------');
    
    result.recordset.forEach(vehicle => {
      console.log(`Vehicle ID: ${vehicle.vehicle_id}`);
      console.log(`Driver: ${vehicle.driver_name} (ID: ${vehicle.driver_id})`);
      console.log(`Type: ${vehicle.vehicle_type}`);
      console.log(`Make/Model: ${vehicle.make_model}`);
      console.log(`Registration: ${vehicle.registration_number}`);
      console.log(`Image: ${vehicle.vehicle_photo || 'No image'}`);
      console.log(`Seats: ${vehicle.seat_count}`);
      console.log(`AC: ${vehicle.air_conditioned ? 'Yes' : 'No'}`);
      console.log(`Verified: ${vehicle.verified ? 'Yes' : 'No'}`);
      console.log('-----------------------------------');
    });
    
  } catch (error) {
    console.error(`Error listing vehicles: ${error.message}`);
    process.exit(1);
  }
}

// Get command line arguments
const driverId = process.argv[2]; // Optional driver ID

// Run the function
listVehicles(driverId)
  .then(() => {
    console.log('Listing completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error(`Unhandled error: ${error.message}`);
    process.exit(1);
  });
