// <PERSON>ript to manually notify drivers about a booking request
const { executeQuery } = require('../config/DB/db');
const emailService = require('../services/emailService');
const logger = require('../config/logger');

// Get booking request ID from command line
const requestId = process.argv[2] || 7; // Default to the most recent request ID

async function notifyDrivers(requestId) {
  console.log(`Notifying drivers about booking request ID: ${requestId}`);
  
  try {
    // Get booking request details
    const requestQuery = "SELECT * FROM BookingRequests WHERE request_id = @requestId";
    const requestResult = await executeQuery(requestQuery, { requestId });
    
    if (!requestResult.recordset || requestResult.recordset.length === 0) {
      console.log(`Booking request with ID ${requestId} not found`);
      return;
    }
    
    const bookingRequest = requestResult.recordset[0];
    console.log('Booking request found:');
    console.log(`- Request ID: ${bookingRequest.request_id}`);
    console.log(`- Tourist ID: ${bookingRequest.tourist_id}`);
    console.log(`- Origin: ${bookingRequest.origin}`);
    console.log(`- Destination: ${bookingRequest.destination}`);
    console.log(`- Vehicle Type: ${bookingRequest.vehicle_type}`);
    console.log(`- Status: ${bookingRequest.status}`);
    
    // Find eligible drivers
    const driversQuery = `
      SELECT d.driver_id, u.email, u.full_name
      FROM Drivers d
      JOIN Users u ON d.user_id = u.user_id
      JOIN Vehicles v ON d.driver_id = v.driver_id
      WHERE v.vehicle_type = @vehicleType AND d.status = 'Approved'
    `;
    
    const driversResult = await executeQuery(driversQuery, { vehicleType: bookingRequest.vehicle_type });
    
    if (!driversResult.recordset || driversResult.recordset.length === 0) {
      console.log(`No eligible drivers found for vehicle type: ${bookingRequest.vehicle_type}`);
      return;
    }
    
    console.log(`\nFound ${driversResult.recordset.length} eligible drivers:`);
    for (const driver of driversResult.recordset) {
      console.log(`- Driver ID: ${driver.driver_id}, Name: ${driver.full_name}, Email: ${driver.email}`);
    }
    
    // Get tourist details
    const touristQuery = `
      SELECT u.full_name
      FROM Users u
      JOIN BookingRequests br ON u.user_id = br.tourist_id
      WHERE br.request_id = @requestId
    `;
    
    const touristResult = await executeQuery(touristQuery, { requestId });
    const touristName = touristResult.recordset[0]?.full_name || 'Tourist';
    
    console.log(`\nTourist name: ${touristName}`);
    
    // Create notifications and send emails
    console.log('\nCreating notifications and sending emails:');
    
    for (const driver of driversResult.recordset) {
      console.log(`\nProcessing driver: ${driver.full_name} (${driver.email})`);
      
      // Check if notification already exists
      const existingNotificationQuery = `
        SELECT notification_id FROM DriverNotifications
        WHERE request_id = @requestId AND driver_id = @driverId
      `;
      
      const existingNotificationResult = await executeQuery(existingNotificationQuery, {
        requestId,
        driverId: driver.driver_id
      });
      
      if (existingNotificationResult.recordset && existingNotificationResult.recordset.length > 0) {
        console.log(`Notification already exists for driver ${driver.driver_id}`);
        continue;
      }
      
      // Create notification record
      console.log('Creating notification record...');
      const notificationQuery = `
        INSERT INTO DriverNotifications
        (request_id, driver_id, response)
        OUTPUT INSERTED.notification_id
        VALUES (@requestId, @driverId, 'pending')
      `;
      
      const notificationResult = await executeQuery(notificationQuery, {
        requestId,
        driverId: driver.driver_id
      });
      
      if (!notificationResult.recordset || !notificationResult.recordset[0]) {
        console.log(`Failed to create notification for driver ${driver.driver_id}`);
        continue;
      }
      
      const notificationId = notificationResult.recordset[0].notification_id;
      console.log(`Notification created with ID: ${notificationId}`);
      
      // Send email
      console.log('Sending email...');
      try {
        const result = await emailService.sendDriverBookingRequestEmail(
          driver.email,
          driver.full_name,
          bookingRequest,
          requestId,
          driver.driver_id,
          notificationId
        );
        
        if (result && result.success) {
          console.log('Email sent successfully');
        } else {
          console.log(`Failed to send email: ${result ? result.error : 'Unknown error'}`);
        }
      } catch (error) {
        console.log(`Error sending email: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error('Error notifying drivers:', error);
  }
}

// Run the function
notifyDrivers(requestId)
  .then(() => {
    console.log('\nNotification completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
