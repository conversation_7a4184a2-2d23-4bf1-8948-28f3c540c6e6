// src/scripts/runMigration.js
const sql = require('mssql');
const fs = require('fs');
const path = require('path');
const config = require('../config/DB/config');

async function runMigration(migrationFilePath) {
  try {
    console.log(`Running migration: ${migrationFilePath}`);
    
    // Check if file exists
    if (!fs.existsSync(migrationFilePath)) {
      console.error(`Migration file not found: ${migrationFilePath}`);
      return;
    }
    
    // Read the SQL file
    const sqlContent = fs.readFileSync(migrationFilePath, 'utf8');
    
    // Connect to database
    console.log('Connecting to database...');
    const pool = new sql.ConnectionPool(config);
    await pool.connect();
    console.log('Connected to database successfully');
    
    // Execute the SQL script
    console.log('Executing migration...');
    await pool.request().query(sqlContent);
    console.log('Migration executed successfully');
    
    // Close the connection
    await pool.close();
    console.log('Connection closed');
  } catch (error) {
    console.error('Error running migration:', error);
  }
}

// Get the migration file path from command line arguments
const migrationFilePath = process.argv[2];

if (!migrationFilePath) {
  console.error('Please provide a migration file path');
  process.exit(1);
}

// Run the migration
runMigration(migrationFilePath);
