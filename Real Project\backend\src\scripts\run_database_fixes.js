// Script to run all database fixes
const { fixUpdatedAtColumn } = require('./fix_updated_at_column');
const logger = require('../config/logger');

async function runAllFixes() {
  try {
    logger.info('Starting database fixes...');
    
    // Fix updated_at column in BookingRequests table
    logger.info('Fixing updated_at column in BookingRequests table...');
    await fixUpdatedAtColumn();
    logger.info('Fixed updated_at column in BookingRequests table');
    
    logger.info('All database fixes completed successfully');
  } catch (error) {
    logger.error(`Error running database fixes: ${error.message}`);
    logger.error(error.stack);
  }
}

// Run the fixes if this script is executed directly
if (require.main === module) {
  runAllFixes()
    .then(() => {
      logger.info('Database fixes script completed');
      process.exit(0);
    })
    .catch(err => {
      logger.error(`Database fixes script failed: ${err.message}`);
      logger.error(err.stack);
      process.exit(1);
    });
}

module.exports = { runAllFixes };
