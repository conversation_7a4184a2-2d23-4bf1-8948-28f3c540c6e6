// <PERSON><PERSON>t to manually send an email for an existing driver notification
const { executeQuery } = require('../config/DB/db');
const emailService = require('../services/emailService');
const logger = require('../config/logger');

// Add console logs for debugging
console.log('Script started');
console.log('Driver ID:', process.argv[2]);

// Initialize logger
logger.info('Starting manual driver notification email script');

async function sendDriverNotificationEmail(driverId) {
  try {
    // Get the most recent notification for this driver
    console.log('Querying for notifications for driver ID:', driverId);
    const notificationQuery = `
      SELECT dn.*, br.*
      FROM DriverNotifications dn
      JOIN BookingRequests br ON dn.request_id = br.request_id
      WHERE dn.driver_id = @driverId AND dn.response = 'pending'
      ORDER BY dn.sent_at DESC
    `;
    console.log('Query:', notificationQuery);

    const notificationResult = await executeQuery(notificationQuery, { driverId });
    console.log('Query result:', notificationResult);

    if (!notificationResult.recordset || notificationResult.recordset.length === 0) {
      console.log('No pending notifications found');
      logger.error(`No pending notifications found for driver ${driverId}`);
      return { success: false, error: 'No pending notifications found' };
    }

    console.log('Found notifications:', notificationResult.recordset.length);

    const notification = notificationResult.recordset[0];
    logger.info(`Found notification ID: ${notification.notification_id} for request ID: ${notification.request_id}`);

    // Get driver details
    console.log('Getting driver details for driver ID:', driverId);
    const driverQuery = `
      SELECT d.driver_id, u.email, u.full_name
      FROM Drivers d
      JOIN Users u ON d.user_id = u.user_id
      WHERE d.driver_id = @driverId
    `;
    console.log('Driver query:', driverQuery);

    const driverResult = await executeQuery(driverQuery, { driverId });

    if (!driverResult.recordset || driverResult.recordset.length === 0) {
      logger.error(`Driver not found: ${driverId}`);
      return { success: false, error: 'Driver not found' };
    }

    const driver = driverResult.recordset[0];
    logger.info(`Found driver: ${driver.full_name} (${driver.email})`);

    // Send email
    const result = await emailService.sendDriverBookingRequestEmail(
      driver.email,
      driver.full_name,
      notification,
      notification.request_id,
      driverId,
      notification.notification_id
    );

    if (result.success) {
      logger.info(`Successfully sent email to driver ${driverId} (${driver.email})`);
      return { success: true };
    } else {
      logger.error(`Failed to send email: ${result.error}`);
      return { success: false, error: result.error };
    }
  } catch (error) {
    logger.error(`Error in sendDriverNotificationEmail: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Get driver ID from command line argument
const driverId = process.argv[2];

if (!driverId) {
  logger.error('Driver ID is required as a command line argument');
  console.error('Usage: node send-driver-notification.js <driverId>');
  process.exit(1);
}

// Run the function
sendDriverNotificationEmail(driverId)
  .then(result => {
    if (result.success) {
      logger.info('Email sent successfully');
      console.log('Email sent successfully');
    } else {
      logger.error(`Failed to send email: ${result.error}`);
      console.error(`Failed to send email: ${result.error}`);
    }
    process.exit(0);
  })
  .catch(error => {
    logger.error(`Unhandled error: ${error.message}`);
    console.error(`Unhandled error: ${error.message}`);
    process.exit(1);
  });
