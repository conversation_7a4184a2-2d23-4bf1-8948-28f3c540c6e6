// <PERSON>ript to send a test notification to a driver
const { executeQuery } = require('../config/DB/db');
const emailService = require('../services/emailService');
const logger = require('../config/logger');

// Email to send notification to
const EMAIL_TO_CHECK = '<EMAIL>';

async function sendTestNotification() {
  try {
    console.log(`Sending test notification to driver with email: ${EMAIL_TO_CHECK}`);
    
    // Step 1: Check if the user exists and get driver ID
    const userQuery = `
      SELECT 
        U.user_id,
        U.email,
        U.role,
        U.full_name,
        D.driver_id,
        D.status
      FROM Users U
      JOIN Drivers D ON U.user_id = D.user_id
      WHERE U.email = @email
    `;
    
    const userResult = await executeQuery(userQuery, { email: EMAIL_TO_CHECK });
    
    if (!userResult.recordset || userResult.recordset.length === 0) {
      console.log(`No driver found with email: ${EMAIL_TO_CHECK}`);
      return;
    }
    
    const user = userResult.recordset[0];
    console.log(`Found driver: ${user.full_name} (ID: ${user.driver_id}, Status: ${user.status})`);
    
    if (user.status !== 'Approved') {
      console.log(`WARNING: Driver account is not approved (Status: ${user.status}). Continuing anyway for testing.`);
    }
    
    // Step 2: Create a test booking request
    console.log('Creating test booking request...');
    
    // Get a tourist ID for the test request
    const touristQuery = `
      SELECT TOP 1 T.tourist_id, U.user_id, U.full_name
      FROM Tourists T
      JOIN Users U ON T.user_id = U.user_id
      WHERE U.role = 'traveler'
    `;
    
    const touristResult = await executeQuery(touristQuery);
    
    if (!touristResult.recordset || touristResult.recordset.length === 0) {
      console.log('No tourists found in the database. Cannot create test booking request.');
      return;
    }
    
    const tourist = touristResult.recordset[0];
    console.log(`Using tourist: ${tourist.full_name} (ID: ${tourist.tourist_id})`);
    
    // Create the booking request
    const createRequestQuery = `
      INSERT INTO BookingRequests (
        tourist_id,
        origin,
        destination,
        waypoints,
        start_date,
        start_time,
        trip_type,
        vehicle_type,
        num_travelers,
        total_distance,
        estimated_duration,
        total_cost,
        driver_accommodation,
        special_requests,
        status
      )
      OUTPUT INSERTED.request_id
      VALUES (
        @touristId,
        'Colombo',
        'Sigiriya Rock Fortress',
        '["Kandy", "Dambulla"]',
        DATEADD(day, 7, GETDATE()), -- 7 days from now
        '09:00',
        'return',
        'car',
        3,
        250,
        180,
        7500,
        'paid',
        'Test booking request for driver notification debugging',
        'pending'
      )
    `;
    
    const createRequestResult = await executeQuery(createRequestQuery, { 
      touristId: tourist.tourist_id
    });
    
    if (!createRequestResult.recordset || !createRequestResult.recordset[0]) {
      console.log('Failed to create test booking request.');
      return;
    }
    
    const requestId = createRequestResult.recordset[0].request_id;
    console.log(`Created test booking request with ID: ${requestId}`);
    
    // Step 3: Create a notification for the driver
    console.log('Creating driver notification...');
    
    const createNotificationQuery = `
      INSERT INTO DriverNotifications (
        request_id,
        driver_id,
        response
      )
      OUTPUT INSERTED.notification_id
      VALUES (
        @requestId,
        @driverId,
        'pending'
      )
    `;
    
    const createNotificationResult = await executeQuery(createNotificationQuery, {
      requestId,
      driverId: user.driver_id
    });
    
    if (!createNotificationResult.recordset || !createNotificationResult.recordset[0]) {
      console.log('Failed to create driver notification.');
      return;
    }
    
    const notificationId = createNotificationResult.recordset[0].notification_id;
    console.log(`Created driver notification with ID: ${notificationId}`);
    
    // Step 4: Get the booking request details for the email
    const requestQuery = `
      SELECT *
      FROM BookingRequests
      WHERE request_id = @requestId
    `;
    
    const requestResult = await executeQuery(requestQuery, { requestId });
    
    if (!requestResult.recordset || !requestResult.recordset.length === 0) {
      console.log('Failed to get booking request details.');
      return;
    }
    
    const bookingRequest = requestResult.recordset[0];
    
    // Step 5: Send email notification
    console.log('Sending email notification...');
    
    try {
      const emailResult = await emailService.sendDriverBookingRequestEmail(
        user.email,
        user.full_name,
        bookingRequest,
        requestId,
        user.driver_id,
        notificationId
      );
      
      if (emailResult && emailResult.success) {
        console.log('Email sent successfully!');
      } else {
        console.log(`Failed to send email: ${emailResult ? emailResult.error : 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`Error sending email: ${error.message}`);
    }
    
    console.log('\nTest notification process completed successfully.');
    console.log('Summary:');
    console.log(`- Driver: ${user.full_name} (ID: ${user.driver_id}, Email: ${user.email})`);
    console.log(`- Booking Request ID: ${requestId}`);
    console.log(`- Notification ID: ${notificationId}`);
    console.log('\nThe driver should now:');
    console.log('1. Receive an email notification');
    console.log('2. See the booking request on their dashboard');
    console.log('3. Be able to accept or reject the request');
    
  } catch (error) {
    console.error('Error sending test notification:', error);
  }
}

// Run the function
sendTestNotification()
  .then(() => {
    console.log('Test notification process completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Error running test notification process:', error);
    process.exit(1);
  });
