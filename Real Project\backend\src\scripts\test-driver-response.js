// Script to test driver response to booking requests
const { executeQuery } = require('../config/DB/db');
const logger = require('../config/logger');
const emailService = require('../services/emailService');

// Get notification ID and response from command line
const notificationId = process.argv[2];
const response = process.argv[3] || 'accepted'; // Default to 'accepted'

if (!notificationId) {
  console.error('Usage: node test-driver-response.js <notificationId> [accepted|rejected]');
  process.exit(1);
}

if (!['accepted', 'rejected'].includes(response)) {
  console.error('Response must be either "accepted" or "rejected"');
  process.exit(1);
}

/**
 * Process driver response to booking request
 */
async function processDriverResponse(notificationId, response) {
  console.log(`Processing driver response: ${response} for notification ID: ${notificationId}`);

  try {
    // Get notification details
    console.log('\n1. Getting notification details...');
    const notificationQuery = `
      SELECT dn.*, br.request_id, br.status as request_status, br.tourist_id
      FROM DriverNotifications dn
      JOIN BookingRequests br ON dn.request_id = br.request_id
      WHERE dn.notification_id = @notificationId
    `;

    const notificationResult = await executeQuery(notificationQuery, { notificationId });

    if (!notificationResult.recordset || notificationResult.recordset.length === 0) {
      console.log(`Notification not found: ${notificationId}`);
      return { success: false, error: 'Notification not found' };
    }

    const notification = notificationResult.recordset[0];
    console.log('Notification found:', JSON.stringify(notification, null, 2));

    // Check if notification can be responded to
    if (notification.response !== 'pending') {
      console.log(`Cannot respond to notification with status: ${notification.response}`);
      return { success: false, error: `Cannot respond to notification with status: ${notification.response}` };
    }

    // Check if booking request is still pending
    if (notification.request_status !== 'pending') {
      console.log(`Cannot respond to booking request with status: ${notification.request_status}`);
      return { success: false, error: `Cannot respond to booking request with status: ${notification.request_status}` };
    }

    // Begin transaction
    await executeQuery('BEGIN TRANSACTION');

    try {
      // Update notification response
      console.log('\n2. Updating notification response...');
      const updateNotificationQuery = `
        UPDATE DriverNotifications
        SET response = @response, response_at = GETDATE()
        WHERE notification_id = @notificationId
      `;

      await executeQuery(updateNotificationQuery, { notificationId, response });
      console.log(`Notification ${notificationId} updated with response: ${response}`);

      // If driver accepted, update booking request status and assign driver
      if (response === 'accepted') {
        console.log('\n3. Driver accepted - Updating booking request status...');
        const updateRequestQuery = `
          UPDATE BookingRequests
          SET status = 'driver_confirmed', assigned_driver_id = @driverId
          WHERE request_id = @requestId
        `;

        await executeQuery(updateRequestQuery, {
          requestId: notification.request_id,
          driverId: notification.driver_id
        });
        console.log(`Booking request ${notification.request_id} updated with status: driver_confirmed`);

        // Update all other pending notifications for this request to 'expired'
        console.log('\n4. Expiring other pending notifications...');
        const updateOtherNotificationsQuery = `
          UPDATE DriverNotifications
          SET response = 'expired', response_at = GETDATE()
          WHERE request_id = @requestId AND notification_id != @notificationId AND response = 'pending'
        `;

        await executeQuery(updateOtherNotificationsQuery, {
          requestId: notification.request_id,
          notificationId
        });
        console.log('Other pending notifications expired');
      }

      // Commit transaction
      await executeQuery('COMMIT TRANSACTION');
      console.log('\nTransaction committed successfully');

      // Get updated notification details
      console.log('\n5. Getting updated notification details...');
      const updatedNotificationQuery = `
        SELECT dn.*, br.*, u.full_name as tourist_name, u.email as tourist_email
        FROM DriverNotifications dn
        JOIN BookingRequests br ON dn.request_id = br.request_id
        JOIN Users u ON br.tourist_id = u.user_id
        WHERE dn.notification_id = @notificationId
      `;

      const updatedResult = await executeQuery(updatedNotificationQuery, { notificationId });
      const updatedNotification = updatedResult.recordset[0];
      console.log('Updated notification:', JSON.stringify(updatedNotification, null, 2));

      // If driver accepted, notify tourist
      if (response === 'accepted') {
        console.log('\n6. Sending confirmation email to tourist...');

        // Get driver details
        const driverDetailsQuery = `
          SELECT u.full_name, u.email, u.phone, v.make_model as vehicle_model,
                 'Not specified' as vehicle_color, v.registration_number as license_plate
          FROM Drivers d
          JOIN Users u ON d.user_id = u.user_id
          JOIN Vehicles v ON d.driver_id = v.driver_id
          WHERE d.driver_id = @driverId
        `;

        const driverDetailsResult = await executeQuery(driverDetailsQuery, {
          driverId: notification.driver_id
        });

        if (!driverDetailsResult.recordset || driverDetailsResult.recordset.length === 0) {
          console.log(`Driver details not found for driver ID: ${notification.driver_id}`);
        } else {
          const driverDetails = driverDetailsResult.recordset[0];
          console.log('Driver details:', JSON.stringify(driverDetails, null, 2));

          // Send email to tourist
          try {
            await sendTouristDriverConfirmationEmail(
              updatedNotification.tourist_email,
              updatedNotification.tourist_name,
              driverDetails,
              updatedNotification
            );
            console.log(`Email sent to tourist: ${updatedNotification.tourist_email}`);
          } catch (error) {
            console.log(`Error sending email to tourist: ${error.message}`);
          }
        }
      }

      return { success: true, message: `Booking request ${response}` };
    } catch (error) {
      // Rollback transaction in case of error
      await executeQuery('ROLLBACK TRANSACTION');
      console.log(`Error processing response: ${error.message}`);
      console.log('Transaction rolled back');
      return { success: false, error: error.message };
    }
  } catch (error) {
    console.log(`Error processing driver response: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Send email to tourist when a driver accepts their booking request
 */
async function sendTouristDriverConfirmationEmail(
  touristEmail,
  touristName,
  driverDetails,
  bookingRequest
) {
  try {
    // Format waypoints for display
    let waypointsHtml = '<li>No stops</li>';
    if (bookingRequest.waypoints) {
      try {
        const waypoints = JSON.parse(bookingRequest.waypoints);
        if (waypoints.length > 0) {
          waypointsHtml = waypoints.map(wp => `<li>${wp}</li>`).join('');
        }
      } catch (e) {
        console.log(`Error parsing waypoints: ${e.message}`);
      }
    }

    // Format date and time
    const startDate = new Date(bookingRequest.start_date).toLocaleDateString();
    const startTime = bookingRequest.start_time;

    // Format duration
    const hours = Math.floor(bookingRequest.estimated_duration / 60);
    const minutes = bookingRequest.estimated_duration % 60;
    const durationText = hours > 0
      ? `${hours} hour${hours > 1 ? 's' : ''}${minutes > 0 ? ` ${minutes} minute${minutes > 1 ? 's' : ''}` : ''}`
      : `${minutes} minute${minutes > 1 ? 's' : ''}`;

    // Generate payment link
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const paymentLink = `${baseUrl}/payment/${bookingRequest.request_id}`;

    // Email subject and body
    const subject = 'Driver Confirmed for Your Trip - Siyoga Travels';
    const body = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #4a6ee0;">Driver Confirmed for Your Trip</h2>
        <p>Hello ${touristName},</p>
        <p>Good news! A driver has accepted your trip request. Here are the details:</p>

        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Trip Details</h3>
          <p><strong>Origin:</strong> ${bookingRequest.origin}</p>
          <p><strong>Destination:</strong> ${bookingRequest.destination}</p>

          <div style="margin: 10px 0;">
            <strong>Stops:</strong>
            <ul>
              ${waypointsHtml}
            </ul>
          </div>

          <p><strong>Start Date:</strong> ${startDate}</p>
          <p><strong>Start Time:</strong> ${startTime}</p>
          <p><strong>Trip Type:</strong> ${bookingRequest.trip_type === 'return' ? 'Return Trip' : 'One-way Trip'}</p>
          <p><strong>Vehicle Type:</strong> ${bookingRequest.vehicle_type}</p>
          <p><strong>Number of Travelers:</strong> ${bookingRequest.num_travelers}</p>
          <p><strong>Total Distance:</strong> ${bookingRequest.total_distance} km</p>
          <p><strong>Estimated Duration:</strong> ${durationText}</p>
        </div>

        <div style="background-color: #e8f0ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Driver Details</h3>
          <p><strong>Driver Name:</strong> ${driverDetails.full_name}</p>
          <p><strong>Phone:</strong> ${driverDetails.phone}</p>
          <p><strong>Vehicle:</strong> ${driverDetails.vehicle_model} (${driverDetails.vehicle_color})</p>
          <p><strong>License Plate:</strong> ${driverDetails.license_plate}</p>
        </div>

        <div style="background-color: #fff8e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Payment Information</h3>
          <p><strong>Total Cost:</strong> Rs. ${bookingRequest.total_cost.toLocaleString()}</p>
          <p><strong>Advance Payment (50%):</strong> Rs. ${Math.round(bookingRequest.total_cost * 0.5).toLocaleString()}</p>
        </div>

        <p>Please proceed with the payment to confirm your booking:</p>

        <div style="margin: 30px 0; text-align: center;">
          <a href="${paymentLink}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Proceed to Payment</a>
        </div>

        <p>Note: Your booking will be confirmed only after the advance payment is made.</p>

        <p>Thank you for choosing Siyoga Travels!</p>

        <p>Best regards,<br>Siyoga Travels Team</p>
      </div>
    `;

    // Send email
    await emailService.sendEmail({
      to: touristEmail,
      subject,
      html: body
    });

    console.log(`Email sent to tourist ${touristName} (${touristEmail}) for booking request ${bookingRequest.request_id}`);
    return { success: true };
  } catch (error) {
    console.log(`Error sending tourist driver confirmation email: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Run the function
processDriverResponse(notificationId, response)
  .then(result => {
    console.log('\nResult:', result);
    process.exit(0);
  })
  .catch(error => {
    console.error('\nUnhandled error:', error);
    process.exit(1);
  });
