// Test script for email template replacement
const { executeQuery } = require('../config/DB/db');
const config = require('../config/config');
const logger = require('../config/logger');

// Mock booking request data
const mockBookingRequest = {
  origin: 'Colombo',
  destination: 'Kandy',
  waypoints: JSON.stringify(['Kadugannawa', 'Peradeniya']),
  start_date: '2025-01-15',
  start_time: '09:00 AM',
  trip_type: 'return',
  vehicle_type: 'KDH High Roof',
  num_travelers: 8,
  total_distance: 120,
  estimated_duration: 180,
  total_cost: 15000,
  driver_accommodation: 'paid',
  special_requests: 'Need extra space for luggage'
};

// Mock driver data
const mockDriverData = {
  driverName: 'John Driver',
  driverEmail: '<EMAIL>',
  driverId: 1,
  requestId: 123,
  notificationId: 20
};

// Get email template from database
const getEmailTemplate = async (templateName) => {
  try {
    const query = `
      SELECT * FROM EmailTemplates
      WHERE template_name = @templateName
      OR template_name = @alternateTemplateName
    `;

    // Try both naming conventions
    const result = await executeQuery(query, {
      templateName,
      alternateTemplateName: templateName.replace('_request', '_notification')
    });

    if (result.recordset && result.recordset.length > 0) {
      logger.info(`Found email template: ${result.recordset[0].template_name}`);
      return result.recordset[0];
    }

    logger.warn(`Email template not found: ${templateName}`);
    return null;
  } catch (error) {
    logger.error(`Error getting email template: ${error.message}`);
    return null;
  }
};

// Test template replacement
const testTemplateReplacement = async () => {
  try {
    // Get email template
    const template = await getEmailTemplate('driver_booking_request');

    if (!template) {
      logger.error('Driver booking request email template not found');
      return;
    }

    // Format waypoints for display
    let waypointsHtml = '<li>No stops</li>';
    if (mockBookingRequest.waypoints) {
      try {
        const waypoints = JSON.parse(mockBookingRequest.waypoints);
        if (waypoints.length > 0) {
          waypointsHtml = waypoints.map(wp => `<li>${wp}</li>`).join('');
        }
      } catch (e) {
        logger.error(`Error parsing waypoints: ${e.message}`);
      }
    }

    // Generate accept/reject links
    const baseUrl = config.FRONTEND_URL || 'http://localhost:5173';
    const acceptLink = `${baseUrl}/driver/booking-response-handler/${mockDriverData.notificationId}/accept`;
    const rejectLink = `${baseUrl}/driver/booking-response-handler/${mockDriverData.notificationId}/reject`;

    // Log the links for debugging
    logger.info(`Generated accept link: ${acceptLink}`);
    logger.info(`Generated reject link: ${rejectLink}`);

    // Replace placeholders in template - support both uppercase and lowercase formats
    let emailBody = template.body
      .replace(/{{DRIVER_NAME}}/g, mockDriverData.driverName)
      .replace(/{{driver_name}}/g, mockDriverData.driverName)
      .replace(/{{ORIGIN}}/g, mockBookingRequest.origin)
      .replace(/{{origin}}/g, mockBookingRequest.origin)
      .replace(/{{DESTINATION}}/g, mockBookingRequest.destination)
      .replace(/{{destination}}/g, mockBookingRequest.destination)
      .replace(/{{WAYPOINTS}}/g, waypointsHtml)
      .replace(/{{waypoints}}/g, waypointsHtml)
      .replace(/{{START_DATE}}/g, new Date(mockBookingRequest.start_date).toLocaleDateString())
      .replace(/{{start_date}}/g, new Date(mockBookingRequest.start_date).toLocaleDateString())
      .replace(/{{START_TIME}}/g, mockBookingRequest.start_time)
      .replace(/{{start_time}}/g, mockBookingRequest.start_time)
      .replace(/{{TRIP_TYPE}}/g, mockBookingRequest.trip_type)
      .replace(/{{trip_type}}/g, mockBookingRequest.trip_type)
      .replace(/{{VEHICLE_TYPE}}/g, mockBookingRequest.vehicle_type)
      .replace(/{{vehicle_type}}/g, mockBookingRequest.vehicle_type)
      .replace(/{{NUM_TRAVELERS}}/g, mockBookingRequest.num_travelers)
      .replace(/{{num_travelers}}/g, mockBookingRequest.num_travelers)
      .replace(/{{TOTAL_DISTANCE}}/g, `${mockBookingRequest.total_distance} km`)
      .replace(/{{total_distance}}/g, mockBookingRequest.total_distance)
      .replace(/{{ESTIMATED_DURATION}}/g, `${mockBookingRequest.estimated_duration} minutes`)
      .replace(/{{estimated_duration}}/g, mockBookingRequest.estimated_duration)
      .replace(/{{TOTAL_COST}}/g, `Rs. ${mockBookingRequest.total_cost}`)
      .replace(/{{total_cost}}/g, mockBookingRequest.total_cost)
      .replace(/{{DRIVER_ACCOMMODATION}}/g, mockBookingRequest.driver_accommodation)
      .replace(/{{driver_accommodation}}/g, mockBookingRequest.driver_accommodation)
      .replace(/{{SPECIAL_REQUESTS}}/g, mockBookingRequest.special_requests || 'None')
      .replace(/{{special_requests}}/g, mockBookingRequest.special_requests || 'None')
      .replace(/{{ACCEPT_LINK}}/g, acceptLink)
      .replace(/{{accept_link}}/g, acceptLink)
      .replace(/{{REJECT_LINK}}/g, rejectLink)
      .replace(/{{reject_link}}/g, rejectLink)
      // Handle the case where the template has hardcoded URLs with {{notification_id}}
      .replace(/http:\/\/localhost:5173\/driver\/booking-response\/{{notification_id}}\/accept/g, acceptLink)
      .replace(/http:\/\/localhost:5173\/driver\/booking-response\/{{notification_id}}\/reject/g, rejectLink)
      .replace(/http:\/\/localhost:3000\/driver\/booking-response\/{{notification_id}}\/accept/g, acceptLink)
      .replace(/http:\/\/localhost:3000\/driver\/booking-response\/{{notification_id}}\/reject/g, rejectLink)
      .replace(/http:\/\/localhost:3000\/driver\/booking-response-handler\/{{notification_id}}\/accept/g, acceptLink)
      .replace(/http:\/\/localhost:3000\/driver\/booking-response-handler\/{{notification_id}}\/reject/g, rejectLink)
      // Also replace the notification_id directly
      .replace(/{{NOTIFICATION_ID}}/g, mockDriverData.notificationId)
      .replace(/{{notification_id}}/g, mockDriverData.notificationId);

    // Check if any template variables remain unreplaced
    const unreplacedVariables = emailBody.match(/{{[^}]+}}/g);
    if (unreplacedVariables) {
      logger.error(`Unreplaced template variables found: ${unreplacedVariables.join(', ')}`);
    } else {
      logger.info('All template variables successfully replaced');
    }

    // Check if the accept/reject links are properly formed
    if (emailBody.includes('{{notification_id}}')) {
      logger.error('Template still contains {{notification_id}} placeholder');
    } else {
      logger.info('No {{notification_id}} placeholders found in the processed template');
    }

    // Save the processed template to a file for inspection
    const fs = require('fs');
    const path = require('path');
    const outputPath = path.join(__dirname, 'processed-template.html');
    fs.writeFileSync(outputPath, emailBody);
    logger.info(`Processed template saved to: ${outputPath}`);

    // Print the accept/reject links for verification
    console.log('\nAccept Link:', acceptLink);
    console.log('Reject Link:', rejectLink);
    console.log('\nCheck the processed template file for the complete email content.');
  } catch (error) {
    logger.error(`Error testing template replacement: ${error.message}`);
  }
};

// Run the test
testTemplateReplacement().catch(err => {
  logger.error(`Test failed: ${err.message}`);
});
