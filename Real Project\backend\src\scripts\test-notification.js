// <PERSON>ript to test the notification process for a booking request
const { executeQuery } = require('../config/DB/db');
const bookingRequestController = require('../controllers/bookingRequestController');
const logger = require('../config/logger');

// Get booking request ID from command line
const requestId = process.argv[2] || 7; // Default to the most recent request ID

async function testNotification(requestId) {
  console.log(`Testing notification process for booking request ID: ${requestId}`);

  try {
    // Get booking request details
    const requestQuery = "SELECT * FROM BookingRequests WHERE request_id = @requestId";
    const requestResult = await executeQuery(requestQuery, { requestId });

    if (!requestResult.recordset || requestResult.recordset.length === 0) {
      console.log(`Booking request with ID ${requestId} not found`);
      return;
    }

    const bookingRequest = requestResult.recordset[0];
    console.log('Booking request found:');
    console.log(`- Request ID: ${bookingRequest.request_id}`);
    console.log(`- Tourist ID: ${bookingRequest.tourist_id}`);
    console.log(`- Origin: ${bookingRequest.origin}`);
    console.log(`- Destination: ${bookingRequest.destination}`);
    console.log(`- Vehicle Type: ${bookingRequest.vehicle_type}`);
    console.log(`- Status: ${bookingRequest.status}`);

    // Call the notifyEligibleDrivers function
    console.log('\nCalling notifyEligibleDrivers function...');
    const result = await bookingRequestController.notifyEligibleDrivers(requestId, bookingRequest.vehicle_type);

    console.log('\nNotification result:');
    console.log(JSON.stringify(result, null, 2));

    // Check if any notifications were created
    const notificationsQuery = `
      SELECT dn.*, u.email, u.full_name
      FROM DriverNotifications dn
      JOIN Drivers d ON dn.driver_id = d.driver_id
      JOIN Users u ON d.user_id = u.user_id
      WHERE dn.request_id = @requestId
    `;

    const notificationsResult = await executeQuery(notificationsQuery, { requestId });

    if (!notificationsResult.recordset || notificationsResult.recordset.length === 0) {
      console.log('\nNo notifications found for this booking request');
    } else {
      console.log(`\nFound ${notificationsResult.recordset.length} notifications:`);
      for (const notification of notificationsResult.recordset) {
        console.log(`- Notification ID: ${notification.notification_id}, Driver: ${notification.full_name}, Email: ${notification.email}, Response: ${notification.response}`);
      }
    }

  } catch (error) {
    console.error('Error testing notification process:', error);
  }
}

// Run the function
testNotification(requestId)
  .then(() => {
    console.log('\nTest completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
