// <PERSON>ript to test the notifyEligibleDrivers function
const { executeQuery } = require('../config/DB/db');
const logger = require('../config/logger');

// Get booking request ID from command line
const requestId = process.argv[2] || 7; // Default to the most recent request ID

/**
 * Notify eligible drivers about a new booking request
 * @param {number} requestId - The booking request ID
 * @param {string} vehicleType - The vehicle type requested
 */
const notifyEligibleDrivers = async (requestId, vehicleType) => {
  console.log(`Running notifyEligibleDrivers for request ID: ${requestId}, vehicle type: ${vehicleType}`);
  
  try {
    // Get the booking request details
    console.log('\n1. Getting booking request details...');
    const requestQuery = "SELECT * FROM BookingRequests WHERE request_id = @requestId";
    const requestResult = await executeQuery(requestQuery, { requestId });

    if (!requestResult.recordset || requestResult.recordset.length === 0) {
      console.log(`Booking request not found: ${requestId}`);
      return { success: false, error: 'Booking request not found' };
    }

    const bookingRequest = requestResult.recordset[0];
    console.log('Booking request found:', JSON.stringify(bookingRequest, null, 2));

    // Find eligible drivers based on vehicle type
    console.log('\n2. Finding eligible drivers...');
    const driversQuery = `
      SELECT d.driver_id, u.email, u.full_name
      FROM Drivers d
      JOIN Users u ON d.user_id = u.user_id
      JOIN Vehicles v ON d.driver_id = v.driver_id
      WHERE v.vehicle_type = @vehicleType AND d.status = 'Approved'
    `;

    const driversResult = await executeQuery(driversQuery, { vehicleType });

    if (!driversResult.recordset || driversResult.recordset.length === 0) {
      console.log(`No eligible drivers found for vehicle type: ${vehicleType}`);
      return { success: false, error: 'No eligible drivers found' };
    }

    console.log(`Found ${driversResult.recordset.length} eligible drivers:`, JSON.stringify(driversResult.recordset, null, 2));

    // Create notifications and send emails to all eligible drivers
    console.log('\n3. Creating notifications and sending emails...');
    const notifiedDrivers = [];

    for (const driver of driversResult.recordset) {
      console.log(`\nProcessing driver: ${driver.full_name} (${driver.email})`);
      
      // Check if notification already exists
      const existingNotificationQuery = `
        SELECT notification_id FROM DriverNotifications
        WHERE request_id = @requestId AND driver_id = @driverId
      `;
      
      const existingNotificationResult = await executeQuery(existingNotificationQuery, {
        requestId,
        driverId: driver.driver_id
      });
      
      if (existingNotificationResult.recordset && existingNotificationResult.recordset.length > 0) {
        console.log(`Notification already exists for driver ${driver.driver_id}`);
        continue;
      }
      
      // Create notification record
      console.log('Creating notification record...');
      const notificationQuery = `
        INSERT INTO DriverNotifications
        (request_id, driver_id, response)
        OUTPUT INSERTED.notification_id
        VALUES (@requestId, @driverId, 'pending')
      `;

      const notificationResult = await executeQuery(notificationQuery, {
        requestId,
        driverId: driver.driver_id
      });

      if (!notificationResult.recordset || !notificationResult.recordset[0]) {
        console.log(`Failed to create notification for driver ${driver.driver_id}`);
        continue;
      }
      
      const notificationId = notificationResult.recordset[0].notification_id;
      console.log(`Notification created with ID: ${notificationId}`);

      // Get tourist details for the email
      console.log('Getting tourist details...');
      const touristQuery = `
        SELECT u.full_name
        FROM Users u
        JOIN BookingRequests br ON u.user_id = br.tourist_id
        WHERE br.request_id = @requestId
      `;

      const touristResult = await executeQuery(touristQuery, { requestId });
      const touristName = touristResult.recordset[0]?.full_name || 'Tourist';
      console.log(`Tourist name: ${touristName}`);

      // Send email notification
      console.log('Sending email notification...');
      try {
        const emailService = require('../services/emailService');
        const emailResult = await emailService.sendDriverBookingRequestEmail(
          driver.email,
          driver.full_name,
          bookingRequest,
          requestId,
          driver.driver_id,
          notificationId
        );
        
        if (emailResult && emailResult.success) {
          console.log('Email sent successfully');
        } else {
          console.log(`Failed to send email: ${emailResult ? emailResult.error : 'Unknown error'}`);
        }
      } catch (error) {
        console.log(`Error sending email: ${error.message}`);
      }

      notifiedDrivers.push({
        driverId: driver.driver_id,
        email: driver.email,
        name: driver.full_name
      });
    }

    console.log(`\nNotified ${notifiedDrivers.length} drivers about booking request ${requestId}`);
    return {
      success: true,
      notifiedDrivers: notifiedDrivers.length,
      drivers: notifiedDrivers
    };
  } catch (error) {
    console.log(`Error notifying drivers: ${error.message}`);
    return { success: false, error: error.message };
  }
};

// Run the function
async function main() {
  try {
    // Get vehicle type for the booking request
    const requestQuery = "SELECT vehicle_type FROM BookingRequests WHERE request_id = @requestId";
    const requestResult = await executeQuery(requestQuery, { requestId });
    
    if (!requestResult.recordset || requestResult.recordset.length === 0) {
      console.log(`Booking request with ID ${requestId} not found`);
      return;
    }
    
    const vehicleType = requestResult.recordset[0].vehicle_type;
    
    // Run the notify function
    await notifyEligibleDrivers(requestId, vehicleType);
  } catch (error) {
    console.error('Error:', error);
  }
}

main()
  .then(() => {
    console.log('\nTest completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
