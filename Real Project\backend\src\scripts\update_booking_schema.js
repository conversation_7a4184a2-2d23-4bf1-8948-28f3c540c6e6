// src/scripts/update_booking_schema.js
const fs = require('fs');
const path = require('path');
const { executeQuery } = require('../config/DB/db');
const logger = require('../config/logger');

/**
 * Run the SQL script to update the database schema
 */
async function updateBookingSchema() {
  try {
    logger.info('Starting booking schema update...');
    
    // Read the SQL script
    const sqlFilePath = path.join(__dirname, 'update_booking_schema.sql');
    const sqlScript = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split the script into individual statements
    const statements = sqlScript.split('GO');
    
    // Execute each statement
    for (const statement of statements) {
      if (statement.trim()) {
        await executeQuery(statement);
      }
    }
    
    logger.info('Booking schema update completed successfully');
  } catch (error) {
    logger.error(`Error updating booking schema: ${error.message}`);
    throw error;
  }
}

// Run the update if this script is executed directly
if (require.main === module) {
  updateBookingSchema()
    .then(() => {
      logger.info('Booking schema update script completed');
      process.exit(0);
    })
    .catch(error => {
      logger.error(`Booking schema update script failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = updateBookingSchema;
