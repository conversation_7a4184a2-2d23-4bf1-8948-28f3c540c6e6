-- Create BookingRequests table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[BookingRequests]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[BookingRequests] (
        [request_id] INT IDENTITY(1,1) PRIMARY KEY,
        [tourist_id] INT NOT NULL,
        [origin] NVARCHAR(255) NOT NULL,
        [destination] NVARCHAR(255) NOT NULL,
        [waypoints] NVARCHAR(MAX), -- Stored as JSON array
        [start_date] DATE NOT NULL,
        [start_time] TIME NOT NULL,
        [trip_type] NVARCHAR(10) NOT NULL CHECK ([trip_type] IN ('drop', 'return')),
        [vehicle_type] NVARCHAR(50) NOT NULL,
        [num_travelers] INT NOT NULL,
        [total_distance] FLOAT NOT NULL,
        [estimated_duration] INT NOT NULL, -- In minutes
        [total_cost] DECIMAL(10,2) NOT NULL,
        [driver_accommodation] NVARCHAR(20) CHECK ([driver_accommodation] IN ('provided', 'paid')),
        [special_requests] NVARCHAR(MAX),
        [status] NVARCHAR(20) DEFAULT 'pending' CHECK ([status] IN ('pending', 'driver_confirmed', 'payment_completed', 'cancelled')),
        [created_at] DATETIME DEFAULT GETDATE(),
        [updated_at] DATETIME NULL,
        FOREIGN KEY ([tourist_id]) REFERENCES [dbo].[Users]([UserID])
    );
    
    PRINT 'Created BookingRequests table';
END
ELSE
BEGIN
    PRINT 'BookingRequests table already exists';
END

-- Create DriverNotifications table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DriverNotifications]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[DriverNotifications] (
        [notification_id] INT IDENTITY(1,1) PRIMARY KEY,
        [request_id] INT NOT NULL,
        [driver_id] INT NOT NULL,
        [sent_at] DATETIME DEFAULT GETDATE(),
        [read_at] DATETIME NULL,
        [response] NVARCHAR(20) CHECK ([response] IN ('pending', 'accepted', 'rejected', 'expired')),
        [response_at] DATETIME NULL,
        FOREIGN KEY ([request_id]) REFERENCES [dbo].[BookingRequests]([request_id]),
        FOREIGN KEY ([driver_id]) REFERENCES [dbo].[Drivers]([DriverID])
    );
    
    PRINT 'Created DriverNotifications table';
END
ELSE
BEGIN
    PRINT 'DriverNotifications table already exists';
END

-- Create EmailTemplates table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[EmailTemplates]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[EmailTemplates] (
        [template_id] INT IDENTITY(1,1) PRIMARY KEY,
        [template_name] NVARCHAR(100) NOT NULL,
        [subject] NVARCHAR(255) NOT NULL,
        [body] NVARCHAR(MAX) NOT NULL,
        [created_at] DATETIME DEFAULT GETDATE(),
        [updated_at] DATETIME NULL
    );
    
    PRINT 'Created EmailTemplates table';
    
    -- Insert default email templates
    INSERT INTO [dbo].[EmailTemplates] ([template_name], [subject], [body])
    VALUES
    ('driver_booking_request', 'New Trip Request - Siyoga Travels', 
    N'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4a6ee0;">New Trip Request</h2>
      <p>Hello {{DRIVER_NAME}},</p>
      <p>A tourist has requested a trip that matches your vehicle type. Here are the details:</p>
      
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0;">Trip Details</h3>
        <p><strong>Origin:</strong> {{ORIGIN}}</p>
        <p><strong>Destination:</strong> {{DESTINATION}}</p>
        
        <div style="margin: 10px 0;">
          <strong>Stops:</strong>
          <ul>
            {{WAYPOINTS}}
          </ul>
        </div>
        
        <p><strong>Start Date:</strong> {{START_DATE}}</p>
        <p><strong>Start Time:</strong> {{START_TIME}}</p>
        <p><strong>Trip Type:</strong> {{TRIP_TYPE}}</p>
        <p><strong>Vehicle Type:</strong> {{VEHICLE_TYPE}}</p>
        <p><strong>Number of Travelers:</strong> {{NUM_TRAVELERS}}</p>
        <p><strong>Total Distance:</strong> {{TOTAL_DISTANCE}}</p>
        <p><strong>Estimated Duration:</strong> {{ESTIMATED_DURATION}}</p>
        <p><strong>Driver Accommodation:</strong> {{DRIVER_ACCOMMODATION}}</p>
        <p><strong>Special Requests:</strong> {{SPECIAL_REQUESTS}}</p>
      </div>
      
      <div style="background-color: #e8f0ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0;">Your Earnings</h3>
        <p><strong>Driver Earnings:</strong> {{DRIVER_EARNINGS}}</p>
      </div>
      
      <p>Please respond to this request by clicking one of the buttons below:</p>
      
      <div style="margin: 30px 0; text-align: center;">
        <a href="{{ACCEPT_LINK}}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">Accept Trip</a>
        <a href="{{REJECT_LINK}}" style="background-color: #f44336; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reject Trip</a>
      </div>
      
      <p>Note: This request will expire in 24 hours if no response is received.</p>
      
      <p>Thank you,<br>Siyoga Travels Team</p>
    </div>'),
    
    ('tourist_driver_confirmation', 'Driver Confirmed Your Trip - Siyoga Travels',
    N'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4a6ee0;">Driver Confirmed Your Trip</h2>
      <p>Hello {{TOURIST_NAME}},</p>
      <p>Good news! A driver has confirmed your trip request. Here are the driver details:</p>
      
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0;">Driver Details</h3>
        <p><strong>Driver Name:</strong> {{DRIVER_NAME}}</p>
        <p><strong>Vehicle Type:</strong> {{VEHICLE_TYPE}}</p>
        <p><strong>Vehicle Model:</strong> {{VEHICLE_MODEL}}</p>
      </div>
      
      <p>Please proceed to payment to confirm your booking:</p>
      
      <div style="margin: 30px 0; text-align: center;">
        <a href="{{PAYMENT_LINK}}" style="background-color: #4a6ee0; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Proceed to Payment</a>
      </div>
      
      <p>If you have any questions, please contact our support team.</p>
      
      <p>Thank you for choosing Siyoga Travels!</p>
      
      <p>Best regards,<br>Siyoga Travels Team</p>
    </div>');
    
    PRINT 'Inserted default email templates';
END
ELSE
BEGIN
    PRINT 'EmailTemplates table already exists';
END

-- Add notification_preferences column to Drivers table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Drivers]') AND name = 'notification_preferences')
BEGIN
    ALTER TABLE [dbo].[Drivers]
    ADD [notification_preferences] NVARCHAR(MAX);
    
    PRINT 'Added notification_preferences column to Drivers table';
END
ELSE
BEGIN
    PRINT 'notification_preferences column already exists in Drivers table';
END
