-- Add assigned_driver_id column to BookingRequests table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[BookingRequests]') AND name = 'assigned_driver_id')
BEGIN
    ALTER TABLE [dbo].[BookingRequests]
    ADD [assigned_driver_id] INT NULL;
    
    -- Add foreign key constraint
    ALTER TABLE [dbo].[BookingRequests]
    ADD CONSTRAINT [FK_BookingRequests_Drivers] FOREIGN KEY ([assigned_driver_id]) REFERENCES [dbo].[Drivers]([driver_id]);
    
    PRINT 'Added assigned_driver_id column to BookingRequests table';
END
ELSE
BEGIN
    PRINT 'assigned_driver_id column already exists in BookingRequests table';
END

-- Update status check constraint to include 'driver_assigned' status
IF EXISTS (SELECT * FROM sys.check_constraints WHERE object_id = OBJECT_ID(N'[dbo].[CK__BookingRe__statu__]') AND parent_object_id = OBJECT_ID(N'[dbo].[BookingRequests]'))
BEGIN
    -- Drop existing constraint
    DECLARE @ConstraintName NVARCHAR(128)
    SELECT @ConstraintName = name FROM sys.check_constraints 
    WHERE parent_object_id = OBJECT_ID(N'[dbo].[BookingRequests]') 
    AND type = 'C' 
    AND definition LIKE '%status%'
    
    IF @ConstraintName IS NOT NULL
    BEGIN
        DECLARE @SQL NVARCHAR(MAX) = N'ALTER TABLE [dbo].[BookingRequests] DROP CONSTRAINT ' + QUOTENAME(@ConstraintName)
        EXEC sp_executesql @SQL
        
        -- Add new constraint
        ALTER TABLE [dbo].[BookingRequests]
        ADD CONSTRAINT [CK_BookingRequests_status] CHECK ([status] IN ('pending', 'driver_confirmed', 'driver_assigned', 'payment_completed', 'cancelled'));
        
        PRINT 'Updated status check constraint in BookingRequests table';
    END
END
