// <PERSON>ript to update the BookingRequests table to add assigned_driver_id column
const { executeQuery } = require('../config/DB/db');
const logger = require('../config/logger');

async function updateBookingSchema() {
  console.log('Updating BookingRequests table schema...');
  
  try {
    // Check if assigned_driver_id column exists
    const checkColumnQuery = `
      SELECT COUNT(*) as column_exists
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'BookingRequests'
      AND COLUMN_NAME = 'assigned_driver_id'
    `;
    
    const columnCheckResult = await executeQuery(checkColumnQuery);
    const columnExists = columnCheckResult.recordset[0].column_exists > 0;
    
    if (!columnExists) {
      console.log('Adding assigned_driver_id column to BookingRequests table...');
      
      // Add the column
      const addColumnQuery = `
        ALTER TABLE BookingRequests
        ADD assigned_driver_id INT NULL
      `;
      
      await executeQuery(addColumnQuery);
      console.log('Column added successfully');
      
      // Add foreign key constraint
      try {
        const addFKQuery = `
          ALTER TABLE BookingRequests
          ADD CONSTRAINT FK_BookingRequests_Drivers
          FOREIGN KEY (assigned_driver_id) REFERENCES Drivers(driver_id)
        `;
        
        await executeQuery(addFKQuery);
        console.log('Foreign key constraint added successfully');
      } catch (fkError) {
        console.log(`Error adding foreign key constraint: ${fkError.message}`);
      }
    } else {
      console.log('assigned_driver_id column already exists');
    }
    
    // Check if driver_assigned status is allowed
    const checkStatusQuery = `
      SELECT value FROM fn_listextendedproperty(
        'MS_Description', 'schema', 'dbo', 'table', 'BookingRequests', 'column', 'status'
      )
    `;
    
    try {
      const statusCheckResult = await executeQuery(checkStatusQuery);
      const statusDescription = statusCheckResult.recordset[0]?.value || '';
      
      if (!statusDescription.includes('driver_assigned')) {
        console.log('Updating status check constraint to include driver_assigned status...');
        
        // First, try to drop the existing constraint
        try {
          const dropConstraintQuery = `
            DECLARE @constraintName NVARCHAR(128)
            SELECT @constraintName = name
            FROM sys.check_constraints
            WHERE parent_object_id = OBJECT_ID('BookingRequests')
            AND type = 'C'
            AND definition LIKE '%status%'
            
            IF @constraintName IS NOT NULL
            BEGIN
              DECLARE @sql NVARCHAR(MAX) = N'ALTER TABLE BookingRequests DROP CONSTRAINT ' + QUOTENAME(@constraintName)
              EXEC sp_executesql @sql
            END
          `;
          
          await executeQuery(dropConstraintQuery);
          console.log('Existing constraint dropped successfully');
        } catch (dropError) {
          console.log(`Error dropping constraint: ${dropError.message}`);
        }
        
        // Add new constraint
        try {
          const addConstraintQuery = `
            ALTER TABLE BookingRequests
            ADD CONSTRAINT CK_BookingRequests_status
            CHECK (status IN ('pending', 'driver_confirmed', 'driver_assigned', 'payment_completed', 'cancelled'))
          `;
          
          await executeQuery(addConstraintQuery);
          console.log('New status constraint added successfully');
        } catch (addError) {
          console.log(`Error adding new constraint: ${addError.message}`);
        }
      } else {
        console.log('Status constraint already includes driver_assigned');
      }
    } catch (statusError) {
      console.log(`Error checking status constraint: ${statusError.message}`);
    }
    
    console.log('Schema update completed');
  } catch (error) {
    console.error('Error updating schema:', error);
  }
}

// Run the function
updateBookingSchema()
  .then(() => {
    console.log('\nUpdate completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
