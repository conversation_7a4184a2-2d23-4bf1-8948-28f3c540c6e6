// src/scripts/update_driver_actions_schema.js
const fs = require('fs');
const path = require('path');
const { executeQuery } = require('../config/DB/db');
const logger = require('../config/logger');

async function updateDriverActionsSchema() {
  logger.info('Updating driver actions schema...');
  
  try {
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'update_driver_actions_schema.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    await executeQuery(sqlContent);
    
    logger.info('Driver actions schema updated successfully');
  } catch (error) {
    logger.error(`Error updating driver actions schema: ${error.message}`);
    throw error;
  }
}

module.exports = updateDriverActionsSchema;

// Run the script if called directly
if (require.main === module) {
  updateDriverActionsSchema()
    .then(() => {
      logger.info('Schema update completed');
      process.exit(0);
    })
    .catch(err => {
      logger.error(`Schema update failed: ${err.message}`);
      process.exit(1);
    });
}
