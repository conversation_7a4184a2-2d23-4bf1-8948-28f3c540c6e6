-- Create PaymentReminders table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PaymentReminders]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[PaymentReminders] (
        [reminder_id] INT IDENTITY(1,1) PRIMARY KEY,
        [request_id] INT NOT NULL,
        [driver_id] INT NOT NULL,
        [sent_at] DATETIME DEFAULT GETDATE(),
        FOREIGN KEY ([request_id]) REFERENCES [dbo].[BookingRequests]([request_id]),
        FOREIGN KEY ([driver_id]) REFERENCES [dbo].[Drivers]([driver_id])
    );
    
    PRINT 'Created PaymentReminders table';
END
ELSE
BEGIN
    PRINT 'PaymentReminders table already exists';
END

-- Create BookingCancellations table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[BookingCancellations]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[BookingCancellations] (
        [cancellation_id] INT IDENTITY(1,1) PRIMARY KEY,
        [request_id] INT NOT NULL,
        [cancelled_by] NVARCHAR(20) NOT NULL CHECK ([cancelled_by] IN ('driver', 'tourist', 'admin')),
        [cancellation_reason] NVARCHAR(MAX) NOT NULL,
        [cancelled_at] DATETIME DEFAULT GETDATE(),
        FOREIGN KEY ([request_id]) REFERENCES [dbo].[BookingRequests]([request_id])
    );
    
    PRINT 'Created BookingCancellations table';
END
ELSE
BEGIN
    PRINT 'BookingCancellations table already exists';
END
