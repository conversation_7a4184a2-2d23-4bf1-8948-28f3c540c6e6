// <PERSON>ript to update the DriverNotifications table to add updated_at column
const { executeQuery } = require('../config/DB/db');
const logger = require('../config/logger');

async function updateDriverNotificationsSchema() {
  console.log('Updating DriverNotifications table schema...');
  
  try {
    // Check if updated_at column exists
    const checkColumnQuery = `
      SELECT COUNT(*) as column_exists
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'DriverNotifications'
      AND COLUMN_NAME = 'updated_at'
    `;
    
    const columnCheckResult = await executeQuery(checkColumnQuery);
    const columnExists = columnCheckResult.recordset[0].column_exists > 0;
    
    if (!columnExists) {
      console.log('Adding updated_at column to DriverNotifications table...');
      
      // Add the column
      const addColumnQuery = `
        ALTER TABLE DriverNotifications
        ADD updated_at DATETIME NULL
      `;
      
      await executeQuery(addColumnQuery);
      console.log('Column added successfully');
    } else {
      console.log('updated_at column already exists');
    }
    
    console.log('Schema update completed successfully');
    return { success: true };
  } catch (error) {
    console.error(`Error updating schema: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Execute the function if this script is run directly
if (require.main === module) {
  updateDriverNotificationsSchema()
    .then(result => {
      if (result.success) {
        console.log('Schema update completed successfully');
        process.exit(0);
      } else {
        console.error(`Schema update failed: ${result.error}`);
        process.exit(1);
      }
    })
    .catch(err => {
      console.error('Unexpected error:', err);
      process.exit(1);
    });
} else {
  // Export for use in other modules
  module.exports = { updateDriverNotificationsSchema };
}
