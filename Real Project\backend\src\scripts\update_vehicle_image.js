// Script to update vehicle_photo field for a specific vehicle
const fs = require('fs');
const path = require('path');
const { executeQuery } = require('../config/DB/db');
const logger = require('../config/logger');

/**
 * Updates the vehicle_photo field for a specific vehicle
 * @param {number} vehicleId - The ID of the vehicle to update
 * @param {string} imagePath - The path to the image file (relative to uploads directory)
 */
async function updateVehicleImage(vehicleId, imagePath) {
  if (!vehicleId || !imagePath) {
    console.error('Error: Vehicle ID and image path are required');
    console.log('Usage: node update_vehicle_image.js <vehicleId> <imagePath>');
    process.exit(1);
  }

  try {
    console.log(`Updating vehicle image for vehicle ID: ${vehicleId}`);
    console.log(`Image path: ${imagePath}`);

    // Check if the vehicle exists
    const checkQuery = `
      SELECT vehicle_id, driver_id, make_model, registration_number, vehicle_photo
      FROM Vehicles
      WHERE vehicle_id = @vehicleId
    `;

    const checkResult = await executeQuery(checkQuery, { vehicleId });

    if (!checkResult.recordset || checkResult.recordset.length === 0) {
      console.error(`Error: Vehicle not found with ID: ${vehicleId}`);
      process.exit(1);
    }

    console.log('Current vehicle details:');
    console.log(checkResult.recordset[0]);

    // Update the vehicle_photo field
    const updateQuery = `
      UPDATE Vehicles
      SET vehicle_photo = @imagePath
      WHERE vehicle_id = @vehicleId;

      SELECT 
        vehicle_id,
        driver_id,
        vehicle_type,
        make_model,
        registration_number,
        vehicle_photo,
        insurance_expiry_date,
        seat_count,
        air_conditioned,
        verified
      FROM Vehicles
      WHERE vehicle_id = @vehicleId;
    `;

    const updateResult = await executeQuery(updateQuery, { 
      vehicleId, 
      imagePath 
    });

    console.log('Vehicle image updated successfully!');
    console.log('Updated vehicle details:');
    console.log(updateResult.recordset[0]);

  } catch (error) {
    console.error(`Error updating vehicle image: ${error.message}`);
    process.exit(1);
  }
}

// Get command line arguments
const vehicleId = process.argv[2];
const imagePath = process.argv[3];

// Run the function
updateVehicleImage(vehicleId, imagePath)
  .then(() => {
    console.log('Update completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error(`Unhandled error: ${error.message}`);
    process.exit(1);
  });
