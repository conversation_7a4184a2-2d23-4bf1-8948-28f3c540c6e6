-- Script to update vehicle_photo field for a specific vehicle
USE TripBookingSystem;
GO

-- Parameters (replace these with your actual values)
DECLARE @VehicleID INT = 1; -- Replace with your vehicle ID
DECLARE @ImagePath NVARCHAR(255) = 'vehicle-images/example-vehicle-image.jpg'; -- Replace with your image path

-- Check if the vehicle exists
IF EXISTS (SELECT 1 FROM Vehicles WHERE vehicle_id = @VehicleID)
BEGIN
    -- Update the vehicle_photo field
    UPDATE Vehicles
    SET vehicle_photo = @ImagePath
    WHERE vehicle_id = @VehicleID;
    
    PRINT 'Vehicle image updated successfully for vehicle ID: ' + CAST(@VehicleID AS NVARCHAR);
    
    -- Show the updated record
    SELECT 
        vehicle_id,
        driver_id,
        vehicle_type,
        make_model,
        registration_number,
        vehicle_photo,
        insurance_expiry_date,
        seat_count,
        air_conditioned,
        verified
    FROM Vehicles
    WHERE vehicle_id = @VehicleID;
END
ELSE
BEGIN
    PRINT 'Vehicle not found with ID: ' + CAST(@VehicleID AS NVARCHAR);
END
