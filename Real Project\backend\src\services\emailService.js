// src/services/emailService.js
const nodemailer = require('nodemailer');
const config = require('../config/config');
const logger = require('../config/logger');
const { executeQuery } = require('../config/DB/db');

// Create test account if no credentials
let transporter;

const initTransporter = async () => {
  // Use the provided Gmail credentials
  transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: '<EMAIL>',
      pass: 'idiu gyou wpua dtqx'
    }
  });

  logger.info('Using Gmail for email sending');
};

// Initialize transporter
initTransporter();

/**
 * Get email template from database
 * @param {string} templateName - Name of the template to retrieve
 */
const getEmailTemplate = async (templateName) => {
  try {
    const query = `
      SELECT * FROM EmailTemplates
      WHERE template_name = @templateName
      OR template_name = @alternateTemplateName
    `;

    // Try both naming conventions
    const result = await executeQuery(query, {
      templateName,
      alternateTemplateName: templateName.replace('_request', '_notification')
    });

    if (result.recordset && result.recordset.length > 0) {
      logger.info(`Found email template: ${result.recordset[0].template_name}`);
      return result.recordset[0];
    }

    logger.warn(`Email template not found: ${templateName}`);

    // Return a default template if not found in database
    return {
      template_id: 0,
      template_name: templateName,
      subject: 'New Trip Request - Siyoga Travels',
      body: `<html>
<body>
  <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <h2 style="color: #4a6ee0;">New Booking Request</h2>
    <p>Dear {{driver_name}},</p>
    <p>A new booking request matching your vehicle type is available:</p>

    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <p><strong>Origin:</strong> {{origin}}</p>
      <p><strong>Destination:</strong> {{destination}}</p>

      <div style="margin: 10px 0;">
        <strong>Stops:</strong>
        <ul>
          {{waypoints}}
        </ul>
      </div>

      <p><strong>Date:</strong> {{start_date}}</p>
      <p><strong>Time:</strong> {{start_time}}</p>
      <p><strong>Trip Type:</strong> {{trip_type}}</p>
      <p><strong>Vehicle Type:</strong> {{vehicle_type}}</p>
      <p><strong>Number of Travelers:</strong> {{num_travelers}}</p>
      <p><strong>Total Distance:</strong> {{total_distance}} km</p>
      <p><strong>Estimated Duration:</strong> {{estimated_duration}}</p>
      <p><strong>Total Cost:</strong> Rs. {{total_cost}}</p>
    </div>

    <p>Please review this request and respond:</p>

    <div style="margin: 30px 0; text-align: center;">
      <a href="{{accept_link}}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">Accept Booking</a>
      <a href="{{reject_link}}" style="background-color: #f44336; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reject Booking</a>
    </div>

    <p>Thank you,<br>Siyoga Travels Team</p>
  </div>
</body>
</html>`
    };
  } catch (error) {
    logger.error(`Error getting email template: ${error.message}`);

    // Return a default template if there's an error
    return {
      template_id: 0,
      template_name: templateName,
      subject: 'New Trip Request - Siyoga Travels',
      body: `<html>
<body>
  <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <h2 style="color: #4a6ee0;">New Booking Request</h2>
    <p>Dear {{driver_name}},</p>
    <p>A new booking request matching your vehicle type is available:</p>

    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <p><strong>Origin:</strong> {{origin}}</p>
      <p><strong>Destination:</strong> {{destination}}</p>

      <div style="margin: 10px 0;">
        <strong>Stops:</strong>
        <ul>
          {{waypoints}}
        </ul>
      </div>

      <p><strong>Date:</strong> {{start_date}}</p>
      <p><strong>Time:</strong> {{start_time}}</p>
      <p><strong>Trip Type:</strong> {{trip_type}}</p>
      <p><strong>Vehicle Type:</strong> {{vehicle_type}}</p>
      <p><strong>Number of Travelers:</strong> {{num_travelers}}</p>
      <p><strong>Total Distance:</strong> {{total_distance}} km</p>
      <p><strong>Estimated Duration:</strong> {{estimated_duration}}</p>
      <p><strong>Total Cost:</strong> Rs. {{total_cost}}</p>
    </div>

    <p>Please review this request and respond:</p>

    <div style="margin: 30px 0; text-align: center;">
      <a href="{{accept_link}}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">Accept Booking</a>
      <a href="{{reject_link}}" style="background-color: #f44336; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reject Booking</a>
    </div>

    <p>Thank you,<br>Siyoga Travels Team</p>
  </div>
</body>
</html>`
    };
  }
};

/**
 * Send email
 * @param {Object} options - Email options
 */
exports.sendEmail = async (options) => {
  try {
    if (!transporter) {
      await initTransporter();
    }

    const mailOptions = {
      from: `"Siyoga Travels" <${config.EMAIL_FROM || '<EMAIL>'}>`,
      to: options.to,
      subject: options.subject,
      text: options.text,
      html: options.html
    };

    const info = await transporter.sendMail(mailOptions);

    logger.info(`Email sent: ${info.messageId}`);

    // Log URL for ethereal emails in development
    if (process.env.NODE_ENV === 'development') {
      logger.info(`Email preview: ${nodemailer.getTestMessageUrl(info)}`);
    }

    return info;
  } catch (error) {
    logger.error(`Email error: ${error.message}`);
    throw error;
  }
};

/**
 * Send driver booking request email
 * @param {string} driverEmail - Driver's email
 * @param {string} driverName - Driver's name
 * @param {Object} bookingRequest - Booking request details
 * @param {number} requestId - Request ID
 * @param {number} driverId - Driver ID
 * @param {number} notificationId - Notification ID
 */
exports.sendDriverBookingRequestEmail = async (
  driverEmail,
  driverName,
  bookingRequest,
  requestId,
  driverId,
  notificationId
) => {
  try {
    // Get email template
    const template = await getEmailTemplate('driver_booking_request');

    if (!template) {
      logger.error('Driver booking request email template not found');
      return { success: false, error: 'Email template not found' };
    }

    // Format waypoints for display
    let waypointsHtml = '<li>No stops</li>';
    if (bookingRequest.waypoints) {
      try {
        const waypoints = JSON.parse(bookingRequest.waypoints);
        if (waypoints.length > 0) {
          waypointsHtml = waypoints.map(wp => `<li>${wp}</li>`).join('');
        }
      } catch (e) {
        logger.error(`Error parsing waypoints: ${e.message}`);
      }
    }

    // Generate accept/reject links
    const baseUrl = config.FRONTEND_URL || 'http://localhost:5173';
    const acceptLink = `${baseUrl}/driver/booking-response-handler/${notificationId}/accept`;
    const rejectLink = `${baseUrl}/driver/booking-response-handler/${notificationId}/reject`;

    // Log the links for debugging
    logger.info(`Generated accept link: ${acceptLink}`);
    logger.info(`Generated reject link: ${rejectLink}`);

    // Replace placeholders in template - support both uppercase and lowercase formats
    let emailBody = template.body
      .replace(/{{DRIVER_NAME}}/g, driverName)
      .replace(/{{driver_name}}/g, driverName)
      .replace(/{{ORIGIN}}/g, bookingRequest.origin)
      .replace(/{{origin}}/g, bookingRequest.origin)
      .replace(/{{DESTINATION}}/g, bookingRequest.destination)
      .replace(/{{destination}}/g, bookingRequest.destination)
      .replace(/{{WAYPOINTS}}/g, waypointsHtml)
      .replace(/{{waypoints}}/g, waypointsHtml)
      .replace(/{{START_DATE}}/g, new Date(bookingRequest.start_date).toLocaleDateString())
      .replace(/{{start_date}}/g, new Date(bookingRequest.start_date).toLocaleDateString())
      .replace(/{{START_TIME}}/g, bookingRequest.start_time)
      .replace(/{{start_time}}/g, bookingRequest.start_time)
      .replace(/{{TRIP_TYPE}}/g, bookingRequest.trip_type)
      .replace(/{{trip_type}}/g, bookingRequest.trip_type)
      .replace(/{{VEHICLE_TYPE}}/g, bookingRequest.vehicle_type)
      .replace(/{{vehicle_type}}/g, bookingRequest.vehicle_type)
      .replace(/{{NUM_TRAVELERS}}/g, bookingRequest.num_travelers)
      .replace(/{{num_travelers}}/g, bookingRequest.num_travelers)
      .replace(/{{TOTAL_DISTANCE}}/g, `${bookingRequest.total_distance} km`)
      .replace(/{{total_distance}}/g, bookingRequest.total_distance)
      .replace(/{{ESTIMATED_DURATION}}/g, `${bookingRequest.estimated_duration} minutes`)
      .replace(/{{estimated_duration}}/g, bookingRequest.estimated_duration)
      .replace(/{{TOTAL_COST}}/g, `Rs. ${bookingRequest.total_cost}`)
      .replace(/{{total_cost}}/g, bookingRequest.total_cost)
      .replace(/{{DRIVER_ACCOMMODATION}}/g, bookingRequest.driver_accommodation)
      .replace(/{{driver_accommodation}}/g, bookingRequest.driver_accommodation)
      .replace(/{{SPECIAL_REQUESTS}}/g, bookingRequest.special_requests || 'None')
      .replace(/{{special_requests}}/g, bookingRequest.special_requests || 'None')
      .replace(/{{ACCEPT_LINK}}/g, acceptLink)
      .replace(/{{accept_link}}/g, acceptLink)
      .replace(/{{REJECT_LINK}}/g, rejectLink)
      .replace(/{{reject_link}}/g, rejectLink)
      // Handle the case where the template has hardcoded URLs with {{notification_id}}
      .replace(/http:\/\/localhost:5173\/driver\/booking-response\/{{notification_id}}\/accept/g, acceptLink)
      .replace(/http:\/\/localhost:5173\/driver\/booking-response\/{{notification_id}}\/reject/g, rejectLink)
      .replace(/http:\/\/localhost:3000\/driver\/booking-response\/{{notification_id}}\/accept/g, acceptLink)
      .replace(/http:\/\/localhost:3000\/driver\/booking-response\/{{notification_id}}\/reject/g, rejectLink)
      .replace(/http:\/\/localhost:3000\/driver\/booking-response-handler\/{{notification_id}}\/accept/g, acceptLink)
      .replace(/http:\/\/localhost:3000\/driver\/booking-response-handler\/{{notification_id}}\/reject/g, rejectLink)
      // Also replace the notification_id directly
      .replace(/{{NOTIFICATION_ID}}/g, notificationId)
      .replace(/{{notification_id}}/g, notificationId);

    // Send email
    await exports.sendEmail({
      to: driverEmail,
      subject: template.subject,
      html: emailBody
    });

    logger.info(`Email sent to driver ${driverId} (${driverEmail}) for booking request ${requestId}`);
    return { success: true };
  } catch (error) {
    logger.error(`Error sending driver booking request email: ${error.message}`);
    return { success: false, error: error.message };
  }
};