// src/services/notificationService.js
const logger = require('../config/logger');
const { executeQuery } = require('../config/DB/db');
const emailService = require('./emailService');

/**
 * Send notification to other drivers when a booking is accepted
 * @param {number} requestId - The booking request ID
 * @param {number} acceptedDriverId - The ID of the driver who accepted the booking
 * @param {Object} bookingDetails - The booking details
 */
exports.notifyOtherDrivers = async (requestId, acceptedDriverId, bookingDetails) => {
  try {
    logger.info(`Notifying other drivers about booking ${requestId} being accepted by driver ${acceptedDriverId}`);

    // Get all drivers who received notifications for this booking
    const driversQuery = `
      SELECT dn.driver_id, u.email, u.full_name
      FROM DriverNotifications dn
      JOIN Drivers d ON dn.driver_id = d.driver_id
      JOIN Users u ON d.user_id = u.user_id
      WHERE dn.request_id = @requestId
      AND dn.driver_id != @acceptedDriverId
      AND dn.response = 'expired'
    `;

    const driversResult = await executeQuery(driversQuery, { 
      requestId, 
      acceptedDriverId 
    });

    if (!driversResult.recordset || driversResult.recordset.length === 0) {
      logger.info(`No other drivers to notify for booking ${requestId}`);
      return { success: true, message: 'No other drivers to notify' };
    }

    // Get accepted driver details
    const acceptedDriverQuery = `
      SELECT u.full_name
      FROM Drivers d
      JOIN Users u ON d.user_id = u.user_id
      WHERE d.driver_id = @driverId
    `;

    const acceptedDriverResult = await executeQuery(acceptedDriverQuery, { 
      driverId: acceptedDriverId 
    });

    if (!acceptedDriverResult.recordset || acceptedDriverResult.recordset.length === 0) {
      logger.warn(`Could not find details for accepted driver ${acceptedDriverId}`);
      return { success: false, message: 'Could not find accepted driver details' };
    }

    const acceptedDriverName = acceptedDriverResult.recordset[0].full_name;

    // Send email to each driver
    const drivers = driversResult.recordset;
    logger.info(`Sending notification emails to ${drivers.length} other drivers`);

    for (const driver of drivers) {
      await sendBookingAcceptedEmail(
        driver.email,
        driver.full_name,
        acceptedDriverName,
        bookingDetails
      );
    }

    return { 
      success: true, 
      message: `Notified ${drivers.length} other drivers` 
    };
  } catch (error) {
    logger.error(`Error notifying other drivers: ${error.message}`);
    return { success: false, error: error.message };
  }
};

/**
 * Send email to a driver when another driver has accepted a booking
 */
const sendBookingAcceptedEmail = async (
  driverEmail,
  driverName,
  acceptedDriverName,
  bookingDetails
) => {
  try {
    // Format date
    const startDate = new Date(bookingDetails.start_date).toLocaleDateString();

    // Email subject and body
    const subject = 'Booking Request No Longer Available - Siyoga Travels';
    const body = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #4a6ee0;">Booking Request Update</h2>
        <p>Hello ${driverName},</p>
        <p>We wanted to inform you that a booking request you received is no longer available.</p>
        <p>The trip from <strong>${bookingDetails.origin}</strong> to <strong>${bookingDetails.destination}</strong> on <strong>${startDate}</strong> has been accepted by another driver.</p>
        
        <p>Thank you for your interest. We'll notify you of new booking requests that match your vehicle type.</p>
        
        <p>Best regards,<br>Siyoga Travels Team</p>
      </div>
    `;

    // Send email
    await emailService.sendEmail({
      to: driverEmail,
      subject,
      html: body
    });

    logger.info(`Email sent to driver ${driverName} (${driverEmail}) about booking being accepted by ${acceptedDriverName}`);
    return { success: true };
  } catch (error) {
    logger.error(`Error sending booking accepted email to driver: ${error.message}`);
    return { success: false, error: error.message };
  }
};
