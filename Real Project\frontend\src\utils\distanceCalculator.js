/**
 * Distance Calculator Utility
 * Handles distance calculations between multiple points for trip planning
 * Supports both one-way (drop) and return trips
 */

// GoMaps.pro API key
const GOMAPS_API_KEY = "AlzaSyoAnnH_UH2-gZFeNfRVYc8E8-iR1r88lLEY";

/**
 * Calculate distance between sequential points
 * @param {Array} locations - Array of location addresses in order [origin, dest1, dest2, ...]
 * @param {Object} options - Additional options
 * @param {boolean} options.isReturnTrip - Whether this is a return trip
 * @param {Array} options.stopTimes - Array of stop times in minutes for each destination
 * @param {number} options.additionalStopTime - Additional time to add for each stop (in hours)
 * @returns {Promise} - Promise that resolves with distance details
 */
export const calculateSequentialDistance = async (locations, options = {}) => {
  try {
    const {
      isReturnTrip = false,
      stopTimes = [],
      additionalStopTime = 3, // Default 3 hours additional stop time
      startTime = '09:00'
    } = options;

    if (!locations || locations.length < 2) {
      return {
        success: false,
        error: 'At least origin and one destination are required'
      };
    }

    // Create a copy of locations for processing
    let routeLocations = [...locations];
    
    // For return trips, add the origin as the final destination
    if (isReturnTrip) {
      routeLocations.push(locations[0]);
    }

    console.log("Calculating distances for route:", routeLocations);

    // Create segments for sequential calculation
    const segments = [];
    let totalDistance = 0;
    let totalDuration = 0;
    let journeyBreakdown = '';

    // Process each segment sequentially
    for (let i = 0; i < routeLocations.length - 1; i++) {
      const origin = routeLocations[i];
      const destination = routeLocations[i + 1];
      
      // Calculate distance for this segment
      const segmentResult = await calculateSegmentDistance(origin, destination);
      
      if (!segmentResult.success) {
        return segmentResult; // Return error if segment calculation failed
      }
      
      // Add segment details
      const segment = {
        from: origin,
        to: destination,
        distance: segmentResult.distance,
        duration: segmentResult.duration,
        segmentIndex: i
      };
      
      segments.push(segment);
      
      // Add to totals
      totalDistance += segmentResult.distance.value;
      totalDuration += segmentResult.duration.value;
      
      // Add to journey breakdown
      journeyBreakdown += `<div class="journey-segment">
        <div class="journey-point"><i class="fas fa-map-marker-alt"></i> ${origin}</div>
        <div class="journey-arrow"><i class="fas fa-arrow-down"></i>${segmentResult.distance.text} (${segmentResult.duration.text})</div>
        <div class="journey-point"><i class="fas fa-map-pin"></i> ${destination}</div>
      </div>`;
      
      // Add stop time for this destination if it's not the final destination
      if (i < routeLocations.length - 2) {
        const stopTimeMinutes = (stopTimes[i] || 0) + (additionalStopTime * 60);
        totalDuration += stopTimeMinutes * 60; // Convert minutes to seconds
        
        // Add stop time to journey breakdown
        journeyBreakdown += `<div class="journey-stop">
          <div class="stop-icon"><i class="fas fa-clock"></i></div>
          <div class="stop-details">Stop time at ${destination}: ${formatStopTime(stopTimeMinutes)}</div>
        </div>`;
      }
    }

    // Convert to km and hours
    const totalDistanceKm = (totalDistance / 1000).toFixed(1);
    const totalDurationHours = (totalDuration / 60 / 60).toFixed(1);
    const totalDurationMins = Math.round(totalDuration / 60);

    // Format time in hours and minutes
    const hours = Math.floor(totalDurationMins / 60);
    const mins = totalDurationMins % 60;
    const timeText = hours > 0
      ? `${hours} hour${hours > 1 ? 's' : ''} ${mins > 0 ? mins + ' mins' : ''}`
      : `${mins} mins`;

    // Calculate trip feasibility
    // Parse start time
    const [startHours, startMinutes] = startTime.split(':').map(Number);
    const startTimeMinutes = startHours * 60 + startMinutes;
    
    // Calculate end time in minutes since midnight
    const endTimeMinutes = startTimeMinutes + totalDurationMins;
    
    // 10:00 PM is 22 hours * 60 minutes = 1320 minutes since midnight
    const exceedsCutoffTime = endTimeMinutes > 1320;
    
    // Calculate how many days are needed
    const daysNeeded = exceedsCutoffTime 
      ? Math.ceil(totalDurationMins / (12 * 60)) // Assuming 12 hours of travel per day
      : 1;

    // Format end time
    const endHours = Math.floor(endTimeMinutes / 60) % 24;
    const endMinutes = endTimeMinutes % 60;
    const endTimeFormatted = `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`;

    // Create detailed breakdown of the calculation
    let distanceCalculationBreakdown = '<strong>Distance Calculation Breakdown:</strong><br>';
    segments.forEach((segment, index) => {
      distanceCalculationBreakdown += `<div class="calculation-segment">
        <div>Segment ${index + 1}: ${segment.from} → ${segment.to}</div>
        <div>Distance: ${segment.distance.text}</div>
        <div>Duration: ${segment.duration.text}</div>
      </div>`;
      
      // Add stop time if applicable
      if (index < segments.length - 1) {
        const stopTimeMinutes = (stopTimes[index] || 0) + (additionalStopTime * 60);
        if (stopTimeMinutes > 0) {
          distanceCalculationBreakdown += `<div class="calculation-stop">
            <div>Stop time at ${segment.to}: ${formatStopTime(stopTimeMinutes)}</div>
          </div>`;
        }
      }
    });
    
    distanceCalculationBreakdown += `<div class="calculation-total">
      <div>Total Distance: ${totalDistanceKm} km</div>
      <div>Total Duration: ${timeText}</div>
      <div>Trip Type: ${isReturnTrip ? 'Return Trip' : 'One-way Trip'}</div>
    </div>`;

    return {
      success: true,
      totalDistance: parseFloat(totalDistanceKm),
      totalDistanceText: `${totalDistanceKm} km`,
      totalDuration: parseFloat(totalDurationHours),
      totalDurationMins: totalDurationMins,
      totalDurationText: timeText,
      segments,
      journeyBreakdown,
      distanceCalculationBreakdown,
      tripDetails: {
        isReturnTrip,
        stopTimes,
        additionalStopTime,
        startTime,
        endTime: endTimeFormatted,
        exceedsCutoffTime,
        daysNeeded
      }
    };
  } catch (error) {
    console.error('Error calculating sequential distance:', error);
    return {
      success: false,
      error: 'Failed to calculate distance. Please try again.'
    };
  }
};

/**
 * Calculate distance between two points
 * @param {string} origin - Origin address
 * @param {string} destination - Destination address
 * @returns {Promise} - Promise that resolves with distance details
 */
const calculateSegmentDistance = async (origin, destination) => {
  try {
    // Create URL for GoMaps.pro Distance Matrix API
    const url = `https://maps.gomaps.pro/maps/api/distancematrix/json?origins=${encodeURIComponent(origin)}&destinations=${encodeURIComponent(destination)}&mode=driving&key=${GOMAPS_API_KEY}`;
    
    console.log(`Calculating distance from ${origin} to ${destination}`);
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.status === "OK" && data.rows && data.rows[0] && data.rows[0].elements && data.rows[0].elements[0] && data.rows[0].elements[0].status === "OK") {
      const element = data.rows[0].elements[0];
      
      return {
        success: true,
        distance: {
          text: element.distance.text,
          value: element.distance.value // in meters
        },
        duration: {
          text: element.duration.text,
          value: element.duration.value // in seconds
        },
        origin: data.origin_addresses[0],
        destination: data.destination_addresses[0]
      };
    } else {
      console.error('GoMaps.pro distance matrix request failed:', data.status);
      return {
        success: false,
        error: `GoMaps.pro API error: ${data.status}`
      };
    }
  } catch (error) {
    console.error('Error fetching GoMaps.pro distance matrix:', error);
    return {
      success: false,
      error: 'Failed to calculate segment distance. Please try again.'
    };
  }
};

/**
 * Format stop time in minutes to a readable string
 * @param {number} minutes - Stop time in minutes
 * @returns {string} - Formatted stop time
 */
const formatStopTime = (minutes) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  
  if (hours > 0 && mins > 0) {
    return `${hours} hour${hours > 1 ? 's' : ''} ${mins} minute${mins > 1 ? 's' : ''}`;
  } else if (hours > 0) {
    return `${hours} hour${hours > 1 ? 's' : ''}`;
  } else {
    return `${mins} minute${mins > 1 ? 's' : ''}`;
  }
};

export default {
  calculateSequentialDistance
};
