/**
 * Map utility functions for the trip planning feature
 * Updated to use GoMaps.pro API
 */

// Sri Lanka coordinates
export const SRI_LANKA_CENTER = { lat: 7.8731, lng: 80.7718 };

// GoMaps.pro API key
export const GOMAPS_API_KEY = "AlzaSyoAnnH_UH2-gZFeNfRVYc8E8-iR1r88lLEY";

// GoMaps.pro API URL with callback
export const GOMAPS_API_URL = "https://maps.gomaps.pro/maps/api/js?key=AlzaSyoAnnH_UH2-gZFeNfRVYc8E8-iR1r88lLEY&libraries=places,drawing&callback=initAutocomplete";

// Error handling for GoMaps API
export const handleGoMapsError = (error) => {
  console.error('GoMaps API error:', error);
  return {
    success: false,
    error: 'GoMaps API error. Please try again.'
  };
};

// Map marker icons
export const MARKER_ICONS = {
  ORIGIN: "https://maps.gomaps.pro/mapfiles/ms/icons/green-dot.png",
  DESTINATION: "https://maps.gomaps.pro/mapfiles/ms/icons/red-dot.png",
  WAYPOINT: "https://maps.gomaps.pro/mapfiles/ms/icons/yellow-dot.png",
  USER_LOCATION: "https://maps.gomaps.pro/mapfiles/ms/icons/blue-dot.png"
};

// Predefined coordinates for major destinations in Sri Lanka
// These are approximate coordinates for testing purposes
export const SRI_LANKA_DESTINATIONS = {
  'Colombo': { lat: 6.9271, lng: 79.8612 },
  'Kandy': { lat: 7.2906, lng: 80.6337 },
  'Galle': { lat: 6.0535, lng: 80.2210 },
  'Anuradhapura': { lat: 8.3114, lng: 80.4037 },
  'Jaffna': { lat: 9.6615, lng: 80.0255 },
  'Trincomalee': { lat: 8.5874, lng: 81.2152 },
  'Nuwara Eliya': { lat: 6.9497, lng: 80.7891 },
  'Polonnaruwa': { lat: 7.9403, lng: 81.0188 },
  'Batticaloa': { lat: 7.7246, lng: 81.7006 },
  'Negombo': { lat: 7.2081, lng: 79.8371 },
  'Mirissa': { lat: 5.9483, lng: 80.4589 },
  'Arugam Bay': { lat: 6.8339, lng: 81.8341 },
  'Unawatuna': { lat: 6.0169, lng: 80.2496 },
  'Pasikuda': { lat: 7.9228, lng: 81.5651 },
  'Bentota': { lat: 6.4213, lng: 79.9959 },
  'Yala National Park': { lat: 6.3698, lng: 81.5046 },
  'Udawalawe National Park': { lat: 6.4389, lng: 80.8982 },
  'Sinharaja Forest Reserve': { lat: 6.4000, lng: 80.5000 },
  'Horton Plains National Park': { lat: 6.8000, lng: 80.8333 },
  'Wilpattu National Park': { lat: 8.4567, lng: 80.0139 },
  'Ella': { lat: 6.8667, lng: 81.0466 },
  'Haputale': { lat: 6.7667, lng: 80.9667 },
  'Adams Peak': { lat: 6.8096, lng: 80.4994 },
  'Dambulla': { lat: 7.8675, lng: 80.6518 },
  'Sigiriya': { lat: 7.9570, lng: 80.7603 }
};

/**
 * Get coordinates for a destination
 * @param {string} destinationName - The name of the destination
 * @returns {Object} - The coordinates of the destination
 */
export const getDestinationCoordinates = (destinationName) => {
  // Check if we have predefined coordinates for this destination
  if (SRI_LANKA_DESTINATIONS[destinationName]) {
    return SRI_LANKA_DESTINATIONS[destinationName];
  }

  // If not, return a random location near Sri Lanka center
  return {
    lat: SRI_LANKA_CENTER.lat + (Math.random() - 0.5) * 2,
    lng: SRI_LANKA_CENTER.lng + (Math.random() - 0.5) * 2
  };
};

/**
 * Calculate the distance between two coordinates in kilometers
 * @param {Object} coord1 - The first coordinate { lat, lng }
 * @param {Object} coord2 - The second coordinate { lat, lng }
 * @returns {number} - The distance in kilometers
 */
export const calculateDistance = (coord1, coord2) => {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(coord2.lat - coord1.lat);
  const dLng = deg2rad(coord2.lng - coord1.lng);
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(coord1.lat)) * Math.cos(deg2rad(coord2.lat)) *
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance in km
  return distance;
};

/**
 * Convert degrees to radians
 * @param {number} deg - Degrees
 * @returns {number} - Radians
 */
const deg2rad = (deg) => {
  return deg * (Math.PI/180);
};

/**
 * Estimate travel time between two coordinates
 * @param {Object} coord1 - The first coordinate { lat, lng }
 * @param {Object} coord2 - The second coordinate { lat, lng }
 * @param {number} avgSpeed - Average speed in km/h (default: 40)
 * @returns {number} - The travel time in hours
 */
export const estimateTravelTime = (coord1, coord2, avgSpeed = 40) => {
  const distance = calculateDistance(coord1, coord2);
  return distance / avgSpeed;
};

/**
 * Create a custom marker for a searched place
 * @param {Object} map - Google Maps instance
 * @param {Object} place - Place object with name, lat, lng
 * @param {Function} onClick - Click handler for the marker
 * @returns {Object} - Google Maps Marker instance
 */
export const createSearchMarker = (map, place, onClick) => {
  if (!map || !place || !window.google) return null;

  // Create marker
  const marker = new window.google.maps.Marker({
    position: { lat: place.lat, lng: place.lng },
    map: map,
    title: place.name,
    animation: window.google.maps.Animation.DROP,
    icon: {
      path: window.google.maps.SymbolPath.CIRCLE,
      scale: 12,
      fillColor: '#EF4444', // Red color for search results
      fillOpacity: 0.8,
      strokeWeight: 2,
      strokeColor: '#B91C1C',
    }
  });

  // Create info window
  const infoWindow = new window.google.maps.InfoWindow({
    content: `
      <div style="padding: 8px; max-width: 200px;">
        <h3 style="font-weight: bold; margin-bottom: 4px;">${place.name}</h3>
        <p style="font-size: 12px; margin-bottom: 8px;">${place.address || ''}</p>
        <button
          id="add-destination-btn"
          style="
            background-color: #4F46E5;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
          "
        >
          Add to Trip
        </button>
      </div>
    `
  });

  // Add click event
  marker.addListener('click', () => {
    infoWindow.open(map, marker);

    // Add click event to the Add button after info window is opened
    setTimeout(() => {
      const addButton = document.getElementById('add-destination-btn');
      if (addButton) {
        addButton.addEventListener('click', () => {
          if (onClick) onClick(place);
          infoWindow.close();
        });
      }
    }, 10);
  });

  return { marker, infoWindow };
};

/**
 * Calculate route between two points using Google Maps Directions API
 * @param {Object} origin - Origin coordinates { lat, lng }
 * @param {Object} destination - Destination coordinates { lat, lng }
 * @returns {Promise} - Promise that resolves with route details
 */
export const calculateRoute = (origin, destination) => {
  return new Promise((resolve, reject) => {
    if (!window.google || !window.google.maps) {
      reject(new Error('Google Maps not loaded'));
      return;
    }

    const directionsService = new window.google.maps.DirectionsService();

    directionsService.route(
      {
        origin: origin,
        destination: destination,
        travelMode: window.google.maps.TravelMode.DRIVING,
        unitSystem: window.google.maps.UnitSystem.METRIC
      },
      (result, status) => {
        if (status === window.google.maps.DirectionsStatus.OK) {
          const route = result.routes[0];
          const leg = route.legs[0];

          resolve({
            distance: leg.distance,
            duration: leg.duration,
            startAddress: leg.start_address,
            endAddress: leg.end_address,
            steps: leg.steps,
            result: result // Include full result for rendering
          });
        } else {
          reject(new Error(`Directions request failed: ${status}`));
        }
      }
    );
  });
};

/**
 * Calculate distance between multiple points using GoMaps.pro API
 * Enhanced for the trip planning system with additional features:
 * - Support for multi-destination trip planning
 * - Trip feasibility analysis
 * - Driver accommodation handling
 *
 * @param {string} origin - Origin address
 * @param {string} destination - Destination address
 * @param {Array} waypoints - Array of waypoint objects with location property
 * @param {Object} options - Additional options for trip planning
 * @param {string} options.startTime - Trip start time in 24-hour format (HH:MM)
 * @param {boolean} options.includeStopTime - Whether to include additional time at each stop (default: true)
 * @param {number} options.stopTimeHours - Hours to add for each stop (default: 3)
 * @returns {Promise} - Promise that resolves with distance details and trip feasibility analysis
 */
export const calculateGoMapsDistance = async (origin, destination, waypoints = [], options = {}) => {
  try {
    // Default options
    const {
      startTime = '09:00',
      includeStopTime = true,
      stopTimeHours = 3
    } = options;

    // Build the complete route addresses array in order: origin, waypoints, destination
    const waypointAddresses = waypoints.map(wp => wp.location || wp);

    // Create route addresses array in the correct order: origin -> waypoints -> destination
    // This is the key fix: we need to ensure the route follows the exact order specified by the user
    const routeAddresses = [origin];

    // Add all waypoints in order
    if (waypointAddresses.length > 0) {
      routeAddresses.push(...waypointAddresses);
    }

    // Add final destination if it's different from origin
    if (origin !== destination) {
      routeAddresses.push(destination);
    }

    console.log("Route addresses in order:", routeAddresses);

    // Create a single distance matrix request for all segments
    const allSegmentsUrl = "https://maps.gomaps.pro/maps/api/distancematrix/json" +
                        "?origins=" + encodeURIComponent(routeAddresses.join('|')) +
                        "&destinations=" + encodeURIComponent(routeAddresses.join('|')) +
                        "&mode=driving" +
                        "&key=" + GOMAPS_API_KEY;

    console.log("Fetching distance matrix for all segments");
    const response = await fetch(allSegmentsUrl);
    const data = await response.json();

    console.log("Distance matrix API response:", data);

    if (data.status === "OK") {
      // Process the distance matrix to calculate total distance
      let totalDistance = 0;
      let totalDuration = 0;
      let segments = [];
      let journeyBreakdown = '';
      let numStops = 0;

      console.log("Origin addresses:", data.origin_addresses);
      console.log("Destination addresses:", data.destination_addresses);
      console.log("Number of rows:", data.rows.length);

      // Create a mapping of addresses to their original indices
      const addressToIndex = {};
      routeAddresses.forEach((address, index) => {
        addressToIndex[address] = index;
      });

      // Process each segment of the journey in sequence
      // This ensures we calculate: Origin -> Dest1 -> Dest2 -> ... -> Final Dest
      for (let i = 0; i < routeAddresses.length - 1; i++) {
        // Get the element from the current point to the next point in sequence
        // The distance matrix is a 2D array where rows[i].elements[j] gives the distance from address i to address j
        const nextIndex = i + 1;
        const element = data.rows[i].elements[nextIndex];

        console.log(`Processing segment ${i}: ${routeAddresses[i]} to ${routeAddresses[nextIndex]}`);
        console.log(`Element status: ${element ? element.status : 'undefined'}`);

        if (element && element.status === "OK") {
          totalDistance += element.distance.value;
          totalDuration += element.duration.value;

          // Count this as a stop if it's not the final destination
          if (i < routeAddresses.length - 2) {
            numStops++;
          }

          // Format segment details for journey breakdown
          const segmentDistance = (element.distance.value / 1000).toFixed(1);
          const segmentDuration = Math.round(element.duration.value / 60);

          const fromLocation = data.origin_addresses[i];
          const toLocation = data.destination_addresses[nextIndex];

          // Determine if this is origin, a waypoint, or the final destination
          const isOrigin = i === 0;
          const isLastDestination = i === routeAddresses.length - 2;

          // For the journey breakdown, we need to handle the destination numbering correctly
          // The first segment is Origin -> Destination 1
          // The second segment is Destination 1 -> Destination 2, etc.
          const segmentIndex = i;
          const destinationNumber = segmentIndex + 1; // Destination numbers start at 1

          // Add origin point only for the first segment
          if (isOrigin) {
            journeyBreakdown += `<div class="journey-segment">
              <div class="journey-point"><i class="fas fa-map-marker-alt"></i> ${fromLocation}</div>`;
          }

          // Add the route segment arrow
          journeyBreakdown += `<div class="journey-arrow"><i class="fas fa-arrow-down"></i>${segmentDistance} km (${segmentDuration} mins)</div>`;

          // Add the destination point
          journeyBreakdown += `<div class="journey-point">${isLastDestination ?
            '<i class="fas fa-flag-checkered"></i>' :
            '<i class="fas fa-map-pin"></i>'
          } ${isLastDestination ? `Destination: ${toLocation}` : `Stop ${destinationNumber}: ${toLocation}`}</div>`;

          // Close the segment div if this is the last destination
          if (isLastDestination) {
            journeyBreakdown += `</div>`;
          }

          // Add segment to segments array with correct destination numbering
          segments.push({
            from: fromLocation,
            to: toLocation,
            destinationNumber: destinationNumber, // Always use the correct destination number
            isLastDestination: isLastDestination,
            distance: {
              text: `${segmentDistance} km`,
              value: element.distance.value
            },
            duration: {
              text: `${segmentDuration} mins`,
              value: element.duration.value
            }
          });
        } else {
          console.error(`Error calculating segment ${i}: Element status not OK or element is undefined`);
          return {
            success: false,
            error: `Failed to calculate route segment ${i+1}. Please try again with valid addresses.`
          };
        }
      }

      // Add distance summary section to journey breakdown
      journeyBreakdown += '<br><strong>Distance Summary:</strong><br>';
      journeyBreakdown += '<div class="journey-segment">';

      // Add each segment to the distance summary with correct labels
      segments.forEach((segment, index) => {
        // For the first segment (index 0), it's Origin to Stop 1
        // For subsequent segments, it's Stop N to Stop N+1 or Stop N to Destination
        const isLastSegment = index === segments.length - 1;
        const fromLabel = index === 0 ? 'Origin' : `Stop ${index}`;
        const toLabel = isLastSegment ? 'Destination' : `Stop ${index + 1}`;

        journeyBreakdown += `<div class="journey-point">${fromLabel} to ${toLabel}: ${segment.distance.text}</div>`;
      });

      // Add total distance
      journeyBreakdown += `<div class="journey-point" style="font-weight: bold; margin-top: 10px;">Total Distance: ${(totalDistance / 1000).toFixed(1)} km</div>`;
      journeyBreakdown += '</div>';

      // Convert to km and hours
      const totalDistanceKm = (totalDistance / 1000).toFixed(1);
      const totalDurationHours = (totalDuration / 60 / 60).toFixed(1);
      const totalDurationMins = Math.round(totalDuration / 60);

      // Calculate additional time for stops if requested
      let totalTripDurationMins = totalDurationMins;
      if (includeStopTime && numStops > 0) {
        const stopTimeMinutes = stopTimeHours * 60 * numStops;
        totalTripDurationMins += stopTimeMinutes;
      }

      // Format time in hours and minutes
      const hours = Math.floor(totalDurationMins / 60);
      const mins = totalDurationMins % 60;
      const timeText = hours > 0
        ? `${hours} hour${hours > 1 ? 's' : ''} ${mins > 0 ? mins + ' mins' : ''}`
        : `${mins} mins`;

      // Format total trip time including stops
      const tripHours = Math.floor(totalTripDurationMins / 60);
      const tripMins = totalTripDurationMins % 60;
      const tripTimeText = tripHours > 0
        ? `${tripHours} hour${tripHours > 1 ? 's' : ''} ${tripMins > 0 ? tripMins + ' mins' : ''}`
        : `${tripMins} mins`;

      // Calculate trip feasibility
      // Parse start time
      const [startHours, startMinutes] = startTime.split(':').map(Number);
      const startTimeMinutes = startHours * 60 + startMinutes;

      // Calculate end time in minutes since midnight
      const endTimeMinutes = startTimeMinutes + totalTripDurationMins;

      // 10:00 PM is 22 hours * 60 minutes = 1320 minutes since midnight
      const exceedsCutoffTime = endTimeMinutes > 1320;

      // Calculate how many days are needed
      const daysNeeded = exceedsCutoffTime
        ? Math.ceil(totalTripDurationMins / (12 * 60)) // Assuming 12 hours of travel per day
        : 1;

      // Format end time
      const endHours = Math.floor(endTimeMinutes / 60) % 24;
      const endMinutes = endTimeMinutes % 60;
      const endTimeFormatted = `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`;

      return {
        success: true,
        totalDistance: parseFloat(totalDistanceKm),
        totalDistanceText: `${totalDistanceKm} km`,
        totalDuration: parseFloat(totalDurationHours),
        totalDurationMins: totalDurationMins,
        totalDurationText: timeText,
        // Trip planning specific data
        tripDetails: {
          numStops,
          stopTimeHours,
          totalTripDurationMins,
          totalTripDurationText: tripTimeText,
          startTime,
          endTime: endTimeFormatted,
          exceedsCutoffTime,
          daysNeeded
        },
        segments,
        journeyBreakdown,
        addresses: {
          origins: data.origin_addresses,
          destinations: data.destination_addresses
        },
        // Add route property for compatibility with calculateGoMapsRoute
        route: {
          routes: [{
            legs: segments.map(segment => ({
              distance: { text: segment.distance.text, value: segment.distance.value },
              duration: { text: segment.duration.text, value: segment.duration.value },
              start_address: segment.from,
              end_address: segment.to
            }))
          }]
        }
      };
    } else {
      console.error('GoMaps.pro distance matrix request failed:', data.status);
      return {
        success: false,
        error: `GoMaps.pro API error: ${data.status}`
      };
    }
  } catch (error) {
    console.error('Error fetching GoMaps.pro distance matrix:', error);
    return {
      success: false,
      error: 'Failed to calculate distance. Please try again.'
    };
  }
};
