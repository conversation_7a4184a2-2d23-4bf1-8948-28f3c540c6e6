/**
 * Utility functions for the new multi-destination trip planning
 * Based on the GoMaps.pro API
 */

// Sri Lanka coordinates
export const SRI_LANKA_CENTER = { lat: 7.8731, lng: 80.7718 };

// GoMaps.pro API key
export const GOMAPS_API_KEY = "AlzaSyiDEaUjW-MreVKn_7pW3N5QavKJqVUbGaX";

// GoMaps.pro API URL with callback
export const GOMAPS_API_URL = "https://maps.gomaps.pro/maps/api/js?key=AlzaSyoAnnH_UH2-gZFeNfRVYc8E8-iR1r88lLEY&libraries=places,drawing&callback=initAutocomplete";

// Map marker icons
export const MARKER_ICONS = {
  ORIGIN: "https://maps.gomaps.pro/mapfiles/ms/icons/green-dot.png",
  DESTINATION: "https://maps.gomaps.pro/mapfiles/ms/icons/red-dot.png",
  WAYPOINT: "https://maps.gomaps.pro/mapfiles/ms/icons/yellow-dot.png",
  USER_LOCATION: "https://maps.gomaps.pro/mapfiles/ms/icons/blue-dot.png"
};

/**
 * Calculate distance between two coordinates using Haversine formula
 * @param {Object} coord1 - The first coordinate { lat, lng }
 * @param {Object} coord2 - The second coordinate { lat, lng }
 * @returns {number} - The distance in kilometers
 */
export const calculateDistance = (coord1, coord2) => {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(coord2.lat - coord1.lat);
  const dLon = deg2rad(coord2.lng - coord1.lng);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(coord1.lat)) * Math.cos(deg2rad(coord2.lat)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in km
  return distance;
};

/**
 * Convert degrees to radians
 * @param {number} deg - Degrees
 * @returns {number} - Radians
 */
const deg2rad = (deg) => {
  return deg * (Math.PI/180);
};

/**
 * Estimate travel time between two coordinates
 * @param {Object} coord1 - The first coordinate { lat, lng }
 * @param {Object} coord2 - The second coordinate { lat, lng }
 * @param {number} avgSpeed - Average speed in km/h (default: 40)
 * @returns {number} - The travel time in hours
 */
export const estimateTravelTime = (coord1, coord2, avgSpeed = 40) => {
  const distance = calculateDistance(coord1, coord2);
  return distance / avgSpeed;
};

/**
 * Calculate route between two points using GoMaps.pro API
 * @param {string} origin - Origin address or coordinates
 * @param {string} destination - Destination address or coordinates
 * @param {Array} waypoints - Array of waypoint addresses or coordinates
 * @returns {Promise} - Promise that resolves with route details
 */
export const calculateGoMapsRoute = async (origin, destination, waypoints = []) => {
  try {
    console.log("Calculating route from", origin, "to", destination, "with waypoints:", waypoints);

    // Ensure Google Maps API is loaded
    if (!window.google || !window.google.maps) {
      throw new Error('Google Maps API not loaded');
    }

    // Validate inputs
    if (!origin || !destination) {
      throw new Error('Origin and destination are required');
    }

    // Filter out empty waypoints
    const validWaypoints = waypoints.filter(wp => wp && wp.trim() !== '');
    console.log("Valid waypoints:", validWaypoints);

    // Create directions service
    const directionsService = new window.google.maps.DirectionsService();

    // Format waypoints for the directions request
    const formattedWaypoints = validWaypoints.map(waypoint => ({
      location: waypoint,
      stopover: true
    }));

    // Create the request
    const request = {
      origin: origin,
      destination: destination,
      waypoints: formattedWaypoints,
      optimizeWaypoints: false, // Don't reorder waypoints
      travelMode: window.google.maps.TravelMode.DRIVING,
      unitSystem: window.google.maps.UnitSystem.METRIC,
      provideRouteAlternatives: false // Don't get alternative routes to ensure we get the exact route we want
    };

    console.log("Directions request:", JSON.stringify(request, null, 2));

    // Make the request
    return new Promise((resolve, reject) => {
      directionsService.route(request, (result, status) => {
        if (status === window.google.maps.DirectionsStatus.OK) {
          console.log("Route calculation successful, routes:", result.routes.length);

          // Process the result
          const route = result.routes[0];

          // Calculate total distance and duration
          let totalDistance = 0;
          let totalDuration = 0;
          const segments = [];

          // Build journey breakdown HTML
          let journeyBreakdown = '<strong>Journey Breakdown:</strong><br>';

          // Add origin
          journeyBreakdown += '<div class="journey-segment">';

          // Process each leg of the journey
          route.legs.forEach((leg, index) => {
            totalDistance += leg.distance.value;
            totalDuration += leg.duration.value;

            // Format segment details
            const segmentDistance = (leg.distance.value / 1000).toFixed(1);
            const segmentDuration = Math.round(leg.duration.value / 60);
            const fromLocation = leg.start_address;
            const toLocation = leg.end_address;

            // Add to journey breakdown HTML
            if (index === 0) {
              // First leg - origin
              journeyBreakdown += `<div class="journey-point"><i class="fas fa-map-marker-alt"></i> ${fromLocation}</div>`;
            }

            // Add the route segment
            journeyBreakdown += `<div class="journey-arrow"><i class="fas fa-long-arrow-alt-down"></i> ${segmentDistance} km (${segmentDuration} mins)</div>`;

            // Add the destination
            const isLastDestination = index === route.legs.length - 1;
            const destinationNumber = index + 1;
            const pointIcon = isLastDestination
              ? '<i class="fas fa-flag-checkered"></i>'
              : `<i class="fas fa-map-pin"></i>`;

            journeyBreakdown += `<div class="journey-point">${pointIcon} Destination ${destinationNumber}: ${toLocation}</div>`;

            // Add to segments array
            segments.push({
              from: fromLocation,
              to: toLocation,
              distance: {
                text: `${segmentDistance} km`,
                value: leg.distance.value
              },
              duration: {
                text: `${segmentDuration} mins`,
                value: leg.duration.value
              }
            });
          });

          journeyBreakdown += '</div>';

          // Add distance summary
          journeyBreakdown += '<br><strong>Distance Summary:</strong><br>';
          journeyBreakdown += '<div class="journey-segment">';

          // Origin to first destination
          if (route.legs.length > 0) {
            const firstLegDistance = (route.legs[0].distance.value / 1000).toFixed(1);
            journeyBreakdown += `<div class="journey-point">Origin to Destination 1: ${firstLegDistance} km</div>`;
          }

          // Between destinations
          for (let i = 1; i < route.legs.length; i++) {
            const legDistance = (route.legs[i].distance.value / 1000).toFixed(1);
            const fromDestNum = i;
            const toDestNum = i + 1;

            journeyBreakdown += `<div class="journey-point">Destination ${fromDestNum} to Destination ${toDestNum}: ${legDistance} km</div>`;
          }

          // Total distance confirmation
          journeyBreakdown += `<div class="journey-point" style="font-weight: bold; margin-top: 10px;">Total Distance: ${(totalDistance / 1000).toFixed(1)} km</div>`;
          journeyBreakdown += '</div>';

          // Convert to km and hours/minutes
          const totalDistanceKm = (totalDistance / 1000).toFixed(1);
          const totalDurationMins = Math.round(totalDuration / 60);

          // Format time in hours and minutes
          const hours = Math.floor(totalDurationMins / 60);
          const mins = totalDurationMins % 60;
          const timeText = hours > 0
            ? `${hours} hour${hours > 1 ? 's' : ''} ${mins > 0 ? ' ' + mins + ' mins' : ''}`
            : `${mins} mins`;

          console.log(`Total distance: ${totalDistanceKm} km, duration: ${timeText}`);

          // Resolve with route details
          resolve({
            success: true,
            totalDistance: parseFloat(totalDistanceKm),
            totalDistanceText: `${totalDistanceKm} km`,
            totalDuration: totalDuration / 3600, // Convert to hours
            totalDurationMins: totalDurationMins,
            totalDurationText: timeText,
            journeyBreakdown,
            segments,
            route: result // Include full result for rendering
          });
        } else {
          console.error('Directions request failed:', status);
          reject(new Error(`Directions request failed: ${status}`));
        }
      });
    });
  } catch (error) {
    console.error('Error calculating route:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
