# Siyoga Travel Booking System

A simple and understandable travel booking system built with React and Node.js.

## Project Structure

```
Siyoga/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/         # Page components
│   │   ├── context/       # React context (auth, etc.)
│   │   ├── utils/         # Helper functions
│   │   └── styles/        # CSS files
│   └── package.json
├── server/                # Node.js backend
│   ├── src/
│   │   ├── routes/        # API routes
│   │   ├── models/        # Database models
│   │   ├── middleware/    # Custom middleware
│   │   ├── utils/         # Helper functions
│   │   └── config/        # Configuration files
│   └── package.json
├── database/              # Database scripts
│   ├── schema.sql         # Database schema
│   └── seeds/            # Sample data
└── docs/                 # Documentation
```

## Features

### Phase 1: Authentication System
- [x] User Registration (Tourist)
- [x] Email Verification
- [x] User Login with JWT
- [ ] Driver Registration
- [ ] Admin Panel

### Phase 2: Trip Planning
- [ ] Destination Management
- [ ] Trip Planning Interface
- [ ] Distance Calculation

### Phase 3: Booking System
- [ ] Driver Matching
- [ ] Booking Requests
- [ ] Trip Execution

## Technology Stack

- **Frontend**: React.js with functional components and hooks
- **Backend**: Node.js with Express.js
- **Database**: MySQL
- **Authentication**: JWT tokens
- **File Upload**: Multer for document uploads
- **Email**: Nodemailer for email verification

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- MySQL/phpMyAdmin
- Git

### Installation

1. Clone the repository
2. Install backend dependencies:
   ```bash
   cd server
   npm install
   ```
3. Install frontend dependencies:
   ```bash
   cd client
   npm install
   ```
4. Setup database using the SQL scripts in `/database/schema.sql`
5. Configure environment variables in `/server/.env`
6. Start the development servers:
   ```bash
   # Backend (from server directory)
   npm run dev
   
   # Frontend (from client directory)
   npm start
   ```

## Development Guidelines

1. **Simple Structure**: Each feature in its own clearly named folder
2. **Clear Naming**: Descriptive function and variable names
3. **Minimal Dependencies**: Only essential packages
4. **Consistent Patterns**: Same structure for all CRUD operations
5. **Comprehensive Comments**: Explain business logic clearly
