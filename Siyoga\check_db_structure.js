const mysql = require('mysql2/promise');

async function checkDatabaseStructure() {
  try {
    console.log('🔍 Checking Database Structure...\n');

    const dbConfig = {
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'siyoga_travel_booking',
      port: 3306
    };

    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');

    // Check existing tables
    console.log('\n📋 Existing Tables:');
    const [tables] = await connection.execute('SHOW TABLES');
    tables.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });

    // Check vehicles table structure
    console.log('\n🚗 Vehicles Table Structure:');
    try {
      const [vehicleColumns] = await connection.execute('DESCRIBE vehicles');
      vehicleColumns.forEach(col => {
        console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? '(Required)' : '(Optional)'}`);
      });
    } catch (error) {
      console.log('  ❌ Vehicles table does not exist');
    }

    // Check drivers table structure
    console.log('\n👤 Drivers Table Structure:');
    try {
      const [driverColumns] = await connection.execute('DESCRIBE drivers');
      driverColumns.forEach(col => {
        console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? '(Required)' : '(Optional)'}`);
      });
    } catch (error) {
      console.log('  ❌ Drivers table does not exist');
    }

    // Check bookings table structure
    console.log('\n📝 Bookings Table Structure:');
    try {
      const [bookingColumns] = await connection.execute('DESCRIBE bookings');
      bookingColumns.forEach(col => {
        console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? '(Required)' : '(Optional)'}`);
      });
    } catch (error) {
      console.log('  ❌ Bookings table does not exist');
    }

    await connection.end();
    console.log('\n✅ Database structure check completed!');

  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  }
}

checkDatabaseStructure();
