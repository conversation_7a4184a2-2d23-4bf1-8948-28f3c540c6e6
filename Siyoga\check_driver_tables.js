const mysql = require('mysql2/promise');

async function checkDriverTables() {
  try {
    console.log('🔍 Checking Driver Tables...\n');

    const dbConfig = {
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'siyoga_travel_booking',
      port: 3306
    };

    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');

    // Check if vehicles table exists
    console.log('\n📋 Checking vehicles table...');
    try {
      const [vehiclesStructure] = await connection.execute('DESCRIBE vehicles');
      console.log('✅ Vehicles table exists with columns:');
      vehiclesStructure.forEach(col => {
        console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? '(Required)' : '(Optional)'}`);
      });
    } catch (error) {
      console.log('❌ Vehicles table does not exist');
      console.log('Need to create vehicles table');
    }

    // Check drivers table
    console.log('\n📋 Checking drivers table...');
    try {
      const [driversStructure] = await connection.execute('DESCRIBE drivers');
      console.log('✅ Drivers table exists with columns:');
      driversStructure.forEach(col => {
        console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? '(Required)' : '(Optional)'}`);
      });
    } catch (error) {
      console.log('❌ Drivers table does not exist');
    }

    // Check bookings table structure
    console.log('\n📋 Checking bookings table...');
    const [bookingsStructure] = await connection.execute('DESCRIBE bookings');
    console.log('✅ Bookings table exists with columns:');
    bookingsStructure.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? '(Required)' : '(Optional)'}`);
    });

    // Check vehicle categories
    console.log('\n📋 Vehicle Categories:');
    const [categories] = await connection.execute('SELECT category_id, category_name, vehicle_type FROM vehicle_categories');
    categories.forEach(cat => {
      console.log(`  - ID ${cat.category_id}: ${cat.category_name} (${cat.vehicle_type})`);
    });

    await connection.end();
    console.log('\n✅ Database check completed!');

  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  }
}

checkDriverTables();
