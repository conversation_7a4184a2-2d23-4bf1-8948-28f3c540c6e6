const mysql = require('mysql2/promise');
const bcrypt = require('./server/node_modules/bcryptjs');

async function checkDriverUser() {
  try {
    console.log('🔍 Checking Driver User...\n');

    const dbConfig = {
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'siyoga_travel_booking',
      port: 3306
    };

    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');

    // Check if driver user exists
    const [users] = await connection.execute(
      'SELECT user_id, email, role, is_verified, is_active FROM users WHERE email = ?',
      ['<EMAIL>']
    );

    if (users.length > 0) {
      const user = users[0];
      console.log('👤 Driver user found:');
      console.log('   - User ID:', user.user_id);
      console.log('   - Email:', user.email);
      console.log('   - Role:', user.role);
      console.log('   - Verified:', user.is_verified ? 'Yes' : 'No');
      console.log('   - Active:', user.is_active ? 'Yes' : 'No');

      // Check driver profile
      const [drivers] = await connection.execute(
        'SELECT * FROM drivers WHERE user_id = ?',
        [user.user_id]
      );

      if (drivers.length > 0) {
        const driver = drivers[0];
        console.log('\n🚗 Driver profile found:');
        console.log('   - Driver ID:', driver.driver_id);
        console.log('   - Name:', driver.first_name, driver.last_name);
        console.log('   - Phone:', driver.phone);
        console.log('   - Status:', driver.status);
      } else {
        console.log('\n❌ Driver profile not found');
      }

      // Test password
      console.log('\n🔑 Testing password...');
      const [passwordResult] = await connection.execute(
        'SELECT password FROM users WHERE email = ?',
        ['<EMAIL>']
      );

      if (passwordResult.length > 0) {
        const isValid = await bcrypt.compare('password123', passwordResult[0].password);
        console.log('   - Password valid:', isValid ? 'Yes' : 'No');
        
        if (!isValid) {
          console.log('\n🔧 Updating password...');
          const hashedPassword = await bcrypt.hash('password123', 10);
          await connection.execute(
            'UPDATE users SET password = ? WHERE email = ?',
            [hashedPassword, '<EMAIL>']
          );
          console.log('✅ Password updated');
        }
      }

    } else {
      console.log('❌ Driver user not found. Creating...');
      
      // Create driver user
      const hashedPassword = await bcrypt.hash('password123', 10);
      const [userResult] = await connection.execute(`
        INSERT INTO users (email, password, role, is_verified, is_active)
        VALUES (?, ?, 'driver', 1, 1)
      `, ['<EMAIL>', hashedPassword]);

      const userId = userResult.insertId;
      console.log(`✅ Created user account with ID: ${userId}`);

      // Create driver profile
      const [driverResult] = await connection.execute(`
        INSERT INTO drivers (
          user_id, first_name, last_name, phone, nic_number, 
          license_number, license_expiry, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'approved')
      `, [
        userId,
        'Saman',
        'Perera',
        '**********',
        '123456789V',
        '********',
        '2025-12-31'
      ]);

      console.log(`✅ Created driver profile with ID: ${driverResult.insertId}`);
    }

    await connection.end();
    console.log('\n✅ Driver user check completed!');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkDriverUser();
