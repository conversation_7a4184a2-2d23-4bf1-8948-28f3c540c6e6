const mysql = require('mysql2/promise');

async function checkVehiclesTable() {
  try {
    console.log('🔍 Checking vehicles table structure...\n');

    const dbConfig = {
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'siyoga_travel_booking',
      port: 3306
    };

    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');

    // Check table structure
    const [columns] = await connection.execute('DESCRIBE vehicles');
    console.log('\n📋 Vehicles table columns:');
    columns.forEach(col => {
      console.log(`- ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(not null)'} ${col.Key ? `[${col.Key}]` : ''}`);
    });

    await connection.end();

  } catch (error) {
    console.error('❌ Error checking vehicles table:', error.message);
  }
}

checkVehiclesTable();
