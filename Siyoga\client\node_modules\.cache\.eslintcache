[{"D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\index.js": "1", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\App.js": "2", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\Login.js": "3", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\Register.js": "4", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\Dashboard.js": "5", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\context\\AuthContext.js": "6", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\VerifyOTP.js": "7", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\TripPlanner.js": "8", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\utils\\mapUtils.js": "9", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\components\\LoadingSpinner.js": "10", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\components\\Toast.js": "11"}, {"size": 232, "mtime": 1751363693037, "results": "12", "hashOfConfig": "13"}, {"size": 2195, "mtime": 1751370698061, "results": "14", "hashOfConfig": "13"}, {"size": 2172, "mtime": 1751363748355, "results": "15", "hashOfConfig": "13"}, {"size": 5681, "mtime": 1751365907764, "results": "16", "hashOfConfig": "13"}, {"size": 8031, "mtime": 1751366896782, "results": "17", "hashOfConfig": "13"}, {"size": 2742, "mtime": 1751365278086, "results": "18", "hashOfConfig": "13"}, {"size": 5502, "mtime": 1751365874951, "results": "19", "hashOfConfig": "13"}, {"size": 29363, "mtime": 1751370139244, "results": "20", "hashOfConfig": "13"}, {"size": 9846, "mtime": 1751370500531, "results": "21", "hashOfConfig": "13"}, {"size": 896, "mtime": 1751367817368, "results": "22", "hashOfConfig": "13"}, {"size": 2792, "mtime": 1751368387348, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jw41da", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\index.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\App.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\Login.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\Register.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\Dashboard.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\context\\AuthContext.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\VerifyOTP.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\TripPlanner.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\utils\\mapUtils.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\components\\LoadingSpinner.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\components\\Toast.js", [], []]