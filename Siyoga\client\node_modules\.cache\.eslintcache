[{"D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\index.js": "1", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\App.js": "2", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\Login.js": "3", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\Register.js": "4", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\Dashboard.js": "5", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\context\\AuthContext.js": "6", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\VerifyOTP.js": "7", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\TripPlanner.js": "8", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\utils\\mapUtils.js": "9", "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\components\\LoadingSpinner.js": "10"}, {"size": 232, "mtime": 1751363693037, "results": "11", "hashOfConfig": "12"}, {"size": 2486, "mtime": 1751367616604, "results": "13", "hashOfConfig": "12"}, {"size": 2172, "mtime": 1751363748355, "results": "14", "hashOfConfig": "12"}, {"size": 5681, "mtime": 1751365907764, "results": "15", "hashOfConfig": "12"}, {"size": 8031, "mtime": 1751366896782, "results": "16", "hashOfConfig": "12"}, {"size": 2742, "mtime": 1751365278086, "results": "17", "hashOfConfig": "12"}, {"size": 5502, "mtime": 1751365874951, "results": "18", "hashOfConfig": "12"}, {"size": 24532, "mtime": 1751367922096, "results": "19", "hashOfConfig": "12"}, {"size": 8880, "mtime": 1751367432458, "results": "20", "hashOfConfig": "12"}, {"size": 896, "mtime": 1751367817368, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jw41da", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\index.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\App.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\Login.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\Register.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\Dashboard.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\context\\AuthContext.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\VerifyOTP.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\pages\\TripPlanner.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\utils\\mapUtils.js", [], [], "D:\\Projects\\Siyoga SDP\\Simple Project\\Siyoga\\client\\src\\components\\LoadingSpinner.js", [], []]