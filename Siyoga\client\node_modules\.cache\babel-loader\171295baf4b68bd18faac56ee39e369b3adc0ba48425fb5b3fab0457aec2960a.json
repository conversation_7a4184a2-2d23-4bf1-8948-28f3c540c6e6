{"ast": null, "code": "'use strict';\n\nvar aCallable = require('../internals/a-callable');\nvar isNullOrUndefined = require('../internals/is-null-or-undefined');\n\n// `GetMethod` abstract operation\n// https://tc39.es/ecma262/#sec-getmethod\nmodule.exports = function (V, P) {\n  var func = V[P];\n  return isNullOrUndefined(func) ? undefined : aCallable(func);\n};", "map": {"version": 3, "names": ["aCallable", "require", "isNullOrUndefined", "module", "exports", "V", "P", "func", "undefined"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/Siyoga/client/node_modules/core-js-pure/internals/get-method.js"], "sourcesContent": ["'use strict';\nvar aCallable = require('../internals/a-callable');\nvar isNullOrUndefined = require('../internals/is-null-or-undefined');\n\n// `GetMethod` abstract operation\n// https://tc39.es/ecma262/#sec-getmethod\nmodule.exports = function (V, P) {\n  var func = V[P];\n  return isNullOrUndefined(func) ? undefined : aCallable(func);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAGC,OAAO,CAAC,yBAAyB,CAAC;AAClD,IAAIC,iBAAiB,GAAGD,OAAO,CAAC,mCAAmC,CAAC;;AAEpE;AACA;AACAE,MAAM,CAACC,OAAO,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAC/B,IAAIC,IAAI,GAAGF,CAAC,CAACC,CAAC,CAAC;EACf,OAAOJ,iBAAiB,CAACK,IAAI,CAAC,GAAGC,SAAS,GAAGR,SAAS,CAACO,IAAI,CAAC;AAC9D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}