{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\Siyoga\\\\client\\\\src\\\\components\\\\Toast.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\n\n// Simple toast notification system\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nlet toastId = 0;\nconst toastCallbacks = [];\nexport const toast = {\n  success: message => showToast(message, 'success'),\n  error: message => showToast(message, 'error'),\n  info: message => showToast(message, 'info'),\n  warning: message => showToast(message, 'warning')\n};\nconst showToast = (message, type) => {\n  const id = ++toastId;\n  const toastData = {\n    id,\n    message,\n    type,\n    timestamp: Date.now()\n  };\n  toastCallbacks.forEach(callback => callback(toastData));\n\n  // Auto remove after 3 seconds\n  setTimeout(() => {\n    toastCallbacks.forEach(callback => callback({\n      id,\n      remove: true\n    }));\n  }, 3000);\n};\nexport const ToastContainer = () => {\n  _s();\n  const [toasts, setToasts] = useState([]);\n  useEffect(() => {\n    const handleToast = toastData => {\n      if (toastData.remove) {\n        setToasts(prev => prev.filter(t => t.id !== toastData.id));\n      } else {\n        setToasts(prev => [...prev, toastData]);\n      }\n    };\n    toastCallbacks.push(handleToast);\n    return () => {\n      const index = toastCallbacks.indexOf(handleToast);\n      if (index > -1) {\n        toastCallbacks.splice(index, 1);\n      }\n    };\n  }, []);\n  const removeToast = id => {\n    setToasts(prev => prev.filter(t => t.id !== id));\n  };\n  const getToastStyles = type => {\n    const baseStyles = {\n      padding: '12px 16px',\n      marginBottom: '8px',\n      borderRadius: '6px',\n      color: 'white',\n      fontWeight: '500',\n      fontSize: '14px',\n      cursor: 'pointer',\n      transition: 'all 0.3s ease',\n      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n      position: 'relative',\n      overflow: 'hidden'\n    };\n    const typeStyles = {\n      success: {\n        background: '#10b981'\n      },\n      error: {\n        background: '#ef4444'\n      },\n      info: {\n        background: '#3b82f6'\n      },\n      warning: {\n        background: '#f59e0b'\n      }\n    };\n    return {\n      ...baseStyles,\n      ...typeStyles[type]\n    };\n  };\n  if (toasts.length === 0) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: '20px',\n      right: '20px',\n      zIndex: 9999,\n      maxWidth: '400px',\n      width: '100%'\n    },\n    children: toasts.map(toast => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: getToastStyles(toast.type),\n      onClick: () => removeToast(toast.id),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: toast.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginLeft: '12px',\n            fontSize: '16px',\n            opacity: 0.8,\n            cursor: 'pointer'\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 11\n      }, this)\n    }, toast.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n};\n_s(ToastContainer, \"oL0MrtDCqig+amxuKH2EOlnBcjg=\");\n_c = ToastContainer;\nexport default {\n  toast,\n  ToastContainer\n};\nvar _c;\n$RefreshReg$(_c, \"ToastContainer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "toastId", "toastCallbacks", "toast", "success", "message", "showToast", "error", "info", "warning", "type", "id", "toastData", "timestamp", "Date", "now", "for<PERSON>ach", "callback", "setTimeout", "remove", "ToastContainer", "_s", "toasts", "setToasts", "handleToast", "prev", "filter", "t", "push", "index", "indexOf", "splice", "removeToast", "getToastStyles", "baseStyles", "padding", "marginBottom", "borderRadius", "color", "fontWeight", "fontSize", "cursor", "transition", "boxShadow", "position", "overflow", "typeStyles", "background", "length", "style", "top", "right", "zIndex", "max<PERSON><PERSON><PERSON>", "width", "children", "map", "onClick", "display", "alignItems", "justifyContent", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginLeft", "opacity", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/Siyoga/client/src/components/Toast.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\n// Simple toast notification system\nlet toastId = 0;\nconst toastCallbacks = [];\n\nexport const toast = {\n  success: (message) => showToast(message, 'success'),\n  error: (message) => showToast(message, 'error'),\n  info: (message) => showToast(message, 'info'),\n  warning: (message) => showToast(message, 'warning')\n};\n\nconst showToast = (message, type) => {\n  const id = ++toastId;\n  const toastData = { id, message, type, timestamp: Date.now() };\n  \n  toastCallbacks.forEach(callback => callback(toastData));\n  \n  // Auto remove after 3 seconds\n  setTimeout(() => {\n    toastCallbacks.forEach(callback => callback({ id, remove: true }));\n  }, 3000);\n};\n\nexport const ToastContainer = () => {\n  const [toasts, setToasts] = useState([]);\n\n  useEffect(() => {\n    const handleToast = (toastData) => {\n      if (toastData.remove) {\n        setToasts(prev => prev.filter(t => t.id !== toastData.id));\n      } else {\n        setToasts(prev => [...prev, toastData]);\n      }\n    };\n\n    toastCallbacks.push(handleToast);\n\n    return () => {\n      const index = toastCallbacks.indexOf(handleToast);\n      if (index > -1) {\n        toastCallbacks.splice(index, 1);\n      }\n    };\n  }, []);\n\n  const removeToast = (id) => {\n    setToasts(prev => prev.filter(t => t.id !== id));\n  };\n\n  const getToastStyles = (type) => {\n    const baseStyles = {\n      padding: '12px 16px',\n      marginBottom: '8px',\n      borderRadius: '6px',\n      color: 'white',\n      fontWeight: '500',\n      fontSize: '14px',\n      cursor: 'pointer',\n      transition: 'all 0.3s ease',\n      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n      position: 'relative',\n      overflow: 'hidden'\n    };\n\n    const typeStyles = {\n      success: { background: '#10b981' },\n      error: { background: '#ef4444' },\n      info: { background: '#3b82f6' },\n      warning: { background: '#f59e0b' }\n    };\n\n    return { ...baseStyles, ...typeStyles[type] };\n  };\n\n  if (toasts.length === 0) return null;\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: '20px',\n      right: '20px',\n      zIndex: 9999,\n      maxWidth: '400px',\n      width: '100%'\n    }}>\n      {toasts.map(toast => (\n        <div\n          key={toast.id}\n          style={getToastStyles(toast.type)}\n          onClick={() => removeToast(toast.id)}\n        >\n          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n            <span>{toast.message}</span>\n            <span style={{ \n              marginLeft: '12px', \n              fontSize: '16px',\n              opacity: 0.8,\n              cursor: 'pointer'\n            }}>\n              ×\n            </span>\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default { toast, ToastContainer };\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;;AAElD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,IAAIC,OAAO,GAAG,CAAC;AACf,MAAMC,cAAc,GAAG,EAAE;AAEzB,OAAO,MAAMC,KAAK,GAAG;EACnBC,OAAO,EAAGC,OAAO,IAAKC,SAAS,CAACD,OAAO,EAAE,SAAS,CAAC;EACnDE,KAAK,EAAGF,OAAO,IAAKC,SAAS,CAACD,OAAO,EAAE,OAAO,CAAC;EAC/CG,IAAI,EAAGH,OAAO,IAAKC,SAAS,CAACD,OAAO,EAAE,MAAM,CAAC;EAC7CI,OAAO,EAAGJ,OAAO,IAAKC,SAAS,CAACD,OAAO,EAAE,SAAS;AACpD,CAAC;AAED,MAAMC,SAAS,GAAGA,CAACD,OAAO,EAAEK,IAAI,KAAK;EACnC,MAAMC,EAAE,GAAG,EAAEV,OAAO;EACpB,MAAMW,SAAS,GAAG;IAAED,EAAE;IAAEN,OAAO;IAAEK,IAAI;IAAEG,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;EAAE,CAAC;EAE9Db,cAAc,CAACc,OAAO,CAACC,QAAQ,IAAIA,QAAQ,CAACL,SAAS,CAAC,CAAC;;EAEvD;EACAM,UAAU,CAAC,MAAM;IACfhB,cAAc,CAACc,OAAO,CAACC,QAAQ,IAAIA,QAAQ,CAAC;MAAEN,EAAE;MAAEQ,MAAM,EAAE;IAAK,CAAC,CAAC,CAAC;EACpE,CAAC,EAAE,IAAI,CAAC;AACV,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd,MAAM0B,WAAW,GAAIZ,SAAS,IAAK;MACjC,IAAIA,SAAS,CAACO,MAAM,EAAE;QACpBI,SAAS,CAACE,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,EAAE,KAAKC,SAAS,CAACD,EAAE,CAAC,CAAC;MAC5D,CAAC,MAAM;QACLY,SAAS,CAACE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEb,SAAS,CAAC,CAAC;MACzC;IACF,CAAC;IAEDV,cAAc,CAAC0B,IAAI,CAACJ,WAAW,CAAC;IAEhC,OAAO,MAAM;MACX,MAAMK,KAAK,GAAG3B,cAAc,CAAC4B,OAAO,CAACN,WAAW,CAAC;MACjD,IAAIK,KAAK,GAAG,CAAC,CAAC,EAAE;QACd3B,cAAc,CAAC6B,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,WAAW,GAAIrB,EAAE,IAAK;IAC1BY,SAAS,CAACE,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,EAAE,KAAKA,EAAE,CAAC,CAAC;EAClD,CAAC;EAED,MAAMsB,cAAc,GAAIvB,IAAI,IAAK;IAC/B,MAAMwB,UAAU,GAAG;MACjBC,OAAO,EAAE,WAAW;MACpBC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,KAAK;MACnBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,eAAe;MAC3BC,SAAS,EAAE,6BAA6B;MACxCC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;IACZ,CAAC;IAED,MAAMC,UAAU,GAAG;MACjB1C,OAAO,EAAE;QAAE2C,UAAU,EAAE;MAAU,CAAC;MAClCxC,KAAK,EAAE;QAAEwC,UAAU,EAAE;MAAU,CAAC;MAChCvC,IAAI,EAAE;QAAEuC,UAAU,EAAE;MAAU,CAAC;MAC/BtC,OAAO,EAAE;QAAEsC,UAAU,EAAE;MAAU;IACnC,CAAC;IAED,OAAO;MAAE,GAAGb,UAAU;MAAE,GAAGY,UAAU,CAACpC,IAAI;IAAE,CAAC;EAC/C,CAAC;EAED,IAAIY,MAAM,CAAC0B,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAEpC,oBACEhD,OAAA;IAAKiD,KAAK,EAAE;MACVL,QAAQ,EAAE,OAAO;MACjBM,GAAG,EAAE,MAAM;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,OAAO;MACjBC,KAAK,EAAE;IACT,CAAE;IAAAC,QAAA,EACCjC,MAAM,CAACkC,GAAG,CAACrD,KAAK,iBACfH,OAAA;MAEEiD,KAAK,EAAEhB,cAAc,CAAC9B,KAAK,CAACO,IAAI,CAAE;MAClC+C,OAAO,EAAEA,CAAA,KAAMzB,WAAW,CAAC7B,KAAK,CAACQ,EAAE,CAAE;MAAA4C,QAAA,eAErCvD,OAAA;QAAKiD,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAL,QAAA,gBACrFvD,OAAA;UAAAuD,QAAA,EAAOpD,KAAK,CAACE;QAAO;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5BhE,OAAA;UAAMiD,KAAK,EAAE;YACXgB,UAAU,EAAE,MAAM;YAClBzB,QAAQ,EAAE,MAAM;YAChB0B,OAAO,EAAE,GAAG;YACZzB,MAAM,EAAE;UACV,CAAE;UAAAc,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC,GAdD7D,KAAK,CAACQ,EAAE;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAeV,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAnFWD,cAAc;AAAA+C,EAAA,GAAd/C,cAAc;AAqF3B,eAAe;EAAEjB,KAAK;EAAEiB;AAAe,CAAC;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}