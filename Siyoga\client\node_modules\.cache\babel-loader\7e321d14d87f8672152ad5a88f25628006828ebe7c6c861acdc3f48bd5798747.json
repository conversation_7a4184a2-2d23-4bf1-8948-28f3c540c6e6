{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\Siyoga\\\\client\\\\src\\\\components\\\\LoadingSpinner.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = ({\n  message = 'Loading...',\n  size = 'medium'\n}) => {\n  const sizeStyles = {\n    small: {\n      width: '20px',\n      height: '20px'\n    },\n    medium: {\n      width: '40px',\n      height: '40px'\n    },\n    large: {\n      width: '60px',\n      height: '60px'\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '20px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"spinner\",\n      style: {\n        ...sizeStyles[size],\n        border: '4px solid #f3f3f3',\n        borderTop: '4px solid #667eea',\n        borderRadius: '50%',\n        marginBottom: '10px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        color: '#666',\n        fontSize: '14px',\n        margin: 0,\n        textAlign: 'center'\n      },\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LoadingSpinner", "message", "size", "sizeStyles", "small", "width", "height", "medium", "large", "style", "display", "flexDirection", "alignItems", "justifyContent", "padding", "children", "className", "border", "borderTop", "borderRadius", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "fontSize", "margin", "textAlign", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/Siyoga/client/src/components/LoadingSpinner.js"], "sourcesContent": ["import React from 'react';\n\nconst LoadingSpinner = ({ message = 'Loading...', size = 'medium' }) => {\n  const sizeStyles = {\n    small: { width: '20px', height: '20px' },\n    medium: { width: '40px', height: '40px' },\n    large: { width: '60px', height: '60px' }\n  };\n\n  return (\n    <div style={{\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '20px'\n    }}>\n      <div\n        className=\"spinner\"\n        style={{\n          ...sizeStyles[size],\n          border: '4px solid #f3f3f3',\n          borderTop: '4px solid #667eea',\n          borderRadius: '50%',\n          marginBottom: '10px'\n        }}\n      />\n      <p style={{\n        color: '#666',\n        fontSize: '14px',\n        margin: 0,\n        textAlign: 'center'\n      }}>\n        {message}\n      </p>\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAGA,CAAC;EAAEC,OAAO,GAAG,YAAY;EAAEC,IAAI,GAAG;AAAS,CAAC,KAAK;EACtE,MAAMC,UAAU,GAAG;IACjBC,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO,CAAC;IACxCC,MAAM,EAAE;MAAEF,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO,CAAC;IACzCE,KAAK,EAAE;MAAEH,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO;EACzC,CAAC;EAED,oBACEP,OAAA;IAAKU,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,gBACAhB,OAAA;MACEiB,SAAS,EAAC,SAAS;MACnBP,KAAK,EAAE;QACL,GAAGN,UAAU,CAACD,IAAI,CAAC;QACnBe,MAAM,EAAE,mBAAmB;QAC3BC,SAAS,EAAE,mBAAmB;QAC9BC,YAAY,EAAE,KAAK;QACnBC,YAAY,EAAE;MAChB;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACFzB,OAAA;MAAGU,KAAK,EAAE;QACRgB,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE;MACb,CAAE;MAAAb,QAAA,EACCd;IAAO;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV,CAAC;AAACK,EAAA,GAnCI7B,cAAc;AAqCpB,eAAeA,cAAc;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}