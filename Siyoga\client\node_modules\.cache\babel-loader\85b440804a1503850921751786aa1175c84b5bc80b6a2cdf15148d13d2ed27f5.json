{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\Siyoga\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport { ToastContainer } from './components/Toast';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport VerifyOTP from './pages/VerifyOTP';\nimport Dashboard from './pages/Dashboard';\nimport TripPlanner from './pages/TripPlanner';\nimport DriverDashboard from './pages/DriverDashboard';\nimport './App.css';\n\n// Protected Route Component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProtectedRoute({\n  children\n}) {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 12\n    }, this);\n  }\n  return user ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/login\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 28\n  }, this);\n}\n\n// Public Route Component (redirect to appropriate dashboard if logged in)\n_s(ProtectedRoute, \"EmJkapf7qiLC5Br5eCoEq4veZes=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nfunction PublicRoute({\n  children\n}) {\n  _s2();\n  const {\n    user,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 12\n    }, this);\n  }\n  if (user) {\n    return user.role === 'driver' ? /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/driver-dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 37\n    }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 75\n    }, this);\n  }\n  return children;\n}\n_s2(PublicRoute, \"EmJkapf7qiLC5Br5eCoEq4veZes=\", false, function () {\n  return [useAuth];\n});\n_c2 = PublicRoute;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: [/*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n              children: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n              children: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/verify-otp\",\n            element: /*#__PURE__*/_jsxDEV(VerifyOTP, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/trip-planner\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(TripPlanner, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n}\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"PublicRoute\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "ToastContainer", "<PERSON><PERSON>", "Register", "VerifyOTP", "Dashboard", "<PERSON><PERSON><PERSON><PERSON>", "DriverDashboard", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "user", "loading", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "PublicRoute", "_s2", "role", "_c2", "App", "path", "element", "_c3", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/Siyoga/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport { ToastContainer } from './components/Toast';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport VerifyOTP from './pages/VerifyOTP';\nimport Dashboard from './pages/Dashboard';\nimport TripPlanner from './pages/TripPlanner';\nimport DriverDashboard from './pages/DriverDashboard';\nimport './App.css';\n\n// Protected Route Component\nfunction ProtectedRoute({ children }) {\n  const { user, loading } = useAuth();\n  \n  if (loading) {\n    return <div className=\"loading\">Loading...</div>;\n  }\n  \n  return user ? children : <Navigate to=\"/login\" />;\n}\n\n// Public Route Component (redirect to appropriate dashboard if logged in)\nfunction PublicRoute({ children }) {\n  const { user, loading } = useAuth();\n\n  if (loading) {\n    return <div className=\"loading\">Loading...</div>;\n  }\n\n  if (user) {\n    return user.role === 'driver' ? <Navigate to=\"/driver-dashboard\" /> : <Navigate to=\"/dashboard\" />;\n  }\n\n  return children;\n}\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"App\">\n          <Routes>\n            <Route path=\"/\" element={<Navigate to=\"/login\" />} />\n            <Route \n              path=\"/login\" \n              element={\n                <PublicRoute>\n                  <Login />\n                </PublicRoute>\n              } \n            />\n            <Route\n              path=\"/register\"\n              element={\n                <PublicRoute>\n                  <Register />\n                </PublicRoute>\n              }\n            />\n            <Route\n              path=\"/verify-otp\"\n              element={<VerifyOTP />}\n            />\n            <Route\n              path=\"/dashboard\"\n              element={\n                <ProtectedRoute>\n                  <Dashboard />\n                </ProtectedRoute>\n              }\n            />\n            <Route\n              path=\"/trip-planner\"\n              element={\n                <ProtectedRoute>\n                  <TripPlanner />\n                </ProtectedRoute>\n              }\n            />\n          </Routes>\n        </div>\n      </Router>\n      <ToastContainer />\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,YAAY,EAAEC,OAAO,QAAQ,uBAAuB;AAC7D,SAASC,cAAc,QAAQ,oBAAoB;AACnD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,cAAcA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACpC,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGd,OAAO,CAAC,CAAC;EAEnC,IAAIc,OAAO,EAAE;IACX,oBAAOL,OAAA;MAAKM,SAAS,EAAC,SAAS;MAAAJ,QAAA,EAAC;IAAU;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAClD;EAEA,OAAON,IAAI,GAAGF,QAAQ,gBAAGF,OAAA,CAACX,QAAQ;IAACsB,EAAE,EAAC;EAAQ;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACnD;;AAEA;AAAAP,EAAA,CAVSF,cAAc;EAAA,QACKV,OAAO;AAAA;AAAAqB,EAAA,GAD1BX,cAAc;AAWvB,SAASY,WAAWA,CAAC;EAAEX;AAAS,CAAC,EAAE;EAAAY,GAAA;EACjC,MAAM;IAAEV,IAAI;IAAEC;EAAQ,CAAC,GAAGd,OAAO,CAAC,CAAC;EAEnC,IAAIc,OAAO,EAAE;IACX,oBAAOL,OAAA;MAAKM,SAAS,EAAC,SAAS;MAAAJ,QAAA,EAAC;IAAU;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAClD;EAEA,IAAIN,IAAI,EAAE;IACR,OAAOA,IAAI,CAACW,IAAI,KAAK,QAAQ,gBAAGf,OAAA,CAACX,QAAQ;MAACsB,EAAE,EAAC;IAAmB;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGV,OAAA,CAACX,QAAQ;MAACsB,EAAE,EAAC;IAAY;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpG;EAEA,OAAOR,QAAQ;AACjB;AAACY,GAAA,CAZQD,WAAW;EAAA,QACQtB,OAAO;AAAA;AAAAyB,GAAA,GAD1BH,WAAW;AAcpB,SAASI,GAAGA,CAAA,EAAG;EACb,oBACEjB,OAAA,CAACV,YAAY;IAAAY,QAAA,gBACXF,OAAA,CAACd,MAAM;MAAAgB,QAAA,eACLF,OAAA;QAAKM,SAAS,EAAC,KAAK;QAAAJ,QAAA,eAClBF,OAAA,CAACb,MAAM;UAAAe,QAAA,gBACLF,OAAA,CAACZ,KAAK;YAAC8B,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEnB,OAAA,CAACX,QAAQ;cAACsB,EAAE,EAAC;YAAQ;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDV,OAAA,CAACZ,KAAK;YACJ8B,IAAI,EAAC,QAAQ;YACbC,OAAO,eACLnB,OAAA,CAACa,WAAW;cAAAX,QAAA,eACVF,OAAA,CAACP,KAAK;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACd;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFV,OAAA,CAACZ,KAAK;YACJ8B,IAAI,EAAC,WAAW;YAChBC,OAAO,eACLnB,OAAA,CAACa,WAAW;cAAAX,QAAA,eACVF,OAAA,CAACN,QAAQ;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACd;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFV,OAAA,CAACZ,KAAK;YACJ8B,IAAI,EAAC,aAAa;YAClBC,OAAO,eAAEnB,OAAA,CAACL,SAAS;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACFV,OAAA,CAACZ,KAAK;YACJ8B,IAAI,EAAC,YAAY;YACjBC,OAAO,eACLnB,OAAA,CAACC,cAAc;cAAAC,QAAA,eACbF,OAAA,CAACJ,SAAS;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFV,OAAA,CAACZ,KAAK;YACJ8B,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLnB,OAAA,CAACC,cAAc;cAAAC,QAAA,eACbF,OAAA,CAACH,WAAW;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACTV,OAAA,CAACR,cAAc;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEnB;AAACU,GAAA,GAjDQH,GAAG;AAmDZ,eAAeA,GAAG;AAAC,IAAAL,EAAA,EAAAI,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}