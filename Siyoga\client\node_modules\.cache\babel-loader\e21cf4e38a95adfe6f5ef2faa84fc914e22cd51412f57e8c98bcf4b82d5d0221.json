{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\Siyoga\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport { ToastContainer } from './components/Toast';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport VerifyOTP from './pages/VerifyOTP';\nimport Dashboard from './pages/Dashboard';\nimport TripPlanner from './pages/TripPlanner';\nimport './App.css';\n\n// Protected Route Component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProtectedRoute({\n  children\n}) {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 12\n    }, this);\n  }\n  return user ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/login\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 28\n  }, this);\n}\n\n// Public Route Component (redirect to dashboard if logged in)\n_s(ProtectedRoute, \"EmJkapf7qiLC5Br5eCoEq4veZes=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nfunction PublicRoute({\n  children\n}) {\n  _s2();\n  const {\n    user,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 12\n    }, this);\n  }\n  return user ? /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/dashboard\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 17\n  }, this) : children;\n}\n_s2(PublicRoute, \"EmJkapf7qiLC5Br5eCoEq4veZes=\", false, function () {\n  return [useAuth];\n});\n_c2 = PublicRoute;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: [/*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n              children: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n              children: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/verify-otp\",\n            element: /*#__PURE__*/_jsxDEV(VerifyOTP, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/trip-planner\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(TripPlanner, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n      position: \"top-right\",\n      autoClose: 3000,\n      hideProgressBar: false,\n      newestOnTop: false,\n      closeOnClick: true,\n      rtl: false,\n      pauseOnFocusLoss: true,\n      draggable: true,\n      pauseOnHover: true,\n      theme: \"light\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n}\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"PublicRoute\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "ToastContainer", "<PERSON><PERSON>", "Register", "VerifyOTP", "Dashboard", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "user", "loading", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "PublicRoute", "_s2", "_c2", "App", "path", "element", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "_c3", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/Siyoga/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport { ToastContainer } from './components/Toast';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport VerifyOTP from './pages/VerifyOTP';\nimport Dashboard from './pages/Dashboard';\nimport TripPlanner from './pages/TripPlanner';\nimport './App.css';\n\n// Protected Route Component\nfunction ProtectedRoute({ children }) {\n  const { user, loading } = useAuth();\n  \n  if (loading) {\n    return <div className=\"loading\">Loading...</div>;\n  }\n  \n  return user ? children : <Navigate to=\"/login\" />;\n}\n\n// Public Route Component (redirect to dashboard if logged in)\nfunction PublicRoute({ children }) {\n  const { user, loading } = useAuth();\n  \n  if (loading) {\n    return <div className=\"loading\">Loading...</div>;\n  }\n  \n  return user ? <Navigate to=\"/dashboard\" /> : children;\n}\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"App\">\n          <Routes>\n            <Route path=\"/\" element={<Navigate to=\"/login\" />} />\n            <Route \n              path=\"/login\" \n              element={\n                <PublicRoute>\n                  <Login />\n                </PublicRoute>\n              } \n            />\n            <Route\n              path=\"/register\"\n              element={\n                <PublicRoute>\n                  <Register />\n                </PublicRoute>\n              }\n            />\n            <Route\n              path=\"/verify-otp\"\n              element={<VerifyOTP />}\n            />\n            <Route\n              path=\"/dashboard\"\n              element={\n                <ProtectedRoute>\n                  <Dashboard />\n                </ProtectedRoute>\n              }\n            />\n            <Route\n              path=\"/trip-planner\"\n              element={\n                <ProtectedRoute>\n                  <TripPlanner />\n                </ProtectedRoute>\n              }\n            />\n          </Routes>\n        </div>\n      </Router>\n      <ToastContainer\n        position=\"top-right\"\n        autoClose={3000}\n        hideProgressBar={false}\n        newestOnTop={false}\n        closeOnClick\n        rtl={false}\n        pauseOnFocusLoss\n        draggable\n        pauseOnHover\n        theme=\"light\"\n      />\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,YAAY,EAAEC,OAAO,QAAQ,uBAAuB;AAC7D,SAASC,cAAc,QAAQ,oBAAoB;AACnD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,cAAcA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACpC,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGb,OAAO,CAAC,CAAC;EAEnC,IAAIa,OAAO,EAAE;IACX,oBAAOL,OAAA;MAAKM,SAAS,EAAC,SAAS;MAAAJ,QAAA,EAAC;IAAU;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAClD;EAEA,OAAON,IAAI,GAAGF,QAAQ,gBAAGF,OAAA,CAACV,QAAQ;IAACqB,EAAE,EAAC;EAAQ;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACnD;;AAEA;AAAAP,EAAA,CAVSF,cAAc;EAAA,QACKT,OAAO;AAAA;AAAAoB,EAAA,GAD1BX,cAAc;AAWvB,SAASY,WAAWA,CAAC;EAAEX;AAAS,CAAC,EAAE;EAAAY,GAAA;EACjC,MAAM;IAAEV,IAAI;IAAEC;EAAQ,CAAC,GAAGb,OAAO,CAAC,CAAC;EAEnC,IAAIa,OAAO,EAAE;IACX,oBAAOL,OAAA;MAAKM,SAAS,EAAC,SAAS;MAAAJ,QAAA,EAAC;IAAU;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAClD;EAEA,OAAON,IAAI,gBAAGJ,OAAA,CAACV,QAAQ;IAACqB,EAAE,EAAC;EAAY;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,GAAGR,QAAQ;AACvD;AAACY,GAAA,CARQD,WAAW;EAAA,QACQrB,OAAO;AAAA;AAAAuB,GAAA,GAD1BF,WAAW;AAUpB,SAASG,GAAGA,CAAA,EAAG;EACb,oBACEhB,OAAA,CAACT,YAAY;IAAAW,QAAA,gBACXF,OAAA,CAACb,MAAM;MAAAe,QAAA,eACLF,OAAA;QAAKM,SAAS,EAAC,KAAK;QAAAJ,QAAA,eAClBF,OAAA,CAACZ,MAAM;UAAAc,QAAA,gBACLF,OAAA,CAACX,KAAK;YAAC4B,IAAI,EAAC,GAAG;YAACC,OAAO,eAAElB,OAAA,CAACV,QAAQ;cAACqB,EAAE,EAAC;YAAQ;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDV,OAAA,CAACX,KAAK;YACJ4B,IAAI,EAAC,QAAQ;YACbC,OAAO,eACLlB,OAAA,CAACa,WAAW;cAAAX,QAAA,eACVF,OAAA,CAACN,KAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACd;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFV,OAAA,CAACX,KAAK;YACJ4B,IAAI,EAAC,WAAW;YAChBC,OAAO,eACLlB,OAAA,CAACa,WAAW;cAAAX,QAAA,eACVF,OAAA,CAACL,QAAQ;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACd;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFV,OAAA,CAACX,KAAK;YACJ4B,IAAI,EAAC,aAAa;YAClBC,OAAO,eAAElB,OAAA,CAACJ,SAAS;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACFV,OAAA,CAACX,KAAK;YACJ4B,IAAI,EAAC,YAAY;YACjBC,OAAO,eACLlB,OAAA,CAACC,cAAc;cAAAC,QAAA,eACbF,OAAA,CAACH,SAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFV,OAAA,CAACX,KAAK;YACJ4B,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLlB,OAAA,CAACC,cAAc;cAAAC,QAAA,eACbF,OAAA,CAACF,WAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACTV,OAAA,CAACP,cAAc;MACb0B,QAAQ,EAAC,WAAW;MACpBC,SAAS,EAAE,IAAK;MAChBC,eAAe,EAAE,KAAM;MACvBC,WAAW,EAAE,KAAM;MACnBC,YAAY;MACZC,GAAG,EAAE,KAAM;MACXC,gBAAgB;MAChBC,SAAS;MACTC,YAAY;MACZC,KAAK,EAAC;IAAO;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU,CAAC;AAEnB;AAACmB,GAAA,GA5DQb,GAAG;AA8DZ,eAAeA,GAAG;AAAC,IAAAJ,EAAA,EAAAG,GAAA,EAAAc,GAAA;AAAAC,YAAA,CAAAlB,EAAA;AAAAkB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}