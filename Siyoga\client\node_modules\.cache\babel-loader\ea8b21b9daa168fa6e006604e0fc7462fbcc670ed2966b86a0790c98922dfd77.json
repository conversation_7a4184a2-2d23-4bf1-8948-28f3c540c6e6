{"ast": null, "code": "'use strict';\n\nimport utils from './../utils.js';\nclass InterceptorManager {\n  constructor() {\n    this.handlers = [];\n  }\n\n  /**\n   * Add a new interceptor to the stack\n   *\n   * @param {Function} fulfilled The function to handle `then` for a `Promise`\n   * @param {Function} rejected The function to handle `reject` for a `Promise`\n   *\n   * @return {Number} An ID used to remove interceptor later\n   */\n  use(fulfilled, rejected, options) {\n    this.handlers.push({\n      fulfilled,\n      rejected,\n      synchronous: options ? options.synchronous : false,\n      runWhen: options ? options.runWhen : null\n    });\n    return this.handlers.length - 1;\n  }\n\n  /**\n   * Remove an interceptor from the stack\n   *\n   * @param {Number} id The ID that was returned by `use`\n   *\n   * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise\n   */\n  eject(id) {\n    if (this.handlers[id]) {\n      this.handlers[id] = null;\n    }\n  }\n\n  /**\n   * Clear all interceptors from the stack\n   *\n   * @returns {void}\n   */\n  clear() {\n    if (this.handlers) {\n      this.handlers = [];\n    }\n  }\n\n  /**\n   * Iterate over all the registered interceptors\n   *\n   * This method is particularly useful for skipping over any\n   * interceptors that may have become `null` calling `eject`.\n   *\n   * @param {Function} fn The function to call for each interceptor\n   *\n   * @returns {void}\n   */\n  forEach(fn) {\n    utils.forEach(this.handlers, function forEachHandler(h) {\n      if (h !== null) {\n        fn(h);\n      }\n    });\n  }\n}\nexport default InterceptorManager;", "map": {"version": 3, "names": ["utils", "InterceptorManager", "constructor", "handlers", "use", "fulfilled", "rejected", "options", "push", "synchronous", "runWhen", "length", "eject", "id", "clear", "for<PERSON>ach", "fn", "forEachHandler", "h"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/Siyoga/client/node_modules/axios/lib/core/InterceptorManager.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\n\nclass InterceptorManager {\n  constructor() {\n    this.handlers = [];\n  }\n\n  /**\n   * Add a new interceptor to the stack\n   *\n   * @param {Function} fulfilled The function to handle `then` for a `Promise`\n   * @param {Function} rejected The function to handle `reject` for a `Promise`\n   *\n   * @return {Number} An ID used to remove interceptor later\n   */\n  use(fulfilled, rejected, options) {\n    this.handlers.push({\n      fulfilled,\n      rejected,\n      synchronous: options ? options.synchronous : false,\n      runWhen: options ? options.runWhen : null\n    });\n    return this.handlers.length - 1;\n  }\n\n  /**\n   * Remove an interceptor from the stack\n   *\n   * @param {Number} id The ID that was returned by `use`\n   *\n   * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise\n   */\n  eject(id) {\n    if (this.handlers[id]) {\n      this.handlers[id] = null;\n    }\n  }\n\n  /**\n   * Clear all interceptors from the stack\n   *\n   * @returns {void}\n   */\n  clear() {\n    if (this.handlers) {\n      this.handlers = [];\n    }\n  }\n\n  /**\n   * Iterate over all the registered interceptors\n   *\n   * This method is particularly useful for skipping over any\n   * interceptors that may have become `null` calling `eject`.\n   *\n   * @param {Function} fn The function to call for each interceptor\n   *\n   * @returns {void}\n   */\n  forEach(fn) {\n    utils.forEach(this.handlers, function forEachHandler(h) {\n      if (h !== null) {\n        fn(h);\n      }\n    });\n  }\n}\n\nexport default InterceptorManager;\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,eAAe;AAEjC,MAAMC,kBAAkB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,QAAQ,GAAG,EAAE;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,GAAGA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IAChC,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC;MACjBH,SAAS;MACTC,QAAQ;MACRG,WAAW,EAAEF,OAAO,GAAGA,OAAO,CAACE,WAAW,GAAG,KAAK;MAClDC,OAAO,EAAEH,OAAO,GAAGA,OAAO,CAACG,OAAO,GAAG;IACvC,CAAC,CAAC;IACF,OAAO,IAAI,CAACP,QAAQ,CAACQ,MAAM,GAAG,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,KAAKA,CAACC,EAAE,EAAE;IACR,IAAI,IAAI,CAACV,QAAQ,CAACU,EAAE,CAAC,EAAE;MACrB,IAAI,CAACV,QAAQ,CAACU,EAAE,CAAC,GAAG,IAAI;IAC1B;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEC,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAACX,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,GAAG,EAAE;IACpB;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEY,OAAOA,CAACC,EAAE,EAAE;IACVhB,KAAK,CAACe,OAAO,CAAC,IAAI,CAACZ,QAAQ,EAAE,SAASc,cAAcA,CAACC,CAAC,EAAE;MACtD,IAAIA,CAAC,KAAK,IAAI,EAAE;QACdF,EAAE,CAACE,CAAC,CAAC;MACP;IACF,CAAC,CAAC;EACJ;AACF;AAEA,eAAejB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}