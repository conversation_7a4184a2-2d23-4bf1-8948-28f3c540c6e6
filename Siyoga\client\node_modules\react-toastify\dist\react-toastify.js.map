{"version": 3, "file": "react-toastify.js", "sources": ["../src/utils/propValidator.ts", "../src/utils/collapseToast.ts", "../src/utils/cssTransition.tsx", "../src/utils/mapper.ts", "../src/core/eventManager.ts", "../src/components/Icons.tsx", "../src/hooks/useToastContainer.ts", "../src/hooks/useToast.ts", "../src/components/CloseButton.tsx", "../src/components/ProgressBar.tsx", "../src/components/Toast.tsx", "../src/components/Transitions.tsx", "../src/components/ToastContainer.tsx", "../src/core/toast.ts", "../src/utils/constant.ts"], "sourcesContent": ["import { isValidElement } from 'react';\n\nexport const isNum = (v: any): v is Number =>\n  typeof v === 'number' && !isNaN(v);\n\nexport const isStr = (v: any): v is String => typeof v === 'string';\n\nexport const isFn = (v: any): v is Function => typeof v === 'function';\n\nexport const parseClassName = (v: any) => (isStr(v) || isFn(v) ? v : null);\n\nexport const getAutoCloseDelay = (\n  toastAutoClose?: false | number,\n  containerAutoClose?: false | number\n) =>\n  toastAutoClose === false || (isNum(toastAutoClose) && toastAutoClose > 0)\n    ? toastAutoClose\n    : containerAutoClose;\n\nexport const canBeRendered = <T>(content: T): boolean =>\n  isValidElement(content) || isStr(content) || isFn(content) || isNum(content);\n", "import { Default } from './constant';\n\n/**\n * Used to collapse toast after exit animation\n */\nexport function collapseToast(\n  node: HTMLElement,\n  done: () => void,\n  duration = Default.COLLAPSE_DURATION\n) {\n  const { scrollHeight, style } = node;\n\n  requestAnimationFrame(() => {\n    style.minHeight = 'initial';\n    style.height = scrollHeight + 'px';\n    style.transition = `all ${duration}ms`;\n\n    requestAnimationFrame(() => {\n      style.height = '0';\n      style.padding = '0';\n      style.margin = '0';\n      setTimeout(done, duration as number);\n    });\n  });\n}\n", "import React, { useEffect, useLayoutEffect, useRef } from 'react';\nimport { collapseToast } from './collapseToast';\nimport { Default, SyntheticEvent } from './constant';\n\nimport { ToastTransitionProps } from '../types';\n\nexport interface CSSTransitionProps {\n  /**\n   * Css class to apply when toast enter\n   */\n  enter: string;\n\n  /**\n   * Css class to apply when toast leave\n   */\n  exit: string;\n\n  /**\n   * Append current toast position to the classname.\n   * If multiple classes are provided, only the last one will get the position\n   * For instance `myclass--top-center`...\n   * `Default: false`\n   */\n  appendPosition?: boolean;\n\n  /**\n   * Collapse toast smoothly when exit animation end\n   * `Default: true`\n   */\n  collapse?: boolean;\n\n  /**\n   * Collapse transition duration\n   * `Default: 300`\n   */\n  collapseDuration?: number;\n}\n\nconst enum AnimationStep {\n  Enter,\n  Exit\n}\n\n/**\n * Css animation that just work.\n * You could use animate.css for instance\n *\n *\n * ```\n * cssTransition({\n *   enter: \"animate__animated animate__bounceIn\",\n *   exit: \"animate__animated animate__bounceOut\"\n * })\n * ```\n *\n */\nexport function cssTransition({\n  enter,\n  exit,\n  appendPosition = false,\n  collapse = true,\n  collapseDuration = Default.COLLAPSE_DURATION\n}: CSSTransitionProps) {\n  return function ToastTransition({\n    children,\n    position,\n    preventExitTransition,\n    done,\n    nodeRef,\n    isIn\n  }: ToastTransitionProps) {\n    const enterClassName = appendPosition ? `${enter}--${position}` : enter;\n    const exitClassName = appendPosition ? `${exit}--${position}` : exit;\n    const animationStep = useRef(AnimationStep.Enter);\n\n    useLayoutEffect(() => {\n      const node = nodeRef.current!;\n      const classToToken = enterClassName.split(' ');\n\n      const onEntered = (e: AnimationEvent) => {\n        if (e.target !== nodeRef.current) return;\n\n        node.dispatchEvent(new Event(SyntheticEvent.ENTRANCE_ANIMATION_END));\n        node.removeEventListener('animationend', onEntered);\n        node.removeEventListener('animationcancel', onEntered);\n        if (\n          animationStep.current === AnimationStep.Enter &&\n          e.type !== 'animationcancel'\n        ) {\n          node.classList.remove(...classToToken);\n        }\n      };\n\n      const onEnter = () => {\n        node.classList.add(...classToToken);\n        node.addEventListener('animationend', onEntered);\n        node.addEventListener('animationcancel', onEntered);\n      };\n\n      onEnter();\n    }, []);\n\n    useEffect(() => {\n      const node = nodeRef.current!;\n\n      const onExited = () => {\n        node.removeEventListener('animationend', onExited);\n        collapse ? collapseToast(node, done, collapseDuration) : done();\n      };\n\n      const onExit = () => {\n        animationStep.current = AnimationStep.Exit;\n        node.className += ` ${exitClassName}`;\n        node.addEventListener('animationend', onExited);\n      };\n\n      if (!isIn) preventExitTransition ? onExited() : onExit();\n    }, [isIn]);\n\n    return <>{children}</>;\n  };\n}\n", "import { Toast, ToastItem, ToastItemStatus } from '../types';\n\nexport function toToastItem(toast: Toast, status: ToastItemStatus): ToastItem {\n  return toast != null\n    ? {\n        content: toast.content,\n        containerId: toast.props.containerId,\n        id: toast.props.toastId,\n        theme: toast.props.theme,\n        type: toast.props.type,\n        data: toast.props.data || {},\n        isLoading: toast.props.isLoading,\n        icon: toast.props.icon,\n        status\n      }\n    : // monkey patch for now\n      ({} as ToastItem);\n}\n", "import {\n  Id,\n  ToastContent,\n  ClearWaitingQueueParams,\n  NotValidatedToastProps,\n  ToastItem\n} from '../types';\nimport { ContainerInstance } from '../hooks';\n\nexport const enum Event {\n  Show,\n  Clear,\n  DidMount,\n  WillUnmount,\n  Change,\n  ClearWaitingQueue\n}\n\ntype OnShowCallback = (\n  content: ToastContent,\n  options: NotValidatedToastProps\n) => void;\ntype OnClearCallback = (id?: Id) => void;\ntype OnClearWaitingQueue = (params: ClearWaitingQueueParams) => void;\ntype OnDidMountCallback = (containerInstance: ContainerInstance) => void;\ntype OnWillUnmountCallback = OnDidMountCallback;\n\nexport type OnChangeCallback = (toast: ToastItem) => void;\n\ntype Callback =\n  | OnShowCallback\n  | OnClearCallback\n  | OnClearWaitingQueue\n  | OnDidMountCallback\n  | OnWillUnmountCallback\n  | OnChangeCallback;\ntype TimeoutId = ReturnType<typeof setTimeout>;\n\nexport interface EventManager {\n  list: Map<Event, Callback[]>;\n  emitQueue: Map<Event, TimeoutId[]>;\n  on(event: Event.Show, callback: OnShowCallback): EventManager;\n  on(event: Event.Clear, callback: OnClearCallback): EventManager;\n  on(\n    event: Event.ClearWaitingQueue,\n    callback: OnClearWaitingQueue\n  ): EventManager;\n  on(event: Event.DidMount, callback: OnDidMountCallback): EventManager;\n  on(event: Event.WillUnmount, callback: OnWillUnmountCallback): EventManager;\n  on(event: Event.Change, callback: OnChangeCallback): EventManager;\n  off(event: Event, callback?: Callback): EventManager;\n  cancelEmit(event: Event): EventManager;\n  emit<TData>(\n    event: Event.Show,\n    content: React.ReactNode | ToastContent<TData>,\n    options: NotValidatedToastProps\n  ): void;\n  emit(event: Event.Clear, id?: string | number): void;\n  emit(event: Event.ClearWaitingQueue, params: ClearWaitingQueueParams): void;\n  emit(event: Event.DidMount, containerInstance: ContainerInstance): void;\n  emit(event: Event.WillUnmount, containerInstance: ContainerInstance): void;\n  emit(event: Event.Change, data: ToastItem): void;\n}\n\nexport const eventManager: EventManager = {\n  list: new Map(),\n  emitQueue: new Map(),\n\n  on(event: Event, callback: Callback) {\n    this.list.has(event) || this.list.set(event, []);\n    this.list.get(event)!.push(callback);\n    return this;\n  },\n\n  off(event, callback) {\n    if (callback) {\n      const cb = this.list.get(event)!.filter(cb => cb !== callback);\n      this.list.set(event, cb);\n      return this;\n    }\n    this.list.delete(event);\n    return this;\n  },\n\n  cancelEmit(event) {\n    const timers = this.emitQueue.get(event);\n    if (timers) {\n      timers.forEach(clearTimeout);\n      this.emitQueue.delete(event);\n    }\n\n    return this;\n  },\n\n  /**\n   * Enqueue the event at the end of the call stack\n   * Doing so let the user call toast as follow:\n   * toast('1')\n   * toast('2')\n   * toast('3')\n   * Without setTimemout the code above will not work\n   */\n  emit(event: Event, ...args: any[]) {\n    this.list.has(event) &&\n      this.list.get(event)!.forEach((callback: Callback) => {\n        const timer: TimeoutId = setTimeout(() => {\n          // @ts-ignore\n          callback(...args);\n        }, 0);\n\n        this.emitQueue.has(event) || this.emitQueue.set(event, []);\n        this.emitQueue.get(event)!.push(timer);\n      });\n  }\n};\n", "import React, { cloneElement, isValidElement } from 'react';\n\nimport { Theme, ToastProps, TypeOptions } from '../types';\nimport { Default, isFn, isNum, isStr } from '../utils';\n\n/**\n * Used when providing custom icon\n */\nexport interface IconProps {\n  theme: Theme;\n  type: TypeOptions;\n}\n\nexport type BuiltInIconProps = React.SVGProps<SVGSVGElement> & IconProps;\n\nconst Svg: React.FC<BuiltInIconProps> = ({ theme, type, ...rest }) => (\n  <svg\n    viewBox=\"0 0 24 24\"\n    width=\"100%\"\n    height=\"100%\"\n    fill={\n      theme === 'colored'\n        ? 'currentColor'\n        : `var(--toastify-icon-color-${type})`\n    }\n    {...rest}\n  />\n);\n\nfunction Warning(props: BuiltInIconProps) {\n  return (\n    <Svg {...props}>\n      <path d=\"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z\" />\n    </Svg>\n  );\n}\n\nfunction Info(props: BuiltInIconProps) {\n  return (\n    <Svg {...props}>\n      <path d=\"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z\" />\n    </Svg>\n  );\n}\n\nfunction Success(props: BuiltInIconProps) {\n  return (\n    <Svg {...props}>\n      <path d=\"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z\" />\n    </Svg>\n  );\n}\n\nfunction Error(props: BuiltInIconProps) {\n  return (\n    <Svg {...props}>\n      <path d=\"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z\" />\n    </Svg>\n  );\n}\n\nfunction Spinner() {\n  return <div className={`${Default.CSS_NAMESPACE}__spinner`} />;\n}\n\nexport const Icons = {\n  info: Info,\n  warning: Warning,\n  success: Success,\n  error: Error,\n  spinner: Spinner\n};\n\nconst maybeIcon = (type: string): type is keyof typeof Icons => type in Icons;\n\nexport function getIcon({ theme, type, isLoading, icon }: ToastProps) {\n  let Icon: React.ReactNode = null;\n  const iconProps = { theme, type };\n\n  if (icon === false) {\n    // hide\n  } else if (isFn(icon)) {\n    Icon = icon(iconProps);\n  } else if (isValidElement(icon)) {\n    Icon = cloneElement(icon, iconProps);\n  } else if (isStr(icon) || isNum(icon)) {\n    Icon = icon;\n  } else if (isLoading) {\n    Icon = Icons.spinner();\n  } else if (maybeIcon(type)) {\n    Icon = Icons[type](iconProps);\n  }\n\n  return Icon;\n}\n", "import {\n  useEffect,\n  useRef,\n  useReducer,\n  cloneElement,\n  isValidElement,\n  useState,\n  ReactElement\n} from 'react';\nimport {\n  parseClassName,\n  canBeRendered,\n  isFn,\n  isNum,\n  isStr,\n  getAutoCloseDelay,\n  toToastItem\n} from '../utils';\nimport { eventManager, Event } from '../core/eventManager';\n\nimport {\n  Id,\n  ToastContainerProps,\n  ToastProps,\n  ToastContent,\n  Toast,\n  ToastPosition,\n  ClearWaitingQueueParams,\n  NotValidatedToastProps\n} from '../types';\n\nimport { getIcon } from '../components/Icons';\n\ninterface QueuedToast {\n  toastContent: ToastContent;\n  toastProps: ToastProps;\n  staleId?: Id;\n}\n\nexport interface ContainerInstance {\n  toastKey: number;\n  displayedToast: number;\n  props: ToastContainerProps;\n  containerId?: Id | null;\n  isToastActive: (toastId: Id) => boolean;\n  getToast: (id: Id) => Toast | null | undefined;\n  queue: QueuedToast[];\n  count: number;\n}\n\nexport function useToastContainer(props: ToastContainerProps) {\n  const [, forceUpdate] = useReducer(x => x + 1, 0);\n  const [toastIds, setToastIds] = useState<Id[]>([]);\n  const containerRef = useRef(null);\n  const toastToRender = useRef(new Map<Id, Toast>()).current;\n  const isToastActive = (id: Id) => toastIds.indexOf(id) !== -1;\n  const instance = useRef<ContainerInstance>({\n    toastKey: 1,\n    displayedToast: 0,\n    count: 0,\n    queue: [],\n    props,\n    containerId: null,\n    isToastActive,\n    getToast: id => toastToRender.get(id)\n  }).current;\n\n  useEffect(() => {\n    instance.containerId = props.containerId;\n    eventManager\n      .cancelEmit(Event.WillUnmount)\n      .on(Event.Show, buildToast)\n      .on(Event.Clear, toastId => containerRef.current && removeToast(toastId))\n      .on(Event.ClearWaitingQueue, clearWaitingQueue)\n      .emit(Event.DidMount, instance);\n\n    return () => {\n      toastToRender.clear();\n      eventManager.emit(Event.WillUnmount, instance);\n    };\n  }, []);\n\n  useEffect(() => {\n    instance.props = props;\n    instance.isToastActive = isToastActive;\n    instance.displayedToast = toastIds.length;\n  });\n\n  function clearWaitingQueue({ containerId }: ClearWaitingQueueParams) {\n    const { limit } = instance.props;\n    if (limit && (!containerId || instance.containerId === containerId)) {\n      instance.count -= instance.queue.length;\n      instance.queue = [];\n    }\n  }\n\n  function removeToast(toastId?: Id) {\n    setToastIds(state =>\n      toastId == null ? [] : state.filter(id => id !== toastId)\n    );\n  }\n\n  function dequeueToast() {\n    const { toastContent, toastProps, staleId } =\n      instance.queue.shift() as QueuedToast;\n    appendToast(toastContent, toastProps, staleId);\n  }\n\n  /**\n   * check if a container is attached to the dom\n   * check for multi-container, build only if associated\n   * check for duplicate toastId if no update\n   */\n  function isNotValid(options: NotValidatedToastProps) {\n    return (\n      !containerRef.current ||\n      (instance.props.enableMultiContainer &&\n        options.containerId !== instance.props.containerId) ||\n      (toastToRender.has(options.toastId) && options.updateId == null)\n    );\n  }\n\n  // this function and all the function called inside needs to rely on refs\n  function buildToast(\n    content: ToastContent,\n    { delay, staleId, ...options }: NotValidatedToastProps\n  ) {\n    if (!canBeRendered(content) || isNotValid(options)) return;\n\n    const { toastId, updateId, data } = options;\n    const { props } = instance;\n    const closeToast = () => removeToast(toastId);\n    const isNotAnUpdate = updateId == null;\n\n    if (isNotAnUpdate) instance.count++;\n\n    const toastProps = {\n      ...props,\n      style: props.toastStyle,\n      key: instance.toastKey++,\n      ...Object.fromEntries(\n        Object.entries(options).filter(([_, v]) => v != null)\n      ),\n      toastId,\n      updateId,\n      data,\n      closeToast,\n      isIn: false,\n      className: parseClassName(options.className || props.toastClassName),\n      bodyClassName: parseClassName(\n        options.bodyClassName || props.bodyClassName\n      ),\n      progressClassName: parseClassName(\n        options.progressClassName || props.progressClassName\n      ),\n      autoClose: options.isLoading\n        ? false\n        : getAutoCloseDelay(options.autoClose, props.autoClose),\n      deleteToast() {\n        const removed = toToastItem(toastToRender.get(toastId)!, 'removed');\n        toastToRender.delete(toastId);\n\n        eventManager.emit(Event.Change, removed);\n\n        const queueLen = instance.queue.length;\n        instance.count =\n          toastId == null\n            ? instance.count - instance.displayedToast\n            : instance.count - 1;\n\n        if (instance.count < 0) instance.count = 0;\n\n        if (queueLen > 0) {\n          const freeSlot = toastId == null ? instance.props.limit! : 1;\n\n          if (queueLen === 1 || freeSlot === 1) {\n            instance.displayedToast++;\n            dequeueToast();\n          } else {\n            const toDequeue = freeSlot > queueLen ? queueLen : freeSlot;\n            instance.displayedToast = toDequeue;\n\n            for (let i = 0; i < toDequeue; i++) dequeueToast();\n          }\n        } else {\n          forceUpdate();\n        }\n      }\n    } as ToastProps;\n\n    toastProps.iconOut = getIcon(toastProps);\n\n    if (isFn(options.onOpen)) toastProps.onOpen = options.onOpen;\n    if (isFn(options.onClose)) toastProps.onClose = options.onClose;\n\n    toastProps.closeButton = props.closeButton;\n\n    if (options.closeButton === false || canBeRendered(options.closeButton)) {\n      toastProps.closeButton = options.closeButton;\n    } else if (options.closeButton === true) {\n      toastProps.closeButton = canBeRendered(props.closeButton)\n        ? props.closeButton\n        : true;\n    }\n\n    let toastContent = content;\n\n    if (isValidElement(content) && !isStr(content.type)) {\n      toastContent = cloneElement(content as ReactElement, {\n        closeToast,\n        toastProps,\n        data\n      });\n    } else if (isFn(content)) {\n      toastContent = content({ closeToast, toastProps, data });\n    }\n\n    // not handling limit + delay by design. Waiting for user feedback first\n    if (\n      props.limit &&\n      props.limit > 0 &&\n      instance.count > props.limit &&\n      isNotAnUpdate\n    ) {\n      instance.queue.push({ toastContent, toastProps, staleId });\n    } else if (isNum(delay)) {\n      setTimeout(() => {\n        appendToast(toastContent, toastProps, staleId);\n      }, delay);\n    } else {\n      appendToast(toastContent, toastProps, staleId);\n    }\n  }\n\n  function appendToast(\n    content: ToastContent,\n    toastProps: ToastProps,\n    staleId?: Id\n  ) {\n    const { toastId } = toastProps;\n\n    if (staleId) toastToRender.delete(staleId);\n\n    const toast = {\n      content,\n      props: toastProps\n    };\n    toastToRender.set(toastId, toast);\n\n    setToastIds(state => [...state, toastId].filter(id => id !== staleId));\n    eventManager.emit(\n      Event.Change,\n      toToastItem(toast, toast.props.updateId == null ? 'added' : 'updated')\n    );\n  }\n\n  function getToastToRender<T>(\n    cb: (position: ToastPosition, toastList: Toast[]) => T\n  ) {\n    const toRender = new Map<ToastPosition, Toast[]>();\n    const collection = Array.from(toastToRender.values());\n\n    if (props.newestOnTop) collection.reverse();\n\n    collection.forEach(toast => {\n      const { position } = toast.props;\n      toRender.has(position) || toRender.set(position, []);\n      toRender.get(position)!.push(toast);\n    });\n\n    return Array.from(toRender, p => cb(p[0], p[1]));\n  }\n\n  return {\n    getToastToRender,\n    containerRef,\n    isToastActive\n  };\n}\n", "import {\n  useState,\n  useRef,\n  useEffect,\n  isValidElement,\n  DOMAttributes\n} from 'react';\n\nimport { isFn, Default, Direction, SyntheticEvent } from '../utils';\nimport { ToastProps } from '../types';\n\ninterface Draggable {\n  start: number;\n  x: number;\n  y: number;\n  delta: number;\n  removalDistance: number;\n  canCloseOnClick: boolean;\n  canDrag: boolean;\n  boundingRect: DOMRect | null;\n  didMove: boolean;\n}\n\ntype DragEvent = MouseEvent & TouchEvent;\n\nfunction getX(e: DragEvent) {\n  return e.targetTouches && e.targetTouches.length >= 1\n    ? e.targetTouches[0].clientX\n    : e.clientX;\n}\n\nfunction getY(e: DragEvent) {\n  return e.targetTouches && e.targetTouches.length >= 1\n    ? e.targetTouches[0].clientY\n    : e.clientY;\n}\n\nexport function useToast(props: ToastProps) {\n  const [isRunning, setIsRunning] = useState(false);\n  const [preventExitTransition, setPreventExitTransition] = useState(false);\n  const toastRef = useRef<HTMLDivElement>(null);\n  const drag = useRef<Draggable>({\n    start: 0,\n    x: 0,\n    y: 0,\n    delta: 0,\n    removalDistance: 0,\n    canCloseOnClick: true,\n    canDrag: false,\n    boundingRect: null,\n    didMove: false\n  }).current;\n  const syncProps = useRef(props);\n  const { autoClose, pauseOnHover, closeToast, onClick, closeOnClick } = props;\n\n  useEffect(() => {\n    syncProps.current = props;\n  });\n\n  useEffect(() => {\n    if (toastRef.current)\n      toastRef.current.addEventListener(\n        SyntheticEvent.ENTRANCE_ANIMATION_END,\n        playToast,\n        { once: true }\n      );\n\n    if (isFn(props.onOpen))\n      props.onOpen(isValidElement(props.children) && props.children.props);\n\n    return () => {\n      const props = syncProps.current;\n      if (isFn(props.onClose))\n        props.onClose(isValidElement(props.children) && props.children.props);\n    };\n  }, []);\n\n  useEffect(() => {\n    props.pauseOnFocusLoss && bindFocusEvents();\n    return () => {\n      props.pauseOnFocusLoss && unbindFocusEvents();\n    };\n  }, [props.pauseOnFocusLoss]);\n\n  function onDragStart(\n    e: React.MouseEvent<HTMLElement, MouseEvent> | React.TouchEvent<HTMLElement>\n  ) {\n    if (props.draggable) {\n      // required for ios safari to prevent default swipe behavior\n      if (e.nativeEvent.type === 'touchstart') e.nativeEvent.preventDefault();\n\n      bindDragEvents();\n      const toast = toastRef.current!;\n      drag.canCloseOnClick = true;\n      drag.canDrag = true;\n      drag.boundingRect = toast.getBoundingClientRect();\n      toast.style.transition = '';\n      drag.x = getX(e.nativeEvent as DragEvent);\n      drag.y = getY(e.nativeEvent as DragEvent);\n\n      if (props.draggableDirection === Direction.X) {\n        drag.start = drag.x;\n        drag.removalDistance =\n          toast.offsetWidth * (props.draggablePercent / 100);\n      } else {\n        drag.start = drag.y;\n        drag.removalDistance =\n          toast.offsetHeight *\n          (props.draggablePercent === Default.DRAGGABLE_PERCENT\n            ? props.draggablePercent * 1.5\n            : props.draggablePercent / 100);\n      }\n    }\n  }\n\n  function onDragTransitionEnd(\n    e: React.MouseEvent<HTMLElement, MouseEvent> | React.TouchEvent<HTMLElement>\n  ) {\n    if (drag.boundingRect) {\n      const { top, bottom, left, right } = drag.boundingRect;\n\n      if (\n        e.nativeEvent.type !== 'touchend' &&\n        props.pauseOnHover &&\n        drag.x >= left &&\n        drag.x <= right &&\n        drag.y >= top &&\n        drag.y <= bottom\n      ) {\n        pauseToast();\n      } else {\n        playToast();\n      }\n    }\n  }\n\n  function playToast() {\n    setIsRunning(true);\n  }\n\n  function pauseToast() {\n    setIsRunning(false);\n  }\n\n  function bindFocusEvents() {\n    if (!document.hasFocus()) pauseToast();\n\n    window.addEventListener('focus', playToast);\n    window.addEventListener('blur', pauseToast);\n  }\n\n  function unbindFocusEvents() {\n    window.removeEventListener('focus', playToast);\n    window.removeEventListener('blur', pauseToast);\n  }\n\n  function bindDragEvents() {\n    drag.didMove = false;\n    document.addEventListener('mousemove', onDragMove);\n    document.addEventListener('mouseup', onDragEnd);\n\n    document.addEventListener('touchmove', onDragMove);\n    document.addEventListener('touchend', onDragEnd);\n  }\n\n  function unbindDragEvents() {\n    document.removeEventListener('mousemove', onDragMove);\n    document.removeEventListener('mouseup', onDragEnd);\n\n    document.removeEventListener('touchmove', onDragMove);\n    document.removeEventListener('touchend', onDragEnd);\n  }\n\n  function onDragMove(e: MouseEvent | TouchEvent) {\n    const toast = toastRef.current!;\n    if (drag.canDrag && toast) {\n      drag.didMove = true;\n      if (isRunning) pauseToast();\n      drag.x = getX(e as DragEvent);\n      drag.y = getY(e as DragEvent);\n      if (props.draggableDirection === Direction.X) {\n        drag.delta = drag.x - drag.start;\n      } else {\n        drag.delta = drag.y - drag.start;\n      }\n\n      // prevent false positif during a toast click\n      if (drag.start !== drag.x) drag.canCloseOnClick = false;\n      toast.style.transform = `translate${props.draggableDirection}(${drag.delta}px)`;\n      toast.style.opacity = `${\n        1 - Math.abs(drag.delta / drag.removalDistance)\n      }`;\n    }\n  }\n\n  function onDragEnd() {\n    unbindDragEvents();\n    const toast = toastRef.current!;\n    if (drag.canDrag && drag.didMove && toast) {\n      drag.canDrag = false;\n      if (Math.abs(drag.delta) > drag.removalDistance) {\n        setPreventExitTransition(true);\n        props.closeToast();\n        return;\n      }\n      toast.style.transition = 'transform 0.2s, opacity 0.2s';\n      toast.style.transform = `translate${props.draggableDirection}(0)`;\n      toast.style.opacity = '1';\n    }\n  }\n\n  const eventHandlers: DOMAttributes<HTMLElement> = {\n    onMouseDown: onDragStart,\n    onTouchStart: onDragStart,\n    onMouseUp: onDragTransitionEnd,\n    onTouchEnd: onDragTransitionEnd\n  };\n\n  if (autoClose && pauseOnHover) {\n    eventHandlers.onMouseEnter = pauseToast;\n    eventHandlers.onMouseLeave = playToast;\n  }\n\n  // prevent toast from closing when user drags the toast\n  if (closeOnClick) {\n    eventHandlers.onClick = (e: React.MouseEvent) => {\n      onClick && onClick(e);\n      drag.canCloseOnClick && closeToast();\n    };\n  }\n\n  return {\n    playToast,\n    pauseToast,\n    isRunning,\n    preventExitTransition,\n    toastRef,\n    eventHandlers\n  };\n}\n", "import React from 'react';\nimport { Default } from '../utils';\nimport { Theme, TypeOptions } from '../types';\n\nexport interface CloseButtonProps {\n  closeToast: (e: React.MouseEvent<HTMLElement>) => void;\n  type: TypeOptions;\n  ariaLabel?: string;\n  theme: Theme;\n}\n\nexport function CloseButton({\n  closeToast,\n  theme,\n  ariaLabel = 'close'\n}: CloseButtonProps) {\n  return (\n    <button\n      className={`${Default.CSS_NAMESPACE}__close-button ${Default.CSS_NAMESPACE}__close-button--${theme}`}\n      type=\"button\"\n      onClick={e => {\n        e.stopPropagation();\n        closeToast(e);\n      }}\n      aria-label={ariaLabel}\n    >\n      <svg aria-hidden=\"true\" viewBox=\"0 0 14 16\">\n        <path\n          fillRule=\"evenodd\"\n          d=\"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z\"\n        />\n      </svg>\n    </button>\n  );\n}\n", "import React from 'react';\nimport cx from 'clsx';\n\nimport { Default, isFn, Type } from './../utils';\nimport { TypeOptions, ToastClassName, Theme } from '../types';\n\nexport interface ProgressBarProps {\n  /**\n   * The animation delay which determine when to close the toast\n   */\n  delay: number;\n\n  /**\n   * Whether or not the animation is running or paused\n   */\n  isRunning: boolean;\n\n  /**\n   * Func to close the current toast\n   */\n  closeToast: () => void;\n\n  /**\n   * Optional type : info, success ...\n   */\n  type?: TypeOptions;\n\n  /**\n   * The theme that is currently used\n   */\n  theme: Theme;\n\n  /**\n   * Hide or not the progress bar\n   */\n  hide?: boolean;\n\n  /**\n   * Optionnal className\n   */\n  className?: ToastClassName;\n\n  /**\n   * Optionnal inline style\n   */\n  style?: React.CSSProperties;\n\n  /**\n   * Tell wether or not controlled progress bar is used\n   */\n  controlledProgress?: boolean;\n\n  /**\n   * Controlled progress value\n   */\n  progress?: number | string;\n\n  /**\n   * Support rtl content\n   */\n  rtl?: boolean;\n\n  /**\n   * Tell if the component is visible on screen or not\n   */\n  isIn?: boolean;\n}\n\nexport function ProgressBar({\n  delay,\n  isRunning,\n  closeToast,\n  type = Type.DEFAULT,\n  hide,\n  className,\n  style: userStyle,\n  controlledProgress,\n  progress,\n  rtl,\n  isIn,\n  theme\n}: ProgressBarProps) {\n  const isHidden = hide || (controlledProgress && progress === 0);\n  const style: React.CSSProperties = {\n    ...userStyle,\n    animationDuration: `${delay}ms`,\n    animationPlayState: isRunning ? 'running' : 'paused',\n    opacity: isHidden ? 0 : 1\n  };\n\n  if (controlledProgress) style.transform = `scaleX(${progress})`;\n  const defaultClassName = cx(\n    `${Default.CSS_NAMESPACE}__progress-bar`,\n    controlledProgress\n      ? `${Default.CSS_NAMESPACE}__progress-bar--controlled`\n      : `${Default.CSS_NAMESPACE}__progress-bar--animated`,\n    `${Default.CSS_NAMESPACE}__progress-bar-theme--${theme}`,\n    `${Default.CSS_NAMESPACE}__progress-bar--${type}`,\n    {\n      [`${Default.CSS_NAMESPACE}__progress-bar--rtl`]: rtl\n    }\n  );\n  const classNames = isFn(className)\n    ? className({\n        rtl,\n        type,\n        defaultClassName\n      })\n    : cx(defaultClassName, className);\n\n  // 🧐 controlledProgress is derived from progress\n  // so if controlledProgress is set\n  // it means that this is also the case for progress\n  const animationEvent = {\n    [controlledProgress && (progress as number)! >= 1\n      ? 'onTransitionEnd'\n      : 'onAnimationEnd']:\n      controlledProgress && (progress as number)! < 1\n        ? null\n        : () => {\n            isIn && closeToast();\n          }\n  };\n\n  // TODO: add aria-valuenow, aria-valuemax, aria-valuemin\n\n  return (\n    <div\n      role=\"progressbar\"\n      aria-hidden={isHidden ? 'true' : 'false'}\n      aria-label=\"notification timer\"\n      className={classNames}\n      style={style}\n      {...animationEvent}\n    />\n  );\n}\n", "import React, { cloneElement, isValidElement, ReactNode } from 'react';\nimport cx from 'clsx';\n\nimport { ProgressBar } from './ProgressBar';\nimport { CloseButton } from './CloseButton';\nimport { ToastProps } from '../types';\nimport { Default, isFn } from '../utils';\nimport { useToast } from '../hooks/useToast';\n\nexport const Toast: React.FC<ToastProps> = props => {\n  const { isRunning, preventExitTransition, toastRef, eventHandlers } =\n    useToast(props);\n  const {\n    closeButton,\n    children,\n    autoClose,\n    onClick,\n    type,\n    hideProgressBar,\n    closeToast,\n    transition: Transition,\n    position,\n    className,\n    style,\n    bodyClassName,\n    bodyStyle,\n    progressClassName,\n    progressStyle,\n    updateId,\n    role,\n    progress,\n    rtl,\n    toastId,\n    deleteToast,\n    isIn,\n    isLoading,\n    iconOut,\n    closeOnClick,\n    theme\n  } = props;\n  const defaultClassName = cx(\n    `${Default.CSS_NAMESPACE}__toast`,\n    `${Default.CSS_NAMESPACE}__toast-theme--${theme}`,\n    `${Default.CSS_NAMESPACE}__toast--${type}`,\n    {\n      [`${Default.CSS_NAMESPACE}__toast--rtl`]: rtl\n    },\n    {\n      [`${Default.CSS_NAMESPACE}__toast--close-on-click`]: closeOnClick\n    }\n  );\n  const cssClasses = isFn(className)\n    ? className({\n        rtl,\n        position,\n        type,\n        defaultClassName\n      })\n    : cx(defaultClassName, className);\n  const isProgressControlled = !!progress || !autoClose;\n\n  const closeButtonProps = { closeToast, type, theme };\n  let Close: React.ReactNode = null;\n\n  if (closeButton === false) {\n    // hide\n  } else if (isFn(closeButton)) {\n    Close = closeButton(closeButtonProps);\n  } else if (isValidElement(closeButton)) {\n    Close = cloneElement(closeButton, closeButtonProps);\n  } else {\n    Close = CloseButton(closeButtonProps);\n  }\n\n  return (\n    <Transition\n      isIn={isIn}\n      done={deleteToast}\n      position={position}\n      preventExitTransition={preventExitTransition}\n      nodeRef={toastRef}\n    >\n      <div\n        id={toastId as string}\n        onClick={onClick}\n        className={cssClasses}\n        {...eventHandlers}\n        style={style}\n        ref={toastRef}\n      >\n        <div\n          {...(isIn && { role: role })}\n          className={\n            isFn(bodyClassName)\n              ? bodyClassName({ type })\n              : cx(`${Default.CSS_NAMESPACE}__toast-body`, bodyClassName)\n          }\n          style={bodyStyle}\n        >\n          {iconOut != null && (\n            <div\n              className={cx(`${Default.CSS_NAMESPACE}__toast-icon`, {\n                [`${Default.CSS_NAMESPACE}--animate-icon ${Default.CSS_NAMESPACE}__zoom-enter`]:\n                  !isLoading\n              })}\n            >\n              {iconOut}\n            </div>\n          )}\n          <div>{children as ReactNode}</div>\n        </div>\n        {Close}\n        <ProgressBar\n          {...(updateId && !isProgressControlled\n            ? { key: `pb-${updateId}` }\n            : {})}\n          rtl={rtl}\n          theme={theme}\n          delay={autoClose as number}\n          isRunning={isRunning}\n          isIn={isIn}\n          closeToast={closeToast}\n          hide={hideProgressBar}\n          type={type}\n          style={progressStyle}\n          className={progressClassName}\n          controlledProgress={isProgressControlled}\n          progress={progress || 0}\n        />\n      </div>\n    </Transition>\n  );\n};\n", "import { Default, cssTransition } from '../utils';\n\nconst getConfig = (animationName: string, appendPosition = false) => ({\n  enter: `${Default.CSS_NAMESPACE}--animate ${Default.CSS_NAMESPACE}__${animationName}-enter`,\n  exit: `${Default.CSS_NAMESPACE}--animate ${Default.CSS_NAMESPACE}__${animationName}-exit`,\n  appendPosition\n});\n\nconst Bounce = cssTransition(getConfig('bounce', true));\n\nconst Slide = cssTransition(getConfig('slide', true));\n\nconst Zoom = cssTransition(getConfig('zoom'));\n\nconst Flip = cssTransition(getConfig('flip'));\n\nexport { Bounce, Slide, Zoom, Flip };\n", "// https://github.com/yannickcr/eslint-plugin-react/issues/3140\n/* eslint react/prop-types: \"off\" */\nimport React, { forwardRef, StyleHTMLAttributes, useEffect } from 'react';\nimport cx from 'clsx';\n\nimport { Toast } from './Toast';\nimport { CloseButton } from './CloseButton';\nimport { Bounce } from './Transitions';\nimport { Direction, Default, parseClassName, isFn } from '../utils';\nimport { useToastContainer } from '../hooks/useToastContainer';\nimport { ToastContainerProps, ToastPosition } from '../types';\n\nexport const ToastContainer = forwardRef<HTMLDivElement, ToastContainerProps>(\n  (props, ref) => {\n    const { getToastToRender, containerRef, isToastActive } =\n      useToastContainer(props);\n    const { className, style, rtl, containerId } = props;\n\n    function getClassName(position: ToastPosition) {\n      const defaultClassName = cx(\n        `${Default.CSS_NAMESPACE}__toast-container`,\n        `${Default.CSS_NAMESPACE}__toast-container--${position}`,\n        { [`${Default.CSS_NAMESPACE}__toast-container--rtl`]: rtl }\n      );\n      return isFn(className)\n        ? className({\n            position,\n            rtl,\n            defaultClassName\n          })\n        : cx(defaultClassName, parseClassName(className));\n    }\n\n    useEffect(() => {\n      if (ref) {\n        (ref as React.MutableRefObject<HTMLDivElement>).current =\n          containerRef.current!;\n      }\n    }, []);\n\n    return (\n      <div\n        ref={containerRef}\n        className={Default.CSS_NAMESPACE as string}\n        id={containerId as string}\n      >\n        {getToastToRender((position, toastList) => {\n          const containerStyle: React.CSSProperties = !toastList.length\n            ? { ...style, pointerEvents: 'none' }\n            : { ...style };\n\n          return (\n            <div\n              className={getClassName(position)}\n              style={containerStyle}\n              key={`container-${position}`}\n            >\n              {toastList.map(({ content, props: toastProps }, i) => {\n                return (\n                  <Toast\n                    {...toastProps}\n                    isIn={isToastActive(toastProps.toastId)}\n                    style={\n                      {\n                        ...toastProps.style,\n                        '--nth': i + 1,\n                        '--len': toastList.length\n                      } as StyleHTMLAttributes<HTMLDivElement>\n                    }\n                    key={`toast-${toastProps.key}`}\n                  >\n                    {content}\n                  </Toast>\n                );\n              })}\n            </div>\n          );\n        })}\n      </div>\n    );\n  }\n);\n\nToastContainer.displayName = 'ToastContainer';\n\nToastContainer.defaultProps = {\n  position: 'top-right',\n  transition: Bounce,\n  autoClose: 5000,\n  closeButton: CloseButton,\n  pauseOnHover: true,\n  pauseOnFocusLoss: true,\n  closeOnClick: true,\n  draggable: true,\n  draggablePercent: Default.DRAGGABLE_PERCENT as number,\n  draggableDirection: Direction.X,\n  role: 'alert',\n  theme: 'light'\n};\n", "import { P<PERSON><PERSON><PERSON>, TYPE, isStr, isNum, isFn, Type } from '../utils';\nimport { eventManager, OnChangeCallback, Event } from './eventManager';\nimport {\n  ToastContent,\n  ToastOptions,\n  ToastProps,\n  Id,\n  UpdateOptions,\n  ClearWaitingQueueParams,\n  NotValidatedToastProps,\n  TypeOptions\n} from '../types';\nimport { ContainerInstance } from '../hooks';\n\ninterface EnqueuedToast {\n  content: ToastContent<any>;\n  options: NotValidatedToastProps;\n}\n\nlet containers = new Map<ContainerInstance | Id, ContainerInstance>();\nlet latestInstance: ContainerInstance | Id;\nlet queue: EnqueuedToast[] = [];\nlet TOAST_ID = 1;\n\n/**\n * Get the toast by id, given it's in the DOM, otherwise returns null\n */\nfunction getToast(toastId: Id, { containerId }: ToastOptions) {\n  const container = containers.get(containerId || latestInstance);\n  return container && container.getToast(toastId);\n}\n\n/**\n * Generate a random toastId\n */\nfunction generateToastId() {\n  return `${TOAST_ID++}`;\n}\n\n/**\n * Generate a toastId or use the one provided\n */\nfunction getToastId(options?: ToastOptions) {\n  return options && (isStr(options.toastId) || isNum(options.toastId))\n    ? options.toastId\n    : generateToastId();\n}\n\n/**\n * If the container is not mounted, the toast is enqueued and\n * the container lazy mounted\n */\nfunction dispatchToast<TData>(\n  content: ToastContent<TData>,\n  options: NotValidatedToastProps\n): Id {\n  if (containers.size > 0) {\n    eventManager.emit(Event.Show, content, options);\n  } else {\n    queue.push({ content, options });\n  }\n\n  return options.toastId;\n}\n\n/**\n * Merge provided options with the defaults settings and generate the toastId\n */\nfunction mergeOptions(type: string, options?: ToastOptions) {\n  return {\n    ...options,\n    type: (options && options.type) || type,\n    toastId: getToastId(options)\n  } as NotValidatedToastProps;\n}\n\nfunction createToastByType(type: string) {\n  return <TData = unknown>(\n    content: ToastContent<TData>,\n    options?: ToastOptions\n  ) => dispatchToast(content, mergeOptions(type, options));\n}\n\nfunction toast<TData = unknown>(\n  content: ToastContent<TData>,\n  options?: ToastOptions\n) {\n  return dispatchToast(content, mergeOptions(Type.DEFAULT, options));\n}\n\ntoast.loading = <TData = unknown>(\n  content: ToastContent<TData>,\n  options?: ToastOptions\n) =>\n  dispatchToast(\n    content,\n    mergeOptions(Type.DEFAULT, {\n      isLoading: true,\n      autoClose: false,\n      closeOnClick: false,\n      closeButton: false,\n      draggable: false,\n      ...options\n    })\n  );\n\nexport interface ToastPromiseParams<\n  TData = unknown,\n  TError = unknown,\n  TPending = unknown\n> {\n  pending?: string | UpdateOptions<TPending>;\n  success?: string | UpdateOptions<TData>;\n  error?: string | UpdateOptions<TError>;\n}\n\nfunction handlePromise<TData = unknown, TError = unknown, TPending = unknown>(\n  promise: Promise<TData> | (() => Promise<TData>),\n  { pending, error, success }: ToastPromiseParams<TData, TError, TPending>,\n  options?: ToastOptions\n) {\n  let id: Id;\n\n  if (pending) {\n    id = isStr(pending)\n      ? toast.loading(pending, options)\n      : toast.loading(pending.render, {\n          ...options,\n          ...(pending as ToastOptions)\n        });\n  }\n\n  const resetParams = {\n    isLoading: null,\n    autoClose: null,\n    closeOnClick: null,\n    closeButton: null,\n    draggable: null\n  };\n\n  const resolver = <T>(\n    type: TypeOptions,\n    input: string | UpdateOptions<T> | undefined,\n    result: T\n  ) => {\n    // Remove the toast if the input has not been provided. This prevents the toast from hanging\n    // in the pending state if a success/error toast has not been provided.\n    if (input == null) {\n      toast.dismiss(id);\n      return;\n    }\n\n    const baseParams = {\n      type,\n      ...resetParams,\n      ...options,\n      data: result\n    };\n    const params = isStr(input) ? { render: input } : input;\n\n    // if the id is set we know that it's an update\n    if (id) {\n      toast.update(id, {\n        ...baseParams,\n        ...params\n      } as UpdateOptions);\n    } else {\n      // using toast.promise without loading\n      toast(params!.render, {\n        ...baseParams,\n        ...params\n      } as ToastOptions);\n    }\n\n    return result;\n  };\n\n  const p = isFn(promise) ? promise() : promise;\n\n  //call the resolvers only when needed\n  p.then(result => resolver('success', success, result)).catch(err =>\n    resolver('error', error, err)\n  );\n\n  return p;\n}\n\ntoast.promise = handlePromise;\ntoast.success = createToastByType(Type.SUCCESS);\ntoast.info = createToastByType(Type.INFO);\ntoast.error = createToastByType(Type.ERROR);\ntoast.warning = createToastByType(Type.WARNING);\ntoast.warn = toast.warning;\ntoast.dark = (content: ToastContent, options?: ToastOptions) =>\n  dispatchToast(\n    content,\n    mergeOptions(Type.DEFAULT, {\n      theme: 'dark',\n      ...options\n    })\n  );\n\n/**\n * Remove toast programmaticaly\n */\ntoast.dismiss = (id?: Id) => {\n  if (containers.size > 0) {\n    eventManager.emit(Event.Clear, id);\n  } else {\n    queue = queue.filter(t => id != null && t.options.toastId !== id);\n  }\n};\n\n/**\n * Clear waiting queue when limit is used\n */\ntoast.clearWaitingQueue = (params: ClearWaitingQueueParams = {}) =>\n  eventManager.emit(Event.ClearWaitingQueue, params);\n\n/**\n * return true if one container is displaying the toast\n */\ntoast.isActive = (id: Id) => {\n  let isToastActive = false;\n\n  containers.forEach(container => {\n    if (container.isToastActive && container.isToastActive(id)) {\n      isToastActive = true;\n    }\n  });\n\n  return isToastActive;\n};\n\ntoast.update = <TData = unknown>(\n  toastId: Id,\n  options: UpdateOptions<TData> = {}\n) => {\n  setTimeout(() => {\n    const toast = getToast(toastId, options as ToastOptions);\n    if (toast) {\n      const { props: oldOptions, content: oldContent } = toast;\n\n      const nextOptions = {\n        delay: 100,\n        ...oldOptions,\n        ...options,\n        toastId: options.toastId || toastId,\n        updateId: generateToastId()\n      } as ToastProps & UpdateOptions;\n\n      if (nextOptions.toastId !== toastId) nextOptions.staleId = toastId;\n\n      const content = nextOptions.render || oldContent;\n      delete nextOptions.render;\n\n      dispatchToast(content, nextOptions);\n    }\n  }, 0);\n};\n\n/**\n * Used for controlled progress bar.\n */\ntoast.done = (id: Id) => {\n  toast.update(id, {\n    progress: 1\n  });\n};\n\n/**\n * Subscribe to change when a toast is added, removed and updated\n *\n * Usage:\n * ```\n * const unsubscribe = toast.onChange((payload) => {\n *   switch (payload.status) {\n *   case \"added\":\n *     // new toast added\n *     break;\n *   case \"updated\":\n *     // toast updated\n *     break;\n *   case \"removed\":\n *     // toast has been removed\n *     break;\n *   }\n * })\n * ```\n */\ntoast.onChange = (callback: OnChangeCallback) => {\n  eventManager.on(Event.Change, callback);\n  return () => {\n    eventManager.off(Event.Change, callback);\n  };\n};\n\n/**\n * @deprecated\n * Will be removed in the next major release.\n */\ntoast.POSITION = POSITION;\n\n/**\n * @deprecated\n * Will be removed in the next major release.\n */\ntoast.TYPE = TYPE;\n\n/**\n * Wait until the ToastContainer is mounted to dispatch the toast\n * and attach isActive method\n */\neventManager\n  .on(Event.DidMount, (containerInstance: ContainerInstance) => {\n    latestInstance = containerInstance.containerId || containerInstance;\n    containers.set(latestInstance, containerInstance);\n\n    queue.forEach(item => {\n      eventManager.emit(Event.Show, item.content, item.options);\n    });\n\n    queue = [];\n  })\n  .on(Event.WillUnmount, (containerInstance: ContainerInstance) => {\n    containers.delete(containerInstance.containerId || containerInstance);\n\n    if (containers.size === 0) {\n      eventManager\n        .off(Event.Show)\n        .off(Event.Clear)\n        .off(Event.ClearWaitingQueue);\n    }\n  });\n\nexport { toast };\n", "import { ToastPosition, TypeOptions } from '../types';\n\ntype KeyOfPosition =\n  | 'TOP_LEFT'\n  | 'TOP_RIGHT'\n  | 'TOP_CENTER'\n  | 'BOTTOM_LEFT'\n  | 'BOTTOM_RIGHT'\n  | 'BOTTOM_CENTER';\n\ntype KeyOfType = 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR' | 'DEFAULT';\n\n/**\n * @deprecated\n */\nexport const POSITION: { [key in KeyOfPosition]: ToastPosition } = {\n  TOP_LEFT: 'top-left',\n  TOP_RIGHT: 'top-right',\n  TOP_CENTER: 'top-center',\n  BOTTOM_LEFT: 'bottom-left',\n  BOTTOM_RIGHT: 'bottom-right',\n  BOTTOM_CENTER: 'bottom-center'\n};\n\n/**\n * @deprecated\n */\nexport const TYPE: { [key in KeyOfType]: TypeOptions } = {\n  INFO: 'info',\n  SUCCESS: 'success',\n  WARNING: 'warning',\n  ERROR: 'error',\n  DEFAULT: 'default'\n};\n\nexport const enum Type {\n  INFO = 'info',\n  SUCCESS = 'success',\n  WARNING = 'warning',\n  ERROR = 'error',\n  DEFAULT = 'default'\n}\n\nexport const enum Default {\n  COLLAPSE_DURATION = 300,\n  DEBOUNCE_DURATION = 50,\n  CSS_NAMESPACE = 'Toastify',\n  DRAGGABLE_PERCENT = 80\n}\n\nexport const enum Direction {\n  X = 'x',\n  Y = 'y'\n}\n\nexport const enum SyntheticEvent {\n  ENTRANCE_ANIMATION_END = 'd'\n}\n"], "names": ["isNum", "v", "isNaN", "isStr", "isFn", "parseClassName", "canBeRendered", "content", "isValidElement", "collapseToast", "node", "done", "duration", "scrollHeight", "style", "requestAnimationFrame", "minHeight", "height", "transition", "padding", "margin", "setTimeout", "cssTransition", "enter", "exit", "appendPosition", "collapse", "collapseDuration", "children", "position", "preventExitTransition", "nodeRef", "isIn", "enterClassName", "exitClassName", "animationStep", "useRef", "useLayoutEffect", "current", "classToToken", "split", "onEntered", "e", "target", "dispatchEvent", "Event", "removeEventListener", "type", "classList", "remove", "add", "addEventListener", "useEffect", "onExited", "className", "React", "toToastItem", "toast", "status", "containerId", "props", "id", "toastId", "theme", "data", "isLoading", "icon", "eventManager", "list", "Map", "emitQueue", "on", "event", "callback", "this", "has", "set", "get", "push", "off", "cb", "filter", "delete", "cancelEmit", "timers", "for<PERSON>ach", "clearTimeout", "emit", "timer", "Svg", "_ref", "rest", "viewBox", "width", "fill", "Icons", "info", "d", "warning", "success", "error", "spinner", "useToastContainer", "forceUpdate", "useReducer", "x", "toastIds", "setToastIds", "useState", "containerRef", "toastToRender", "isToastActive", "indexOf", "instance", "<PERSON><PERSON><PERSON>", "displayedToast", "count", "queue", "getToast", "clearWaitingQueue", "limit", "length", "removeToast", "state", "dequeueToast", "toastContent", "toastProps", "staleId", "shift", "appendToast", "buildToast", "delay", "options", "enableMultiContainer", "updateId", "isNotValid", "closeToast", "isNotAnUpdate", "toastStyle", "key", "Object", "fromEntries", "entries", "_ref3", "_", "toastClassName", "bodyClassName", "progressClassName", "autoClose", "toastAutoClose", "containerAutoClose", "deleteToast", "removed", "queueLen", "freeSlot", "toDequeue", "i", "iconOut", "Icon", "iconProps", "cloneElement", "maybeIcon", "getIcon", "onOpen", "onClose", "closeButton", "clear", "getToastToRender", "to<PERSON><PERSON>", "collection", "Array", "from", "values", "newestOnTop", "reverse", "p", "getX", "targetTouches", "clientX", "getY", "clientY", "useToast", "isRunning", "setIsRunning", "setPreventExitTransition", "toastRef", "drag", "start", "y", "delta", "removalDistance", "canCloseOnClick", "canDrag", "boundingRect", "did<PERSON>ove", "syncProps", "pauseOnHover", "onClick", "closeOnClick", "onDragStart", "draggable", "nativeEvent", "preventDefault", "document", "onDragMove", "onDragEnd", "getBoundingClientRect", "draggableDirection", "offsetWidth", "draggablePercent", "offsetHeight", "onDragTransitionEnd", "top", "bottom", "left", "right", "pauseToast", "playToast", "transform", "opacity", "Math", "abs", "once", "pauseOnFocusLoss", "hasFocus", "window", "eventHandlers", "onMouseDown", "onTouchStart", "onMouseUp", "onTouchEnd", "onMouseEnter", "onMouseLeave", "CloseButton", "aria<PERSON><PERSON><PERSON>", "stopPropagation", "fillRule", "ProgressBar", "hide", "userStyle", "controlledProgress", "progress", "rtl", "isHidden", "animationDuration", "animationPlayState", "defaultClassName", "cx", "classNames", "role", "Toast", "hideProgressBar", "Transition", "bodyStyle", "progressStyle", "cssClasses", "isProgressControlled", "closeButtonProps", "Close", "ref", "getConfig", "animationName", "<PERSON><PERSON><PERSON>", "Slide", "Zoom", "Flip", "ToastContainer", "forwardRef", "getClassName", "toastList", "containerStyle", "pointerEvents", "map", "displayName", "defaultProps", "latestInstance", "containers", "TOAST_ID", "generateToastId", "getToastId", "dispatchToast", "size", "mergeOptions", "createToastByType", "loading", "promise", "pending", "render", "resetParams", "resolver", "input", "result", "dismiss", "baseParams", "params", "update", "then", "catch", "err", "warn", "dark", "t", "isActive", "container", "oldOptions", "<PERSON><PERSON><PERSON><PERSON>", "nextOptions", "onChange", "POSITION", "TOP_LEFT", "TOP_RIGHT", "TOP_CENTER", "BOTTOM_LEFT", "BOTTOM_RIGHT", "BOTTOM_CENTER", "TYPE", "INFO", "SUCCESS", "WARNING", "ERROR", "DEFAULT", "containerInstance", "item"], "mappings": "mKAEaA,EAASC,GACP,iBAANA,IAAmBC,MAAMD,GAErBE,EAASF,GAAqC,iBAANA,EAExCG,EAAQH,GAAuC,mBAANA,EAEzCI,EAAkBJ,GAAYE,EAAMF,IAAMG,EAAKH,GAAKA,EAAI,KAUxDK,EAAoBC,GAC/BC,iBAAeD,IAAYJ,EAAMI,IAAYH,EAAKG,IAAYP,EAAMO,YCftDE,EACdC,EACAC,EACAC,YAAAA,IAAAA,OAEA,MAAMC,aAAEA,EAAFC,MAAgBA,GAAUJ,EAEhCK,sBAAsB,KACpBD,EAAME,UAAY,UAClBF,EAAMG,OAASJ,EAAe,KAC9BC,EAAMI,kBAAoBN,MAE1BG,sBAAsB,KACpBD,EAAMG,OAAS,IACfH,EAAMK,QAAU,IAChBL,EAAMM,OAAS,IACfC,WAAWV,EAAMC,gBCmCPU,SAAcC,MAC5BA,EAD4BC,KAE5BA,EAF4BC,eAG5BA,GAAiB,EAHWC,SAI5BA,GAAW,EAJiBC,iBAK5BA,SAEA,uBAAgCC,SAC9BA,EAD8BC,SAE9BA,EAF8BC,sBAG9BA,EAH8BnB,KAI9BA,EAJ8BoB,QAK9BA,EAL8BC,KAM9BA,KAEA,MAAMC,EAAiBR,KAAoBF,MAAUM,IAAaN,EAC5DW,EAAgBT,KAAoBD,MAASK,IAAaL,EAC1DW,EAAgBC,YA8CtB,OA5CAC,kBAAgB,KACd,MAAM3B,EAAOqB,EAAQO,QACfC,EAAeN,EAAeO,MAAM,KAEpCC,EAAaC,IACbA,EAAEC,SAAWZ,EAAQO,UAEzB5B,EAAKkC,cAAc,IAAIC,YACvBnC,EAAKoC,oBAAoB,eAAgBL,GACzC/B,EAAKoC,oBAAoB,kBAAmBL,OAE1CN,EAAcG,SACH,oBAAXI,EAAEK,MAEFrC,EAAKsC,UAAUC,UAAUV,KAK3B7B,EAAKsC,UAAUE,OAAOX,GACtB7B,EAAKyC,iBAAiB,eAAgBV,GACtC/B,EAAKyC,iBAAiB,kBAAmBV,IAI1C,IAEHW,YAAU,KACR,MAAM1C,EAAOqB,EAAQO,QAEfe,EAAW,KACf3C,EAAKoC,oBAAoB,eAAgBO,GACzC3B,EAAWjB,EAAcC,EAAMC,EAAMgB,GAAoBhB,KAStDqB,IAAMF,EAAwBuB,KALjClB,EAAcG,UACd5B,EAAK4C,eAAiBpB,IACtBxB,EAAKyC,iBAAiB,eAAgBE,MAIvC,CAACrB,IAEGuB,gDAAG3B,aCrHE4B,EAAYC,EAAcC,GACxC,OAAgB,MAATD,EACH,CACElD,QAASkD,EAAMlD,QACfoD,YAAaF,EAAMG,MAAMD,YACzBE,GAAIJ,EAAMG,MAAME,QAChBC,MAAON,EAAMG,MAAMG,MACnBhB,KAAMU,EAAMG,MAAMb,KAClBiB,KAAMP,EAAMG,MAAMI,MAAQ,GAC1BC,UAAWR,EAAMG,MAAMK,UACvBC,KAAMT,EAAMG,MAAMM,KAClBR,OAAAA,GAGD,SCgDMS,EAA6B,CACxCC,KAAM,IAAIC,IACVC,UAAW,IAAID,IAEfE,GAAGC,EAAcC,GAGf,OAFAC,KAAKN,KAAKO,IAAIH,IAAUE,KAAKN,KAAKQ,IAAIJ,EAAO,IAC7CE,KAAKN,KAAKS,IAAIL,GAAQM,KAAKL,SAI7BM,IAAIP,EAAOC,GACT,GAAIA,EAAU,CACZ,MAAMO,EAAKN,KAAKN,KAAKS,IAAIL,GAAQS,OAAOD,GAAMA,IAAOP,GAErD,OADAC,KAAKN,KAAKQ,IAAIJ,EAAOQ,QAIvB,OADAN,KAAKN,KAAKc,OAAOV,SAInBW,WAAWX,GACT,MAAMY,EAASV,KAAKJ,UAAUO,IAAIL,GAMlC,OALIY,IACFA,EAAOC,QAAQC,cACfZ,KAAKJ,UAAUY,OAAOV,UAc1Be,KAAKf,GACHE,KAAKN,KAAKO,IAAIH,IACZE,KAAKN,KAAKS,IAAIL,GAAQa,QAASZ,IAC7B,MAAMe,EAAmBnE,WAAW,KAElCoD,kCACC,GAEHC,KAAKJ,UAAUK,IAAIH,IAAUE,KAAKJ,UAAUM,IAAIJ,EAAO,IACvDE,KAAKJ,UAAUO,IAAIL,GAAQM,KAAKU,OChGlCC,EAAkCC,QAAC3B,MAAEA,EAAFhB,KAASA,KAAS4C,YACzDpC,+BACEqC,QAAQ,YACRC,MAAM,OACN5E,OAAO,OACP6E,KACY,YAAV/B,EACI,4CAC6BhB,QAE/B4C,KAwCKI,EAAQ,CACnBC,KA7BF,SAAcpC,GACZ,OACEL,wBAACkC,MAAQ7B,GACPL,gCAAM0C,EAAE,oPA2BZC,QAtCF,SAAiBtC,GACf,OACEL,wBAACkC,MAAQ7B,GACPL,gCAAM0C,EAAE,ifAoCZE,QAvBF,SAAiBvC,GACf,OACEL,wBAACkC,MAAQ7B,GACPL,gCAAM0C,EAAE,iLAqBZG,MAhBF,SAAexC,GACb,OACEL,wBAACkC,MAAQ7B,GACPL,gCAAM0C,EAAE,yUAcZI,QATF,WACE,OAAO9C,+BAAKD,2CCZEgD,EAAkB1C,GAChC,OAAS2C,GAAeC,aAAWC,GAAKA,EAAI,EAAG,IACxCC,EAAUC,GAAeC,WAAe,IACzCC,EAAezE,SAAO,MACtB0E,EAAgB1E,SAAO,IAAIiC,KAAkB/B,QAC7CyE,EAAiBlD,IAAqC,IAA1B6C,EAASM,QAAQnD,GAC7CoD,EAAW7E,SAA0B,CACzC8E,SAAU,EACVC,eAAgB,EAChBC,MAAO,EACPC,MAAO,GACPzD,MAAAA,EACAD,YAAa,KACboD,cAAAA,EACAO,SAAUzD,GAAMiD,EAAcjC,IAAIhB,KACjCvB,QAuBH,SAASiF,SAAkB5D,YAAEA,KAC3B,MAAM6D,MAAEA,GAAUP,EAASrD,OACvB4D,GAAW7D,GAAesD,EAAStD,cAAgBA,IACrDsD,EAASG,OAASH,EAASI,MAAMI,OACjCR,EAASI,MAAQ,IAIrB,SAASK,EAAY5D,GACnB6C,EAAYgB,GACC,MAAX7D,EAAkB,GAAK6D,EAAM1C,OAAOpB,GAAMA,IAAOC,IAIrD,SAAS8D,IACP,MAAMC,aAAEA,EAAFC,WAAgBA,EAAhBC,QAA4BA,GAChCd,EAASI,MAAMW,QACjBC,EAAYJ,EAAcC,EAAYC,GAkBxC,SAASG,EACP3H,SACA4H,MAAEA,EAAFJ,QAASA,KAAYK,KAErB,IAAK9H,EAAcC,IAdrB,SAAoB6H,GAClB,OACGvB,EAAavE,SACb2E,EAASrD,MAAMyE,sBACdD,EAAQzE,cAAgBsD,EAASrD,MAAMD,aACxCmD,EAAcnC,IAAIyD,EAAQtE,UAAgC,MAApBsE,EAAQE,SASlBC,CAAWH,GAAU,OAEpD,MAAMtE,QAAEA,EAAFwE,SAAWA,EAAXtE,KAAqBA,GAASoE,GAC9BxE,MAAEA,GAAUqD,EACZuB,EAAa,IAAMd,EAAY5D,GAC/B2E,EAA4B,MAAZH,EAElBG,GAAexB,EAASG,QAE5B,MAAMU,EAAa,IACdlE,EACH9C,MAAO8C,EAAM8E,WACbC,IAAK1B,EAASC,cACX0B,OAAOC,YACRD,OAAOE,QAAQV,GAASnD,OAAO8D,QAAEC,EAAG/I,YAAY,MAALA,KAE7C6D,QAAAA,EACAwE,SAAAA,EACAtE,KAAAA,EACAwE,WAAAA,EACAxG,MAAM,EACNsB,UAAWjD,EAAe+H,EAAQ9E,WAAaM,EAAMqF,gBACrDC,cAAe7I,EACb+H,EAAQc,eAAiBtF,EAAMsF,eAEjCC,kBAAmB9I,EACjB+H,EAAQe,mBAAqBvF,EAAMuF,mBAErCC,WAAWhB,EAAQnE,YN/IvBoF,EMiJ0BjB,EAAQgB,UNhJlCE,EMgJ6C1F,EAAMwF,WN9IhC,IAAnBC,GAA6BrJ,EAAMqJ,IAAmBA,EAAiB,EACnEA,EACAC,GM6IAC,cACE,MAAMC,EAAUhG,EAAYsD,EAAcjC,IAAIf,GAAW,WACzDgD,EAAc5B,OAAOpB,GAErBK,EAAaoB,OAAmBiE,GAEhC,MAAMC,EAAWxC,EAASI,MAAMI,OAQhC,GAPAR,EAASG,MACI,MAAXtD,EACImD,EAASG,MAAQH,EAASE,eAC1BF,EAASG,MAAQ,EAEnBH,EAASG,MAAQ,IAAGH,EAASG,MAAQ,GAErCqC,EAAW,EAAG,CAChB,MAAMC,EAAsB,MAAX5F,EAAkBmD,EAASrD,MAAM4D,MAAS,EAE3D,GAAiB,IAAbiC,GAA+B,IAAbC,EACpBzC,EAASE,iBACTS,QACK,CACL,MAAM+B,EAAYD,EAAWD,EAAWA,EAAWC,EACnDzC,EAASE,eAAiBwC,EAE1B,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAWC,IAAKhC,UAGtCrB,MN9KuB,IAC/B8C,EACAC,EMiLExB,EAAW+B,wBDnHS9F,MAAEA,EAAFhB,KAASA,EAATkB,UAAeA,EAAfC,KAA0BA,KAC5C4F,EAAwB,KAC5B,MAAMC,EAAY,CAAEhG,MAAAA,EAAOhB,KAAAA,GAgB3B,OAda,IAATmB,IAEO9D,EAAK8D,GACd4F,EAAO5F,EAAK6F,GACHvJ,iBAAe0D,GACxB4F,EAAOE,eAAa9F,EAAM6F,GACjB5J,EAAM+D,IAASlE,EAAMkE,GAC9B4F,EAAO5F,EACED,EACT6F,EAAO/D,EAAMM,UAfEtD,CAAAA,GAA6CA,KAAQgD,EAgB3DkE,CAAUlH,KACnB+G,EAAO/D,EAAMhD,GAAMgH,KAGdD,ECiGgBI,CAAQpC,GAEzB1H,EAAKgI,EAAQ+B,UAASrC,EAAWqC,OAAS/B,EAAQ+B,QAClD/J,EAAKgI,EAAQgC,WAAUtC,EAAWsC,QAAUhC,EAAQgC,SAExDtC,EAAWuC,YAAczG,EAAMyG,aAEH,IAAxBjC,EAAQiC,aAAyB/J,EAAc8H,EAAQiC,aACzDvC,EAAWuC,YAAcjC,EAAQiC,aACA,IAAxBjC,EAAQiC,cACjBvC,EAAWuC,aAAc/J,EAAcsD,EAAMyG,cACzCzG,EAAMyG,aAIZ,IAAIxC,EAAetH,EAEfC,iBAAeD,KAAaJ,EAAMI,EAAQwC,MAC5C8E,EAAemC,eAAazJ,EAAyB,CACnDiI,WAAAA,EACAV,WAAAA,EACA9D,KAAAA,IAEO5D,EAAKG,KACdsH,EAAetH,EAAQ,CAAEiI,WAAAA,EAAYV,WAAAA,EAAY9D,KAAAA,KAKjDJ,EAAM4D,OACN5D,EAAM4D,MAAQ,GACdP,EAASG,MAAQxD,EAAM4D,OACvBiB,EAEAxB,EAASI,MAAMvC,KAAK,CAAE+C,aAAAA,EAAcC,WAAAA,EAAYC,QAAAA,IACvC/H,EAAMmI,GACf9G,WAAW,KACT4G,EAAYJ,EAAcC,EAAYC,IACrCI,GAEHF,EAAYJ,EAAcC,EAAYC,GAI1C,SAASE,EACP1H,EACAuH,EACAC,GAEA,MAAMjE,QAAEA,GAAYgE,EAEhBC,GAASjB,EAAc5B,OAAO6C,GAElC,MAAMtE,EAAQ,CACZlD,QAAAA,EACAqD,MAAOkE,GAEThB,EAAclC,IAAId,EAASL,GAE3BkD,EAAYgB,GAAS,IAAIA,EAAO7D,GAASmB,OAAOpB,GAAMA,IAAOkE,IAC7D5D,EAAaoB,OAEX/B,EAAYC,EAA+B,MAAxBA,EAAMG,MAAM0E,SAAmB,QAAU,YAqBhE,OA9MAlF,YAAU,KACR6D,EAAStD,YAAcC,EAAMD,YAC7BQ,EACGgB,cACAZ,KAAe2D,GACf3D,KAAgBT,GAAW+C,EAAavE,SAAWoF,EAAY5D,IAC/DS,KAA4BgD,GAC5BhC,OAAqB0B,GAEjB,KACLH,EAAcwD,QACdnG,EAAaoB,OAAwB0B,KAEtC,IAEH7D,YAAU,KACR6D,EAASrD,MAAQA,EACjBqD,EAASF,cAAgBA,EACzBE,EAASE,eAAiBT,EAASe,SA4L9B,CACL8C,iBAlBF,SACEvF,GAEA,MAAMwF,EAAW,IAAInG,IACfoG,EAAaC,MAAMC,KAAK7D,EAAc8D,UAU5C,OARIhH,EAAMiH,aAAaJ,EAAWK,UAElCL,EAAWpF,QAAQ5B,IACjB,MAAM5B,SAAEA,GAAa4B,EAAMG,MAC3B4G,EAAS7F,IAAI9C,IAAa2I,EAAS5F,IAAI/C,EAAU,IACjD2I,EAAS3F,IAAIhD,GAAWiD,KAAKrB,KAGxBiH,MAAMC,KAAKH,EAAUO,GAAK/F,EAAG+F,EAAE,GAAIA,EAAE,MAK5ClE,aAAAA,EACAE,cAAAA,GC3PJ,SAASiE,EAAKtI,GACZ,OAAOA,EAAEuI,eAAiBvI,EAAEuI,cAAcxD,QAAU,EAChD/E,EAAEuI,cAAc,GAAGC,QACnBxI,EAAEwI,QAGR,SAASC,EAAKzI,GACZ,OAAOA,EAAEuI,eAAiBvI,EAAEuI,cAAcxD,QAAU,EAChD/E,EAAEuI,cAAc,GAAGG,QACnB1I,EAAE0I,iBAGQC,EAASzH,GACvB,MAAO0H,EAAWC,GAAgB3E,YAAS,IACpC9E,EAAuB0J,GAA4B5E,YAAS,GAC7D6E,EAAWrJ,SAAuB,MAClCsJ,EAAOtJ,SAAkB,CAC7BuJ,MAAO,EACPlF,EAAG,EACHmF,EAAG,EACHC,MAAO,EACPC,gBAAiB,EACjBC,iBAAiB,EACjBC,SAAS,EACTC,aAAc,KACdC,SAAS,IACR5J,QACG6J,EAAY/J,SAAOwB,IACnBwF,UAAEA,EAAFgD,aAAaA,EAAb5D,WAA2BA,EAA3B6D,QAAuCA,EAAvCC,aAAgDA,GAAiB1I,EA+BvE,SAAS2I,EACP7J,GAEA,GAAIkB,EAAM4I,UAAW,CAEQ,eAAvB9J,EAAE+J,YAAY1J,MAAuBL,EAAE+J,YAAYC,iBAoEzDhB,EAAKQ,SAAU,EACfS,SAASxJ,iBAAiB,YAAayJ,GACvCD,SAASxJ,iBAAiB,UAAW0J,GAErCF,SAASxJ,iBAAiB,YAAayJ,GACvCD,SAASxJ,iBAAiB,WAAY0J,GAtEpC,MAAMpJ,EAAQgI,EAASnJ,QACvBoJ,EAAKK,iBAAkB,EACvBL,EAAKM,SAAU,EACfN,EAAKO,aAAexI,EAAMqJ,wBAC1BrJ,EAAM3C,MAAMI,WAAa,GACzBwK,EAAKjF,EAAIuE,EAAKtI,EAAE+J,aAChBf,EAAKE,EAAIT,EAAKzI,EAAE+J,mBAEZ7I,EAAMmJ,oBACRrB,EAAKC,MAAQD,EAAKjF,EAClBiF,EAAKI,gBACHrI,EAAMuJ,aAAepJ,EAAMqJ,iBAAmB,OAEhDvB,EAAKC,MAAQD,EAAKE,EAClBF,EAAKI,gBACHrI,EAAMyJ,mBACLtJ,EAAMqJ,iBACsB,IAAzBrJ,EAAMqJ,iBACNrJ,EAAMqJ,iBAAmB,OAKrC,SAASE,EACPzK,GAEA,GAAIgJ,EAAKO,aAAc,CACrB,MAAMmB,IAAEA,EAAFC,OAAOA,EAAPC,KAAeA,EAAfC,MAAqBA,GAAU7B,EAAKO,aAGjB,aAAvBvJ,EAAE+J,YAAY1J,MACda,EAAMwI,cACNV,EAAKjF,GAAK6G,GACV5B,EAAKjF,GAAK8G,GACV7B,EAAKE,GAAKwB,GACV1B,EAAKE,GAAKyB,EAEVG,IAEAC,KAKN,SAASA,IACPlC,GAAa,GAGf,SAASiC,IACPjC,GAAa,GAgCf,SAASqB,EAAWlK,GAClB,MAAMe,EAAQgI,EAASnJ,QACnBoJ,EAAKM,SAAWvI,IAClBiI,EAAKQ,SAAU,EACXZ,GAAWkC,IACf9B,EAAKjF,EAAIuE,EAAKtI,GACdgJ,EAAKE,EAAIT,EAAKzI,GAEZgJ,EAAKG,YADHjI,EAAMmJ,mBACKrB,EAAKjF,EAAIiF,EAAKC,MAEdD,EAAKE,EAAIF,EAAKC,MAIzBD,EAAKC,QAAUD,EAAKjF,IAAGiF,EAAKK,iBAAkB,GAClDtI,EAAM3C,MAAM4M,sBAAwB9J,EAAMmJ,sBAAsBrB,EAAKG,WACrEpI,EAAM3C,MAAM6M,YACV,EAAIC,KAAKC,IAAInC,EAAKG,MAAQH,EAAKI,mBAKrC,SAASe,IA7BPF,SAAS7J,oBAAoB,YAAa8J,GAC1CD,SAAS7J,oBAAoB,UAAW+J,GAExCF,SAAS7J,oBAAoB,YAAa8J,GAC1CD,SAAS7J,oBAAoB,WAAY+J,GA2BzC,MAAMpJ,EAAQgI,EAASnJ,QACvB,GAAIoJ,EAAKM,SAAWN,EAAKQ,SAAWzI,EAAO,CAEzC,GADAiI,EAAKM,SAAU,EACX4B,KAAKC,IAAInC,EAAKG,OAASH,EAAKI,gBAG9B,OAFAN,GAAyB,QACzB5H,EAAM4E,aAGR/E,EAAM3C,MAAMI,WAAa,+BACzBuC,EAAM3C,MAAM4M,sBAAwB9J,EAAMmJ,wBAC1CtJ,EAAM3C,MAAM6M,QAAU,KAxJ1BvK,YAAU,KACR+I,EAAU7J,QAAUsB,IAGtBR,YAAU,KACJqI,EAASnJ,SACXmJ,EAASnJ,QAAQa,qBAEfsK,EACA,CAAEK,MAAM,IAGR1N,EAAKwD,EAAMuG,SACbvG,EAAMuG,OAAO3J,iBAAeoD,EAAMhC,WAAagC,EAAMhC,SAASgC,OAEzD,KACL,MAAMA,EAAQuI,EAAU7J,QACpBlC,EAAKwD,EAAMwG,UACbxG,EAAMwG,QAAQ5J,iBAAeoD,EAAMhC,WAAagC,EAAMhC,SAASgC,SAElE,IAEHR,YAAU,KACRQ,EAAMmK,mBAmEDpB,SAASqB,YAAYR,IAE1BS,OAAO9K,iBAAiB,QAASsK,GACjCQ,OAAO9K,iBAAiB,OAAQqK,IArEzB,KACL5J,EAAMmK,mBAwERE,OAAOnL,oBAAoB,QAAS2K,GACpCQ,OAAOnL,oBAAoB,OAAQ0K,MAvElC,CAAC5J,EAAMmK,mBAiIV,MAAMG,EAA4C,CAChDC,YAAa5B,EACb6B,aAAc7B,EACd8B,UAAWlB,EACXmB,WAAYnB,GAgBd,OAbI/D,GAAagD,IACf8B,EAAcK,aAAef,EAC7BU,EAAcM,aAAef,GAI3BnB,IACF4B,EAAc7B,QAAW3J,IACvB2J,GAAWA,EAAQ3J,GACnBgJ,EAAKK,iBAAmBvD,MAIrB,CACLiF,UAAAA,EACAD,WAAAA,EACAlC,UAAAA,EACAxJ,sBAAAA,EACA2J,SAAAA,EACAyC,cAAAA,YClOYO,SAAYjG,WAC1BA,EAD0BzE,MAE1BA,EAF0B2K,UAG1BA,EAAY,WAEZ,OACEnL,kCACED,4DAA6FS,IAC7FhB,KAAK,SACLsJ,QAAS3J,IACPA,EAAEiM,kBACFnG,EAAW9F,iBAEDgM,GAEZnL,6CAAiB,OAAOqC,QAAQ,aAC9BrC,gCACEqL,SAAS,UACT3I,EAAE,wICuCI4I,SAAY1G,MAC1BA,EAD0BmD,UAE1BA,EAF0B9C,WAG1BA,EAH0BzF,KAI1BA,YAJ0B+L,KAK1BA,EAL0BxL,UAM1BA,EACAxC,MAAOiO,EAPmBC,mBAQ1BA,EAR0BC,SAS1BA,EAT0BC,IAU1BA,EAV0BlN,KAW1BA,EAX0B+B,MAY1BA,KAEA,MAAMoL,EAAWL,GAASE,GAAmC,IAAbC,EAC1CnO,EAA6B,IAC9BiO,EACHK,qBAAsBjH,MACtBkH,mBAAoB/D,EAAY,UAAY,SAC5CqC,QAASwB,EAAW,EAAI,GAGtBH,IAAoBlO,EAAM4M,oBAAsBuB,MACpD,MAAMK,EAAmBC,mCAEvBP,2GAGiDjL,+BACNhB,IAC3C,CACE,8BAAiDmM,IAG/CM,EAAapP,EAAKkD,GACpBA,EAAU,CACR4L,IAAAA,EACAnM,KAAAA,EACAuM,iBAAAA,IAEFC,UAAGD,EAAkBhM,GAkBzB,OACEC,+BACEkM,KAAK,4BACQN,EAAW,OAAS,qBACtB,qBACX7L,UAAWkM,EACX1O,MAAOA,EAlBT,CAACkO,GAAuBC,GAAwB,EAC5C,kBACA,kBACFD,GAAuBC,EAAuB,EAC1C,KACA,KACEjN,GAAQwG,aC/GPkH,EAA8B9L,IACzC,MAAM0H,UAAEA,EAAFxJ,sBAAaA,EAAb2J,SAAoCA,EAApCyC,cAA8CA,GAClD7C,EAASzH,IACLyG,YACJA,EADIzI,SAEJA,EAFIwH,UAGJA,EAHIiD,QAIJA,EAJItJ,KAKJA,EALI4M,gBAMJA,EANInH,WAOJA,EACAtH,WAAY0O,EARR/N,SASJA,EATIyB,UAUJA,EAVIxC,MAWJA,EAXIoI,cAYJA,EAZI2G,UAaJA,EAbI1G,kBAcJA,EAdI2G,cAeJA,EAfIxH,SAgBJA,EAhBImH,KAiBJA,EAjBIR,SAkBJA,EAlBIC,IAmBJA,EAnBIpL,QAoBJA,EApBIyF,YAqBJA,EArBIvH,KAsBJA,EAtBIiC,UAuBJA,EAvBI4F,QAwBJA,EAxBIyC,aAyBJA,EAzBIvI,MA0BJA,GACEH,EACE0L,EAAmBC,sDAEmBxL,wBACNhB,IACpC,CACE,uBAA0CmM,GAE5C,CACE,kCAAqD5C,IAGnDyD,EAAa3P,EAAKkD,GACpBA,EAAU,CACR4L,IAAAA,EACArN,SAAAA,EACAkB,KAAAA,EACAuM,iBAAAA,IAEFC,UAAGD,EAAkBhM,GACnB0M,IAAyBf,IAAa7F,EAEtC6G,EAAmB,CAAEzH,WAAAA,EAAYzF,KAAAA,EAAMgB,MAAAA,GAC7C,IAAImM,EAAyB,KAY7B,OAVoB,IAAhB7F,IAGF6F,EADS9P,EAAKiK,GACNA,EAAY4F,GACXzP,iBAAe6J,GAChBL,eAAaK,EAAa4F,GAE1BxB,EAAYwB,IAIpB1M,wBAACqM,GACC5N,KAAMA,EACNrB,KAAM4I,EACN1H,SAAUA,EACVC,sBAAuBA,EACvBC,QAAS0J,GAETlI,+BACEM,GAAIC,EACJuI,QAASA,EACT/I,UAAWyM,KACP7B,EACJpN,MAAOA,EACPqP,IAAK1E,GAELlI,kCACOvB,GAAQ,CAAEyN,KAAMA,GACrBnM,UACElD,EAAK8I,GACDA,EAAc,CAAEnG,KAAAA,IAChBwM,iCAA2CrG,GAEjDpI,MAAO+O,GAEK,MAAXhG,GACCtG,+BACED,UAAWiM,iCAA2C,CACpD,+CACGtL,KAGJ4F,GAGLtG,mCAAM3B,IAEPsO,EACD3M,wBAACsL,MACMvG,IAAa0H,EACd,CAAErH,UAAWL,KACb,GACJ4G,IAAKA,EACLnL,MAAOA,EACPoE,MAAOiB,EACPkC,UAAWA,EACXtJ,KAAMA,EACNwG,WAAYA,EACZsG,KAAMa,EACN5M,KAAMA,EACNjC,MAAOgP,EACPxM,UAAW6F,EACX6F,mBAAoBgB,EACpBf,SAAUA,GAAY,OC7H1BmB,EAAY,SAACC,EAAuB5O,mBAAAA,IAAAA,GAAiB,GAAW,CACpEF,qCAAsE8O,UACtE7O,oCAAqE6O,SACrE5O,eAAAA,IAGI6O,EAAShP,EAAc8O,EAAU,UAAU,IAE3CG,EAAQjP,EAAc8O,EAAU,SAAS,IAEzCI,EAAOlP,EAAc8O,EAAU,SAE/BK,EAAOnP,EAAc8O,EAAU,SCFxBM,EAAiBC,aAC5B,CAAC/M,EAAOuM,KACN,MAAM5F,iBAAEA,EAAF1D,aAAoBA,EAApBE,cAAkCA,GACtCT,EAAkB1C,IACdN,UAAEA,EAAFxC,MAAaA,EAAboO,IAAoBA,EAApBvL,YAAyBA,GAAgBC,EAE/C,SAASgN,EAAa/O,GACpB,MAAMyN,EAAmBC,oEAEuB1N,IAC9C,CAAE,iCAAoDqN,IAExD,OAAO9O,EAAKkD,GACRA,EAAU,CACRzB,SAAAA,EACAqN,IAAAA,EACAI,iBAAAA,IAEFC,UAAGD,EAAkBjP,EAAeiD,IAU1C,OAPAF,YAAU,KACJ+M,IACDA,EAA+C7N,QAC9CuE,EAAavE,UAEhB,IAGDiB,+BACE4M,IAAKtJ,EACLvD,UAAW,WACXO,GAAIF,GAEH4G,EAAiB,CAAC1I,EAAUgP,KAC3B,MAAMC,EAAuCD,EAAUpJ,OAEnD,IAAK3G,GADL,IAAKA,EAAOiQ,cAAe,QAG/B,OACExN,+BACED,UAAWsN,EAAa/O,GACxBf,MAAOgQ,EACPnI,iBAAkB9G,KAEjBgP,EAAUG,IAAI,GAAiCpH,SAAhCrJ,QAAEA,EAASqD,MAAOkE,KAChC,OACEvE,wBAACmM,MACK5H,EACJ9F,KAAM+E,EAAce,EAAWhE,SAC/BhD,MACE,IACKgH,EAAWhH,MACd,QAAS8I,EAAI,EACb,QAASiH,EAAUpJ,QAGvBkB,aAAcb,EAAWa,OAExBpI,WAYrBmQ,EAAeO,YAAc,iBAE7BP,EAAeQ,aAAe,CAC5BrP,SAAU,YACVX,WAAYoP,EACZlH,UAAW,IACXiB,YAAaoE,EACbrC,cAAc,EACd2B,kBAAkB,EAClBzB,cAAc,EACdE,WAAW,EACXS,iBAAkB,GAClBF,uBACA0C,KAAM,QACN1L,MAAO,SC9ET,IACIoN,EADAC,EAAa,IAAI/M,IAEjBgD,EAAyB,GACzBgK,EAAW,EAaf,SAASC,IACP,SAAUD,IAMZ,SAASE,EAAWnJ,GAClB,OAAOA,IAAYjI,EAAMiI,EAAQtE,UAAY9D,EAAMoI,EAAQtE,UACvDsE,EAAQtE,QACRwN,IAON,SAASE,EACPjR,EACA6H,GAQA,OANIgJ,EAAWK,KAAO,EACpBtN,EAAaoB,OAAiBhF,EAAS6H,GAEvCf,EAAMvC,KAAK,CAAEvE,QAAAA,EAAS6H,QAAAA,IAGjBA,EAAQtE,QAMjB,SAAS4N,EAAa3O,EAAcqF,GAClC,MAAO,IACFA,EACHrF,KAAOqF,GAAWA,EAAQrF,MAASA,EACnCe,QAASyN,EAAWnJ,IAIxB,SAASuJ,EAAkB5O,GACzB,MAAO,CACLxC,EACA6H,IACGoJ,EAAcjR,EAASmR,EAAa3O,EAAMqF,IAGjD,SAAS3E,EACPlD,EACA6H,GAEA,OAAOoJ,EAAcjR,EAASmR,YAA2BtJ,IAG3D3E,EAAMmO,QAAU,CACdrR,EACA6H,IAEAoJ,EACEjR,EACAmR,YAA2B,CACzBzN,WAAW,EACXmF,WAAW,EACXkD,cAAc,EACdjC,aAAa,EACbmC,WAAW,KACRpE,KAqFT3E,EAAMoO,QAvEN,SACEA,IAEAzJ,OAEIvE,GAHJiO,QAAEA,EAAF1L,MAAWA,EAAXD,QAAkBA,KAKd2L,IACFjO,EAAK1D,EAAM2R,GACPrO,EAAMmO,QAAQE,EAAS1J,GACvB3E,EAAMmO,QAAQE,EAAQC,OAAQ,IACzB3J,KACC0J,KAIZ,MAAME,EAAc,CAClB/N,UAAW,KACXmF,UAAW,KACXkD,aAAc,KACdjC,YAAa,KACbmC,UAAW,MAGPyF,EAAW,CACflP,EACAmP,EACAC,KAIA,GAAa,MAATD,EAEF,YADAzO,EAAM2O,QAAQvO,GAIhB,MAAMwO,EAAa,CACjBtP,KAAAA,KACGiP,KACA5J,EACHpE,KAAMmO,GAEFG,EAASnS,EAAM+R,GAAS,CAAEH,OAAQG,GAAUA,EAgBlD,OAbIrO,EACFJ,EAAM8O,OAAO1O,EAAI,IACZwO,KACAC,IAIL7O,EAAM6O,EAAQP,OAAQ,IACjBM,KACAC,IAIAH,GAGHpH,EAAI3K,EAAKyR,GAAWA,IAAYA,EAOtC,OAJA9G,EAAEyH,KAAKL,GAAUF,EAAS,UAAW9L,EAASgM,IAASM,MAAMC,GAC3DT,EAAS,QAAS7L,EAAOsM,IAGpB3H,GAITtH,EAAM0C,QAAUwL,aAChBlO,EAAMuC,KAAO2L,UACblO,EAAM2C,MAAQuL,WACdlO,EAAMyC,QAAUyL,aAChBlO,EAAMkP,KAAOlP,EAAMyC,QACnBzC,EAAMmP,KAAO,CAACrS,EAAuB6H,IACnCoJ,EACEjR,EACAmR,YAA2B,CACzB3N,MAAO,UACJqE,KAOT3E,EAAM2O,QAAWvO,IACXuN,EAAWK,KAAO,EACpBtN,EAAaoB,OAAkB1B,GAE/BwD,EAAQA,EAAMpC,OAAO4N,GAAW,MAANhP,GAAcgP,EAAEzK,QAAQtE,UAAYD,IAOlEJ,EAAM8D,kBAAoB,SAAC+K,mBAAAA,IAAAA,EAAkC,IAC3DnO,EAAaoB,OAA8B+M,IAK7C7O,EAAMqP,SAAYjP,IAChB,IAAIkD,GAAgB,EAQpB,OANAqK,EAAW/L,QAAQ0N,IACbA,EAAUhM,eAAiBgM,EAAUhM,cAAclD,KACrDkD,GAAgB,KAIbA,GAGTtD,EAAM8O,OAAS,SACbzO,EACAsE,YAAAA,IAAAA,EAAgC,IAEhC/G,WAAW,KACT,MAAMoC,EApNV,SAAkBK,SAAaH,YAAEA,KAC/B,MAAMoP,EAAY3B,EAAWvM,IAAIlB,GAAewN,GAChD,OAAO4B,GAAaA,EAAUzL,SAASxD,GAkNvBwD,CAASxD,EAASsE,GAChC,GAAI3E,EAAO,CACT,MAAQG,MAAOoP,EAAYzS,QAAS0S,GAAexP,EAE7CyP,EAAc,CAClB/K,MAAO,OACJ6K,KACA5K,EACHtE,QAASsE,EAAQtE,SAAWA,EAC5BwE,SAAUgJ,KAGR4B,EAAYpP,UAAYA,IAASoP,EAAYnL,QAAUjE,GAE3D,MAAMvD,EAAU2S,EAAYnB,QAAUkB,SAC/BC,EAAYnB,OAEnBP,EAAcjR,EAAS2S,KAExB,IAMLzP,EAAM9C,KAAQkD,IACZJ,EAAM8O,OAAO1O,EAAI,CACfoL,SAAU,KAwBdxL,EAAM0P,SAAY1O,IAChBN,EAAaI,KAAiBE,GACvB,KACLN,EAAaY,MAAkBN,KAQnChB,EAAM2P,SC9R6D,CACjEC,SAAU,WACVC,UAAW,YACXC,WAAY,aACZC,YAAa,cACbC,aAAc,eACdC,cAAe,iBD8RjBjQ,EAAMkQ,KCxRmD,CACvDC,KAAM,OACNC,QAAS,UACTC,QAAS,UACTC,MAAO,QACPC,QAAS,WDyRX7P,EACGI,KAAoB0P,IACnB9C,EAAiB8C,EAAkBtQ,aAAesQ,EAClD7C,EAAWxM,IAAIuM,EAAgB8C,GAE/B5M,EAAMhC,QAAQ6O,IACZ/P,EAAaoB,OAAiB2O,EAAK3T,QAAS2T,EAAK9L,WAGnDf,EAAQ,KAET9C,KAAuB0P,IACtB7C,EAAWlM,OAAO+O,EAAkBtQ,aAAesQ,GAE3B,IAApB7C,EAAWK,MACbtN,EACGY,OACAA,OACAA"}