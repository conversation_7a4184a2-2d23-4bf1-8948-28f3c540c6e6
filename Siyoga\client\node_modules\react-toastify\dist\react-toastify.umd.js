!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("clsx")):"function"==typeof define&&define.amd?define(["exports","react","clsx"],t):t((e||self).reactToastify={},e.react,e.clsx)}(this,function(e,t,n){function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var s=/*#__PURE__*/o(t),a=/*#__PURE__*/o(n);const i=e=>"number"==typeof e&&!isNaN(e),r=e=>"string"==typeof e,l=e=>"function"==typeof e,c=e=>r(e)||l(e)?e:null,u=e=>t.isValidElement(e)||r(e)||l(e)||i(e);function d(e,t,n){void 0===n&&(n=300);const{scrollHeight:o,style:s}=e;requestAnimationFrame(()=>{s.minHeight="initial",s.height=o+"px",s.transition=`all ${n}ms`,requestAnimationFrame(()=>{s.height="0",s.padding="0",s.margin="0",setTimeout(t,n)})})}function f(e){let{enter:n,exit:o,appendPosition:a=!1,collapse:i=!0,collapseDuration:r=300}=e;return function(e){let{children:l,position:c,preventExitTransition:u,done:f,nodeRef:p,isIn:m}=e;const g=a?`${n}--${c}`:n,h=a?`${o}--${c}`:o,y=t.useRef(0);return t.useLayoutEffect(()=>{const e=p.current,t=g.split(" "),n=o=>{o.target===p.current&&(e.dispatchEvent(new Event("d")),e.removeEventListener("animationend",n),e.removeEventListener("animationcancel",n),0===y.current&&"animationcancel"!==o.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",n),e.addEventListener("animationcancel",n)},[]),t.useEffect(()=>{const e=p.current,t=()=>{e.removeEventListener("animationend",t),i?d(e,f,r):f()};m||(u?t():(y.current=1,e.className+=` ${h}`,e.addEventListener("animationend",t)))},[m]),s.default.createElement(s.default.Fragment,null,l)}}function p(e,t){return null!=e?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}const m={list:new Map,emitQueue:new Map,on(e,t){return this.list.has(e)||this.list.set(e,[]),this.list.get(e).push(t),this},off(e,t){if(t){const n=this.list.get(e).filter(e=>e!==t);return this.list.set(e,n),this}return this.list.delete(e),this},cancelEmit(e){const t=this.emitQueue.get(e);return t&&(t.forEach(clearTimeout),this.emitQueue.delete(e)),this},emit(e){this.list.has(e)&&this.list.get(e).forEach(t=>{const n=setTimeout(()=>{t(...[].slice.call(arguments,1))},0);this.emitQueue.has(e)||this.emitQueue.set(e,[]),this.emitQueue.get(e).push(n)})}},g=e=>{let{theme:t,type:n,...o}=e;return s.default.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===t?"currentColor":`var(--toastify-icon-color-${n})`,...o})},h={info:function(e){return s.default.createElement(g,{...e},s.default.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return s.default.createElement(g,{...e},s.default.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return s.default.createElement(g,{...e},s.default.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return s.default.createElement(g,{...e},s.default.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return s.default.createElement("div",{className:"Toastify__spinner"})}};function y(e){const[,n]=t.useReducer(e=>e+1,0),[o,s]=t.useState([]),a=t.useRef(null),d=t.useRef(new Map).current,f=e=>-1!==o.indexOf(e),g=t.useRef({toastKey:1,displayedToast:0,count:0,queue:[],props:e,containerId:null,isToastActive:f,getToast:e=>d.get(e)}).current;function y(e){let{containerId:t}=e;const{limit:n}=g.props;!n||t&&g.containerId!==t||(g.count-=g.queue.length,g.queue=[])}function v(e){s(t=>null==e?[]:t.filter(t=>t!==e))}function T(){const{toastContent:e,toastProps:t,staleId:n}=g.queue.shift();C(e,t,n)}function E(e,o){let{delay:s,staleId:f,...y}=o;if(!u(e)||function(e){return!a.current||g.props.enableMultiContainer&&e.containerId!==g.props.containerId||d.has(e.toastId)&&null==e.updateId}(y))return;const{toastId:E,updateId:b,data:I}=y,{props:_}=g,L=()=>v(E),O=null==b;O&&g.count++;const N={..._,style:_.toastStyle,key:g.toastKey++,...Object.fromEntries(Object.entries(y).filter(e=>{let[t,n]=e;return null!=n})),toastId:E,updateId:b,data:I,closeToast:L,isIn:!1,className:c(y.className||_.toastClassName),bodyClassName:c(y.bodyClassName||_.bodyClassName),progressClassName:c(y.progressClassName||_.progressClassName),autoClose:!y.isLoading&&(R=y.autoClose,x=_.autoClose,!1===R||i(R)&&R>0?R:x),deleteToast(){const e=p(d.get(E),"removed");d.delete(E),m.emit(4,e);const t=g.queue.length;if(g.count=null==E?g.count-g.displayedToast:g.count-1,g.count<0&&(g.count=0),t>0){const e=null==E?g.props.limit:1;if(1===t||1===e)g.displayedToast++,T();else{const n=e>t?t:e;g.displayedToast=n;for(let e=0;e<n;e++)T()}}else n()}};var R,x;N.iconOut=function(e){let{theme:n,type:o,isLoading:s,icon:a}=e,c=null;const u={theme:n,type:o};return!1===a||(l(a)?c=a(u):t.isValidElement(a)?c=t.cloneElement(a,u):r(a)||i(a)?c=a:s?c=h.spinner():(e=>e in h)(o)&&(c=h[o](u))),c}(N),l(y.onOpen)&&(N.onOpen=y.onOpen),l(y.onClose)&&(N.onClose=y.onClose),N.closeButton=_.closeButton,!1===y.closeButton||u(y.closeButton)?N.closeButton=y.closeButton:!0===y.closeButton&&(N.closeButton=!u(_.closeButton)||_.closeButton);let w=e;t.isValidElement(e)&&!r(e.type)?w=t.cloneElement(e,{closeToast:L,toastProps:N,data:I}):l(e)&&(w=e({closeToast:L,toastProps:N,data:I})),_.limit&&_.limit>0&&g.count>_.limit&&O?g.queue.push({toastContent:w,toastProps:N,staleId:f}):i(s)?setTimeout(()=>{C(w,N,f)},s):C(w,N,f)}function C(e,t,n){const{toastId:o}=t;n&&d.delete(n);const a={content:e,props:t};d.set(o,a),s(e=>[...e,o].filter(e=>e!==n)),m.emit(4,p(a,null==a.props.updateId?"added":"updated"))}return t.useEffect(()=>(g.containerId=e.containerId,m.cancelEmit(3).on(0,E).on(1,e=>a.current&&v(e)).on(5,y).emit(2,g),()=>{d.clear(),m.emit(3,g)}),[]),t.useEffect(()=>{g.props=e,g.isToastActive=f,g.displayedToast=o.length}),{getToastToRender:function(t){const n=new Map,o=Array.from(d.values());return e.newestOnTop&&o.reverse(),o.forEach(e=>{const{position:t}=e.props;n.has(t)||n.set(t,[]),n.get(t).push(e)}),Array.from(n,e=>t(e[0],e[1]))},containerRef:a,isToastActive:f}}function v(e){return e.targetTouches&&e.targetTouches.length>=1?e.targetTouches[0].clientX:e.clientX}function T(e){return e.targetTouches&&e.targetTouches.length>=1?e.targetTouches[0].clientY:e.clientY}function E(e){const[n,o]=t.useState(!1),[s,a]=t.useState(!1),i=t.useRef(null),r=t.useRef({start:0,x:0,y:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,boundingRect:null,didMove:!1}).current,c=t.useRef(e),{autoClose:u,pauseOnHover:d,closeToast:f,onClick:p,closeOnClick:m}=e;function g(t){if(e.draggable){"touchstart"===t.nativeEvent.type&&t.nativeEvent.preventDefault(),r.didMove=!1,document.addEventListener("mousemove",C),document.addEventListener("mouseup",b),document.addEventListener("touchmove",C),document.addEventListener("touchend",b);const n=i.current;r.canCloseOnClick=!0,r.canDrag=!0,r.boundingRect=n.getBoundingClientRect(),n.style.transition="",r.x=v(t.nativeEvent),r.y=T(t.nativeEvent),"x"===e.draggableDirection?(r.start=r.x,r.removalDistance=n.offsetWidth*(e.draggablePercent/100)):(r.start=r.y,r.removalDistance=n.offsetHeight*(80===e.draggablePercent?1.5*e.draggablePercent:e.draggablePercent/100))}}function h(t){if(r.boundingRect){const{top:n,bottom:o,left:s,right:a}=r.boundingRect;"touchend"!==t.nativeEvent.type&&e.pauseOnHover&&r.x>=s&&r.x<=a&&r.y>=n&&r.y<=o?E():y()}}function y(){o(!0)}function E(){o(!1)}function C(t){const o=i.current;r.canDrag&&o&&(r.didMove=!0,n&&E(),r.x=v(t),r.y=T(t),r.delta="x"===e.draggableDirection?r.x-r.start:r.y-r.start,r.start!==r.x&&(r.canCloseOnClick=!1),o.style.transform=`translate${e.draggableDirection}(${r.delta}px)`,o.style.opacity=""+(1-Math.abs(r.delta/r.removalDistance)))}function b(){document.removeEventListener("mousemove",C),document.removeEventListener("mouseup",b),document.removeEventListener("touchmove",C),document.removeEventListener("touchend",b);const t=i.current;if(r.canDrag&&r.didMove&&t){if(r.canDrag=!1,Math.abs(r.delta)>r.removalDistance)return a(!0),void e.closeToast();t.style.transition="transform 0.2s, opacity 0.2s",t.style.transform=`translate${e.draggableDirection}(0)`,t.style.opacity="1"}}t.useEffect(()=>{c.current=e}),t.useEffect(()=>(i.current&&i.current.addEventListener("d",y,{once:!0}),l(e.onOpen)&&e.onOpen(t.isValidElement(e.children)&&e.children.props),()=>{const e=c.current;l(e.onClose)&&e.onClose(t.isValidElement(e.children)&&e.children.props)}),[]),t.useEffect(()=>(e.pauseOnFocusLoss&&(document.hasFocus()||E(),window.addEventListener("focus",y),window.addEventListener("blur",E)),()=>{e.pauseOnFocusLoss&&(window.removeEventListener("focus",y),window.removeEventListener("blur",E))}),[e.pauseOnFocusLoss]);const I={onMouseDown:g,onTouchStart:g,onMouseUp:h,onTouchEnd:h};return u&&d&&(I.onMouseEnter=E,I.onMouseLeave=y),m&&(I.onClick=e=>{p&&p(e),r.canCloseOnClick&&f()}),{playToast:y,pauseToast:E,isRunning:n,preventExitTransition:s,toastRef:i,eventHandlers:I}}function C(e){let{closeToast:t,theme:n,ariaLabel:o="close"}=e;return s.default.createElement("button",{className:`Toastify__close-button Toastify__close-button--${n}`,type:"button",onClick:e=>{e.stopPropagation(),t(e)},"aria-label":o},s.default.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},s.default.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}function b(e){let{delay:t,isRunning:n,closeToast:o,type:i="default",hide:r,className:c,style:u,controlledProgress:d,progress:f,rtl:p,isIn:m,theme:g}=e;const h=r||d&&0===f,y={...u,animationDuration:`${t}ms`,animationPlayState:n?"running":"paused",opacity:h?0:1};d&&(y.transform=`scaleX(${f})`);const v=a.default("Toastify__progress-bar",d?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${g}`,`Toastify__progress-bar--${i}`,{"Toastify__progress-bar--rtl":p}),T=l(c)?c({rtl:p,type:i,defaultClassName:v}):a.default(v,c);return s.default.createElement("div",{role:"progressbar","aria-hidden":h?"true":"false","aria-label":"notification timer",className:T,style:y,[d&&f>=1?"onTransitionEnd":"onAnimationEnd"]:d&&f<1?null:()=>{m&&o()}})}const I=e=>{const{isRunning:n,preventExitTransition:o,toastRef:i,eventHandlers:r}=E(e),{closeButton:c,children:u,autoClose:d,onClick:f,type:p,hideProgressBar:m,closeToast:g,transition:h,position:y,className:v,style:T,bodyClassName:I,bodyStyle:_,progressClassName:L,progressStyle:O,updateId:N,role:R,progress:x,rtl:w,toastId:M,deleteToast:$,isIn:k,isLoading:B,iconOut:P,closeOnClick:D,theme:A}=e,z=a.default("Toastify__toast",`Toastify__toast-theme--${A}`,`Toastify__toast--${p}`,{"Toastify__toast--rtl":w},{"Toastify__toast--close-on-click":D}),F=l(v)?v({rtl:w,position:y,type:p,defaultClassName:z}):a.default(z,v),S=!!x||!d,q={closeToast:g,type:p,theme:A};let H=null;return!1===c||(H=l(c)?c(q):t.isValidElement(c)?t.cloneElement(c,q):C(q)),s.default.createElement(h,{isIn:k,done:$,position:y,preventExitTransition:o,nodeRef:i},s.default.createElement("div",{id:M,onClick:f,className:F,...r,style:T,ref:i},s.default.createElement("div",{...k&&{role:R},className:l(I)?I({type:p}):a.default("Toastify__toast-body",I),style:_},null!=P&&s.default.createElement("div",{className:a.default("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!B})},P),s.default.createElement("div",null,u)),H,s.default.createElement(b,{...N&&!S?{key:`pb-${N}`}:{},rtl:w,theme:A,delay:d,isRunning:n,isIn:k,closeToast:g,hide:m,type:p,style:O,className:L,controlledProgress:S,progress:x||0})))},_=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},L=f(_("bounce",!0)),O=f(_("slide",!0)),N=f(_("zoom")),R=f(_("flip")),x=t.forwardRef((e,n)=>{const{getToastToRender:o,containerRef:i,isToastActive:r}=y(e),{className:u,style:d,rtl:f,containerId:p}=e;function m(e){const t=a.default("Toastify__toast-container",`Toastify__toast-container--${e}`,{"Toastify__toast-container--rtl":f});return l(u)?u({position:e,rtl:f,defaultClassName:t}):a.default(t,c(u))}return t.useEffect(()=>{n&&(n.current=i.current)},[]),s.default.createElement("div",{ref:i,className:"Toastify",id:p},o((e,t)=>{const n=t.length?{...d}:{...d,pointerEvents:"none"};return s.default.createElement("div",{className:m(e),style:n,key:`container-${e}`},t.map((e,n)=>{let{content:o,props:a}=e;return s.default.createElement(I,{...a,isIn:r(a.toastId),style:{...a.style,"--nth":n+1,"--len":t.length},key:`toast-${a.key}`},o)}))}))});x.displayName="ToastContainer",x.defaultProps={position:"top-right",transition:L,autoClose:5e3,closeButton:C,pauseOnHover:!0,pauseOnFocusLoss:!0,closeOnClick:!0,draggable:!0,draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"};let w,M=new Map,$=[],k=1;function B(){return""+k++}function P(e){return e&&(r(e.toastId)||i(e.toastId))?e.toastId:B()}function D(e,t){return M.size>0?m.emit(0,e,t):$.push({content:e,options:t}),t.toastId}function A(e,t){return{...t,type:t&&t.type||e,toastId:P(t)}}function z(e){return(t,n)=>D(t,A(e,n))}function F(e,t){return D(e,A("default",t))}F.loading=(e,t)=>D(e,A("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),F.promise=function(e,t,n){let o,{pending:s,error:a,success:i}=t;s&&(o=r(s)?F.loading(s,n):F.loading(s.render,{...n,...s}));const c={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},u=(e,t,s)=>{if(null==t)return void F.dismiss(o);const a={type:e,...c,...n,data:s},i=r(t)?{render:t}:t;return o?F.update(o,{...a,...i}):F(i.render,{...a,...i}),s},d=l(e)?e():e;return d.then(e=>u("success",i,e)).catch(e=>u("error",a,e)),d},F.success=z("success"),F.info=z("info"),F.error=z("error"),F.warning=z("warning"),F.warn=F.warning,F.dark=(e,t)=>D(e,A("default",{theme:"dark",...t})),F.dismiss=e=>{M.size>0?m.emit(1,e):$=$.filter(t=>null!=e&&t.options.toastId!==e)},F.clearWaitingQueue=function(e){return void 0===e&&(e={}),m.emit(5,e)},F.isActive=e=>{let t=!1;return M.forEach(n=>{n.isToastActive&&n.isToastActive(e)&&(t=!0)}),t},F.update=function(e,t){void 0===t&&(t={}),setTimeout(()=>{const n=function(e,t){let{containerId:n}=t;const o=M.get(n||w);return o&&o.getToast(e)}(e,t);if(n){const{props:o,content:s}=n,a={delay:100,...o,...t,toastId:t.toastId||e,updateId:B()};a.toastId!==e&&(a.staleId=e);const i=a.render||s;delete a.render,D(i,a)}},0)},F.done=e=>{F.update(e,{progress:1})},F.onChange=e=>(m.on(4,e),()=>{m.off(4,e)}),F.POSITION={TOP_LEFT:"top-left",TOP_RIGHT:"top-right",TOP_CENTER:"top-center",BOTTOM_LEFT:"bottom-left",BOTTOM_RIGHT:"bottom-right",BOTTOM_CENTER:"bottom-center"},F.TYPE={INFO:"info",SUCCESS:"success",WARNING:"warning",ERROR:"error",DEFAULT:"default"},m.on(2,e=>{w=e.containerId||e,M.set(w,e),$.forEach(e=>{m.emit(0,e.content,e.options)}),$=[]}).on(3,e=>{M.delete(e.containerId||e),0===M.size&&m.off(0).off(1).off(5)}),e.Bounce=L,e.Flip=R,e.Icons=h,e.Slide=O,e.ToastContainer=x,e.Zoom=N,e.collapseToast=d,e.cssTransition=f,e.toast=F,e.useToast=E,e.useToastContainer=y});
//# sourceMappingURL=react-toastify.umd.js.map
