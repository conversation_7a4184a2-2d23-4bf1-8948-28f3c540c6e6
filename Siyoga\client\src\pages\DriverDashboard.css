.driver-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dashboard-header {
  background: white;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-header h1 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logout-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.logout-btn:hover {
  background: #c82333;
}

.dashboard-nav {
  background: white;
  padding: 0 20px;
  display: flex;
  gap: 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-nav button {
  background: none;
  border: none;
  padding: 15px 20px;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  font-weight: 500;
  color: #666;
  transition: all 0.3s ease;
}

.dashboard-nav button:hover {
  background: #f8f9fa;
  color: #333;
}

.dashboard-nav button.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: #f8f9fa;
}

.dashboard-content {
  padding: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.loading {
  text-align: center;
  padding: 40px;
  color: white;
  font-size: 18px;
}

/* Vehicles Section */
.vehicles-section {
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 15px;
}

.section-header h2 {
  margin: 0;
  color: #333;
}

.add-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
}

.add-btn:hover {
  background: #218838;
}

.vehicles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.vehicle-card {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  transition: transform 0.2s ease;
}

.vehicle-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.vehicle-card h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.vehicle-card p {
  margin: 8px 0;
  color: #666;
}

/* Add Vehicle Form */
.add-vehicle-form {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 30px;
}

.add-vehicle-form h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.add-vehicle-form input,
.add-vehicle-form select {
  width: 100%;
  padding: 10px;
  margin-bottom: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.add-vehicle-form label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  color: #333;
}

.add-vehicle-form button {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.add-vehicle-form button:hover {
  background: #0056b3;
}

/* Bookings Section */
.bookings-section {
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.bookings-section h2 {
  margin: 0 0 30px 0;
  color: #333;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 15px;
}

.bookings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.booking-card {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  transition: transform 0.2s ease;
}

.booking-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.booking-card h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 18px;
}

.booking-card p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
}

.accept-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 15px;
  width: 100%;
}

.accept-btn:hover {
  background: #218838;
}

.no-data {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 40px;
  grid-column: 1 / -1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .dashboard-nav {
    flex-direction: column;
    padding: 0;
  }

  .dashboard-nav button {
    border-bottom: none;
    border-left: 3px solid transparent;
  }

  .dashboard-nav button.active {
    border-bottom: none;
    border-left-color: #667eea;
  }

  .dashboard-content {
    padding: 20px;
  }

  .section-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .vehicles-grid,
  .bookings-grid {
    grid-template-columns: 1fr;
  }
}
