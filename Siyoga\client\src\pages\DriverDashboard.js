import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './DriverDashboard.css';

const DriverDashboard = () => {
  const [activeTab, setActiveTab] = useState('vehicles');
  const [vehicles, setVehicles] = useState([]);
  const [availableBookings, setAvailableBookings] = useState([]);
  const [myBookings, setMyBookings] = useState([]);
  const [vehicleCategories, setVehicleCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');

  useEffect(() => {
    if (!token || user.role !== 'driver') {
      navigate('/login');
      return;
    }
    loadData();
    loadVehicleCategories();
  }, [activeTab]);

  const loadVehicleCategories = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/bookings/vehicle-categories');
      const data = await response.json();
      if (data.success) {
        setVehicleCategories(data.data);
      }
    } catch (error) {
      console.error('Error loading vehicle categories:', error);
    }
  };

  const loadData = async () => {
    setLoading(true);
    try {
      if (activeTab === 'vehicles') {
        await loadVehicles();
      } else if (activeTab === 'bookings') {
        await loadAvailableBookings();
      } else if (activeTab === 'my-bookings') {
        await loadMyBookings();
      }
    } catch (error) {
      console.error('Error loading data:', error);
    }
    setLoading(false);
  };

  const loadVehicles = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/driver/vehicles', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      if (data.success) {
        setVehicles(data.data);
      }
    } catch (error) {
      console.error('Error loading vehicles:', error);
    }
  };

  const loadAvailableBookings = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/driver/available-bookings', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      if (data.success) {
        setAvailableBookings(data.data);
      }
    } catch (error) {
      console.error('Error loading available bookings:', error);
    }
  };

  const loadMyBookings = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/driver/bookings', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      if (data.success) {
        setMyBookings(data.data);
      }
    } catch (error) {
      console.error('Error loading my bookings:', error);
    }
  };

  const acceptBooking = async (bookingId) => {
    try {
      const response = await fetch(`http://localhost:5001/api/driver/bookings/${bookingId}/accept`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      if (data.success) {
        alert('Booking accepted successfully!');
        loadAvailableBookings(); // Refresh available bookings
      } else {
        alert(data.message || 'Failed to accept booking');
      }
    } catch (error) {
      console.error('Error accepting booking:', error);
      alert('Failed to accept booking');
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    navigate('/login');
  };

  return (
    <div className="driver-dashboard">
      <header className="dashboard-header">
        <h1>Driver Dashboard</h1>
        <div className="header-actions">
          <span>Welcome, {user.profile?.first_name || 'Driver'}</span>
          <button onClick={logout} className="logout-btn">Logout</button>
        </div>
      </header>

      <nav className="dashboard-nav">
        <button 
          className={activeTab === 'vehicles' ? 'active' : ''}
          onClick={() => setActiveTab('vehicles')}
        >
          🚗 My Vehicles
        </button>
        <button 
          className={activeTab === 'bookings' ? 'active' : ''}
          onClick={() => setActiveTab('bookings')}
        >
          📋 Available Bookings
        </button>
        <button 
          className={activeTab === 'my-bookings' ? 'active' : ''}
          onClick={() => setActiveTab('my-bookings')}
        >
          ✅ My Bookings
        </button>
      </nav>

      <main className="dashboard-content">
        {loading && <div className="loading">Loading...</div>}

        {activeTab === 'vehicles' && (
          <VehiclesSection 
            vehicles={vehicles} 
            onReload={loadVehicles} 
            token={token}
            vehicleCategories={vehicleCategories}
          />
        )}

        {activeTab === 'bookings' && (
          <AvailableBookingsSection 
            bookings={availableBookings} 
            onAccept={acceptBooking} 
          />
        )}

        {activeTab === 'my-bookings' && (
          <MyBookingsSection bookings={myBookings} />
        )}
      </main>
    </div>
  );
};

// Vehicles Section Component
const VehiclesSection = ({ vehicles, onReload, token, vehicleCategories }) => {
  const [showAddForm, setShowAddForm] = useState(false);

  return (
    <div className="vehicles-section">
      <div className="section-header">
        <h2>My Vehicles</h2>
        <button 
          onClick={() => setShowAddForm(!showAddForm)}
          className="add-btn"
        >
          {showAddForm ? 'Cancel' : '+ Add Vehicle'}
        </button>
      </div>

      {showAddForm && (
        <AddVehicleForm 
          onSuccess={() => {
            setShowAddForm(false);
            onReload();
          }}
          token={token}
          vehicleCategories={vehicleCategories}
        />
      )}

      <div className="vehicles-grid">
        {vehicles.map(vehicle => (
          <div key={vehicle.vehicle_id} className="vehicle-card">
            <h3>{vehicle.make_model}</h3>
            <p><strong>Type:</strong> {vehicle.category_name}</p>
            <p><strong>Registration:</strong> {vehicle.registration_number}</p>
            <p><strong>Capacity:</strong> {vehicle.seating_capacity} seats</p>
            <p><strong>Status:</strong> {vehicle.is_active ? 'Active' : 'Inactive'}</p>
          </div>
        ))}
        {vehicles.length === 0 && (
          <p className="no-data">No vehicles added yet. Add your first vehicle!</p>
        )}
      </div>
    </div>
  );
};

// Add Vehicle Form Component
const AddVehicleForm = ({ onSuccess, token, vehicleCategories }) => {
  const [formData, setFormData] = useState({
    categoryId: '',
    makeModel: '',
    registrationNumber: '',
    yearManufactured: '',
    color: '',
    seatingCapacity: '',
    hasAc: true,
    insuranceExpiry: ''
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await fetch('http://localhost:5001/api/driver/vehicles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();
      if (data.success) {
        alert('Vehicle added successfully!');
        onSuccess();
      } else {
        alert(data.message || 'Failed to add vehicle');
      }
    } catch (error) {
      console.error('Error adding vehicle:', error);
      alert('Failed to add vehicle');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="add-vehicle-form">
      <h3>Add New Vehicle</h3>
      
      <select
        value={formData.categoryId}
        onChange={(e) => setFormData({...formData, categoryId: e.target.value})}
        required
      >
        <option value="">Select Vehicle Category</option>
        {vehicleCategories.map(cat => (
          <option key={cat.category_id} value={cat.category_id}>
            {cat.category_name}
          </option>
        ))}
      </select>

      <input
        type="text"
        placeholder="Make & Model (e.g., Toyota Corolla)"
        value={formData.makeModel}
        onChange={(e) => setFormData({...formData, makeModel: e.target.value})}
        required
      />

      <input
        type="text"
        placeholder="Registration Number"
        value={formData.registrationNumber}
        onChange={(e) => setFormData({...formData, registrationNumber: e.target.value})}
        required
      />

      <input
        type="number"
        placeholder="Year Manufactured"
        value={formData.yearManufactured}
        onChange={(e) => setFormData({...formData, yearManufactured: e.target.value})}
      />

      <input
        type="text"
        placeholder="Color"
        value={formData.color}
        onChange={(e) => setFormData({...formData, color: e.target.value})}
      />

      <input
        type="number"
        placeholder="Seating Capacity"
        value={formData.seatingCapacity}
        onChange={(e) => setFormData({...formData, seatingCapacity: e.target.value})}
        required
      />

      <input
        type="date"
        placeholder="Insurance Expiry Date"
        value={formData.insuranceExpiry}
        onChange={(e) => setFormData({...formData, insuranceExpiry: e.target.value})}
        required
      />

      <label>
        <input
          type="checkbox"
          checked={formData.hasAc}
          onChange={(e) => setFormData({...formData, hasAc: e.target.checked})}
        />
        Air Conditioning
      </label>

      <button type="submit">Add Vehicle</button>
    </form>
  );
};

// Available Bookings Section Component
const AvailableBookingsSection = ({ bookings, onAccept }) => {
  return (
    <div className="bookings-section">
      <h2>Available Bookings</h2>
      <div className="bookings-grid">
        {bookings.map(booking => (
          <div key={booking.booking_id} className="booking-card">
            <h3>Booking #{booking.booking_id}</h3>
            <p><strong>Tourist:</strong> {booking.first_name} {booking.last_name}</p>
            <p><strong>Phone:</strong> {booking.tourist_phone}</p>
            <p><strong>From:</strong> {booking.pickup_location}</p>
            <p><strong>To:</strong> {JSON.parse(booking.destinations).join(', ')}</p>
            <p><strong>Date:</strong> {new Date(booking.start_date).toLocaleDateString()}</p>
            <p><strong>Time:</strong> {booking.start_time}</p>
            <p><strong>Travelers:</strong> {booking.travelers_count}</p>
            <p><strong>Vehicle Type:</strong> {booking.category_name}</p>
            <p><strong>Distance:</strong> {booking.total_distance_km} km</p>
            <p><strong>Total Cost:</strong> Rs. {booking.total_cost}</p>
            <button
              onClick={() => onAccept(booking.booking_id)}
              className="accept-btn"
            >
              Accept Booking
            </button>
          </div>
        ))}
        {bookings.length === 0 && (
          <p className="no-data">No available bookings matching your vehicle types.</p>
        )}
      </div>
    </div>
  );
};

// My Bookings Section Component
const MyBookingsSection = ({ bookings }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed': return '#28a745';
      case 'in_progress': return '#007bff';
      case 'completed': return '#6c757d';
      case 'cancelled': return '#dc3545';
      default: return '#ffc107';
    }
  };

  return (
    <div className="bookings-section">
      <h2>My Bookings</h2>
      <div className="bookings-grid">
        {bookings.map(booking => (
          <div key={booking.booking_id} className="booking-card">
            <h3>Booking #{booking.booking_id}</h3>
            <p><strong>Tourist:</strong> {booking.first_name} {booking.last_name}</p>
            <p><strong>Phone:</strong> {booking.tourist_phone}</p>
            <p><strong>From:</strong> {booking.pickup_location}</p>
            <p><strong>To:</strong> {JSON.parse(booking.destinations).join(', ')}</p>
            <p><strong>Date:</strong> {new Date(booking.start_date).toLocaleDateString()}</p>
            <p><strong>Time:</strong> {booking.start_time}</p>
            <p><strong>Travelers:</strong> {booking.travelers_count}</p>
            <p><strong>Vehicle Type:</strong> {booking.category_name}</p>
            <p><strong>Distance:</strong> {booking.total_distance_km} km</p>
            <p><strong>Total Cost:</strong> Rs. {booking.total_cost}</p>
            <p>
              <strong>Status:</strong>
              <span style={{
                color: getStatusColor(booking.status),
                fontWeight: 'bold',
                textTransform: 'capitalize'
              }}>
                {booking.status.replace('_', ' ')}
              </span>
            </p>
            {booking.status === 'confirmed' && (
              <p style={{ color: '#007bff', fontWeight: 'bold' }}>
                ⏳ Waiting for tourist payment
              </p>
            )}
          </div>
        ))}
        {bookings.length === 0 && (
          <p className="no-data">No bookings yet.</p>
        )}
      </div>
    </div>
  );
};

export default DriverDashboard;
