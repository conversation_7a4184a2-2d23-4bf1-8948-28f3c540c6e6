import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './DriverDashboard.css';

const DriverDashboard = () => {
  const [activeTab, setActiveTab] = useState('vehicles');
  const [vehicles, setVehicles] = useState([]);
  const [availableBookings, setAvailableBookings] = useState([]);
  const [myBookings, setMyBookings] = useState([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');

  useEffect(() => {
    if (!token || user.role !== 'driver') {
      navigate('/login');
      return;
    }
    loadData();
  }, [activeTab]);

  const loadData = async () => {
    setLoading(true);
    try {
      if (activeTab === 'vehicles') {
        await loadVehicles();
      } else if (activeTab === 'bookings') {
        await loadAvailableBookings();
      } else if (activeTab === 'my-bookings') {
        await loadMyBookings();
      }
    } catch (error) {
      console.error('Error loading data:', error);
    }
    setLoading(false);
  };

  const loadVehicles = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/driver/vehicles', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      if (data.success) {
        setVehicles(data.data);
      }
    } catch (error) {
      console.error('Error loading vehicles:', error);
    }
  };

  const loadAvailableBookings = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/driver/available-bookings', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      if (data.success) {
        setAvailableBookings(data.data);
      }
    } catch (error) {
      console.error('Error loading bookings:', error);
    }
  };

  const loadMyBookings = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/driver/my-bookings', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      if (data.success) {
        setMyBookings(data.data);
      }
    } catch (error) {
      console.error('Error loading my bookings:', error);
    }
  };

  const acceptBooking = async (bookingId) => {
    try {
      const response = await fetch(`http://localhost:5001/api/driver/accept-booking/${bookingId}`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      if (data.success) {
        alert('Booking accepted successfully!');
        loadAvailableBookings();
        loadMyBookings();
      } else {
        alert('Failed to accept booking: ' + data.message);
      }
    } catch (error) {
      console.error('Error accepting booking:', error);
      alert('Error accepting booking');
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    navigate('/login');
  };

  return (
    <div className="driver-dashboard">
      <header className="dashboard-header">
        <h1>Driver Dashboard</h1>
        <div className="header-actions">
          <span>Welcome, {user.profile?.first_name || 'Driver'}</span>
          <button onClick={logout} className="logout-btn">Logout</button>
        </div>
      </header>

      <nav className="dashboard-nav">
        <button 
          className={activeTab === 'vehicles' ? 'active' : ''}
          onClick={() => setActiveTab('vehicles')}
        >
          My Vehicles
        </button>
        <button 
          className={activeTab === 'bookings' ? 'active' : ''}
          onClick={() => setActiveTab('bookings')}
        >
          Available Bookings
        </button>
        <button 
          className={activeTab === 'my-bookings' ? 'active' : ''}
          onClick={() => setActiveTab('my-bookings')}
        >
          My Bookings
        </button>
      </nav>

      <main className="dashboard-content">
        {loading && <div className="loading">Loading...</div>}

        {activeTab === 'vehicles' && (
          <VehiclesSection vehicles={vehicles} onReload={loadVehicles} token={token} />
        )}

        {activeTab === 'bookings' && (
          <AvailableBookingsSection 
            bookings={availableBookings} 
            onAccept={acceptBooking} 
          />
        )}

        {activeTab === 'my-bookings' && (
          <MyBookingsSection bookings={myBookings} />
        )}
      </main>
    </div>
  );
};

// Vehicles Section Component
const VehiclesSection = ({ vehicles, onReload, token }) => {
  const [showAddForm, setShowAddForm] = useState(false);

  return (
    <div className="vehicles-section">
      <div className="section-header">
        <h2>My Vehicles</h2>
        <button 
          className="add-btn"
          onClick={() => setShowAddForm(true)}
        >
          Add Vehicle
        </button>
      </div>

      {showAddForm && (
        <AddVehicleForm 
          onClose={() => setShowAddForm(false)}
          onSuccess={() => {
            setShowAddForm(false);
            onReload();
          }}
          token={token}
        />
      )}

      <div className="vehicles-grid">
        {vehicles.map(vehicle => (
          <div key={vehicle.vehicle_id} className="vehicle-card">
            <h3>{vehicle.make_model}</h3>
            <p><strong>Type:</strong> {vehicle.category_name}</p>
            <p><strong>Registration:</strong> {vehicle.registration_number}</p>
            <p><strong>Capacity:</strong> {vehicle.seating_capacity} seats</p>
            <p><strong>Status:</strong> {vehicle.is_active ? 'Active' : 'Inactive'}</p>
          </div>
        ))}
        {vehicles.length === 0 && (
          <p className="no-data">No vehicles added yet. Add your first vehicle!</p>
        )}
      </div>
    </div>
  );
};

// Available Bookings Section Component
const AvailableBookingsSection = ({ bookings, onAccept }) => {
  return (
    <div className="bookings-section">
      <h2>Available Bookings</h2>
      <div className="bookings-list">
        {bookings.map(booking => (
          <div key={booking.booking_id} className="booking-card">
            <div className="booking-info">
              <h3>Trip from {booking.pickup_location}</h3>
              <p><strong>Destinations:</strong> {JSON.parse(booking.destinations).join(', ')}</p>
              <p><strong>Date:</strong> {new Date(booking.start_date).toLocaleDateString()}</p>
              <p><strong>Time:</strong> {booking.start_time}</p>
              <p><strong>Travelers:</strong> {booking.travelers_count}</p>
              <p><strong>Distance:</strong> {booking.total_distance_km} km</p>
              <p><strong>Total Cost:</strong> Rs. {booking.total_cost}</p>
              <p><strong>Vehicle Type:</strong> {booking.category_name}</p>
            </div>
            <button 
              className="accept-btn"
              onClick={() => onAccept(booking.booking_id)}
            >
              Accept Booking
            </button>
          </div>
        ))}
        {bookings.length === 0 && (
          <p className="no-data">No available bookings matching your vehicle types.</p>
        )}
      </div>
    </div>
  );
};

// My Bookings Section Component
const MyBookingsSection = ({ bookings }) => {
  return (
    <div className="bookings-section">
      <h2>My Bookings</h2>
      <div className="bookings-list">
        {bookings.map(booking => (
          <div key={booking.booking_id} className="booking-card">
            <div className="booking-info">
              <h3>Trip from {booking.pickup_location}</h3>
              <p><strong>Destinations:</strong> {JSON.parse(booking.destinations).join(', ')}</p>
              <p><strong>Date:</strong> {new Date(booking.start_date).toLocaleDateString()}</p>
              <p><strong>Time:</strong> {booking.start_time}</p>
              <p><strong>Travelers:</strong> {booking.travelers_count}</p>
              <p><strong>Total Cost:</strong> Rs. {booking.total_cost}</p>
              <p><strong>Status:</strong> {booking.status}</p>
              <p><strong>Payment:</strong> {booking.payment_status || 'Pending'}</p>
            </div>
            <div className={`status-badge ${booking.status}`}>
              {booking.status.toUpperCase()}
            </div>
          </div>
        ))}
        {bookings.length === 0 && (
          <p className="no-data">No bookings yet.</p>
        )}
      </div>
    </div>
  );
};

// Add Vehicle Form Component
const AddVehicleForm = ({ onClose, onSuccess, token }) => {
  const [formData, setFormData] = useState({
    category_id: '',
    make_model: '',
    registration_number: '',
    year_manufactured: '',
    color: '',
    seating_capacity: '',
    insurance_expiry: ''
  });
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/bookings/vehicle-categories');
      const data = await response.json();
      if (data.success) {
        setCategories(data.data);
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('http://localhost:5001/api/driver/vehicles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();
      if (data.success) {
        alert('Vehicle added successfully!');
        onSuccess();
      } else {
        alert('Failed to add vehicle: ' + data.message);
      }
    } catch (error) {
      console.error('Error adding vehicle:', error);
      alert('Error adding vehicle');
    }
    setLoading(false);
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Add New Vehicle</h3>
          <button onClick={onClose} className="close-btn">×</button>
        </div>
        <form onSubmit={handleSubmit} className="vehicle-form">
          <div className="form-group">
            <label>Vehicle Category:</label>
            <select 
              value={formData.category_id}
              onChange={(e) => setFormData({...formData, category_id: e.target.value})}
              required
            >
              <option value="">Select Category</option>
              {categories.map(cat => (
                <option key={cat.category_id} value={cat.category_id}>
                  {cat.category_name}
                </option>
              ))}
            </select>
          </div>
          <div className="form-group">
            <label>Make & Model:</label>
            <input 
              type="text"
              value={formData.make_model}
              onChange={(e) => setFormData({...formData, make_model: e.target.value})}
              placeholder="e.g., Toyota Corolla"
              required
            />
          </div>
          <div className="form-group">
            <label>Registration Number:</label>
            <input 
              type="text"
              value={formData.registration_number}
              onChange={(e) => setFormData({...formData, registration_number: e.target.value})}
              placeholder="e.g., CAR-1234"
              required
            />
          </div>
          <div className="form-group">
            <label>Year:</label>
            <input 
              type="number"
              value={formData.year_manufactured}
              onChange={(e) => setFormData({...formData, year_manufactured: e.target.value})}
              min="1990"
              max="2025"
            />
          </div>
          <div className="form-group">
            <label>Color:</label>
            <input 
              type="text"
              value={formData.color}
              onChange={(e) => setFormData({...formData, color: e.target.value})}
              placeholder="e.g., White"
            />
          </div>
          <div className="form-group">
            <label>Seating Capacity:</label>
            <input 
              type="number"
              value={formData.seating_capacity}
              onChange={(e) => setFormData({...formData, seating_capacity: e.target.value})}
              min="1"
              max="50"
              required
            />
          </div>
          <div className="form-group">
            <label>Insurance Expiry:</label>
            <input 
              type="date"
              value={formData.insurance_expiry}
              onChange={(e) => setFormData({...formData, insurance_expiry: e.target.value})}
              required
            />
          </div>
          <div className="form-actions">
            <button type="button" onClick={onClose}>Cancel</button>
            <button type="submit" disabled={loading}>
              {loading ? 'Adding...' : 'Add Vehicle'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DriverDashboard;
