import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { calculateRouteDistance } from '../utils/mapUtils';
import { toast } from 'react-toastify';
import LoadingSpinner from '../components/LoadingSpinner';

function TripPlanner() {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [tripData, setTripData] = useState({
    startDate: '',
    startTime: '09:00',
    travelers: 2,
    tripType: 'one-way',
    pickupLocation: '',
    destinations: [''],
    finalDestination: ''
  });
  const [tripCalculation, setTripCalculation] = useState(null);
  const [calculating, setCalculating] = useState(false);

  const handleInputChange = (field, value) => {
    setTripData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addDestination = () => {
    setTripData(prev => ({
      ...prev,
      destinations: [...prev.destinations, '']
    }));
  };

  const updateDestination = (index, value) => {
    setTripData(prev => ({
      ...prev,
      destinations: prev.destinations.map((dest, i) => i === index ? value : dest)
    }));
  };

  const removeDestination = (index) => {
    setTripData(prev => ({
      ...prev,
      destinations: prev.destinations.filter((_, i) => i !== index)
    }));
  };

  const calculateRoute = async () => {
    // Validate inputs
    if (!tripData.pickupLocation.trim()) {
      toast.error('Please enter a pickup location');
      return;
    }

    if (!tripData.destinations[0] || !tripData.destinations[0].trim()) {
      toast.error('Please enter at least one destination');
      return;
    }

    if (!tripData.startDate) {
      toast.error('Please select a start date');
      return;
    }

    setCalculating(true);

    try {
      // Prepare locations array for calculation
      const locations = [tripData.pickupLocation];

      // Add valid destinations
      const validDestinations = tripData.destinations.filter(dest => dest && dest.trim());
      locations.push(...validDestinations);

      // Add final destination if different and specified
      if (tripData.finalDestination && tripData.finalDestination.trim() &&
          tripData.finalDestination !== validDestinations[validDestinations.length - 1]) {
        locations.push(tripData.finalDestination);
      }

      console.log('Calculating route for locations:', locations);

      // Calculate actual distances using Google Maps API
      const result = await calculateRouteDistance(locations, {
        isReturnTrip: tripData.tripType === 'return',
        startTime: tripData.startTime,
        additionalStopTime: 3 // 3 hours stop time
      });

      if (result.success) {
        setTripCalculation(result);
        setCurrentStep(2);
        toast.success('Route calculated successfully!');
      } else {
        toast.error(result.error || 'Failed to calculate route');
      }

    } catch (error) {
      console.error('Error calculating route:', error);
      toast.error('An error occurred while calculating the route');
    } finally {
      setCalculating(false);
    }
  };

  const calculateEndTime = (startTime, durationHours) => {
    const [hours, minutes] = startTime.split(':').map(Number);
    const startMinutes = hours * 60 + minutes;
    const endMinutes = startMinutes + (durationHours * 60);
    const endHours = Math.floor(endMinutes / 60) % 24;
    const endMins = Math.round(endMinutes % 60);
    return `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;
  };

  const proceedToVehicleSelection = () => {
    setCurrentStep(3);
  };

  const goBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      navigate('/dashboard');
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px'
    }}>
      <div style={{
        maxWidth: '1000px',
        margin: '0 auto',
        background: 'white',
        borderRadius: '10px',
        padding: '30px',
        boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '30px',
          borderBottom: '2px solid #f0f0f0',
          paddingBottom: '20px'
        }}>
          <h1 style={{ color: '#333', margin: 0 }}>Plan Your Trip</h1>
          <button
            onClick={goBack}
            style={{
              background: '#6c757d',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '5px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            ← Back
          </button>
        </div>

        <p style={{ color: '#666', marginBottom: '30px' }}>
          Plan your perfect Sri Lankan adventure with our comprehensive trip planner.
        </p>

        {/* Progress Steps */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          marginBottom: '40px',
          gap: '20px'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            color: currentStep >= 1 ? '#667eea' : '#ccc',
            fontWeight: currentStep === 1 ? 'bold' : 'normal'
          }}>
            <span style={{
              background: currentStep >= 1 ? '#667eea' : '#ccc',
              color: 'white',
              borderRadius: '50%',
              width: '25px',
              height: '25px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '8px',
              fontSize: '12px'
            }}>1</span>
            Plan Trip
          </div>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            color: currentStep >= 2 ? '#667eea' : '#ccc',
            fontWeight: currentStep === 2 ? 'bold' : 'normal'
          }}>
            <span style={{
              background: currentStep >= 2 ? '#667eea' : '#ccc',
              color: 'white',
              borderRadius: '50%',
              width: '25px',
              height: '25px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '8px',
              fontSize: '12px'
            }}>2</span>
            Driver Confirmation
          </div>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            color: currentStep >= 3 ? '#667eea' : '#ccc',
            fontWeight: currentStep === 3 ? 'bold' : 'normal'
          }}>
            <span style={{
              background: currentStep >= 3 ? '#667eea' : '#ccc',
              color: 'white',
              borderRadius: '50%',
              width: '25px',
              height: '25px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '8px',
              fontSize: '12px'
            }}>3</span>
            Payment
          </div>
        </div>

        {/* Step 1: Trip Planning */}
        {currentStep === 1 && (
          <div>
            {calculating && (
              <div style={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'rgba(0,0,0,0.5)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 1000
              }}>
                <div style={{
                  background: 'white',
                  padding: '30px',
                  borderRadius: '10px',
                  textAlign: 'center'
                }}>
                  <LoadingSpinner message="Calculating route using Google Maps..." size="large" />
                </div>
              </div>
            )}

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '40px' }}>
            {/* Trip Details */}
            <div>
              <h3 style={{ color: '#333', marginBottom: '20px' }}>Trip Details</h3>
              
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#333' }}>
                  Start Date
                </label>
                <input
                  type="date"
                  value={tripData.startDate}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #ddd',
                    borderRadius: '8px',
                    fontSize: '14px'
                  }}
                />
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#333' }}>
                  Start Time
                </label>
                <input
                  type="time"
                  value={tripData.startTime}
                  onChange={(e) => handleInputChange('startTime', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #ddd',
                    borderRadius: '8px',
                    fontSize: '14px'
                  }}
                />
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#333' }}>
                  Number of Travelers
                </label>
                <input
                  type="number"
                  min="1"
                  max="15"
                  value={tripData.travelers}
                  onChange={(e) => handleInputChange('travelers', parseInt(e.target.value))}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #ddd',
                    borderRadius: '8px',
                    fontSize: '14px'
                  }}
                />
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#333' }}>
                  Trip Type
                </label>
                <div style={{ display: 'flex', gap: '15px' }}>
                  <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                    <input
                      type="radio"
                      name="tripType"
                      value="one-way"
                      checked={tripData.tripType === 'one-way'}
                      onChange={(e) => handleInputChange('tripType', e.target.value)}
                      style={{ marginRight: '8px' }}
                    />
                    One-way Trip
                  </label>
                  <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                    <input
                      type="radio"
                      name="tripType"
                      value="return"
                      checked={tripData.tripType === 'return'}
                      onChange={(e) => handleInputChange('tripType', e.target.value)}
                      style={{ marginRight: '8px' }}
                    />
                    Return Trip
                  </label>
                </div>
                <p style={{ color: '#666', fontSize: '12px', margin: '5px 0 0 0' }}>
                  One-way trip ending at the final destination
                </p>
              </div>
            </div>

            {/* Route Planning */}
            <div>
              <h3 style={{ color: '#333', marginBottom: '20px' }}>Route Planning</h3>
              
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#333' }}>
                  Pickup Location (Origin)
                </label>
                <input
                  type="text"
                  placeholder="Enter pickup location"
                  value={tripData.pickupLocation}
                  onChange={(e) => handleInputChange('pickupLocation', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #ddd',
                    borderRadius: '8px',
                    fontSize: '14px'
                  }}
                />
              </div>

              {tripData.destinations.map((destination, index) => (
                <div key={index} style={{ marginBottom: '15px' }}>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#333' }}>
                    Destination {index + 1}
                  </label>
                  <div style={{ display: 'flex', gap: '10px' }}>
                    <input
                      type="text"
                      placeholder="Enter destination"
                      value={destination}
                      onChange={(e) => updateDestination(index, e.target.value)}
                      style={{
                        flex: 1,
                        padding: '12px',
                        border: '2px solid #ddd',
                        borderRadius: '8px',
                        fontSize: '14px'
                      }}
                    />
                    {tripData.destinations.length > 1 && (
                      <button
                        onClick={() => removeDestination(index)}
                        style={{
                          background: '#dc3545',
                          color: 'white',
                          border: 'none',
                          padding: '12px',
                          borderRadius: '8px',
                          cursor: 'pointer'
                        }}
                      >
                        ✕
                      </button>
                    )}
                  </div>
                </div>
              ))}

              <button
                onClick={addDestination}
                style={{
                  background: '#28a745',
                  color: 'white',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  marginBottom: '20px',
                  fontSize: '14px'
                }}
              >
                + Add Destination
              </button>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#333' }}>
                  Final Destination (Optional)
                </label>
                <input
                  type="text"
                  placeholder="Enter final destination (if different from last waypoint)"
                  value={tripData.finalDestination}
                  onChange={(e) => handleInputChange('finalDestination', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #ddd',
                    borderRadius: '8px',
                    fontSize: '14px'
                  }}
                />
              </div>

              <button
                onClick={calculateRoute}
                disabled={calculating}
                style={{
                  width: '100%',
                  background: calculating ? '#ccc' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  border: 'none',
                  padding: '15px',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  cursor: calculating ? 'not-allowed' : 'pointer',
                  opacity: calculating ? 0.7 : 1
                }}
              >
                {calculating ? '🔄 Calculating...' : '🧮 Calculate Route'}
              </button>
            </div>
            </div>
          </div>
        )}

        {/* Step 2: Trip Calculation Results */}
        {currentStep === 2 && tripCalculation && (
          <div>
            <h2 style={{ color: '#333', marginBottom: '30px' }}>Trip Calculation</h2>
            
            {/* Trip Type and Summary */}
            <div style={{
              background: '#e3f2fd',
              padding: '20px',
              borderRadius: '8px',
              marginBottom: '30px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div>
                <h3 style={{ color: '#1976d2', margin: '0 0 10px 0' }}>
                  Trip Type: {tripCalculation.breakdown.tripType}
                </h3>
              </div>
              <div style={{ textAlign: 'right' }}>
                <div style={{ marginBottom: '10px' }}>
                  <strong style={{ color: '#333' }}>Total Distance</strong>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1976d2' }}>
                    {tripCalculation.breakdown.totalDistance}
                  </div>
                </div>
                <div>
                  <strong style={{ color: '#333' }}>Total Duration</strong>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1976d2' }}>
                    {tripCalculation.breakdown.totalDuration}
                  </div>
                </div>
              </div>
            </div>

            {/* Distance Calculation Breakdown */}
            <div style={{ marginBottom: '30px' }}>
              <h3 style={{ color: '#333', marginBottom: '15px' }}>Distance Calculation Breakdown</h3>
              <div style={{
                background: '#f8f9fa',
                padding: '20px',
                borderRadius: '8px',
                border: '1px solid #e9ecef'
              }}>
                <div style={{ marginBottom: '15px' }}>
                  <strong>Distance Calculation Breakdown:</strong>
                </div>
                {tripCalculation.breakdown.segments.map((segment, index) => (
                  <div key={index} style={{ marginBottom: '10px' }}>
                    <div>Segment {index + 1}: {segment.from} → {segment.to}</div>
                    <div>Distance: {segment.distance}</div>
                    <div>Duration: {segment.duration}</div>
                  </div>
                ))}
                <div style={{ marginTop: '15px', paddingTop: '15px', borderTop: '1px solid #ddd' }}>
                  <div><strong>Total Distance: {tripCalculation.breakdown.totalDistance}</strong></div>
                  <div><strong>Total Duration: {tripCalculation.breakdown.totalDuration}</strong></div>
                  <div><strong>Trip Type: {tripCalculation.breakdown.tripType}</strong></div>
                </div>
              </div>
            </div>

            {/* Trip Feasibility Analysis */}
            <div style={{ marginBottom: '30px' }}>
              <h3 style={{ color: '#333', marginBottom: '15px' }}>Trip Feasibility Analysis</h3>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '20px'
              }}>
                <div style={{ background: '#f8f9fa', padding: '15px', borderRadius: '8px' }}>
                  <div style={{ fontWeight: 'bold', color: '#333' }}>Trip Type</div>
                  <div style={{ color: '#667eea', fontWeight: 'bold' }}>{tripCalculation.feasibility.tripType}</div>
                </div>
                <div style={{ background: '#f8f9fa', padding: '15px', borderRadius: '8px' }}>
                  <div style={{ fontWeight: 'bold', color: '#333' }}>Distance</div>
                  <div style={{ color: '#667eea', fontWeight: 'bold' }}>{tripCalculation.feasibility.distance}</div>
                </div>
                <div style={{ background: '#f8f9fa', padding: '15px', borderRadius: '8px' }}>
                  <div style={{ fontWeight: 'bold', color: '#333' }}>Driving Time</div>
                  <div style={{ color: '#667eea', fontWeight: 'bold' }}>{tripCalculation.feasibility.drivingTime}</div>
                </div>
                <div style={{ background: '#f8f9fa', padding: '15px', borderRadius: '8px' }}>
                  <div style={{ fontWeight: 'bold', color: '#333' }}>Stop Time</div>
                  <div style={{ color: '#667eea', fontWeight: 'bold' }}>{tripCalculation.feasibility.stopTime}</div>
                </div>
                <div style={{ background: '#f8f9fa', padding: '15px', borderRadius: '8px' }}>
                  <div style={{ fontWeight: 'bold', color: '#333' }}>Total Trip Duration</div>
                  <div style={{ color: '#667eea', fontWeight: 'bold' }}>{tripCalculation.feasibility.totalDuration}</div>
                </div>
              </div>
            </div>

            {/* Trip Schedule */}
            <div style={{ marginBottom: '30px' }}>
              <h3 style={{ color: '#333', marginBottom: '15px' }}>Trip Schedule</h3>
              <div style={{
                background: '#f8f9fa',
                padding: '20px',
                borderRadius: '8px',
                border: '1px solid #e9ecef'
              }}>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                  gap: '20px'
                }}>
                  <div>
                    <div style={{ fontWeight: 'bold', color: '#333' }}>Start Time</div>
                    <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#667eea' }}>
                      {tripCalculation.schedule.startTime}
                    </div>
                  </div>
                  <div>
                    <div style={{ fontWeight: 'bold', color: '#333' }}>Estimated End Time</div>
                    <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#667eea' }}>
                      {tripCalculation.schedule.estimatedEndTime}
                    </div>
                  </div>
                  <div>
                    <div style={{ fontWeight: 'bold', color: '#333' }}>Days Needed</div>
                    <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#667eea' }}>
                      {tripCalculation.schedule.daysNeeded}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <button
              onClick={proceedToVehicleSelection}
              style={{
                width: '100%',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                padding: '15px',
                borderRadius: '8px',
                fontSize: '16px',
                fontWeight: 'bold',
                cursor: 'pointer'
              }}
            >
              Continue to Vehicle Selection →
            </button>
          </div>
        )}

        {/* Step 3: Vehicle Selection (placeholder) */}
        {currentStep === 3 && (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <h2 style={{ color: '#333', marginBottom: '20px' }}>Vehicle Selection</h2>
            <p style={{ color: '#666', marginBottom: '30px' }}>
              Vehicle selection interface will be implemented next...
            </p>
            <button
              onClick={() => setCurrentStep(2)}
              style={{
                background: '#6c757d',
                color: 'white',
                border: 'none',
                padding: '10px 20px',
                borderRadius: '5px',
                cursor: 'pointer'
              }}
            >
              ← Back to Trip Calculation
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default TripPlanner;
