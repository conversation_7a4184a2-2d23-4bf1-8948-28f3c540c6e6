// Script to create admin user with proper password hash
const bcrypt = require('bcryptjs');
const mysql = require('mysql2/promise');
require('dotenv').config({ path: './.env' });

async function createAdminUser() {
    let connection;
    
    try {
        // Create database connection
        connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: process.env.DB_NAME || 'siyoga_travel_booking'
        });

        console.log('✅ Connected to database');

        // Hash the password 'admin123'
        const password = 'admin123';
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        console.log('✅ Password hashed successfully');

        // Check if admin user already exists
        const [existingUsers] = await connection.execute(
            'SELECT user_id FROM users WHERE email = ?',
            ['<EMAIL>']
        );

        if (existingUsers.length > 0) {
            // Update existing admin user
            await connection.execute(
                'UPDATE users SET password = ?, is_verified = TRUE, is_active = TRUE WHERE email = ?',
                [hashedPassword, '<EMAIL>']
            );
            console.log('✅ Admin user updated successfully');
        } else {
            // Create new admin user
            await connection.execute(
                'INSERT INTO users (email, password, role, is_verified, is_active) VALUES (?, ?, ?, ?, ?)',
                ['<EMAIL>', hashedPassword, 'admin', true, true]
            );
            console.log('✅ Admin user created successfully');
        }

        console.log('🎉 Admin user setup complete!');
        console.log('📧 Email: <EMAIL>');
        console.log('🔑 Password: admin123');
        console.log('⚠️  Please change the password after first login in production!');

    } catch (error) {
        console.error('❌ Error creating admin user:', error.message);
    } finally {
        if (connection) {
            await connection.end();
            console.log('✅ Database connection closed');
        }
    }
}

// Run the script
createAdminUser();
