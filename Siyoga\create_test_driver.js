const mysql = require('mysql2/promise');
const bcrypt = require('./server/node_modules/bcryptjs');

async function createTestDriver() {
  try {
    console.log('🚗 Creating Test Driver...\n');

    const dbConfig = {
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'siyoga_travel_booking',
      port: 3306
    };

    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');

    // Check if driver already exists
    const [existingUser] = await connection.execute(
      'SELECT user_id FROM users WHERE email = ?',
      ['<EMAIL>']
    );

    if (existingUser.length > 0) {
      console.log('⚠️  Driver already exists with email: <EMAIL>');
      await connection.end();
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('password123', 10);

    // Create user account
    const [userResult] = await connection.execute(`
      INSERT INTO users (email, password, role, is_verified, is_active)
      VALUES (?, ?, 'driver', 1, 1)
    `, ['<EMAIL>', hashedPassword]);

    const userId = userResult.insertId;
    console.log(`✅ Created user account with ID: ${userId}`);

    // Create driver profile
    const [driverResult] = await connection.execute(`
      INSERT INTO drivers (
        user_id, first_name, last_name, phone, nic_number, 
        license_number, license_expiry, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'approved')
    `, [
      userId,
      'Saman',
      'Perera',
      '**********',
      '123456789V',
      '********',
      '2025-12-31'
    ]);

    const driverId = driverResult.insertId;
    console.log(`✅ Created driver profile with ID: ${driverId}`);

    console.log('\n🎉 Test Driver Created Successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: password123');
    console.log('👤 Name: Saman Perera');
    console.log('📱 Phone: **********');

    await connection.end();

  } catch (error) {
    console.error('❌ Error creating test driver:', error.message);
  }
}

createTestDriver();
