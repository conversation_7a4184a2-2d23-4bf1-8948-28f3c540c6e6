-- =============================================
-- Siyoga Travel - Sample Data Insertion
-- =============================================

USE siyoga_travel;

-- Insert sample users (customers)
INSERT INTO users (name, email, phone, password_hash, role, created_at) VALUES
('<PERSON>', '<EMAIL>', '+1234567890', '$2b$10$hashedpassword1', 'customer', '2025-02-15 10:00:00'),
('<PERSON>', '<EMAIL>', '+1234567891', '$2b$10$hashedpassword2', 'customer', '2025-02-16 11:30:00'),
('<PERSON>', '<EMAIL>', '+1234567892', '$2b$10$hashedpassword3', 'customer', '2025-02-17 09:15:00'),
('<PERSON>', '<EMAIL>', '+1234567893', '$2b$10$hashedpassword4', 'customer', '2025-02-18 14:20:00'),
('<PERSON> <PERSON>', '<EMAIL>', '+1234567894', '$2b$10$hashedpassword5', 'customer', '2025-02-19 16:45:00'),
('<PERSON> <PERSON>', '<EMAIL>', '+1234567895', '$2b$10$hashedpassword6', 'customer', '2025-02-20 08:30:00'),
('Robert Miller', '<EMAIL>', '+**********', '$2b$10$hashedpassword7', 'customer', '2025-02-21 12:00:00'),
('Lisa Anderson', '<EMAIL>', '+**********', '$2b$10$hashedpassword8', 'customer', '2025-02-22 15:30:00');

-- Insert admin user
INSERT INTO users (name, email, phone, password_hash, role, created_at) VALUES
('Admin User', '<EMAIL>', '+**********', '$2b$10$hashedadminpassword', 'admin', '2025-02-01 00:00:00');

-- Insert sample drivers
INSERT INTO drivers (user_id, license_number, vehicle_type, experience_years, rating, is_available, location_lat, location_lng, created_at) VALUES
(10, 'DL001234567', 'Car', 5, 4.8, 1, 6.9271, 79.8612, '2025-02-10 09:00:00'),
(11, 'DL001234568', 'Van', 8, 4.9, 1, 6.9319, 79.8478, '2025-02-11 10:00:00'),
(12, 'DL001234569', 'Bus', 12, 4.7, 1, 6.9147, 79.9729, '2025-02-12 11:00:00'),
(13, 'DL001234570', 'Car', 3, 4.6, 1, 6.9497, 79.8543, '2025-02-13 12:00:00'),
(14, 'DL001234571', 'Van', 6, 4.8, 1, 6.9388, 79.8542, '2025-02-14 13:00:00');

-- Insert additional driver users
INSERT INTO users (name, email, phone, password_hash, role, created_at) VALUES
('Driver One', '<EMAIL>', '+**********', '$2b$10$hasheddriverpass1', 'driver', '2025-02-10 09:00:00'),
('Driver Two', '<EMAIL>', '+**********', '$2b$10$hasheddriverpass2', 'driver', '2025-02-11 10:00:00'),
('Driver Three', '<EMAIL>', '+**********', '$2b$10$hasheddriverpass3', 'driver', '2025-02-12 11:00:00'),
('Driver Four', '<EMAIL>', '+**********', '$2b$10$hasheddriverpass4', 'driver', '2025-02-13 12:00:00'),
('Driver Five', '<EMAIL>', '+1234567904', '$2b$10$hasheddriverpass5', 'driver', '2025-02-14 13:00:00');

-- Insert sample bookings
INSERT INTO bookings (user_id, driver_id, pickup_location, dropoff_location, pickup_lat, pickup_lng, dropoff_lat, dropoff_lng, 
                     pickup_time, vehicle_type, passenger_count, estimated_cost, actual_cost, status, created_at) VALUES
(1, 1, 'Colombo Fort Railway Station', 'Bandaranaike International Airport', 6.9344, 79.8428, 7.1808, 79.8841, 
 '2025-03-01 08:00:00', 'Car', 2, 3500.00, 3500.00, 'completed', '2025-02-25 10:00:00'),
(2, 2, 'Galle Face Green', 'Kandy City Center', 6.9271, 79.8612, 7.2906, 80.6337, 
 '2025-03-02 09:30:00', 'Van', 4, 8500.00, 8200.00, 'completed', '2025-02-26 11:30:00'),
(3, 3, 'Mount Lavinia Beach', 'Sigiriya Rock', 6.8344, 79.8633, 7.9568, 80.7598, 
 '2025-03-03 07:00:00', 'Bus', 15, 12000.00, 12000.00, 'completed', '2025-02-27 09:15:00'),
(4, 1, 'Negombo Beach', 'Ella Town', 7.2083, 79.8358, 6.8721, 81.0462, 
 '2025-03-04 06:30:00', 'Car', 3, 15000.00, 14500.00, 'completed', '2025-02-28 14:20:00'),
(5, 2, 'Colombo National Museum', 'Nuwara Eliya', 6.9147, 79.8606, 6.9497, 80.7891, 
 '2025-03-05 10:00:00', 'Van', 5, 11000.00, NULL, 'confirmed', '2025-03-01 16:45:00'),
(6, 4, 'Dehiwala Zoo', 'Bentota Beach', 6.8344, 79.8729, 6.4260, 79.9956, 
 '2025-03-06 11:30:00', 'Car', 2, 4500.00, NULL, 'pending', '2025-03-02 08:30:00'),
(7, 5, 'Pettah Market', 'Anuradhapura', 6.9388, 79.8542, 8.3114, 80.4037, 
 '2025-03-07 05:00:00', 'Van', 6, 13500.00, NULL, 'confirmed', '2025-03-03 12:00:00'),
(8, 3, 'Independence Square', 'Polonnaruwa', 6.9147, 79.8729, 7.9403, 81.0188, 
 '2025-03-08 08:30:00', 'Bus', 20, 16000.00, NULL, 'pending', '2025-03-04 15:30:00');

-- Insert sample payments
INSERT INTO payments (booking_id, amount, payment_method, payment_status, transaction_id, created_at) VALUES
(1, 3500.00, 'credit_card', 'completed', 'TXN001234567890', '2025-03-01 10:30:00'),
(2, 8200.00, 'cash', 'completed', 'CASH001234567891', '2025-03-02 12:00:00'),
(3, 12000.00, 'bank_transfer', 'completed', 'BT001234567892', '2025-03-03 09:30:00'),
(4, 14500.00, 'credit_card', 'completed', 'TXN001234567893', '2025-03-04 08:00:00');

-- Insert sample reviews
INSERT INTO reviews (booking_id, user_id, driver_id, rating, comment, created_at) VALUES
(1, 1, 1, 5, 'Excellent service! Driver was punctual and very professional.', '2025-03-01 11:00:00'),
(2, 2, 2, 4, 'Good trip, comfortable van. Driver was friendly.', '2025-03-02 13:00:00'),
(3, 3, 3, 5, 'Amazing journey to Sigiriya. Bus was clean and driver very knowledgeable.', '2025-03-03 18:00:00'),
(4, 4, 1, 4, 'Great service to Ella. Beautiful scenic route.', '2025-03-04 20:00:00');

-- Insert sample notifications
INSERT INTO notifications (user_id, title, message, type, is_read, created_at) VALUES
(1, 'Booking Confirmed', 'Your booking to Airport has been confirmed', 'booking', 1, '2025-02-25 10:05:00'),
(2, 'Trip Completed', 'Your trip to Kandy has been completed successfully', 'trip', 1, '2025-03-02 15:00:00'),
(3, 'Payment Received', 'Payment of Rs. 12,000 has been received', 'payment', 1, '2025-03-03 09:35:00'),
(4, 'Driver Assigned', 'Driver has been assigned for your Ella trip', 'booking', 1, '2025-02-28 14:25:00'),
(5, 'Booking Reminder', 'Your trip to Nuwara Eliya is tomorrow at 10:00 AM', 'reminder', 0, '2025-03-04 18:00:00'),
(6, 'New Booking Request', 'You have a new booking request to Bentota', 'booking', 0, '2025-03-02 08:35:00'),
(7, 'Trip Confirmed', 'Your Anuradhapura trip has been confirmed', 'booking', 0, '2025-03-03 12:05:00'),
(8, 'Booking Pending', 'Your Polonnaruwa booking is pending driver confirmation', 'booking', 0, '2025-03-04 15:35:00');
