-- =====================================================
-- Siyoga Travel Booking System Database Schema
-- =====================================================

-- Create database (run this first in phpMyAdmin)
CREATE DATABASE IF NOT EXISTS siyoga_travel_booking;
USE siyoga_travel_booking;

-- =====================================================
-- 1. USERS TABLE (Core authentication table)
-- =====================================================
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('tourist', 'driver', 'admin') NOT NULL DEFAULT 'tourist',
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_verified (is_verified)
);

-- =====================================================
-- 2. EMAIL VERIFICATION TABLE
-- =====================================================
CREATE TABLE email_verification (
    token_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    verification_token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_token (verification_token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires (expires_at)
);

-- =====================================================
-- 3. TOURISTS TABLE (Tourist profile information)
-- =====================================================
CREATE TABLE tourists (
    tourist_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    country VARCHAR(100),
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    travel_preferences TEXT, -- JSON string for preferences like adventure, beach, culture
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_phone (phone)
);

-- =====================================================
-- 4. DRIVERS TABLE (Driver profile information)
-- =====================================================
CREATE TABLE drivers (
    driver_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    nic_number VARCHAR(20) NOT NULL UNIQUE,
    license_number VARCHAR(50) NOT NULL UNIQUE,
    license_expiry DATE NOT NULL,
    profile_picture VARCHAR(255), -- File path for profile picture
    nic_front_image VARCHAR(255), -- File path for NIC front image
    nic_back_image VARCHAR(255), -- File path for NIC back image
    license_image VARCHAR(255), -- File path for license image
    status ENUM('pending', 'approved', 'rejected', 'suspended') DEFAULT 'pending',
    admin_notes TEXT, -- Admin notes for approval/rejection
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_nic (nic_number),
    INDEX idx_license (license_number)
);

-- =====================================================
-- 5. VEHICLES TABLE (Driver vehicle information)
-- =====================================================
CREATE TABLE vehicles (
    vehicle_id INT AUTO_INCREMENT PRIMARY KEY,
    driver_id INT NOT NULL,
    vehicle_type ENUM('car', 'van', 'bus', 'suv') NOT NULL,
    make_model VARCHAR(100) NOT NULL,
    registration_number VARCHAR(20) NOT NULL UNIQUE,
    year_manufactured YEAR,
    color VARCHAR(50),
    seating_capacity INT NOT NULL,
    vehicle_image VARCHAR(255), -- File path for vehicle image
    insurance_expiry DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (driver_id) REFERENCES drivers(driver_id) ON DELETE CASCADE,
    INDEX idx_driver_id (driver_id),
    INDEX idx_vehicle_type (vehicle_type),
    INDEX idx_registration (registration_number),
    INDEX idx_active (is_active)
);

-- =====================================================
-- Insert sample admin user (password: admin123)
-- =====================================================
INSERT INTO users (email, password, role, is_verified) VALUES 
('<EMAIL>', '$2b$10$rQZ8kHWKQVXGXqVXGXqVXOeKkKkKkKkKkKkKkKkKkKkKkKkKkKkKk', 'admin', TRUE);

-- =====================================================
-- Create indexes for better performance
-- =====================================================
-- Additional composite indexes for common queries
CREATE INDEX idx_users_role_verified ON users(role, is_verified);
CREATE INDEX idx_drivers_status_active ON drivers(status) WHERE status = 'approved';
CREATE INDEX idx_vehicles_type_active ON vehicles(vehicle_type, is_active);
