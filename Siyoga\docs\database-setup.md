# Database Setup Instructions

## Step-by-Step Database Creation in phpMyAdmin

### Step 1: Create Database
1. Open phpMyAdmin in your browser (usually http://localhost/phpmyadmin)
2. Click on "SQL" tab at the top
3. Copy and paste the content from `/database/01_create_database.sql`
4. Click "Go" to execute

### Step 2: Create Core Tables
1. In phpMyAdmin, select the `siyoga_travel_booking` database from the left sidebar
2. Click on "SQL" tab
3. Copy and paste the content from `/database/02_create_core_tables.sql`
4. Click "Go" to execute

### Step 3: Verify Tables Created
You should see these tables in your database:
- `users` - Core user authentication
- `email_verification` - Email verification tokens
- `tourists` - Tourist profile information

### Database Schema Overview

#### Users Table
- `user_id` (Primary Key)
- `email` (Unique)
- `password` (Hashed)
- `role` (tourist/driver/admin)
- `is_verified` (Boolean)
- `is_active` (Boolean)
- Timestamps

#### Email Verification Table
- `token_id` (Primary Key)
- `user_id` (Foreign Key to users)
- `verification_token` (Unique token)
- `expires_at` (Token expiry)
- `is_used` (Boolean)

#### Tourists Table
- `tourist_id` (Primary Key)
- `user_id` (Foreign Key to users, Unique)
- Personal information (name, phone, DOB, etc.)
- Emergency contact details
- Travel preferences (JSON)

## Connection Details for Backend

After creating the database, you'll use these connection details in your backend:

```javascript
const dbConfig = {
    host: 'localhost',
    user: 'root',        // or your MySQL username
    password: '',        // or your MySQL password
    database: 'siyoga_travel_booking'
};
```

## Next Steps

1. ✅ Create database and tables in phpMyAdmin
2. ⏳ Setup backend server with database connection
3. ⏳ Implement user registration API
4. ⏳ Implement user login API
5. ⏳ Create frontend registration form
6. ⏳ Create frontend login form

## Sample Data

The schema includes a sample admin user:
- Email: <EMAIL>
- Password: admin123 (will be properly hashed in backend)
- Role: admin
