const mysql = require('mysql2/promise');

async function fixBookingsTable() {
  try {
    console.log('🔧 Fixing Bookings Table Structure...\n');

    const dbConfig = {
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'siyoga_travel_booking',
      port: 3306
    };

    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');

    // Drop the existing bookings table
    await connection.execute('DROP TABLE IF EXISTS bookings');
    console.log('✅ Dropped existing bookings table');

    // Create new bookings table with correct structure
    const createBookingsTable = `
      CREATE TABLE bookings (
        booking_id INT AUTO_INCREMENT PRIMARY KEY,
        tourist_id INT NOT NULL,
        pickup_location VARCHAR(255) NOT NULL,
        destinations TEXT NOT NULL,
        trip_type ENUM('one-way', 'round-trip') NOT NULL,
        start_date DATE NOT NULL,
        start_time TIME NOT NULL,
        travelers_count INT NOT NULL,
        selected_category_id INT NOT NULL,
        total_distance_km DECIMAL(8,2) NOT NULL,
        calculated_distance_km DECIMAL(8,2) NOT NULL,
        trip_cost DECIMAL(10,2) NOT NULL,
        accommodation_cost DECIMAL(10,2) DEFAULT 0,
        total_cost DECIMAL(10,2) NOT NULL,
        driver_accommodation_provided BOOLEAN DEFAULT FALSE,
        trip_duration_days INT DEFAULT 1,
        special_requirements TEXT NULL,
        status ENUM('pending', 'confirmed', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
        driver_id INT NULL,
        vehicle_id INT NULL,
        payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_bookings_tourist (tourist_id),
        INDEX idx_bookings_status (status),
        INDEX idx_bookings_date (start_date),
        INDEX idx_bookings_category (selected_category_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    await connection.execute(createBookingsTable);
    console.log('✅ Created new bookings table with correct structure');

    // Verify the new structure
    console.log('\n📋 New Bookings table structure:');
    const [columns] = await connection.execute('DESCRIBE bookings');
    columns.forEach(col => {
      console.log(`  ${col.Field} (${col.Type}) - ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Key ? `[${col.Key}]` : ''}`);
    });

    await connection.end();
    console.log('\n✅ Bookings table fix completed successfully!');

  } catch (error) {
    console.error('❌ Bookings table fix failed:', error.message);
    process.exit(1);
  }
}

fixBookingsTable();
