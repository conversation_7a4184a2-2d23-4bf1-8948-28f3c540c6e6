// Debug login issue
const { getOne } = require('./src/config/database');
const { comparePassword } = require('./src/utils/helpers');

async function debugLogin() {
    try {
        console.log('🔍 Debugging login issue...');
        
        // Get user from database
        const user = await getOne(
            'SELECT user_id, email, password, role, is_verified, is_active FROM users WHERE email = ?',
            ['<EMAIL>']
        );
        
        console.log('User from database:', {
            user_id: user?.user_id,
            email: user?.email,
            role: user?.role,
            is_verified: user?.is_verified,
            is_active: user?.is_active,
            password_length: user?.password?.length
        });
        
        if (user) {
            // Test password comparison
            const testPassword = 'NewUser123456';
            console.log('Testing password:', testPassword);
            console.log('Stored password hash:', user.password);

            const isValid = await comparePassword(testPassword, user.password);
            console.log('Password comparison result:', isValid);

            // Let's also test with bcrypt directly
            const bcrypt = require('bcryptjs');
            const directCompare = await bcrypt.compare(testPassword, user.password);
            console.log('Direct bcrypt comparison:', directCompare);
        }
        
    } catch (error) {
        console.error('Debug error:', error);
    }
    
    process.exit(0);
}

debugLogin();
