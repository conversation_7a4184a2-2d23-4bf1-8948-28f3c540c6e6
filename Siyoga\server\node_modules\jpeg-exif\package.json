{"name": "jpeg-exif", "version": "1.1.4", "description": "Use for parse .jpg file exif info (including GPS info.).", "main": "lib/index.js", "scripts": {"test": "./node_modules/.bin/istanbul cover ./node_modules/mocha/bin/_mocha -report lcovonly -- -R spec && cat ./coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js", "lint": "./node_modules/.bin/eslint ./src/index.js ./test/index.test.js --fix", "build": "babel ./src -d lib --source-maps && cp ./src/*.json ./lib"}, "keywords": ["jpeg", "exif", "gps", "image", "picture", "photo", "jfif"], "homepage": "https://github.com/zhso/jpeg-exif", "repository": {"type": "git", "url": "https://github.com/zhso/jpeg-exif.git"}, "bugs": {"url": "https://github.com/zhso/jpeg-exif/issues", "email": "<EMAIL>"}, "author": "<EMAIL>", "license": "MIT", "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "chai": "^4.2.0", "coveralls": "^3.0.6", "eslint": "^6.2.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-plugin-import": "^2.18.2", "istanbul": "^0.4.5", "mocha": "^6.2.0"}}