{"mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AEAA,oBAAoB;AACpB,MAAM,yCAAmB;IACvB,SAAS;IACT,MAAM;IACN,SAAS;AACX;AAEO,MAAM;IACX,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG,IAAI,SAAS,OAAO,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU;QAC5E,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;IAClC;IAEA,WAAW,MAAM,EAAE,WAAW,OAAO,EAAE;QACrC,WAAW,sCAAgB,CAAC,SAAS,IAAI;QAEzC,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC;QAC1B,IAAI;YACF,IAAI,UAAU,IAAI,YAAY;YAC9B,OAAO,QAAQ,MAAM,CAAC;QACxB,EAAE,OAAO,KAAK;YACZ,OAAO;QACT;IACF;IAEA,WAAW,MAAM,EAAE;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAG,IAAI,CAAC,GAAG,IAAI;IAClD;IAEA,eAAe;QACb,OAAO,AAAC,CAAA,IAAI,CAAC,YAAY,MAAM,CAAA,IAAK,IAAI,CAAC,SAAS;IACpD;IAEA,eAAe;QACb,OAAO,IAAI,CAAC,YAAY,KAAM,CAAA,IAAI,CAAC,SAAS,MAAM,EAAC;IACrD;IAEA,cAAc;QACZ,OAAO,AAAC,CAAA,IAAI,CAAC,WAAW,MAAM,CAAA,IAAK,IAAI,CAAC,SAAS;IACnD;IAEA,cAAc;QACZ,OAAO,IAAI,CAAC,YAAY,KAAM,CAAA,IAAI,CAAC,QAAQ,MAAM,EAAC;IACpD;AACF;AAEA,0CAAa,KAAK,GAAG;IACnB,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;AACV;AAEA,KAAK,IAAI,OAAO,OAAO,mBAAmB,CAAC,SAAS,SAAS,EAC3D,IAAI,IAAI,KAAK,CAAC,GAAG,OAAO,OAAO;IAC7B,IAAI,OAAO,IAAI,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IACtC,IAAI,SAAS,WACX,OAAO;SACF,IAAI,SAAS,WAClB,OAAO;IAET,IAAI,QAAQ,0CAAa,KAAK,CAAC,KAAK;IACpC,0CAAa,SAAS,CAAC,SAAS,OAAQ,CAAA,UAAU,IAAI,KAAK,IAAG,EAAG,GAAG;QAClE,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QACrC,IAAI,CAAC,GAAG,IAAI;QACZ,OAAO;IACT;IAEA,IAAI,UAAU,GACZ,0CAAa,SAAS,CAAC,SAAS,OAAO,KAAK,GAAG;QAC7C,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QACrC,IAAI,CAAC,GAAG,IAAI;QACZ,OAAO;IACT;AAEJ;;;ADjFF,MAAM,oCAAc,IAAI;AACxB,MAAM,oCAAc,IAAI,WAAW,IAAI,YAAY;IAAC;CAAO,EAAE,MAAM,CAAC,CAAC,EAAE,IAAI;AAEpE,MAAM;IACX,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;QAC3F,IAAI,CAAC,GAAG,GAAG;IACb;IAEA,YAAY,MAAM,EAAE;QAClB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,GAAG;QAChC,IAAI,CAAC,GAAG,IAAI,OAAO,MAAM;IAC3B;IAEA,YAAY,MAAM,EAAE,WAAW,OAAO,EAAE;QACtC,IAAI;QACJ,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,oCAAc,QAAQ;gBAC5B;YAEF,KAAK;YACL,KAAK;gBACH,MAAM,oCAAc,QAAQ,CAAC;gBAC7B;YAEF,KAAK;gBACH,MAAM,kCAAY,MAAM,CAAC;gBACzB;YAEF,KAAK;gBACH,MAAM,oCAAc;gBACpB;YAEF;gBACE,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,CAAC;QACvD;QAEA,IAAI,CAAC,WAAW,CAAC;IACnB;IAEA,cAAc,GAAG,EAAE;QACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,AAAC,QAAQ,KAAM;QACzC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,AAAC,QAAQ,IAAK;QACxC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,MAAM;IAClC;IAEA,cAAc,GAAG,EAAE;QACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,MAAM;QAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,AAAC,QAAQ,IAAK;QACxC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,AAAC,QAAQ,KAAM;IAC3C;IAEA,aAAa,GAAG,EAAE;QAChB,IAAI,OAAO,GACT,IAAI,CAAC,aAAa,CAAC;aAEnB,IAAI,CAAC,aAAa,CAAC,MAAM,WAAW;IAExC;IAEA,aAAa,GAAG,EAAE;QAChB,IAAI,OAAO,GACT,IAAI,CAAC,aAAa,CAAC;aAEnB,IAAI,CAAC,aAAa,CAAC,MAAM,WAAW;IAExC;IAEA,KAAK,GAAG,EAAE,MAAM,EAAE;QAChB,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG;YAC3C,IAAI,CAAC,GAAG,IAAI;QACd,OAAO;YACL,MAAM,MAAM,IAAI,WAAW;YAC3B,IAAI,IAAI,CAAC;YACT,IAAI,CAAC,WAAW,CAAC;QACnB;IACF;AACF;AAEA,SAAS,oCAAc,MAAM,EAAE,IAAI;IACjC,IAAI,MAAM,IAAI,YAAY,OAAO,MAAM;IACvC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,IAAI,OAAO,OAAO,UAAU,CAAC;QAC7B,IAAI,MACF,OAAO,AAAC,QAAQ,IAAM,AAAC,CAAA,OAAO,IAAG,KAAM;QAEzC,GAAG,CAAC,EAAE,GAAG;IACX;IACA,OAAO,IAAI,WAAW,IAAI,MAAM;AAClC;AAEA,SAAS,oCAAc,MAAM;IAC3B,IAAI,MAAM,IAAI,WAAW,OAAO,MAAM;IACtC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IACjC,oEAAoE;IACpE,GAAG,CAAC,EAAE,GAAG,OAAO,UAAU,CAAC;IAE7B,OAAO;AACT;AAEA,KAAK,IAAI,OAAO,OAAO,mBAAmB,CAAC,SAAS,SAAS,EAC3D,IAAI,IAAI,KAAK,CAAC,GAAG,OAAO,OAAO;IAC7B,IAAI,OAAO,IAAI,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IACtC,IAAI,SAAS,WACX,OAAO;SACF,IAAI,SAAS,WAClB,OAAO;IAET,IAAI,QAAQ,CAAA,GAAA,yCAAW,EAAE,KAAK,CAAC,KAAK;IACpC,0CAAa,SAAS,CAAC,UAAU,OAAQ,CAAA,UAAU,IAAI,KAAK,IAAG,EAAG,GAAG,SAAU,KAAK;QAClF,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO;QAChC,IAAI,CAAC,GAAG,IAAI;IACd;IAEA,IAAI,UAAU,GACZ,0CAAa,SAAS,CAAC,UAAU,OAAO,KAAK,GAAG,SAAU,KAAK;QAC7D,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO;QAChC,IAAI,CAAC,GAAG,IAAI;IACd;AAEJ;;;;;;AG5HK,MAAM;IACX,WAAW,MAAM,EAAE;QACjB,IAAI,SAAS,IAAI,CAAA,GAAA,yCAAW,EAAE;QAC9B,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA,SAAS,KAAK,EAAE;QACd,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;QACrB,IAAI,SAAS,IAAI,WAAW;QAC5B,IAAI,SAAS,IAAI,CAAA,GAAA,yCAAW,EAAE;QAC9B,IAAI,CAAC,MAAM,CAAC,QAAQ;QACpB,OAAO;IACT;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACbA,MAAM,kDAAgB,CAAA,GAAA,yCAAG;IACvB,YAAY,IAAI,EAAE,SAAS,IAAI,CAAE;QAC/B,KAAK;QACL,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI;QACnB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,KAAK,KACtC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,MAAM;IAE1B;IAEA,OAAO;QACL,OAAO,CAAA,GAAA,yCAAW,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;IACtC;IAEA,OAAO,MAAM,EAAE;QACb,OAAO,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IACjC;IAEA,OAAO,MAAM,EAAE,GAAG,EAAE;QAClB,OAAO,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnC;AACF;AAIO,MAAM,4CAAQ,IAAI,0CAAQ;AAC1B,MAAM,4CAAW,IAAI,0CAAQ,UAAU;AACvC,MAAM,4CAAS;AACf,MAAM,4CAAW,IAAI,0CAAQ,UAAU;AACvC,MAAM,4CAAW,IAAI,0CAAQ,UAAU;AACvC,MAAM,4CAAS;AACf,MAAM,4CAAW,IAAI,0CAAQ,UAAU;AACvC,MAAM,4CAAW,IAAI,0CAAQ,UAAU;AACvC,MAAM,4CAAS;AACf,MAAM,4CAAW,IAAI,0CAAQ,UAAU;AACvC,MAAM,4CAAO,IAAI,0CAAQ;AACzB,MAAM,4CAAU,IAAI,0CAAQ,SAAS;AACrC,MAAM,4CAAQ;AACd,MAAM,4CAAU,IAAI,0CAAQ,SAAS;AACrC,MAAM,4CAAU,IAAI,0CAAQ,SAAS;AACrC,MAAM,4CAAQ;AACd,MAAM,2CAAU,IAAI,0CAAQ,SAAS;AACrC,MAAM,4CAAU,IAAI,0CAAQ,SAAS;AACrC,MAAM,2CAAQ;AACd,MAAM,4CAAU,IAAI,0CAAQ,SAAS;AACrC,MAAM,4CAAU,IAAI,0CAAQ,SAAS;AACrC,MAAM,4CAAQ;AACd,MAAM,4CAAU,IAAI,0CAAQ,SAAS;AACrC,MAAM,4CAAW,IAAI,0CAAQ,UAAU;AACvC,MAAM,4CAAS;AACf,MAAM,4CAAW,IAAI,0CAAQ,UAAU;AAEvC,MAAM,kDAAc;IACzB,YAAY,IAAI,EAAE,MAAM,EAAE,WAAW,QAAQ,CAAC,CAAE;QAC9C,KAAK,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK;IACrB;IAEA,OAAO,MAAM,EAAE;QACb,OAAO,KAAK,CAAC,OAAO,UAAU,IAAI,CAAC,MAAM;IAC3C;IAEA,OAAO,MAAM,EAAE,GAAG,EAAE;QAClB,OAAO,KAAK,CAAC,OAAO,QAAQ,AAAC,MAAM,IAAI,CAAC,MAAM,GAAI;IACpD;AACF;AAEO,MAAM,4CAAY,IAAI,0CAAM,IAAI;AAChC,MAAM,4CAAU;AAChB,MAAM,4CAAY,IAAI,0CAAM,IAAI;AAChC,MAAM,4CAAY,IAAI,0CAAM,IAAI;AAChC,MAAM,4CAAU;AAChB,MAAM,4CAAY,IAAI,0CAAM,IAAI;;;;;;;;AC1EhC,SAAS,0CAAc,MAAM,EAAE,MAAM,EAAE,MAAM;IAClD,IAAI;IACJ,IAAI,OAAO,WAAW,UACpB,MAAM;SAED,IAAI,OAAO,WAAW,YAC3B,MAAM,OAAO,IAAI,CAAC,QAAQ;SAErB,IAAI,UAAW,OAAO,WAAW,UACtC,MAAM,MAAM,CAAC,OAAO;SAEf,IAAI,UAAU,kBAAkB,CAAA,GAAA,yCAAM,GAC3C,MAAM,OAAO,MAAM,CAAC;IAGtB,IAAI,MAAM,MACR,MAAM,IAAI,MAAM;IAGlB,OAAO;AACT;AAEO,MAAM;IACX,YAAY,OAAO,CAAC,CAAC,CAAE;QACrB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,YAAY,GAAG;QAEpB,IAAK,IAAI,OAAO,KAAM;YACpB,MAAM,MAAM,IAAI,CAAC,IAAI;YACrB,IAAI,CAAC,IAAI,GAAG;QACd;IACF;AACF;;;AH9BA,MAAM,kDAAe,CAAA,GAAA,yCAAG;IACtB,YAAY,IAAI,EAAE,MAAM,EAAE,aAAa,OAAO,CAAE;QAC9C,KAAK;QACL,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,UAAU,GAAG;IACpB;IAEA,OAAO,MAAM,EAAE,MAAM,EAAE;QACrB,IAAI;QACJ,MAAM,OAAE,GAAG,EAAE,GAAG;QAEhB,MAAM,MAAM,EAAE;QACd,IAAI,MAAM;QAEV,IAAI,IAAI,CAAC,MAAM,IAAI,MACjB,SAAS,0CAAoB,IAAI,CAAC,MAAM,EAAE,QAAQ;QAGpD,IAAI,IAAI,CAAC,MAAM,YAAY,CAAA,GAAA,yCAAM,GAAG;YAClC,2BAA2B;YAC3B,OAAO,gBAAgB,CAAC,KAAK;gBAC3B,QAAgB;oBAAE,OAAO;gBAAO;gBAChC,cAAgB;oBAAE,OAAO;gBAAI;gBAC7B,gBAAgB;oBAAE,OAAO;oBAAG,UAAU;gBAAK;gBAC3C,SAAgB;oBAAE,OAAO;gBAAO;YAClC;YAEA,MAAM;QACR;QAEA,IAAI,AAAC,UAAU,QAAU,IAAI,CAAC,UAAU,KAAK,SAAU;YACrD,MAAM,SAAS,AAAC,UAAU,OACxB,OAAO,GAAG,GAAG,SACb,AAAC,CAAA,UAAU,OAAO,OAAO,OAAO,GAAG,SAAQ,IAC3C,OAAO,YAAY,GAAG,OAAO,OAAO,GAEpC,OAAO,MAAM;YAEf,MAAO,OAAO,GAAG,GAAG,OAClB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;QAGtC,OACE,IAAK,IAAI,IAAI,GAAG,MAAM,QAAQ,IAAI,KAAK,IACrC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;QAItC,OAAO;IACT;IAEA,KAAK,KAAK,EAAE,GAAG,EAAE,kBAAkB,IAAI,EAAE;QACvC,IAAI,CAAC,OACH,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,OAAO,0CAAoB,IAAI,CAAC,MAAM,EAAE,MAAM;QAG5E,IAAI,OAAO;QACX,IAAI,IAAI,CAAC,MAAM,YAAY,CAAA,GAAA,yCAAM,GAAG;YAClC,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI;YACxB,MAAM;gBAAC,QAAQ;gBAAK,aAAa;YAAC;QACpC;QAEA,KAAK,IAAI,QAAQ,MACf,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;QAG/B,IAAI,OAAO,mBAAmB,IAAI,CAAC,MAAM,YAAY,CAAA,GAAA,yCAAM,GACzD,QAAQ,IAAI,WAAW;QAGzB,OAAO;IACT;IAEA,OAAO,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;QAC5B,IAAI,MAAM;QACV,IAAI,IAAI,CAAC,MAAM,YAAY,CAAA,GAAA,yCAAM,GAAG;YAClC,MAAM;gBACJ,UAAU,EAAE;gBACZ,aAAa,OAAO,GAAG;wBACvB;YACF;YAEA,IAAI,aAAa,GAAG,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK;YACvD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,MAAM,MAAM;QACzC;QAEA,KAAK,IAAI,QAAQ,MACf,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,MAAM;QAGjC,IAAI,IAAI,CAAC,MAAM,YAAY,CAAA,GAAA,yCAAM,GAAG;YAClC,IAAI,IAAI;YACR,MAAO,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAE;gBAC9B,MAAM,MAAM,IAAI,QAAQ,CAAC,IAAI;gBAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,GAAG,EAAE,IAAI,MAAM;YAC7C;QACF;IACF;AACF;;;;;;AInGO,MAAM,kDAAkB,CAAA,GAAA,yCAAK;IAClC,OAAO,MAAM,EAAE,MAAM,EAAE;QACrB,MAAM,OAAE,GAAG,EAAE,GAAG;QAChB,MAAM,SAAS,0CAAoB,IAAI,CAAC,MAAM,EAAE,QAAQ;QAExD,IAAI,IAAI,CAAC,MAAM,YAAY,CAAA,GAAA,yCAAM,GAC/B,SAAS;oBACP;YACA,cAAc;YACd,gBAAgB;YAChB,SAAS;QACX;QAGF,MAAM,MAAM,IAAI,qCAAe,IAAI,CAAC,IAAI,EAAE,QAAQ,QAAQ;QAE1D,OAAO,GAAG,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;QAC5C,OAAO;IACT;IAEA,KAAK,GAAG,EAAE,GAAG,EAAE;QACb,IAAI,eAAe,sCACjB,MAAM,IAAI,OAAO;QAGnB,OAAO,KAAK,CAAC,KAAK,KAAK;IACzB;IAEA,OAAO,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;QACvB,IAAI,eAAe,sCACjB,MAAM,IAAI,OAAO;QAGnB,OAAO,KAAK,CAAC,OAAO,QAAQ,KAAK;IACnC;AACF;AAEA,MAAM;IACJ,YAAY,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAE;QACrC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG;QAC3B,IAAI,CAAC,KAAK,GAAG,EAAE;IACjB;IAEA,IAAI,KAAK,EAAE;QACT,IAAI,AAAC,QAAQ,KAAO,SAAS,IAAI,CAAC,MAAM,EACtC,OAAO;QAGT,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,MAAM;YAC7B,MAAM,OAAE,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM;YAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,GAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI;YAChE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG;YAC1D,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG;QACpB;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IAC1B;IAEA,UAAU;QACR,MAAM,SAAS,EAAE;QACjB,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,EAAE,IAAI,KAAK,IAC1C,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QAEvB,OAAO;IACT;AACF;;;;ACvEO,MAAM,kDAAiB,CAAA,GAAA,yCAAG;IAC/B,YAAY,IAAI,EAAE,QAAQ,EAAE,CAAE;QAC5B,KAAK;QACL,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;IACf;IAEA,OAAO,MAAM,EAAE;QACb,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QAE7B,MAAM,MAAM,CAAC;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;YAC1C,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;YAC1B,IAAI,QAAQ,MACV,GAAG,CAAC,KAAK,GAAG,CAAC,CAAE,CAAA,MAAO,KAAK,CAAC;QAEhC;QAEA,OAAO;IACT;IAEA,OAAO;QACL,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;IACvB;IAEA,OAAO,MAAM,EAAE,IAAI,EAAE;QACnB,IAAI,MAAM;QACV,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;YAC1C,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;YAC1B,IAAI,QAAQ,MACV;gBAAA,IAAI,IAAI,CAAC,KAAK,EAAI,OAAQ,KAAK;YAAI;QAEvC;QAEA,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;IAClC;AACF;;;;ACpCO,MAAM,kDAAiB,CAAA,GAAA,yCAAG;IAC/B,YAAY,IAAI,CAAE;QAChB,KAAK;QACL,IAAI,CAAC,IAAI,GAAG;IACd;IAEA,OAAO,MAAM,EAAE,MAAM,EAAE;QACrB,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;IACpC;IAEA,KAAK,GAAG,EAAE,MAAM,EAAE;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;IAC7B;IAEA,OAAO,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;QAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK;IACxC;AACF;;;;;;ACfO,MAAM,kDAAgB,CAAA,GAAA,yCAAG;IAC9B,YAAY,MAAM,CAAE;QAClB,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,OAAO,MAAM,EAAE,MAAM,EAAE;QACrB,MAAM,SAAS,0CAAoB,IAAI,CAAC,MAAM,EAAE,QAAQ;QACxD,OAAO,OAAO,UAAU,CAAC;IAC3B;IAEA,KAAK,GAAG,EAAE,MAAM,EAAE;QAChB,IAAI,CAAC,KACH,OAAO,0CAAoB,IAAI,CAAC,MAAM,EAAE,MAAM;QAGhD,IAAI,MAAM,IAAI,MAAM;QACpB,IAAI,IAAI,CAAC,MAAM,YAAY,CAAA,GAAA,yCAAM,GAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;QAGzB,OAAO;IACT;IAEA,OAAO,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;QAC1B,IAAI,IAAI,CAAC,MAAM,YAAY,CAAA,GAAA,yCAAM,GAC/B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM;QAGvC,OAAO,OAAO,WAAW,CAAC;IAC5B;AACF;;;;ACjCO,MAAM,kDAAa,CAAA,GAAA,yCAAG;IAC3B,YAAY,IAAI,EAAE,UAAU,EAAE,CAAE;QAC9B,KAAK;QACL,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,OAAO,MAAM,EAAE;QACb,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI;IAChC;IAEA,OAAO;QACL,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;IACvB;IAEA,OAAO,MAAM,EAAE,GAAG,EAAE;QAClB,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QACnC,IAAI,UAAU,IACZ,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,IAAI,CAAC;QAGlD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;IAClC;AACF;;;;ACxBO,MAAM,kDAAiB,CAAA,GAAA,yCAAG;IAC/B,YAAY,IAAI,EAAE,YAAY,IAAI,CAAE;QAClC,KAAK;QACL,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,SAAS,GAAG;IACnB;IAEA,OAAO,MAAM,EAAE,MAAM,EAAE;QACrB,IAAI,aAAE,SAAS,EAAE,GAAG,IAAI;QACxB,IAAI,OAAO,cAAc,YACvB,YAAY,UAAU,IAAI,CAAC,QAAQ;QAGrC,IAAI,WACF,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;IAEpC;IAEA,KAAK,GAAG,EAAE,MAAM,EAAE;QAChB,IAAI,aAAE,SAAS,EAAE,GAAG,IAAI;QACxB,IAAI,OAAO,cAAc,YACvB,YAAY,UAAU,IAAI,CAAC,QAAQ;QAGrC,IAAI,WACF,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;aAE3B,OAAO;IAEX;IAEA,OAAO,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;QAC1B,IAAI,aAAE,SAAS,EAAE,GAAG,IAAI;QACxB,IAAI,OAAO,cAAc,YACvB,YAAY,UAAU,IAAI,CAAC,QAAQ;QAGrC,IAAI,WACF,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK;IAEzC;AACF;;;;;ACxCO,MAAM,iDAAiB,CAAA,GAAA,yCAAG;IAC/B,YAAY,IAAI,EAAE,QAAQ,CAAC,CAAE;QAC3B,KAAK;QACL,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;IACf;IACA,OAAO,MAAM,EAAE,MAAM,EAAE;QACrB,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;QAC9B,OAAO;IACT;IAEA,KAAK,IAAI,EAAE,MAAM,EAAE;QACjB,MAAM,QAAQ,0CAAoB,IAAI,CAAC,KAAK,EAAE,MAAM;QACpD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK;IAC5B;IAEA,OAAO,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;QAC1B,OAAO,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;IACvC;AACF;;;;;;AClBA,MAAM,kDAAgB,CAAA,GAAA,yCAAG;IACvB,YAAY,MAAM,EAAE,WAAW,OAAO,CAAE;QACtC,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,OAAO,MAAM,EAAE,MAAM,EAAE;QACrB,IAAI,QAAQ;QAEZ,IAAI,YAAE,QAAQ,EAAE,GAAG,IAAI;QACvB,IAAI,OAAO,aAAa,YACtB,WAAW,SAAS,IAAI,CAAC,QAAQ,WAAW;QAE9C,IAAI,QAAQ,oCAAc;QAE1B,IAAI,IAAI,CAAC,MAAM,IAAI,MACjB,SAAS,0CAAoB,IAAI,CAAC,MAAM,EAAE,QAAQ;aAC7C;YACL,IAAI;YACH,CAAA,UAAC,MAAM,UAAE,MAAM,OAAE,GAAG,EAAC,GAAG,MAAK;YAE9B,MAAO,AAAC,MAAM,SAAS,QAAQ,KAC5B,CAAA,MAAM,CAAC,IAAI,KAAK,QAChB,UAAU,KAAK,MAAM,CAAC,MAAI,EAAE,KAAK,IAAI,EAEtC,OAAO;YAGT,SAAS,MAAM,OAAO,GAAG;QAC3B;QAGA,MAAM,SAAS,OAAO,UAAU,CAAC,QAAQ;QAEzC,IAAI,AAAC,IAAI,CAAC,MAAM,IAAI,QAAU,OAAO,GAAG,GAAG,OAAO,MAAM,EACtD,OAAO,GAAG,IAAE;QAGd,OAAO;IACT;IAEA,KAAK,GAAG,EAAE,MAAM,EAAE;QAChB,8CAA8C;QAC9C,IAAI,QAAQ,aAAa,QAAQ,MAC/B,OAAO,0CAAoB,IAAI,CAAC,MAAM,EAAE,MAAM;QAGhD,IAAI,YAAE,QAAQ,EAAE,GAAG,IAAI;QACvB,IAAI,OAAO,aAAa,YACtB,WAAW,SAAS,IAAI,CAAC,UAAU,OAAO,OAAO,GAAG,GAAG,WAAW,UAAU,OAAO,OAAO,GAAG,GAAG,cAAc;QAGhH,IAAI,aAAa,WACf,WAAW;QAGb,IAAI,OAAO,iCAAW,KAAK;QAC3B,IAAI,IAAI,CAAC,MAAM,YAAY,CAAA,GAAA,yCAAM,GAC/B,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI;QAG1B,IAAK,IAAI,CAAC,MAAM,IAAI,MAClB,QAAQ,oCAAc;QAGxB,OAAO;IACT;IAEA,OAAO,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;QAC1B,IAAI,YAAE,QAAQ,EAAE,GAAG,IAAI;QACvB,IAAI,OAAO,aAAa,YACtB,WAAW,SAAS,IAAI,CAAC,UAAU,OAAO,OAAO,GAAG,GAAG,WAAW,UAAU,OAAO,OAAO,GAAG,GAAG,cAAc;QAGhH,IAAI,IAAI,CAAC,MAAM,YAAY,CAAA,GAAA,yCAAM,GAC/B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,iCAAW,KAAK;QAG7C,OAAO,WAAW,CAAC,KAAK;QAExB,IAAK,IAAI,CAAC,MAAM,IAAI,MAClB,OAAO,oCAAc,aAAa,IAChC,OAAO,aAAa,CAAC,UACrB,OAAO,UAAU,CAAC;IAExB;AACF;AAEA,SAAS,oCAAc,QAAQ;IAC7B,OAAO;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,+CAA+C;YAC/C,kDAAkD;YAClD,OAAO;IACX;AACF;AAEA,SAAS,iCAAW,MAAM,EAAE,QAAQ;IAClC,OAAQ;QACN,KAAK;YACH,OAAO,OAAO,MAAM;QACtB,KAAK;YACH,IAAI,MAAM;YACV,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,IAAI,IAAI,OAAO,UAAU,CAAC;gBAE1B,IAAI,KAAK,UAAU,KAAK,UAAU,IAAI,OAAO,MAAM,GAAG,GAAG;oBACvD,IAAI,KAAK,OAAO,UAAU,CAAC,EAAE;oBAC7B,IAAI,AAAC,CAAA,KAAK,MAAK,MAAO,QACpB,IAAI,AAAC,CAAA,AAAC,CAAA,IAAI,KAAI,KAAM,EAAC,IAAM,CAAA,KAAK,KAAI,IAAK;yBAEzC,uBAAuB;oBACvB;gBAEJ;gBAEA,IAAI,AAAC,CAAA,IAAI,UAAS,MAAO,GACvB;qBACK,IAAI,AAAC,CAAA,IAAI,UAAS,MAAO,GAC9B,OAAO;qBACF,IAAI,AAAC,CAAA,IAAI,UAAS,MAAO,GAC9B,OAAO;qBACF,IAAI,AAAC,CAAA,IAAI,UAAS,MAAO,GAC9B,OAAO;YAEX;YACA,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,OAAO,MAAM,GAAG;QACzB;YACE,MAAM,IAAI,MAAM,sBAAsB;IAC1C;AACF;;;;;ACrJO,MAAM,kDAAe,CAAA,GAAA,yCAAG;IAC7B,YAAY,SAAS,CAAC,CAAC,CAAE;QACvB,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,OAAO,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE;QACjC,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,QAAQ;QACxC,IAAI,CAAC,YAAY,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM;QAE1C,IAAI,IAAI,CAAC,OAAO,IAAI,MAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK;QAEzB,OAAO;IACT;IAEA,OAAO,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;QAC7B,MAAM,MAAM,CAAC;QAEb,2BAA2B;QAC3B,OAAO,gBAAgB,CAAC,KAAK;YAC3B,QAAgB;gBAAE,OAAO;YAAO;YAChC,cAAgB;gBAAE,OAAO,OAAO,GAAG;YAAC;YACpC,gBAAgB;gBAAE,OAAO;gBAAG,UAAU;YAAK;YAC3C,SAAgB;gBAAE,OAAO;YAAO;QAClC;QAEA,OAAO;IACT;IAEA,aAAa,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;QAChC,IAAK,IAAI,OAAO,OAAQ;YACtB,IAAI;YACJ,MAAM,OAAO,MAAM,CAAC,IAAI;YACxB,IAAI,OAAO,SAAS,YAClB,MAAM,KAAK,IAAI,CAAC,KAAK;iBAErB,MAAM,KAAK,MAAM,CAAC,QAAQ;YAG5B,IAAI,QAAQ;gBACV,IAAI,eAAe,2CACjB,OAAO,cAAc,CAAC,KAAK,KAAK;qBAEhC,GAAG,CAAC,IAAI,GAAG;;YAIf,IAAI,cAAc,GAAG,OAAO,GAAG,GAAG,IAAI,YAAY;QACpD;IAEF;IAEA,KAAK,GAAG,EAAE,MAAM,EAAE,kBAAkB,IAAI,EAAE;QACxC,IAAI,OAAO,MAAQ,MAAM,CAAC;QAC1B,MAAM,MAAM;oBACV;iBACA;YACA,aAAa;QACf;QAEA,IAAI,IAAI,CAAC,SAAS,IAAI,MACpB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAGtB,IAAI,OAAO;QACX,IAAK,IAAI,OAAO,IAAI,CAAC,MAAM,CAAE;YAC3B,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;YAC7B,IAAI,KAAK,IAAI,IAAI,MACf,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;QAEhC;QAEA,IAAI,iBACF,QAAQ,IAAI,WAAW;QAGzB,OAAO;IACT;IAEA,OAAO,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;QAC1B,IAAI;QACJ,IAAI,IAAI,CAAC,SAAS,IAAI,MACpB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK;QAG3B,MAAM,MAAM;YACV,UAAU,EAAE;YACZ,aAAa,OAAO,GAAG;oBACvB;iBACA;YACA,aAAa;QACf;QAEA,IAAI,aAAa,GAAG,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK;QAErD,IAAK,IAAI,OAAO,IAAI,CAAC,MAAM,CAAE;YAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;YACvB,IAAI,KAAK,MAAM,IAAI,MACjB,KAAK,MAAM,CAAC,QAAQ,GAAG,CAAC,IAAI,EAAE;QAElC;QAEA,IAAI,IAAI;QACR,MAAO,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAE;YAC9B,MAAM,MAAM,IAAI,QAAQ,CAAC,IAAI;YAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,GAAG,EAAE,IAAI,MAAM;QAC7C;IACF;AACF;;;;AC9GA,MAAM,gCAAU,CAAC,QAAQ;IACvB,OAAO,UAAU,MAAM,CAAC,CAAC,SAAS,MAAQ,WAAW,OAAO,CAAC,IAAI,EAAE;AACrE;AAEO,MAAM,kDAAwB,CAAA,GAAA,yCAAK;IACxC,YAAY,IAAI,EAAE,WAAW,CAAC,CAAC,CAAE;QAC/B,KAAK;QACL,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,OAAO,SAAS,UAClB,IAAI,CAAC,WAAW,GAAG,KAAK,KAAK,CAAC;IAElC;IAEA,OAAO,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE;QACjC,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,QAAQ;QAExC,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UACvB,IAAI,OAAO,GAAG,8BAAQ,QAAQ,IAAI,CAAC,WAAW;aAE9C,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QAGjC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM;QAGrD,MAAM,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC;QACzC,IAAK,UAAU,MACb,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,IAAI,OAAO,CAAC,CAAC;QAGlD,IAAI,kBAAkB,2CACpB,OAAO,OAAO,MAAM,CAAC,QAAQ;QAG/B,IAAI,CAAC,YAAY,CAAC,QAAQ,KAAK;QAE/B,IAAI,IAAI,CAAC,OAAO,IAAI,MAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK;QAEzB,OAAO;IACT;IAEA,KAAK,GAAG,EAAE,MAAM,EAAE,kBAAkB,IAAI,EAAE;QACxC,IAAI,KAAK;QACT,IAAI,CAAC,KACH,MAAM,IAAI,MAAM;QAGlB,IAAI,IAAI,CAAC,SAAS,IAAI,MACpB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAGtB,MAAM,MAAM;oBACV;iBACA;YACA,aAAa;QACf;QAEA,IAAI,OAAO;QACX,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UACvB,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,EAAE;QAGtC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EACtB,IAAK,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAE;YAChC,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI;YAChC,IAAI,KAAK,IAAI,IAAI,MACf,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;QAEhC;QAGF,MAAM,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC;QACzC,IAAK,UAAU,MACb,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,IAAI,OAAO,CAAC,CAAC;QAGlD,IAAK,OAAO,OAAQ;YAClB,OAAO,MAAM,CAAC,IAAI;YAClB,IAAI,KAAK,IAAI,IAAI,MACf,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;QAEhC;QAEA,IAAI,iBACF,QAAQ,IAAI,WAAW;QAGzB,OAAO;IACT;IAEA,OAAO,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;QAC1B,IAAI,KAAK;QACT,IAAI,IAAI,CAAC,SAAS,IAAI,MACpB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK;QAG3B,MAAM,MAAM;YACV,UAAU,EAAE;YACZ,aAAa,OAAO,GAAG;oBACvB;iBACA;YACA,aAAa;QACf;QAEA,IAAI,aAAa,GAAG,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK;QAErD,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,OAAO;QAGtC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EACtB,IAAK,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAE;YAChC,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI;YAChC,IAAI,KAAK,MAAM,IAAI,MACjB,KAAK,MAAM,CAAC,QAAQ,GAAG,CAAC,IAAI,EAAE;QAElC;QAGF,MAAM,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC;QACzC,IAAK,OAAO,OAAQ;YAClB,OAAO,MAAM,CAAC,IAAI;YAClB,IAAI,KAAK,MAAM,IAAI,MACjB,KAAK,MAAM,CAAC,QAAQ,GAAG,CAAC,IAAI,EAAE;QAElC;QAEA,IAAI,IAAI;QACR,MAAO,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAE;YAC9B,MAAM,MAAM,IAAI,QAAQ,CAAC,IAAI;YAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,GAAG,EAAE,IAAI,MAAM;QAC7C;IACF;AACF;;;;;;;;;;;ACvIO,MAAM,kDAAgB,CAAA,GAAA,yCAAG;IAC9B,YAAY,UAAU,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAE;QAC1C,KAAK;QACL,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,IAAI,CAAC,IAAI,KAAK,QAAU,IAAI,CAAC,IAAI,GAAG;QACxC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,MAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG;QACrD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,MAAQ,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG;QAC/D,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,MAAQ,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG;QAC/D,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,MAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG;QACrD,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;YAC3B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,YACrC,MAAM,IAAI,MAAM;YAElB,IAAI,CAAC,gBAAgB,GAAG,QAAQ,UAAU;QAC5C;IACF;IAEA,OAAO,MAAM,EAAE,GAAG,EAAE;QAClB,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ;QAE9C,uBAAuB;QACvB,IAAI,AAAC,WAAW,IAAI,CAAC,OAAO,CAAC,SAAS,IAAK,IAAI,CAAC,OAAO,CAAC,SAAS,EAC/D,OAAO;QAGT,IAAI;QACJ,OAAQ,IAAI,CAAC,OAAO,CAAC,IAAI;YACvB,KAAK;gBAAa,WAAW,IAAI,YAAY;gBAAE;YAC/C,KAAK;gBAAa,WAAW,OAAO,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI;gBAAI;YAClE,KAAK;gBAAa,WAAW,IAAI,MAAM,CAAC,YAAY;gBAAE;YACtD;gBACE,IAAI,IAAI;gBACR,MAAO,EAAE,MAAM,CACb,IAAI,EAAE,MAAM;gBAGd,WAAW,EAAE,YAAY,IAAI;QACjC;QAEA,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EACzB,YAAY,IAAI,CAAC,gBAAgB,CAAC;QAGpC,MAAM,MAAM,SAAS;QAErB,IAAI,IAAI,CAAC,IAAI,IAAI,MAAM;YACrB,IAAI,MAAM;YACV,MAAM,cAAc;gBAClB,IAAI,OAAO,MAAQ,OAAO;gBAE1B,MAAM,OAAE,GAAG,EAAE,GAAG;gBAChB,OAAO,GAAG,GAAG;gBACb,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC/B,OAAO,GAAG,GAAG;gBACb,OAAO;YACT;YAEA,yEAAyE;YACzE,uEAAuE;YACvE,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EACnB,OAAO,IAAI,0CAAyB;gBAClC,KAAK;YAAW;YAGpB,OAAO;QACT,OACE,OAAO;IAEX;IAEA,KAAK,GAAG,EAAE,GAAG,EAAE;QACb,MAAM,SAAS;QACf,OAAQ,IAAI,CAAC,OAAO,CAAC,IAAI;YACvB,KAAK;YAAS,KAAK;gBACjB;YACF,KAAK;gBACH,MAAM,IAAI,MAAM;gBAChB;YACF;gBACE,MAAO,IAAI,MAAM,CACf,MAAM,IAAI,MAAM;QAEtB;QAEA,IAAI,QAAE,IAAI,EAAE,GAAG,IAAI;QACnB,IAAI,QAAQ,MAAM;YAChB,IAAI,CAAE,CAAA,eAAe,yCAAU,GAC7B,MAAM,IAAI,MAAM;YAGjB,CAAA,QAAE,IAAI,EAAE,GAAG,GAAE;YACd,MAAM,IAAI,KAAK;QACjB;QAEA,IAAI,OAAO,KAAK;YACd,oGAAoG;YACpG,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK;YAC1B,IAAI,WAAW,IAAI;QACrB;QAEA,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI;IAC7B;IAEA,OAAO,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;QACvB,IAAI;QACJ,MAAM,SAAS;QACf,IAAK,OAAO,MAAO;YACjB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,SAAS;YACrD;QACF;QAEA,OAAQ,IAAI,CAAC,OAAO,CAAC,IAAI;YACvB,KAAK;gBACH,WAAW,IAAI,WAAW;gBAC1B;YACF,KAAK;gBACH,WAAW,OAAO,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK;gBAClD;YACF,KAAK;gBACH,MAAM,IAAI,MAAM;gBAChB,WAAW,IAAI,WAAW;gBAC1B;YACF;gBACE,WAAW;gBACX,MAAO,IAAI,MAAM,CACf,MAAM,IAAI,MAAM;QAEtB;QAEA,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EACzB,YAAY,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG;QAG9C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,IAAI,aAAa,GAAG;QAEnD,IAAI,QAAE,IAAI,EAAE,GAAG,IAAI;QACnB,IAAI,QAAQ,MAAM;YAChB,IAAI,CAAE,CAAA,eAAe,yCAAU,GAC7B,MAAM,IAAI,MAAM;YAGjB,CAAA,QAAE,IAAI,EAAE,GAAG,GAAE;YACd,MAAM,IAAI,KAAK;QACjB;QAEA,IAAI,QAAQ,CAAC,IAAI,CAAC;kBAChB;iBACA;oBACA;QACF;QAEA,OAAO,IAAI,aAAa,IAAI,KAAK,IAAI,CAAC,KAAK;IAC7C;AACF;AAGO,MAAM;IACX,YAAY,IAAI,EAAE,KAAK,CAAE;QACvB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;IACf;AACF;;", "sources": ["index.js", "src/EncodeStream.js", "src/DecodeStream.js", "src/Array.js", "src/Base.js", "src/Number.js", "src/utils.js", "src/LazyArray.js", "src/Bitfield.js", "src/Boolean.js", "src/Buffer.js", "src/Enum.js", "src/Optional.js", "src/Reserved.js", "src/String.js", "src/Struct.js", "src/VersionedStruct.js", "src/Pointer.js"], "sourcesContent": ["export {EncodeStream} from './src/EncodeStream.js';\nexport {DecodeStream} from './src/DecodeStream.js';\nexport {Array} from './src/Array.js';\nexport {LazyArray} from './src/LazyArray.js';\nexport {Bitfield} from './src/Bitfield.js';\nexport {Boolean} from './src/Boolean.js';\nexport {Buffer} from './src/Buffer.js';\nexport {Enum} from './src/Enum.js';\nexport {Optional} from './src/Optional.js';\nexport {Reserved} from './src/Reserved.js';\nexport {String} from './src/String.js';\nexport {Struct} from './src/Struct.js';\nexport {VersionedStruct} from './src/VersionedStruct.js';\n\nexport * from './src/utils.js';\nexport * from './src/Number.js';\nexport * from './src/Pointer.js';\n", "import {DecodeStream} from './DecodeStream.js';\n\nconst textEncoder = new TextEncoder();\nconst isBigEndian = new Uint8Array(new Uint16Array([0x1234]).buffer)[0] == 0x12;\n\nexport class EncodeStream {\n  constructor(buffer) {\n    this.buffer = buffer;\n    this.view = new DataView(this.buffer.buffer, this.buffer.byteOffset, this.buffer.byteLength);\n    this.pos = 0;\n  }\n\n  writeBuffer(buffer) {\n    this.buffer.set(buffer, this.pos);\n    this.pos += buffer.length;\n  }\n\n  writeString(string, encoding = 'ascii') {\n    let buf;\n    switch (encoding) {\n      case 'utf16le':\n      case 'utf16-le':\n      case 'ucs2': // node treats this the same as utf16.\n        buf = stringToUtf16(string, isBigEndian);\n        break;\n\n      case 'utf16be':\n      case 'utf16-be':\n        buf = stringToUtf16(string, !isBigEndian);\n        break;\n\n      case 'utf8':\n        buf = textEncoder.encode(string);\n        break;\n\n      case 'ascii':\n        buf = stringToAscii(string);\n        break;\n\n      default:\n        throw new Error(`Unsupported encoding: ${encoding}`);\n    }\n\n    this.writeBuffer(buf);\n  }\n\n  writeUInt24BE(val) {\n    this.buffer[this.pos++] = (val >>> 16) & 0xff;\n    this.buffer[this.pos++] = (val >>> 8) & 0xff;\n    this.buffer[this.pos++] = val & 0xff;\n  }\n\n  writeUInt24LE(val) {\n    this.buffer[this.pos++] = val & 0xff;\n    this.buffer[this.pos++] = (val >>> 8) & 0xff;\n    this.buffer[this.pos++] = (val >>> 16) & 0xff;\n  }\n\n  writeInt24BE(val) {\n    if (val >= 0) {\n      this.writeUInt24BE(val);\n    } else {\n      this.writeUInt24BE(val + 0xffffff + 1);\n    }\n  }\n\n  writeInt24LE(val) {\n    if (val >= 0) {\n      this.writeUInt24LE(val);\n    } else {\n      this.writeUInt24LE(val + 0xffffff + 1);\n    }\n  }\n\n  fill(val, length) {\n    if (length < this.buffer.length) {\n      this.buffer.fill(val, this.pos, this.pos + length);\n      this.pos += length;\n    } else {\n      const buf = new Uint8Array(length);\n      buf.fill(val);\n      this.writeBuffer(buf);\n    }\n  }\n}\n\nfunction stringToUtf16(string, swap) {\n  let buf = new Uint16Array(string.length);\n  for (let i = 0; i < string.length; i++) {\n    let code = string.charCodeAt(i);\n    if (swap) {\n      code = (code >> 8) | ((code & 0xff) << 8);\n    }\n    buf[i] = code;\n  }\n  return new Uint8Array(buf.buffer);\n}\n\nfunction stringToAscii(string) {\n  let buf = new Uint8Array(string.length);\n  for (let i = 0; i < string.length; i++) {\n    // Match node.js behavior - encoding allows 8-bit rather than 7-bit.\n    buf[i] = string.charCodeAt(i);\n  }\n  return buf;\n}\n\nfor (let key of Object.getOwnPropertyNames(DataView.prototype)) {\n  if (key.slice(0, 3) === 'set') {\n    let type = key.slice(3).replace('Ui', 'UI');\n    if (type === 'Float32') {\n      type = 'Float';\n    } else if (type === 'Float64') {\n      type = 'Double';\n    }\n    let bytes = DecodeStream.TYPES[type];\n    EncodeStream.prototype['write' + type + (bytes === 1 ? '' : 'BE')] = function (value) {\n      this.view[key](this.pos, value, false);\n      this.pos += bytes;\n    };\n\n    if (bytes !== 1) {\n      EncodeStream.prototype['write' + type + 'LE'] = function (value) {\n        this.view[key](this.pos, value, true);\n        this.pos += bytes;\n      };\n    }\n  }\n}\n", "// Node back-compat.\nconst ENCODING_MAPPING = {\n  utf16le: 'utf-16le',\n  ucs2: 'utf-16le',\n  utf16be: 'utf-16be'\n}\n\nexport class DecodeStream {\n  constructor(buffer) {\n    this.buffer = buffer;\n    this.view = new DataView(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n    this.pos = 0;\n    this.length = this.buffer.length;\n  }\n\n  readString(length, encoding = 'ascii') {\n    encoding = ENCODING_MAPPING[encoding] || encoding;\n\n    let buf = this.readBuffer(length);\n    try {\n      let decoder = new TextDecoder(encoding);\n      return decoder.decode(buf);\n    } catch (err) {\n      return buf;\n    }\n  }\n\n  readBuffer(length) {\n    return this.buffer.slice(this.pos, (this.pos += length));\n  }\n\n  readUInt24BE() {\n    return (this.readUInt16BE() << 8) + this.readUInt8();\n  }\n\n  readUInt24LE() {\n    return this.readUInt16LE() + (this.readUInt8() << 16);\n  }\n\n  readInt24BE() {\n    return (this.readInt16BE() << 8) + this.readUInt8();\n  }\n\n  readInt24LE() {\n    return this.readUInt16LE() + (this.readInt8() << 16);\n  }\n}\n\nDecodeStream.TYPES = {\n  UInt8: 1,\n  UInt16: 2,\n  UInt24: 3,\n  UInt32: 4,\n  Int8: 1,\n  Int16: 2,\n  Int24: 3,\n  Int32: 4,\n  Float: 4,\n  Double: 8\n};\n\nfor (let key of Object.getOwnPropertyNames(DataView.prototype)) {\n  if (key.slice(0, 3) === 'get') {\n    let type = key.slice(3).replace('Ui', 'UI');\n    if (type === 'Float32') {\n      type = 'Float';\n    } else if (type === 'Float64') {\n      type = 'Double';\n    }\n    let bytes = DecodeStream.TYPES[type];\n    DecodeStream.prototype['read' + type + (bytes === 1 ? '' : 'BE')] = function () {\n      const ret = this.view[key](this.pos, false);\n      this.pos += bytes;\n      return ret;\n    };\n\n    if (bytes !== 1) {\n      DecodeStream.prototype['read' + type + 'LE'] = function () {\n        const ret = this.view[key](this.pos, true);\n        this.pos += bytes;\n        return ret;\n      };\n    }\n  }\n}\n", "import {Base} from './Base.js';\nimport {Number as NumberT} from './Number.js';\nimport * as utils from './utils.js';\n\nclass ArrayT extends Base {\n  constructor(type, length, lengthType = 'count') {\n    super();\n    this.type = type;\n    this.length = length;\n    this.lengthType = lengthType;\n  }\n\n  decode(stream, parent) {\n    let length;\n    const { pos } = stream;\n\n    const res = [];\n    let ctx = parent;\n\n    if (this.length != null) {\n      length = utils.resolveLength(this.length, stream, parent);\n    }\n\n    if (this.length instanceof NumberT) {\n      // define hidden properties\n      Object.defineProperties(res, {\n        parent:         { value: parent },\n        _startOffset:   { value: pos },\n        _currentOffset: { value: 0, writable: true },\n        _length:        { value: length }\n      });\n\n      ctx = res;\n    }\n\n    if ((length == null) || (this.lengthType === 'bytes')) {\n      const target = (length != null) ?\n        stream.pos + length\n      : (parent != null ? parent._length : undefined) ?\n        parent._startOffset + parent._length\n      :\n        stream.length;\n\n      while (stream.pos < target) {\n        res.push(this.type.decode(stream, ctx));\n      }\n\n    } else {\n      for (let i = 0, end = length; i < end; i++) {\n        res.push(this.type.decode(stream, ctx));\n      }\n    }\n\n    return res;\n  }\n\n  size(array, ctx, includePointers = true) {\n    if (!array) {\n      return this.type.size(null, ctx) * utils.resolveLength(this.length, null, ctx);\n    }\n\n    let size = 0;\n    if (this.length instanceof NumberT) {\n      size += this.length.size();\n      ctx = {parent: ctx, pointerSize: 0};\n    }\n\n    for (let item of array) {\n      size += this.type.size(item, ctx);\n    }\n\n    if (ctx && includePointers && this.length instanceof NumberT) {\n      size += ctx.pointerSize;\n    }\n    \n    return size;\n  }\n\n  encode(stream, array, parent) {\n    let ctx = parent;\n    if (this.length instanceof NumberT) {\n      ctx = {\n        pointers: [],\n        startOffset: stream.pos,\n        parent\n      };\n\n      ctx.pointerOffset = stream.pos + this.size(array, ctx, false);\n      this.length.encode(stream, array.length);\n    }\n\n    for (let item of array) {\n      this.type.encode(stream, item, ctx);\n    }\n\n    if (this.length instanceof NumberT) {\n      let i = 0;\n      while (i < ctx.pointers.length) {\n        const ptr = ctx.pointers[i++];\n        ptr.type.encode(stream, ptr.val, ptr.parent);\n      }\n    }\n  }\n}\n\nexport {ArrayT as Array};\n", "import {DecodeStream} from './DecodeStream.js';\nimport {EncodeStream} from './EncodeStream.js';\n\nexport class Base {\n  fromBuffer(buffer) {\n    let stream = new DecodeStream(buffer);\n    return this.decode(stream);\n  }\n\n  toBuffer(value) {\n    let size = this.size(value);\n    let buffer = new Uint8Array(size);\n    let stream = new EncodeStream(buffer);\n    this.encode(stream, value);\n    return buffer;\n  }\n}\n", "import {DecodeStream} from './DecodeStream.js';\nimport {Base} from './Base.js';\n\nclass NumberT extends Base {\n  constructor(type, endian = 'BE') {\n    super();\n    this.type = type;\n    this.endian = endian;\n    this.fn = this.type;\n    if (this.type[this.type.length - 1] !== '8') {\n      this.fn += this.endian;\n    }\n  }\n\n  size() {\n    return DecodeStream.TYPES[this.type];\n  }\n\n  decode(stream) {\n    return stream[`read${this.fn}`]();\n  }\n\n  encode(stream, val) {\n    return stream[`write${this.fn}`](val);\n  }\n}\n\nexport {NumberT as Number};\n\nexport const uint8 = new NumberT('UInt8');\nexport const uint16be = new NumberT('UInt16', 'BE');\nexport const uint16 = uint16be;\nexport const uint16le = new NumberT('UInt16', 'LE');\nexport const uint24be = new NumberT('UInt24', 'BE');\nexport const uint24 = uint24be;\nexport const uint24le = new NumberT('UInt24', 'LE');\nexport const uint32be = new NumberT('UInt32', 'BE');\nexport const uint32 = uint32be;\nexport const uint32le = new NumberT('UInt32', 'LE');\nexport const int8 = new NumberT('Int8');\nexport const int16be = new NumberT('Int16', 'BE');\nexport const int16 = int16be;\nexport const int16le = new NumberT('Int16', 'LE');\nexport const int24be = new NumberT('Int24', 'BE');\nexport const int24 = int24be;\nexport const int24le = new NumberT('Int24', 'LE');\nexport const int32be = new NumberT('Int32', 'BE');\nexport const int32 = int32be;\nexport const int32le = new NumberT('Int32', 'LE');\nexport const floatbe = new NumberT('Float', 'BE');\nexport const float = floatbe;\nexport const floatle = new NumberT('Float', 'LE');\nexport const doublebe = new NumberT('Double', 'BE');\nexport const double = doublebe;\nexport const doublele = new NumberT('Double', 'LE');\n\nexport class Fixed extends NumberT {\n  constructor(size, endian, fracBits = size >> 1) {\n    super(`Int${size}`, endian);\n    this._point = 1 << fracBits;\n  }\n\n  decode(stream) {\n    return super.decode(stream) / this._point;\n  }\n\n  encode(stream, val) {\n    return super.encode(stream, (val * this._point) | 0);\n  }\n}\n\nexport const fixed16be = new Fixed(16, 'BE');\nexport const fixed16 = fixed16be;\nexport const fixed16le = new Fixed(16, 'LE');\nexport const fixed32be = new Fixed(32, 'BE');\nexport const fixed32 = fixed32be;\nexport const fixed32le = new Fixed(32, 'LE');\n", "import {Number as NumberT} from './Number.js';\n\nexport function resolveLength(length, stream, parent) {\n  let res;\n  if (typeof length === 'number') {\n    res = length;\n\n  } else if (typeof length === 'function') {\n    res = length.call(parent, parent);\n\n  } else if (parent && (typeof length === 'string')) {\n    res = parent[length];\n\n  } else if (stream && length instanceof NumberT) {\n    res = length.decode(stream);\n  }\n\n  if (isNaN(res)) {\n    throw new Error('Not a fixed size');\n  }\n\n  return res;\n};\n\nexport class PropertyDescriptor {\n  constructor(opts = {}) {\n    this.enumerable = true;\n    this.configurable = true;\n\n    for (let key in opts) {\n      const val = opts[key];\n      this[key] = val;\n    }\n  }\n}\n", "import {Array as ArrayT} from './Array.js';\nimport {Number as NumberT} from './Number.js';\nimport * as utils from './utils.js';\n\nexport class LazyArray extends ArrayT {\n  decode(stream, parent) {\n    const { pos } = stream;\n    const length = utils.resolveLength(this.length, stream, parent);\n\n    if (this.length instanceof NumberT) {\n      parent = {\n        parent,\n        _startOffset: pos,\n        _currentOffset: 0,\n        _length: length\n      };\n    }\n\n    const res = new LazyArrayValue(this.type, length, stream, parent);\n\n    stream.pos += length * this.type.size(null, parent);\n    return res;\n  }\n\n  size(val, ctx) {\n    if (val instanceof LazyArrayValue) {\n      val = val.toArray();\n    }\n\n    return super.size(val, ctx);\n  }\n\n  encode(stream, val, ctx) {\n    if (val instanceof LazyArrayValue) {\n      val = val.toArray();\n    }\n\n    return super.encode(stream, val, ctx);\n  }\n}\n\nclass LazyArrayValue {\n  constructor(type, length, stream, ctx) {\n    this.type = type;\n    this.length = length;\n    this.stream = stream;\n    this.ctx = ctx;\n    this.base = this.stream.pos;\n    this.items = [];\n  }\n\n  get(index) {\n    if ((index < 0) || (index >= this.length)) {\n      return undefined;\n    }\n\n    if (this.items[index] == null) {\n      const { pos } = this.stream;\n      this.stream.pos = this.base + (this.type.size(null, this.ctx) * index);\n      this.items[index] = this.type.decode(this.stream, this.ctx);\n      this.stream.pos = pos;\n    }\n\n    return this.items[index];\n  }\n\n  toArray() {\n    const result = [];\n    for (let i = 0, end = this.length; i < end; i++) {\n      result.push(this.get(i));\n    }\n    return result;\n  }\n}\n", "import {Base} from './Base.js';\n\nexport class Bitfield extends Base {\n  constructor(type, flags = []) {\n    super();\n    this.type = type;\n    this.flags = flags;\n  }\n\n  decode(stream) {\n    const val = this.type.decode(stream);\n\n    const res = {};\n    for (let i = 0; i < this.flags.length; i++) {\n      const flag = this.flags[i];\n      if (flag != null) {\n        res[flag] = !!(val & (1 << i));\n      }\n    }\n\n    return res;\n  }\n\n  size() {\n    return this.type.size();\n  }\n\n  encode(stream, keys) {\n    let val = 0;\n    for (let i = 0; i < this.flags.length; i++) {\n      const flag = this.flags[i];\n      if (flag != null) {\n        if (keys[flag]) { val |= (1 << i); }\n      }\n    }\n\n    return this.type.encode(stream, val);\n  }\n}\n", "import {Base} from './Base.js';\n\nexport class BooleanT extends Base {\n  constructor(type) {\n    super();\n    this.type = type;\n  }\n\n  decode(stream, parent) {\n    return !!this.type.decode(stream, parent);\n  }\n\n  size(val, parent) {\n    return this.type.size(val, parent);\n  }\n\n  encode(stream, val, parent) {\n    return this.type.encode(stream, +val, parent);\n  }\n}\n\nexport {BooleanT as Boolean};\n", "import {Base} from './Base.js';\nimport {Number as NumberT} from './Number.js';\nimport * as utils from './utils.js';\n\nexport class BufferT extends Base {\n  constructor(length) {\n    super();\n    this.length = length;\n  }\n  \n  decode(stream, parent) {\n    const length = utils.resolveLength(this.length, stream, parent);\n    return stream.readBuffer(length);\n  }\n\n  size(val, parent) {\n    if (!val) {\n      return utils.resolveLength(this.length, null, parent);\n    }\n\n    let len = val.length;\n    if (this.length instanceof NumberT) {\n      len += this.length.size();\n    }\n\n    return len;\n  }\n\n  encode(stream, buf, parent) {\n    if (this.length instanceof NumberT) {\n      this.length.encode(stream, buf.length);\n    }\n\n    return stream.writeBuffer(buf);\n  }\n}\n\nexport {BufferT as Buffer};\n", "import {Base} from './Base.js';\n\nexport class Enum extends Base {\n  constructor(type, options = []) {\n    super();\n    this.type = type;\n    this.options = options;\n  }\n  \n  decode(stream) {\n    const index = this.type.decode(stream);\n    return this.options[index] || index;\n  }\n\n  size() {\n    return this.type.size();\n  }\n\n  encode(stream, val) {\n    const index = this.options.indexOf(val);\n    if (index === -1) {\n      throw new Error(`Unknown option in enum: ${val}`);\n    }\n\n    return this.type.encode(stream, index);\n  }\n}\n", "import {Base} from './Base.js';\n\nexport class Optional extends Base {\n  constructor(type, condition = true) {\n    super();\n    this.type = type;\n    this.condition = condition;\n  }\n\n  decode(stream, parent) {\n    let { condition } = this;\n    if (typeof condition === 'function') {\n      condition = condition.call(parent, parent);\n    }\n\n    if (condition) {\n      return this.type.decode(stream, parent);\n    }\n  }\n\n  size(val, parent) {\n    let { condition } = this;\n    if (typeof condition === 'function') {\n      condition = condition.call(parent, parent);\n    }\n\n    if (condition) {\n      return this.type.size(val, parent);\n    } else {\n      return 0;\n    }\n  }\n\n  encode(stream, val, parent) {\n    let { condition } = this;\n    if (typeof condition === 'function') {\n      condition = condition.call(parent, parent);\n    }\n\n    if (condition) {\n      return this.type.encode(stream, val, parent);\n    }\n  }\n}\n", "import {Base} from './Base.js';\nimport * as utils from './utils.js';\n\nexport class Reserved extends Base {\n  constructor(type, count = 1) {\n    super();\n    this.type = type;\n    this.count = count;\n  }\n  decode(stream, parent) {\n    stream.pos += this.size(null, parent);\n    return undefined;\n  }\n\n  size(data, parent) {\n    const count = utils.resolveLength(this.count, null, parent);\n    return this.type.size() * count;\n  }\n\n  encode(stream, val, parent) {\n    return stream.fill(0, this.size(val, parent));\n  }\n}\n", "import {Base} from './Base.js';\nimport {Number as NumberT} from './Number.js';\nimport * as utils from './utils.js';\n\nclass StringT extends Base {\n  constructor(length, encoding = 'ascii') {\n    super();\n    this.length = length;\n    this.encoding = encoding;\n  }\n\n  decode(stream, parent) {\n    let length, pos;\n\n    let { encoding } = this;\n    if (typeof encoding === 'function') {\n      encoding = encoding.call(parent, parent) || 'ascii';\n    }\n    let width = encodingWidth(encoding);\n\n    if (this.length != null) {\n      length = utils.resolveLength(this.length, stream, parent);\n    } else {\n      let buffer;\n      ({buffer, length, pos} = stream);\n\n      while ((pos < length - width + 1) &&\n        (buffer[pos] !== 0x00 ||\n        (width === 2 && buffer[pos+1] !== 0x00)\n        )) {\n        pos += width;\n      }\n\n      length = pos - stream.pos;\n    }\n\n\n    const string = stream.readString(length, encoding);\n\n    if ((this.length == null) && (stream.pos < stream.length)) {\n      stream.pos+=width;\n    }\n\n    return string;\n  }\n\n  size(val, parent) {\n    // Use the defined value if no value was given\n    if (val === undefined || val === null) {\n      return utils.resolveLength(this.length, null, parent);\n    }\n\n    let { encoding } = this;\n    if (typeof encoding === 'function') {\n      encoding = encoding.call(parent != null ? parent.val : undefined, parent != null ? parent.val : undefined) || 'ascii';\n    }\n\n    if (encoding === 'utf16be') {\n      encoding = 'utf16le';\n    }\n\n    let size = byteLength(val, encoding);\n    if (this.length instanceof NumberT) {\n      size += this.length.size();\n    }\n\n    if ((this.length == null)) {\n      size += encodingWidth(encoding);\n    }\n\n    return size;\n  }\n\n  encode(stream, val, parent) {\n    let { encoding } = this;\n    if (typeof encoding === 'function') {\n      encoding = encoding.call(parent != null ? parent.val : undefined, parent != null ? parent.val : undefined) || 'ascii';\n    }\n\n    if (this.length instanceof NumberT) {\n      this.length.encode(stream, byteLength(val, encoding));\n    }\n\n    stream.writeString(val, encoding);\n\n    if ((this.length == null)) {\n      return encodingWidth(encoding) == 2 ?\n        stream.writeUInt16LE(0x0000) :\n        stream.writeUInt8(0x00);\n    }\n  }\n}\n\nfunction encodingWidth(encoding) {\n  switch(encoding) {\n    case 'ascii':\n    case 'utf8': // utf8 is a byte-based encoding for zero-term string\n      return 1;\n    case 'utf16le':\n    case 'utf16-le':\n    case 'utf-16be':\n    case 'utf-16le':\n    case 'utf16be':\n    case 'utf16-be':\n    case 'ucs2':\n      return 2;\n    default:\n      //TODO: assume all other encodings are 1-byters\n      //throw new Error('Unknown encoding ' + encoding);\n      return 1;\n  }\n}\n\nfunction byteLength(string, encoding) {\n  switch (encoding) {\n    case 'ascii':\n      return string.length;\n    case 'utf8':\n      let len = 0;\n      for (let i = 0; i < string.length; i++) {\n        let c = string.charCodeAt(i);\n\n        if (c >= 0xd800 && c <= 0xdbff && i < string.length - 1) {\n          let c2 = string.charCodeAt(++i);\n          if ((c2 & 0xfc00) === 0xdc00) {\n            c = ((c & 0x3ff) << 10) + (c2 & 0x3ff) + 0x10000;\n          } else {\n            // unmatched surrogate.\n            i--;\n          }\n        }\n\n        if ((c & 0xffffff80) === 0) {\n          len++;\n        } else if ((c & 0xfffff800) === 0) {\n          len += 2;\n        } else if ((c & 0xffff0000) === 0) {\n          len += 3;\n        } else if ((c & 0xffe00000) === 0) {\n          len += 4;\n        }\n      }\n      return len;\n    case 'utf16le':\n    case 'utf16-le':\n    case 'utf16be':\n    case 'utf16-be':\n    case 'ucs2':\n      return string.length * 2;\n    default:\n      throw new Error('Unknown encoding ' + encoding);\n  }\n}\n\nexport {StringT as String};\n", "import {Base} from './Base.js';\nimport * as utils from './utils.js';\n\nexport class Struct extends Base {\n  constructor(fields = {}) {\n    super();\n    this.fields = fields;\n  }\n\n  decode(stream, parent, length = 0) {\n    const res = this._setup(stream, parent, length);\n    this._parseFields(stream, res, this.fields);\n\n    if (this.process != null) {\n      this.process.call(res, stream);\n    }\n    return res;\n  }\n\n  _setup(stream, parent, length) {\n    const res = {};\n\n    // define hidden properties\n    Object.defineProperties(res, {\n      parent:         { value: parent },\n      _startOffset:   { value: stream.pos },\n      _currentOffset: { value: 0, writable: true },\n      _length:        { value: length }\n    });\n\n    return res;\n  }\n\n  _parseFields(stream, res, fields) {\n    for (let key in fields) {\n      var val;\n      const type = fields[key];\n      if (typeof type === 'function') {\n        val = type.call(res, res);\n      } else {\n        val = type.decode(stream, res);\n      }\n\n      if (val !== undefined) {\n        if (val instanceof utils.PropertyDescriptor) {\n          Object.defineProperty(res, key, val);\n        } else {\n          res[key] = val;\n        }\n      }\n\n      res._currentOffset = stream.pos - res._startOffset;\n    }\n\n  }\n\n  size(val, parent, includePointers = true) {\n    if (val == null) { val = {}; }\n    const ctx = {\n      parent,\n      val,\n      pointerSize: 0\n    };\n\n    if (this.preEncode != null) {\n      this.preEncode.call(val);\n    }\n\n    let size = 0;\n    for (let key in this.fields) {\n      const type = this.fields[key];\n      if (type.size != null) {\n        size += type.size(val[key], ctx);\n      }\n    }\n\n    if (includePointers) {\n      size += ctx.pointerSize;\n    }\n\n    return size;\n  }\n\n  encode(stream, val, parent) {\n    let type;\n    if (this.preEncode != null) {\n      this.preEncode.call(val, stream);\n    }\n\n    const ctx = {\n      pointers: [],\n      startOffset: stream.pos,\n      parent,\n      val,\n      pointerSize: 0\n    };\n\n    ctx.pointerOffset = stream.pos + this.size(val, ctx, false);\n\n    for (let key in this.fields) {\n      type = this.fields[key];\n      if (type.encode != null) {\n        type.encode(stream, val[key], ctx);\n      }\n    }\n\n    let i = 0;\n    while (i < ctx.pointers.length) {\n      const ptr = ctx.pointers[i++];\n      ptr.type.encode(stream, ptr.val, ptr.parent);\n    }\n  }\n}\n", "import {Struct} from './Struct.js';\n\nconst getPath = (object, pathArray) => {\n  return pathArray.reduce((prevObj, key) => prevObj && prevObj[key], object);\n};\n\nexport class VersionedStruct extends Struct {\n  constructor(type, versions = {}) {\n    super();\n    this.type = type;\n    this.versions = versions;\n    if (typeof type === 'string') {\n      this.versionPath = type.split('.');\n    }\n  }\n\n  decode(stream, parent, length = 0) {\n    const res = this._setup(stream, parent, length);\n\n    if (typeof this.type === 'string') {\n      res.version = getPath(parent, this.versionPath);\n    } else {\n      res.version = this.type.decode(stream);\n    }\n\n    if (this.versions.header) {\n      this._parseFields(stream, res, this.versions.header);\n    }\n\n    const fields = this.versions[res.version];\n    if ((fields == null)) {\n      throw new Error(`Unknown version ${res.version}`);\n    }\n\n    if (fields instanceof VersionedStruct) {\n      return fields.decode(stream, parent);\n    }\n\n    this._parseFields(stream, res, fields);\n\n    if (this.process != null) {\n      this.process.call(res, stream);\n    }\n    return res;\n  }\n\n  size(val, parent, includePointers = true) {\n    let key, type;\n    if (!val) {\n      throw new Error('Not a fixed size');\n    }\n\n    if (this.preEncode != null) {\n      this.preEncode.call(val);\n    }\n\n    const ctx = {\n      parent,\n      val,\n      pointerSize: 0\n    };\n\n    let size = 0;\n    if (typeof this.type !== 'string') {\n      size += this.type.size(val.version, ctx);\n    }\n\n    if (this.versions.header) {\n      for (key in this.versions.header) {\n        type = this.versions.header[key];\n        if (type.size != null) {\n          size += type.size(val[key], ctx);\n        }\n      }\n    }\n\n    const fields = this.versions[val.version];\n    if ((fields == null)) {\n      throw new Error(`Unknown version ${val.version}`);\n    }\n\n    for (key in fields) {\n      type = fields[key];\n      if (type.size != null) {\n        size += type.size(val[key], ctx);\n      }\n    }\n\n    if (includePointers) {\n      size += ctx.pointerSize;\n    }\n\n    return size;\n  }\n\n  encode(stream, val, parent) {\n    let key, type;\n    if (this.preEncode != null) {\n      this.preEncode.call(val, stream);\n    }\n\n    const ctx = {\n      pointers: [],\n      startOffset: stream.pos,\n      parent,\n      val,\n      pointerSize: 0\n    };\n\n    ctx.pointerOffset = stream.pos + this.size(val, ctx, false);\n\n    if (typeof this.type !== 'string') {\n      this.type.encode(stream, val.version);\n    }\n\n    if (this.versions.header) {\n      for (key in this.versions.header) {\n        type = this.versions.header[key];\n        if (type.encode != null) {\n          type.encode(stream, val[key], ctx);\n        }\n      }\n    }\n\n    const fields = this.versions[val.version];\n    for (key in fields) {\n      type = fields[key];\n      if (type.encode != null) {\n        type.encode(stream, val[key], ctx);\n      }\n    }\n\n    let i = 0;\n    while (i < ctx.pointers.length) {\n      const ptr = ctx.pointers[i++];\n      ptr.type.encode(stream, ptr.val, ptr.parent);\n    }\n  }\n}\n", "import * as utils from './utils.js';\nimport {Base} from './Base.js';\n\nexport class Pointer extends Base {\n  constructor(offsetType, type, options = {}) {\n    super();\n    this.offsetType = offsetType;\n    this.type = type;\n    this.options = options;\n    if (this.type === 'void') { this.type = null; }\n    if (this.options.type == null) { this.options.type = 'local'; }\n    if (this.options.allowNull == null) { this.options.allowNull = true; }\n    if (this.options.nullValue == null) { this.options.nullValue = 0; }\n    if (this.options.lazy == null) { this.options.lazy = false; }\n    if (this.options.relativeTo) {\n      if (typeof this.options.relativeTo !== 'function') {\n        throw new Error('relativeTo option must be a function');\n      }\n      this.relativeToGetter = options.relativeTo;\n    }\n  }\n\n  decode(stream, ctx) {\n    const offset = this.offsetType.decode(stream, ctx);\n\n    // handle NULL pointers\n    if ((offset === this.options.nullValue) && this.options.allowNull) {\n      return null;\n    }\n\n    let relative;\n    switch (this.options.type) {\n      case 'local':     relative = ctx._startOffset; break;\n      case 'immediate': relative = stream.pos - this.offsetType.size(); break;\n      case 'parent':    relative = ctx.parent._startOffset; break;\n      default:\n        var c = ctx;\n        while (c.parent) {\n          c = c.parent;\n        }\n\n        relative = c._startOffset || 0;\n    }\n\n    if (this.options.relativeTo) {\n      relative += this.relativeToGetter(ctx);\n    }\n\n    const ptr = offset + relative;\n\n    if (this.type != null) {\n      let val = null;\n      const decodeValue = () => {\n        if (val != null) { return val; }\n\n        const { pos } = stream;\n        stream.pos = ptr;\n        val = this.type.decode(stream, ctx);\n        stream.pos = pos;\n        return val;\n      };\n\n      // If this is a lazy pointer, define a getter to decode only when needed.\n      // This obviously only works when the pointer is contained by a Struct.\n      if (this.options.lazy) {\n        return new utils.PropertyDescriptor({\n          get: decodeValue});\n      }\n\n      return decodeValue();\n    } else {\n      return ptr;\n    }\n  }\n\n  size(val, ctx) {\n    const parent = ctx;\n    switch (this.options.type) {\n      case 'local': case 'immediate':\n        break;\n      case 'parent':\n        ctx = ctx.parent;\n        break;\n      default: // global\n        while (ctx.parent) {\n          ctx = ctx.parent;\n        }\n    }\n\n    let { type } = this;\n    if (type == null) {\n      if (!(val instanceof VoidPointer)) {\n        throw new Error(\"Must be a VoidPointer\");\n      }\n\n      ({ type } = val);\n      val = val.value;\n    }\n\n    if (val && ctx) {\n      // Must be written as two separate lines rather than += in case `type.size` mutates ctx.pointerSize.\n      let size = type.size(val, parent);\n      ctx.pointerSize += size;\n    }\n\n    return this.offsetType.size();\n  }\n\n  encode(stream, val, ctx) {\n    let relative;\n    const parent = ctx;\n    if ((val == null)) {\n      this.offsetType.encode(stream, this.options.nullValue);\n      return;\n    }\n\n    switch (this.options.type) {\n      case 'local':\n        relative = ctx.startOffset;\n        break;\n      case 'immediate':\n        relative = stream.pos + this.offsetType.size(val, parent);\n        break;\n      case 'parent':\n        ctx = ctx.parent;\n        relative = ctx.startOffset;\n        break;\n      default: // global\n        relative = 0;\n        while (ctx.parent) {\n          ctx = ctx.parent;\n        }\n    }\n\n    if (this.options.relativeTo) {\n      relative += this.relativeToGetter(parent.val);\n    }\n\n    this.offsetType.encode(stream, ctx.pointerOffset - relative);\n\n    let { type } = this;\n    if (type == null) {\n      if (!(val instanceof VoidPointer)) {\n        throw new Error(\"Must be a VoidPointer\");\n      }\n\n      ({ type } = val);\n      val = val.value;\n    }\n\n    ctx.pointers.push({\n      type,\n      val,\n      parent\n    });\n\n    return ctx.pointerOffset += type.size(val, parent);\n  }\n}\n\n// A pointer whose type is determined at decode time\nexport class VoidPointer {\n  constructor(type, value) {\n    this.type = type;\n    this.value = value;\n  }\n}\n"], "names": [], "version": 3, "file": "main.cjs.map"}