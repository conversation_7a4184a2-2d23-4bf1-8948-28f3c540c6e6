{"name": "siyoga-travel-booking-server", "version": "1.0.0", "description": "Backend server for Siyoga Travel Booking System", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["travel", "booking", "nodejs", "express", "mysql"], "author": "Siyoga Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.0", "nodemailer": "^6.9.7", "pdfkit": "^0.17.1", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}