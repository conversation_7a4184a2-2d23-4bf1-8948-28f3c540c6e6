{"name": "siyoga-travel-booking-server", "version": "1.0.0", "description": "Backend server for Siyoga Travel Booking System", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["travel", "booking", "nodejs", "express", "mysql"], "author": "Siyoga Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}