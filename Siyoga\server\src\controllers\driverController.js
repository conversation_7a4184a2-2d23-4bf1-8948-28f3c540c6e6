const db = require('../config/database');

/**
 * Get driver's vehicles
 */
const getDriverVehicles = async (req, res) => {
  try {
    console.log('req.user:', req.user);
    const userId = req.user.user_id;

    // Get driver_id from user_id
    const [driverResult] = await db.execute(
      'SELECT driver_id FROM drivers WHERE user_id = ?',
      [userId]
    );

    if (driverResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Driver profile not found'
      });
    }

    const driverId = driverResult[0].driver_id;

    // Get vehicles with category information
    const [vehicles] = await db.execute(`
      SELECT 
        v.*,
        vc.category_name,
        vc.vehicle_type as category_type
      FROM vehicles v
      JOIN vehicle_categories vc ON v.category_id = vc.category_id
      WHERE v.driver_id = ?
      ORDER BY v.created_at DESC
    `, [driverId]);

    res.json({
      success: true,
      data: vehicles
    });

  } catch (error) {
    console.error('Error fetching driver vehicles:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch vehicles',
      error: error.message
    });
  }
};

/**
 * Add new vehicle for driver
 */
const addVehicle = async (req, res) => {
  try {
    const userId = req.user.user_id;
    const {
      category_id,
      make_model,
      registration_number,
      year_manufactured,
      color,
      seating_capacity,
      insurance_expiry
    } = req.body;

    // Get driver_id from user_id
    const [driverResult] = await db.execute(
      'SELECT driver_id FROM drivers WHERE user_id = ?',
      [userId]
    );

    if (driverResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Driver profile not found'
      });
    }

    const driverId = driverResult[0].driver_id;

    // Get vehicle category info to set vehicle_type
    const [categoryResult] = await db.execute(
      'SELECT vehicle_type FROM vehicle_categories WHERE category_id = ?',
      [category_id]
    );

    if (categoryResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle category not found'
      });
    }

    const vehicleType = categoryResult[0].vehicle_type;

    // Insert vehicle
    const [result] = await db.execute(`
      INSERT INTO vehicles (
        driver_id, category_id, vehicle_type, make_model, registration_number,
        year_manufactured, color, seating_capacity, insurance_expiry, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
    `, [
      driverId,
      category_id,
      vehicleType,
      make_model,
      registration_number,
      year_manufactured || null,
      color || null,
      seating_capacity,
      insurance_expiry
    ]);

    res.status(201).json({
      success: true,
      message: 'Vehicle added successfully',
      data: {
        vehicle_id: result.insertId
      }
    });

  } catch (error) {
    console.error('Error adding vehicle:', error);
    if (error.code === 'ER_DUP_ENTRY') {
      res.status(400).json({
        success: false,
        message: 'Registration number already exists'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to add vehicle',
        error: error.message
      });
    }
  }
};

/**
 * Get available bookings for driver based on vehicle types
 */
const getAvailableBookings = async (req, res) => {
  try {
    const userId = req.user.user_id;

    // Get driver_id from user_id
    const [driverResult] = await db.execute(
      'SELECT driver_id FROM drivers WHERE user_id = ?',
      [userId]
    );

    if (driverResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Driver profile not found'
      });
    }

    const driverId = driverResult[0].driver_id;

    // Get driver's vehicle categories
    const [driverCategories] = await db.execute(`
      SELECT DISTINCT v.category_id
      FROM vehicles v
      WHERE v.driver_id = ? AND v.is_active = 1
    `, [driverId]);

    if (driverCategories.length === 0) {
      return res.json({
        success: true,
        data: [],
        message: 'No active vehicles found. Add vehicles to see available bookings.'
      });
    }

    const categoryIds = driverCategories.map(cat => cat.category_id);
    const placeholders = categoryIds.map(() => '?').join(',');

    // Get available bookings matching driver's vehicle categories
    const [bookings] = await db.execute(`
      SELECT 
        b.*,
        vc.category_name,
        vc.vehicle_type,
        t.first_name as tourist_first_name,
        t.last_name as tourist_last_name
      FROM bookings b
      JOIN vehicle_categories vc ON b.selected_category_id = vc.category_id
      JOIN tourists t ON b.tourist_id = t.tourist_id
      WHERE b.status = 'pending' 
        AND b.driver_id IS NULL
        AND b.selected_category_id IN (${placeholders})
      ORDER BY b.created_at ASC
    `, categoryIds);

    res.json({
      success: true,
      data: bookings
    });

  } catch (error) {
    console.error('Error fetching available bookings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch available bookings',
      error: error.message
    });
  }
};

/**
 * Accept a booking
 */
const acceptBooking = async (req, res) => {
  try {
    const userId = req.user.user_id;
    const bookingId = req.params.bookingId;

    // Get driver_id from user_id
    const [driverResult] = await db.execute(
      'SELECT driver_id FROM drivers WHERE user_id = ?',
      [userId]
    );

    if (driverResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Driver profile not found'
      });
    }

    const driverId = driverResult[0].driver_id;

    // Check if booking exists and is available
    const [bookingResult] = await db.execute(
      'SELECT * FROM bookings WHERE booking_id = ? AND status = "pending" AND driver_id IS NULL',
      [bookingId]
    );

    if (bookingResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found or already accepted'
      });
    }

    const booking = bookingResult[0];

    // Check if driver has a vehicle matching the required category
    const [vehicleResult] = await db.execute(`
      SELECT vehicle_id FROM vehicles 
      WHERE driver_id = ? AND category_id = ? AND is_active = 1
      LIMIT 1
    `, [driverId, booking.selected_category_id]);

    if (vehicleResult.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'You do not have a vehicle matching the required category'
      });
    }

    const vehicleId = vehicleResult[0].vehicle_id;

    // Accept the booking
    await db.execute(`
      UPDATE bookings 
      SET driver_id = ?, vehicle_id = ?, status = 'confirmed'
      WHERE booking_id = ?
    `, [driverId, vehicleId, bookingId]);

    res.json({
      success: true,
      message: 'Booking accepted successfully'
    });

  } catch (error) {
    console.error('Error accepting booking:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to accept booking',
      error: error.message
    });
  }
};

/**
 * Get driver's accepted bookings
 */
const getMyBookings = async (req, res) => {
  try {
    const userId = req.user.user_id;

    // Get driver_id from user_id
    const [driverResult] = await db.execute(
      'SELECT driver_id FROM drivers WHERE user_id = ?',
      [userId]
    );

    if (driverResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Driver profile not found'
      });
    }

    const driverId = driverResult[0].driver_id;

    // Get driver's bookings
    const [bookings] = await db.execute(`
      SELECT 
        b.*,
        vc.category_name,
        vc.vehicle_type,
        v.make_model,
        v.registration_number,
        t.first_name as tourist_first_name,
        t.last_name as tourist_last_name,
        t.phone as tourist_phone
      FROM bookings b
      JOIN vehicle_categories vc ON b.selected_category_id = vc.category_id
      JOIN vehicles v ON b.vehicle_id = v.vehicle_id
      JOIN tourists t ON b.tourist_id = t.tourist_id
      WHERE b.driver_id = ?
      ORDER BY b.created_at DESC
    `, [driverId]);

    res.json({
      success: true,
      data: bookings
    });

  } catch (error) {
    console.error('Error fetching driver bookings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch bookings',
      error: error.message
    });
  }
};

module.exports = {
  getDriverVehicles,
  addVehicle,
  getAvailableBookings,
  acceptBooking,
  getMyBookings
};
