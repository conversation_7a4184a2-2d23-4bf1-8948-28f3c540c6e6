const db = require('../config/database');

/**
 * Add a new vehicle for the driver
 */
const addVehicle = async (req, res) => {
  try {
    const {
      categoryId,
      makeModel,
      registrationNumber,
      yearManufactured,
      color,
      seatingCapacity,
      insuranceExpiry
    } = req.body;

    const userId = req.user.user_id;

    // Get driver_id from users table
    const [driverResult] = await db.execute(
      'SELECT driver_id FROM drivers WHERE user_id = ?',
      [userId]
    );

    if (driverResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Driver profile not found'
      });
    }

    const driverId = driverResult[0].driver_id;

    // Get vehicle category to determine vehicle_type
    const [categoryResult] = await db.execute(
      'SELECT vehicle_type FROM vehicle_categories WHERE category_id = ?',
      [categoryId]
    );

    if (categoryResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle category not found'
      });
    }

    const vehicleType = categoryResult[0].vehicle_type;

    // Insert vehicle
    const [result] = await db.execute(`
      INSERT INTO vehicles (
        driver_id, category_id, vehicle_type, make_model, registration_number,
        year_manufactured, color, seating_capacity, has_ac, insurance_expiry
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      driverId,
      categoryId,
      vehicleType,
      makeModel,
      registrationNumber,
      yearManufactured || null,
      color || null,
      seatingCapacity,
      hasAc ? 1 : 0,
      insuranceExpiry
    ]);

    res.status(201).json({
      success: true,
      message: 'Vehicle added successfully',
      data: {
        vehicleId: result.insertId
      }
    });

  } catch (error) {
    console.error('Error adding vehicle:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add vehicle',
      error: error.message
    });
  }
};

/**
 * Get driver's vehicles
 */
const getDriverVehicles = async (req, res) => {
  try {
    const userId = req.user.user_id;

    // Get driver_id
    const [driverResult] = await db.execute(
      'SELECT driver_id FROM drivers WHERE user_id = ?',
      [userId]
    );

    if (driverResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Driver profile not found'
      });
    }

    const driverId = driverResult[0].driver_id;

    // Get vehicles with category details
    const [vehicles] = await db.execute(`
      SELECT 
        v.*,
        vc.category_name,
        vc.vehicle_type as category_vehicle_type
      FROM vehicles v
      JOIN vehicle_categories vc ON v.category_id = vc.category_id
      WHERE v.driver_id = ?
      ORDER BY v.created_at DESC
    `, [driverId]);

    res.json({
      success: true,
      data: vehicles
    });

  } catch (error) {
    console.error('Error fetching driver vehicles:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch vehicles',
      error: error.message
    });
  }
};

/**
 * Get available bookings for driver based on vehicle types
 */
const getAvailableBookings = async (req, res) => {
  try {
    const userId = req.user.user_id;

    // Get driver_id and their vehicle types
    const [driverResult] = await db.execute(`
      SELECT DISTINCT
        d.driver_id,
        vc.vehicle_type
      FROM drivers d
      JOIN vehicles v ON d.driver_id = v.driver_id
      JOIN vehicle_categories vc ON v.category_id = vc.category_id
      WHERE d.user_id = ? AND v.is_active = 1
    `, [userId]);

    if (driverResult.length === 0) {
      return res.json({
        success: true,
        data: []
      });
    }

    const driverId = driverResult[0].driver_id;
    const vehicleTypes = driverResult.map(row => row.vehicle_type);

    // Get available bookings that match driver's vehicle types
    const [bookings] = await db.execute(`
      SELECT 
        b.*,
        vc.category_name,
        vc.vehicle_type,
        t.first_name,
        t.last_name,
        t.phone as tourist_phone
      FROM bookings b
      JOIN vehicle_categories vc ON b.selected_category_id = vc.category_id
      JOIN tourists t ON b.tourist_id = t.tourist_id
      WHERE b.status = 'pending' 
        AND b.driver_id IS NULL
        AND vc.vehicle_type IN (${vehicleTypes.map(() => '?').join(',')})
      ORDER BY b.created_at ASC
    `, vehicleTypes);

    res.json({
      success: true,
      data: bookings
    });

  } catch (error) {
    console.error('Error fetching available bookings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch available bookings',
      error: error.message
    });
  }
};

/**
 * Accept a booking
 */
const acceptBooking = async (req, res) => {
  try {
    const { bookingId } = req.params;
    const userId = req.user.user_id;

    // Get driver_id
    const [driverResult] = await db.execute(
      'SELECT driver_id FROM drivers WHERE user_id = ?',
      [userId]
    );

    if (driverResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Driver profile not found'
      });
    }

    const driverId = driverResult[0].driver_id;

    // Check if booking is still available
    const [bookingCheck] = await db.execute(
      'SELECT * FROM bookings WHERE booking_id = ? AND status = "pending" AND driver_id IS NULL',
      [bookingId]
    );

    if (bookingCheck.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Booking is no longer available'
      });
    }

    // Accept the booking
    await db.execute(
      'UPDATE bookings SET driver_id = ?, status = "confirmed" WHERE booking_id = ?',
      [driverId, bookingId]
    );

    res.json({
      success: true,
      message: 'Booking accepted successfully'
    });

  } catch (error) {
    console.error('Error accepting booking:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to accept booking',
      error: error.message
    });
  }
};

/**
 * Get driver's accepted bookings
 */
const getDriverBookings = async (req, res) => {
  try {
    const userId = req.user.user_id;

    // Get driver_id
    const [driverResult] = await db.execute(
      'SELECT driver_id FROM drivers WHERE user_id = ?',
      [userId]
    );

    if (driverResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Driver profile not found'
      });
    }

    const driverId = driverResult[0].driver_id;

    // Get driver's bookings
    const [bookings] = await db.execute(`
      SELECT 
        b.*,
        vc.category_name,
        vc.vehicle_type,
        t.first_name,
        t.last_name,
        t.phone as tourist_phone
      FROM bookings b
      JOIN vehicle_categories vc ON b.selected_category_id = vc.category_id
      JOIN tourists t ON b.tourist_id = t.tourist_id
      WHERE b.driver_id = ?
      ORDER BY b.created_at DESC
    `, [driverId]);

    res.json({
      success: true,
      data: bookings
    });

  } catch (error) {
    console.error('Error fetching driver bookings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch driver bookings',
      error: error.message
    });
  }
};

module.exports = {
  addVehicle,
  getDriverVehicles,
  getAvailableBookings,
  acceptBooking,
  getDriverBookings
};
