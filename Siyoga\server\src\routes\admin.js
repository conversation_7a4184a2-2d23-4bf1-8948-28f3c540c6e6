// Admin routes
const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const {
    getDashboardStats,
    getAllUsers,
    getAllDrivers,
    getAllBookings,
    updateDriverStatus,
    toggleUserStatus
} = require('../controllers/adminController');
const { formatResponse } = require('../utils/helpers');
// const PDFDocument = require('pdfkit');
const { executeQuery } = require('../config/database');

const router = express.Router();

// Apply authentication and admin role check to all routes
router.use(authenticateToken);
router.use(requireAdmin);

// Dashboard statistics
router.get('/dashboard/stats', getDashboardStats);

// User management
router.get('/users', getAllUsers);
router.put('/users/:userId/toggle-status', toggleUserStatus);

// Driver management
router.get('/drivers', getAllDrivers);
router.put('/drivers/:driverId/status', [
    body('status').isIn(['pending', 'approved', 'rejected', 'suspended']).withMessage('Invalid status'),
    body('adminNotes').optional().isLength({ max: 500 }).withMessage('Admin notes too long')
], updateDriverStatus);

// Booking management
router.get('/bookings', getAllBookings);

// Report generation routes
router.get('/reports/users', async (req, res) => {
    try {
        const { format = 'json', startDate, endDate } = req.query;
        
        let query = `
            SELECT 
                u.user_id,
                u.email,
                u.role,
                u.is_verified,
                u.is_active,
                u.created_at,
                CASE 
                    WHEN u.role = 'tourist' THEN CONCAT(t.first_name, ' ', t.last_name)
                    WHEN u.role = 'driver' THEN CONCAT(d.first_name, ' ', d.last_name)
                    ELSE 'N/A'
                END as full_name,
                CASE 
                    WHEN u.role = 'tourist' THEN t.phone
                    WHEN u.role = 'driver' THEN d.phone
                    ELSE NULL
                END as phone
            FROM users u
            LEFT JOIN tourists t ON u.user_id = t.user_id
            LEFT JOIN drivers d ON u.user_id = d.user_id
            WHERE u.role != 'admin'
        `;
        
        const params = [];
        
        if (startDate) {
            query += ' AND u.created_at >= ?';
            params.push(startDate);
        }
        
        if (endDate) {
            query += ' AND u.created_at <= ?';
            params.push(endDate + ' 23:59:59');
        }
        
        query += ' ORDER BY u.created_at DESC';
        
        const users = await executeQuery(query, params);
        
        if (format === 'pdf') {
            // Generate PDF report - temporarily disabled
            return res.status(501).json(formatResponse(false, 'PDF generation temporarily disabled'));
            /*
            const doc = new PDFDocument();
            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', 'attachment; filename="users-report.pdf"');
            
            doc.pipe(res);
            
            // PDF Header
            doc.fontSize(20).text('Siyoga Travels - Users Report', 50, 50);
            doc.fontSize(12).text(`Generated on: ${new Date().toLocaleDateString()}`, 50, 80);
            
            if (startDate || endDate) {
                doc.text(`Date Range: ${startDate || 'Beginning'} to ${endDate || 'Present'}`, 50, 100);
            }
            
            // Table headers
            let yPosition = 140;
            doc.fontSize(10);
            doc.text('ID', 50, yPosition);
            doc.text('Name', 80, yPosition);
            doc.text('Email', 200, yPosition);
            doc.text('Role', 350, yPosition);
            doc.text('Status', 400, yPosition);
            doc.text('Created', 450, yPosition);
            
            // Draw line under headers
            doc.moveTo(50, yPosition + 15).lineTo(550, yPosition + 15).stroke();
            
            yPosition += 25;
            
            // Table data
            users.forEach((user, index) => {
                if (yPosition > 750) {
                    doc.addPage();
                    yPosition = 50;
                }
                
                doc.text(user.user_id.toString(), 50, yPosition);
                doc.text(user.full_name || 'N/A', 80, yPosition);
                doc.text(user.email, 200, yPosition);
                doc.text(user.role, 350, yPosition);
                doc.text(user.is_active ? 'Active' : 'Inactive', 400, yPosition);
                doc.text(new Date(user.created_at).toLocaleDateString(), 450, yPosition);
                
                yPosition += 20;
            });
            
            // Summary
            doc.addPage();
            doc.fontSize(16).text('Summary', 50, 50);
            doc.fontSize(12);
            doc.text(`Total Users: ${users.length}`, 50, 80);
            doc.text(`Tourists: ${users.filter(u => u.role === 'tourist').length}`, 50, 100);
            doc.text(`Drivers: ${users.filter(u => u.role === 'driver').length}`, 50, 120);
            doc.text(`Active Users: ${users.filter(u => u.is_active).length}`, 50, 140);
            doc.text(`Verified Users: ${users.filter(u => u.is_verified).length}`, 50, 160);
            
            doc.end();
            */
        } else {
            res.json(formatResponse(true, 'Users report generated successfully', {
                users,
                summary: {
                    total: users.length,
                    tourists: users.filter(u => u.role === 'tourist').length,
                    drivers: users.filter(u => u.role === 'driver').length,
                    active: users.filter(u => u.is_active).length,
                    verified: users.filter(u => u.is_verified).length
                }
            }));
        }
        
    } catch (error) {
        console.error('Users report error:', error);
        res.status(500).json(formatResponse(false, 'Failed to generate users report'));
    }
});

router.get('/reports/bookings', async (req, res) => {
    try {
        const { format = 'json', startDate, endDate, status } = req.query;
        
        let query = `
            SELECT 
                b.*,
                CONCAT(t.first_name, ' ', t.last_name) as tourist_name,
                tu.email as tourist_email,
                CONCAT(d.first_name, ' ', d.last_name) as driver_name,
                du.email as driver_email,
                vc.category_name
            FROM bookings b
            JOIN tourists t ON b.tourist_id = t.tourist_id
            JOIN users tu ON t.user_id = tu.user_id
            LEFT JOIN drivers d ON b.driver_id = d.driver_id
            LEFT JOIN users du ON d.user_id = du.user_id
            LEFT JOIN vehicle_categories vc ON b.selected_category_id = vc.category_id
            WHERE 1=1
        `;
        
        const params = [];
        
        if (startDate) {
            query += ' AND b.created_at >= ?';
            params.push(startDate);
        }
        
        if (endDate) {
            query += ' AND b.created_at <= ?';
            params.push(endDate + ' 23:59:59');
        }
        
        if (status) {
            query += ' AND b.status = ?';
            params.push(status);
        }
        
        query += ' ORDER BY b.created_at DESC';
        
        const bookings = await executeQuery(query, params);
        
        if (format === 'pdf') {
            // Generate PDF report - temporarily disabled
            return res.status(501).json(formatResponse(false, 'PDF generation temporarily disabled'));
            /*
            const doc = new PDFDocument();
            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', 'attachment; filename="bookings-report.pdf"');
            
            doc.pipe(res);
            
            // PDF Header
            doc.fontSize(20).text('Siyoga Travels - Bookings Report', 50, 50);
            doc.fontSize(12).text(`Generated on: ${new Date().toLocaleDateString()}`, 50, 80);
            
            if (startDate || endDate) {
                doc.text(`Date Range: ${startDate || 'Beginning'} to ${endDate || 'Present'}`, 50, 100);
            }
            
            if (status) {
                doc.text(`Status Filter: ${status}`, 50, 120);
            }
            
            // Summary
            const totalRevenue = bookings.reduce((sum, b) => sum + (b.total_cost || 0), 0);
            doc.text(`Total Bookings: ${bookings.length}`, 50, 140);
            doc.text(`Total Revenue: LKR ${totalRevenue.toLocaleString()}`, 50, 160);
            
            // Table headers
            let yPosition = 200;
            doc.fontSize(8);
            doc.text('ID', 50, yPosition);
            doc.text('Tourist', 80, yPosition);
            doc.text('Driver', 150, yPosition);
            doc.text('Destination', 220, yPosition);
            doc.text('Date', 300, yPosition);
            doc.text('Status', 360, yPosition);
            doc.text('Cost', 420, yPosition);
            
            // Draw line under headers
            doc.moveTo(50, yPosition + 15).lineTo(500, yPosition + 15).stroke();
            
            yPosition += 25;
            
            // Table data
            bookings.forEach((booking) => {
                if (yPosition > 750) {
                    doc.addPage();
                    yPosition = 50;
                }
                
                doc.text(booking.booking_id.toString(), 50, yPosition);
                doc.text(booking.tourist_name || 'N/A', 80, yPosition);
                doc.text(booking.driver_name || 'Unassigned', 150, yPosition);
                doc.text(booking.destination || 'N/A', 220, yPosition);
                doc.text(new Date(booking.start_date).toLocaleDateString(), 300, yPosition);
                doc.text(booking.status, 360, yPosition);
                doc.text(`LKR ${(booking.total_cost || 0).toLocaleString()}`, 420, yPosition);
                
                yPosition += 15;
            });
            
            doc.end();
            */
        } else {
            const totalRevenue = bookings.reduce((sum, b) => sum + (b.total_cost || 0), 0);
            
            res.json(formatResponse(true, 'Bookings report generated successfully', {
                bookings,
                summary: {
                    total: bookings.length,
                    totalRevenue,
                    byStatus: bookings.reduce((acc, b) => {
                        acc[b.status] = (acc[b.status] || 0) + 1;
                        return acc;
                    }, {})
                }
            }));
        }
        
    } catch (error) {
        console.error('Bookings report error:', error);
        res.status(500).json(formatResponse(false, 'Failed to generate bookings report'));
    }
});

module.exports = router;
