const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const {
  createBooking,
  getVehicleCategories,
  getUserBookings
} = require('../controllers/bookingController');

/**
 * Booking Routes
 * Simple and clean implementation for booking management
 */

// Get all vehicle categories (public route)
router.get('/vehicle-categories', getVehicleCategories);

// Create new booking (protected route)
router.post('/create', authenticateToken, createBooking);

// Get user's bookings (protected route)
router.get('/my-bookings', authenticateToken, getUserBookings);

module.exports = router;
