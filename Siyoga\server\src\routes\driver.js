const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const {
  addVehicle,
  getDriverVehicles,
  getAvailableBookings,
  acceptBooking,
  getDriverBookings
} = require('../controllers/driverController');

// All routes require authentication
router.use(authenticateToken);

// Vehicle management
router.post('/vehicles', addVehicle);
router.get('/vehicles', getDriverVehicles);

// Booking management
router.get('/available-bookings', getAvailableBookings);
router.post('/bookings/:bookingId/accept', acceptBooking);
router.get('/bookings', getDriverBookings);

module.exports = router;
