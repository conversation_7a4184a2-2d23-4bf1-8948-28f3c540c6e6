const express = require('express');
const router = express.Router();
const driverController = require('../controllers/driverController');
const { authenticateToken, requireDriver } = require('../middleware/auth');

// All routes require authentication and driver role
router.use(authenticateToken);
router.use(requireDriver);

// Vehicle management routes
router.get('/vehicles', driverController.getDriverVehicles);
router.post('/vehicles', driverController.addVehicle);

// Booking management routes
router.get('/available-bookings', driverController.getAvailableBookings);
router.post('/accept-booking/:bookingId', driverController.acceptBooking);
router.get('/my-bookings', driverController.getMyBookings);

module.exports = router;
