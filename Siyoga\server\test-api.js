// Simple API test script
const axios = require('axios');

const API_BASE = 'http://localhost:5001/api';

// Test data
const testTourist = {
    email: '<EMAIL>',
    password: 'Test123456',
    role: 'tourist',
    firstName: '<PERSON>',
    lastName: 'Doe',
    phone: '**********'
};

const testDriver = {
    email: '<EMAIL>',
    password: 'Driver123456',
    role: 'driver',
    firstName: '<PERSON>',
    lastName: 'Smith',
    phone: '**********'
};

// Test functions
async function testHealthCheck() {
    try {
        console.log('\n🔍 Testing Health Check...');
        const response = await axios.get('http://localhost:5001/health');
        console.log('✅ Health Check:', response.data);
        return true;
    } catch (error) {
        console.log('❌ Health Check failed:', error.message);
        return false;
    }
}

async function testTouristRegistration() {
    try {
        console.log('\n🔍 Testing Tourist Registration...');
        const response = await axios.post(`${API_BASE}/auth/register`, testTourist);
        console.log('✅ Tourist Registration:', response.data);
        return response.data;
    } catch (error) {
        console.log('❌ Tourist Registration failed:', error.response?.data || error.message);
        return null;
    }
}

async function testDriverRegistration() {
    try {
        console.log('\n🔍 Testing Driver Registration...');
        const response = await axios.post(`${API_BASE}/auth/register`, testDriver);
        console.log('✅ Driver Registration:', response.data);
        return response.data;
    } catch (error) {
        console.log('❌ Driver Registration failed:', error.response?.data || error.message);
        return null;
    }
}

async function testLogin(email, password) {
    try {
        console.log(`\n🔍 Testing Login for ${email}...`);
        const response = await axios.post(`${API_BASE}/auth/login`, {
            email,
            password
        });
        console.log('✅ Login successful:', response.data);
        return response.data;
    } catch (error) {
        console.log('❌ Login failed:', error.response?.data || error.message);
        return null;
    }
}

async function testEmailVerification(token) {
    try {
        console.log('\n🔍 Testing Email Verification...');
        const response = await axios.post(`${API_BASE}/auth/verify-email`, { token });
        console.log('✅ Email Verification:', response.data);
        return response.data;
    } catch (error) {
        console.log('❌ Email Verification failed:', error.response?.data || error.message);
        return null;
    }
}

async function testGetUserInfo(token) {
    try {
        console.log('\n🔍 Testing Get User Info...');
        const response = await axios.get(`${API_BASE}/auth/me`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        console.log('✅ User Info:', response.data);
        return response.data;
    } catch (error) {
        console.log('❌ Get User Info failed:', error.response?.data || error.message);
        return null;
    }
}

// Run all tests
async function runTests() {
    console.log('🚀 Starting API Tests...');
    console.log('================================');

    // Test 1: Health Check
    const healthOk = await testHealthCheck();
    if (!healthOk) {
        console.log('❌ Server is not running. Please start the server first.');
        return;
    }

    // Test 2: Tourist Registration
    const touristReg = await testTouristRegistration();

    // Test 3: Driver Registration
    const driverReg = await testDriverRegistration();

    // Test 4: Login before verification (should work but user won't be verified)
    const loginResult = await testLogin(testTourist.email, testTourist.password);

    // Test 5: Email verification (using hardcoded tokens from server console)
    console.log('\n🔍 Testing Email Verification...');
    console.log('📝 Note: Using verification tokens from server console');

    // These tokens are from the server console output
    const touristToken = '2f24f6d8-24af-4f9e-bc45-5f55af4bfb75';
    const driverToken = '38837406-885f-4540-8301-872dc9dc0927';

    await testEmailVerification(touristToken);
    await testEmailVerification(driverToken);

    // Test 6: Login after verification
    const verifiedLogin = await testLogin(testTourist.email, testTourist.password);

    // Test 7: Get user info with token
    if (verifiedLogin && verifiedLogin.data && verifiedLogin.data.token) {
        await testGetUserInfo(verifiedLogin.data.token);
    }

    console.log('\n================================');
    console.log('🎉 API Tests Completed!');
    console.log('\n📝 Summary:');
    console.log('✅ User Registration (Tourist & Driver)');
    console.log('✅ Email Verification');
    console.log('✅ User Login');
    console.log('✅ JWT Token Authentication');
    console.log('✅ Get User Profile');
}

// Run the tests
runTests().catch(console.error);
