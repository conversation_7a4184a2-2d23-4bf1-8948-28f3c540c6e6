// Test complete authentication flow with existing user
const axios = require('axios');

const API_BASE = 'http://localhost:5001/api';

async function testCompleteFlow() {
    try {
        console.log('🚀 Testing Complete Authentication Flow...');
        console.log('================================');

        // <NAME_EMAIL> that was just created
        const userEmail = '<EMAIL>';
        const userPassword = 'NewUser123456';
        const verificationToken = '895c0d55-201b-466d-a224-283a98a3e50f';

        // Step 1: Verify email
        console.log('\n1️⃣ Verifying email...');
        const verifyResponse = await axios.post(`${API_BASE}/auth/verify-email`, {
            token: verificationToken
        });
        console.log('✅ Email verification:', verifyResponse.data);

        // Step 2: Login after verification
        console.log('\n2️⃣ Logging in after verification...');
        const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
            email: userEmail,
            password: userPassword
        });
        console.log('✅ Login successful:', loginResponse.data);

        // Step 3: Get user info with token
        if (loginResponse.data.data && loginResponse.data.data.token) {
            console.log('\n3️⃣ Getting user info with JWT token...');
            const userInfoResponse = await axios.get(`${API_BASE}/auth/me`, {
                headers: {
                    'Authorization': `Bearer ${loginResponse.data.data.token}`
                }
            });
            console.log('✅ User info:', userInfoResponse.data);
        }

        console.log('\n🎉 Complete Authentication Flow Successful!');
        console.log('\n📋 Summary:');
        console.log('✅ User Registration');
        console.log('✅ Email Verification');
        console.log('✅ User Login');
        console.log('✅ JWT Token Authentication');
        console.log('✅ Protected Route Access');
        console.log('✅ User Profile Retrieval');

    } catch (error) {
        console.error('❌ Error:', error.response?.data || error.message);
    }
}

testCompleteFlow();
