// Final test with fixed password field
const axios = require('axios');

const API_BASE = 'http://localhost:5001/api';

async function testFinal() {
    try {
        console.log('🚀 Testing Complete Flow with Fixed Password Field...');
        
        const userEmail = '<EMAIL>';
        const userPassword = 'TestUser123456';
        const verificationToken = 'bff48540-775e-4dbc-a35a-5e9300b5486c';

        // Step 1: Verify email
        console.log('\n1️⃣ Verifying email...');
        const verifyResponse = await axios.post(`${API_BASE}/auth/verify-email`, {
            token: verificationToken
        });
        console.log('✅ Email verification:', verifyResponse.data);

        // Step 2: Login
        console.log('\n2️⃣ Logging in...');
        const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
            email: userEmail,
            password: userPassword
        });
        console.log('✅ Login successful:', loginResponse.data);

        // Step 3: Get user info
        if (loginResponse.data.data && loginResponse.data.data.token) {
            console.log('\n3️⃣ Getting user info...');
            const userInfoResponse = await axios.get(`${API_BASE}/auth/me`, {
                headers: {
                    'Authorization': `Bearer ${loginResponse.data.data.token}`
                }
            });
            console.log('✅ User info:', userInfoResponse.data);
        }

        console.log('\n🎉 ALL TESTS PASSED! Authentication system working perfectly!');

    } catch (error) {
        console.error('❌ Error:', error.response?.data || error.message);
    }
}

testFinal();
