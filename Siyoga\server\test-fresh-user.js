// Test fresh user registration and login
const axios = require('axios');

const API_BASE = 'http://localhost:5001/api';

// Fresh test data
const freshUser = {
    email: '<EMAIL>',
    password: 'NewUser123456',
    role: 'tourist',
    firstName: 'New',
    lastName: 'User',
    phone: '0772222222'
};

async function testFreshUser() {
    try {
        console.log('🚀 Testing Fresh User Registration and Login...');
        
        // Step 1: Register
        console.log('\n1️⃣ Registering fresh user...');
        const regResponse = await axios.post(`${API_BASE}/auth/register`, freshUser);
        console.log('✅ Registration:', regResponse.data);
        
        // Step 2: Try login before verification (should fail)
        console.log('\n2️⃣ Trying login before verification...');
        try {
            const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
                email: freshUser.email,
                password: freshUser.password
            });
            console.log('✅ Login before verification:', loginResponse.data);
        } catch (error) {
            console.log('❌ Login before verification failed (expected):', error.response?.data?.message);
        }
        
        // Step 3: Verify email with token from server console
        console.log('\n3️⃣ Verifying email...');
        const verificationToken = '895c0d55-201b-466d-a224-283a98a3e50f'; // From server console

        const verifyResponse = await axios.post(`${API_BASE}/auth/verify-email`, {
            token: verificationToken
        });
        console.log('✅ Email verification:', verifyResponse.data);

        // Step 4: Login after verification
        console.log('\n4️⃣ Trying login after verification...');
        const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
            email: freshUser.email,
            password: freshUser.password
        });
        console.log('✅ Login after verification:', loginResponse.data);

        // Step 5: Get user info with token
        if (loginResponse.data.data && loginResponse.data.data.token) {
            console.log('\n5️⃣ Getting user info...');
            const userInfoResponse = await axios.get(`${API_BASE}/auth/me`, {
                headers: {
                    'Authorization': `Bearer ${loginResponse.data.data.token}`
                }
            });
            console.log('✅ User info:', userInfoResponse.data);
        }

        console.log('\n🎉 Complete authentication flow successful!');

    } catch (error) {
        console.error('❌ Error:', error.response?.data || error.message);
    }
}

testFreshUser();
