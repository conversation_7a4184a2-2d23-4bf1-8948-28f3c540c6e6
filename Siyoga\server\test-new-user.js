// Test with completely new user after password field fix
const axios = require('axios');

const API_BASE = 'http://localhost:5001/api';

const testUser = {
    email: '<EMAIL>',
    password: 'TestUser123456',
    role: 'tourist',
    firstName: 'Test',
    lastName: 'User2',
    phone: '0773333333'
};

async function testNewUser() {
    try {
        console.log('🚀 Testing New User After Password Field Fix...');
        
        // Step 1: Register
        console.log('\n1️⃣ Registering new user...');
        const regResponse = await axios.post(`${API_BASE}/auth/register`, testUser);
        console.log('✅ Registration:', regResponse.data);
        
        console.log('\n📝 Check server console for verification token...');
        
    } catch (error) {
        console.error('❌ Error:', error.response?.data || error.message);
    }
}

testNewUser();
