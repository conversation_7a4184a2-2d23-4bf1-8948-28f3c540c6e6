const axios = require('axios');

const BASE_URL = 'http://localhost:5001/api';

async function testDriverAPI() {
  try {
    console.log('🧪 Testing Driver API...\n');

    // 1. Login as driver
    console.log('1. Testing driver login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginResponse.data.success) {
      console.log('✅ Driver login successful');
      const token = loginResponse.data.data.token;
      const headers = { 'Authorization': `Bearer ${token}` };

      // 2. Test get vehicle categories
      console.log('\n2. Testing vehicle categories...');
      const categoriesResponse = await axios.get(`${BASE_URL}/bookings/vehicle-categories`);
      console.log('✅ Vehicle categories:', categoriesResponse.data.data.length, 'categories found');

      // 3. Test get driver vehicles (should be empty initially)
      console.log('\n3. Testing get driver vehicles...');
      const vehiclesResponse = await axios.get(`${BASE_URL}/driver/vehicles`, { headers });
      console.log('✅ Driver vehicles:', vehiclesResponse.data.data.length, 'vehicles found');

      // 4. Test add vehicle
      console.log('\n4. Testing add vehicle...');
      const addVehicleResponse = await axios.post(`${BASE_URL}/driver/vehicles`, {
        categoryId: 1, // Cars
        makeModel: 'Toyota Corolla 2020',
        registrationNumber: 'CAR-TEST-001',
        yearManufactured: 2020,
        color: 'White',
        seatingCapacity: 4,
        hasAc: true,
        insuranceExpiry: '2025-12-31'
      }, { headers });

      if (addVehicleResponse.data.success) {
        console.log('✅ Vehicle added successfully');
        
        // 5. Test get vehicles again
        console.log('\n5. Testing get vehicles after adding...');
        const vehiclesResponse2 = await axios.get(`${BASE_URL}/driver/vehicles`, { headers });
        console.log('✅ Driver vehicles:', vehiclesResponse2.data.data.length, 'vehicles found');
        console.log('Vehicle details:', vehiclesResponse2.data.data[0]);
      }

      // 6. Test get available bookings
      console.log('\n6. Testing get available bookings...');
      const availableBookingsResponse = await axios.get(`${BASE_URL}/driver/available-bookings`, { headers });
      console.log('✅ Available bookings:', availableBookingsResponse.data.data.length, 'bookings found');
      
      if (availableBookingsResponse.data.data.length > 0) {
        console.log('First booking:', availableBookingsResponse.data.data[0]);
      }

      // 7. Test get driver bookings
      console.log('\n7. Testing get driver bookings...');
      const driverBookingsResponse = await axios.get(`${BASE_URL}/driver/bookings`, { headers });
      console.log('✅ Driver bookings:', driverBookingsResponse.data.data.length, 'bookings found');

    } else {
      console.log('❌ Driver login failed:', loginResponse.data.message);
    }

  } catch (error) {
    console.error('❌ Error testing driver API:', error.response?.data || error.message);
  }
}

testDriverAPI();
