const axios = require('axios');

async function testDriverLogin() {
  try {
    console.log('🚗 Testing Driver Login and Dashboard...\n');

    // Test driver login
    console.log('1. Testing driver login...');
    const loginResponse = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginResponse.data.success) {
      console.log('✅ Driver login successful');
      console.log('👤 User:', loginResponse.data.data.user.email, '- Role:', loginResponse.data.data.user.role);
      
      const token = loginResponse.data.data.token;
      const headers = { 'Authorization': `Bearer ${token}` };

      // Test get vehicles
      console.log('\n2. Testing get vehicles...');
      const vehiclesResponse = await axios.get('http://localhost:5001/api/driver/vehicles', { headers });
      console.log('✅ Vehicles API working:', vehiclesResponse.data.success);
      console.log('📊 Vehicles count:', vehiclesResponse.data.data.length);

      // Test get available bookings
      console.log('\n3. Testing get available bookings...');
      const bookingsResponse = await axios.get('http://localhost:5001/api/driver/available-bookings', { headers });
      console.log('✅ Available bookings API working:', bookingsResponse.data.success);
      console.log('📊 Available bookings count:', bookingsResponse.data.data.length);

      // Test get my bookings
      console.log('\n4. Testing get my bookings...');
      const myBookingsResponse = await axios.get('http://localhost:5001/api/driver/my-bookings', { headers });
      console.log('✅ My bookings API working:', myBookingsResponse.data.success);
      console.log('📊 My bookings count:', myBookingsResponse.data.data.length);

      console.log('\n🎉 All driver APIs working successfully!');
      console.log('\n📝 Driver Dashboard Ready:');
      console.log('   - Login: ✅');
      console.log('   - Vehicle Management: ✅');
      console.log('   - Available Bookings: ✅');
      console.log('   - My Bookings: ✅');

    } else {
      console.log('❌ Driver login failed:', loginResponse.data.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.message || error.message);
  }
}

testDriverLogin();
