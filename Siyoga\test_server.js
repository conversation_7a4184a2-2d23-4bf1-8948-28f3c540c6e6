// Simple test to check server startup
require('dotenv').config({ path: './server/.env' });

console.log('🔧 Testing server startup...');
console.log('Environment variables:');
console.log('PORT:', process.env.PORT);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_NAME:', process.env.DB_NAME);

// Test database connection
const mysql = require('mysql2/promise');

async function testDB() {
    try {
        const connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: process.env.DB_NAME || 'siyoga_travel_booking'
        });
        
        console.log('✅ Database connection successful');
        await connection.end();
        
        // Now try to start the server
        console.log('🚀 Starting server...');
        require('./server/src/server.js');
        
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
    }
}

testDB();
