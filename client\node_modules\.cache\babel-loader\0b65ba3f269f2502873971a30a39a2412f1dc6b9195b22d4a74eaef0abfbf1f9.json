{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\client\\\\src\\\\elements\\\\Create.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Create() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = Create;\nexport default Create;\nvar _c;\n$RefreshReg$(_c, \"Create\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Create", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/client/src/elements/Create.jsx"], "sourcesContent": ["import React from 'react'\r\n\r\nfunction Create() {\r\n  return (\r\n    <div className='container'>\r\n      \r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Create\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEzB,SAASC,MAAMA,CAAA,EAAG;EAChB,oBACED,OAAA;IAAKE,SAAS,EAAC;EAAW;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAErB,CAAC;AAEV;AAACC,EAAA,GANQN,MAAM;AAQf,eAAeA,MAAM;AAAA,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}