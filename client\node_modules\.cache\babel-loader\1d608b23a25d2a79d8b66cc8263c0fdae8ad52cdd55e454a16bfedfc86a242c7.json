{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\client\\\\src\\\\elements\\\\Create.jsx\",\n  _s2 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from \"axios\";\nimport { Link, useNavigate } from 'react-router-dom'; //navigations within application\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Create() {\n  _s2();\n  const [values, setValues] = useState({\n    name: \"\",\n    email: \"\",\n    gender: \"\",\n    age: \"\"\n  });\n  function handleSubmit(e) {\n    var _s = $RefreshSig$();\n    e.preventDefault();\n    _s(axios.post(\"/add_user\", values).then(_s(res => {\n      _s();\n      //navigate to home after succesfull data si\n      const navigate = useNavigate();\n      navigate(\"/\");\n      console.log(res);\n    }, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n      return [useNavigate];\n    })), \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n      return [useNavigate];\n    }).catch(err => {\n      console.log(err);\n    });\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container vh-100 vw-100 bg-primary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Add Student\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"name\",\n          children: \"Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"name\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            name: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"email\",\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          name: \"email\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            email: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"gender\",\n          children: \"Gender\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"gender\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            gender: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"age\",\n          children: \"Age\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          name: \"age\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            age: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-success\",\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n}\n_s2(Create, \"uyu0u3QVrAJwy75ifa3g8QiSSGc=\");\n_c = Create;\nexport default Create;\nvar _c;\n$RefreshReg$(_c, \"Create\");", "map": {"version": 3, "names": ["React", "useState", "axios", "Link", "useNavigate", "jsxDEV", "_jsxDEV", "Create", "_s2", "values", "set<PERSON><PERSON><PERSON>", "name", "email", "gender", "age", "handleSubmit", "e", "_s", "$RefreshSig$", "preventDefault", "post", "then", "res", "navigate", "console", "log", "catch", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "required", "onChange", "target", "value", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/client/src/elements/Create.jsx"], "sourcesContent": ["import React, { useState } from 'react'\r\nimport axios from \"axios\";\r\nimport {Link, useNavigate} from 'react-router-dom' //navigations within application\r\n\r\nfunction Create() {\r\n  const [values, setValues] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    gender: \"\",\r\n    age: \"\",\r\n  });\r\n\r\n  function handleSubmit(e) {\r\n    e.preventDefault();\r\n    axios\r\n      .post(\"/add_user\", values)\r\n      .then((res) => {\r\n\r\n        //navigate to home after succesfull data si\r\n        const navigate = useNavigate()\r\n        navigate(\"/\")\r\n        console.log(res);\r\n      })\r\n      .catch((err) => {\r\n        console.log(err);\r\n      });\r\n  }\r\n\r\n  return (\r\n    <div className=\"container vh-100 vw-100 bg-primary\">\r\n      <div className=\"row\">\r\n        <h3>Add Student</h3>\r\n\r\n        <form onSubmit={handleSubmit}></form>\r\n        <div className=\"form-group my-3\">\r\n          <label htmlFor=\"name\">Name</label>\r\n          <input\r\n            type=\"text\"\r\n            name=\"name\"\r\n            required\r\n            onChange={(e) => setValues({ ...values, name: e.target.value })}\r\n          />\r\n        </div>\r\n        <div className=\"form-group my-3\">\r\n          <label htmlFor=\"email\">Email</label>\r\n          <input\r\n            type=\"email\"\r\n            name=\"email\"\r\n            required\r\n            onChange={(e) => setValues({ ...values, email: e.target.value })}\r\n          />\r\n        </div>\r\n        <div className=\"form-group my-3\">\r\n          <label htmlFor=\"gender\">Gender</label>\r\n          <input\r\n            type=\"text\"\r\n            name=\"gender\"\r\n            required\r\n            onChange={(e) => setValues({ ...values, gender: e.target.value })}\r\n          />\r\n        </div>\r\n        <div className=\"form-group my-3\">\r\n          <label htmlFor=\"age\">Age</label>\r\n          <input\r\n            type=\"number\"\r\n            name=\"age\"\r\n            required\r\n            onChange={(e) => setValues({ ...values, age: e.target.value })}\r\n          />\r\n        </div>\r\n        <div className=\"form-group my-3\">\r\n          <button type=\"submit\" className=\"btn btn-success\">\r\n            Save\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Create;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAAQC,IAAI,EAAEC,WAAW,QAAO,kBAAkB,EAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,MAAMA,CAAA,EAAG;EAAAC,GAAA;EAChB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC;IACnCU,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,GAAG,EAAE;EACP,CAAC,CAAC;EAEF,SAASC,YAAYA,CAACC,CAAC,EAAE;IAAA,IAAAC,EAAA,GAAAC,YAAA;IACvBF,CAAC,CAACG,cAAc,CAAC,CAAC;IAClBF,EAAA,CAAAf,KAAK,CACFkB,IAAI,CAAC,WAAW,EAAEX,MAAM,CAAC,CACzBY,IAAI,CAAAJ,EAAA,CAAEK,GAAG,IAAK;MAAAL,EAAA;MAEb;MACA,MAAMM,QAAQ,GAAGnB,WAAW,CAAC,CAAC;MAC9BmB,QAAQ,CAAC,GAAG,CAAC;MACbC,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;IAClB,CAAC;MAAA,QAHkBlB,WAAW;IAAA,EAG7B,CAAC;MAAA,QAHiBA,WAAW;IAAA,GAI7BsB,KAAK,CAAEC,GAAG,IAAK;MACdH,OAAO,CAACC,GAAG,CAACE,GAAG,CAAC;IAClB,CAAC,CAAC;EACN;EAEA,oBACErB,OAAA;IAAKsB,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eACjDvB,OAAA;MAAKsB,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClBvB,OAAA;QAAAuB,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEpB3B,OAAA;QAAM4B,QAAQ,EAAEnB;MAAa;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrC3B,OAAA;QAAKsB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BvB,OAAA;UAAO6B,OAAO,EAAC,MAAM;UAAAN,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClC3B,OAAA;UACE8B,IAAI,EAAC,MAAM;UACXzB,IAAI,EAAC,MAAM;UACX0B,QAAQ;UACRC,QAAQ,EAAGtB,CAAC,IAAKN,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAEE,IAAI,EAAEK,CAAC,CAACuB,MAAM,CAACC;UAAM,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN3B,OAAA;QAAKsB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BvB,OAAA;UAAO6B,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpC3B,OAAA;UACE8B,IAAI,EAAC,OAAO;UACZzB,IAAI,EAAC,OAAO;UACZ0B,QAAQ;UACRC,QAAQ,EAAGtB,CAAC,IAAKN,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAEG,KAAK,EAAEI,CAAC,CAACuB,MAAM,CAACC;UAAM,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN3B,OAAA;QAAKsB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BvB,OAAA;UAAO6B,OAAO,EAAC,QAAQ;UAAAN,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtC3B,OAAA;UACE8B,IAAI,EAAC,MAAM;UACXzB,IAAI,EAAC,QAAQ;UACb0B,QAAQ;UACRC,QAAQ,EAAGtB,CAAC,IAAKN,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAEI,MAAM,EAAEG,CAAC,CAACuB,MAAM,CAACC;UAAM,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN3B,OAAA;QAAKsB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BvB,OAAA;UAAO6B,OAAO,EAAC,KAAK;UAAAN,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChC3B,OAAA;UACE8B,IAAI,EAAC,QAAQ;UACbzB,IAAI,EAAC,KAAK;UACV0B,QAAQ;UACRC,QAAQ,EAAGtB,CAAC,IAAKN,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAEK,GAAG,EAAEE,CAAC,CAACuB,MAAM,CAACC;UAAM,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN3B,OAAA;QAAKsB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BvB,OAAA;UAAQ8B,IAAI,EAAC,QAAQ;UAACR,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACzB,GAAA,CA1EQD,MAAM;AAAAkC,EAAA,GAANlC,MAAM;AA4Ef,eAAeA,MAAM;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}