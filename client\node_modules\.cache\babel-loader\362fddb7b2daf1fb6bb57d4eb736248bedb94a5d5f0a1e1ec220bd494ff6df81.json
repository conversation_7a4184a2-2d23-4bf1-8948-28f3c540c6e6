{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\client\\\\src\\\\elements\\\\Create.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Create() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container vh-100 vw-100 bg-primary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Add Student\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          class: \"btn btn-success\",\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"name\",\n            children: \"Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              name: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              email: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"gender\",\n            children: \"Gender\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"gender\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              gender: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"age\",\n            children: \"Age\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            name: \"age\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              age: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-success\",\n            children: \"Save\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 4\n  }, this);\n}\n_c = Create;\nexport default Create;\nvar _c;\n$RefreshReg$(_c, \"Create\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Create", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Link", "to", "class", "onSubmit", "handleSubmit", "htmlFor", "type", "name", "required", "onChange", "e", "set<PERSON><PERSON><PERSON>", "values", "target", "value", "email", "gender", "age", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/client/src/elements/Create.jsx"], "sourcesContent": ["import React from 'react'\r\n\r\nfunction Create() {\r\n  return (\r\n   <div className='container vh-100 vw-100 bg-primary'>\r\n        <div className='row'>\r\n            <h3>Add Student</h3>\r\n            <div className='d-flex justify-content-end'>\r\n                <Link to='/' class='btn btn-success'>Home</Link>\r\n            </div>\r\n            <form onSubmit={handleSubmit}>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='name'>Name</label>\r\n                    <input type='text' name='name' required onChange={(e)=> setValues({...values, name: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='email'>Email</label>\r\n                    <input type='email' name='email' required onChange={(e)=> setValues({...values, email: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='gender'>Gender</label>\r\n                    <input type='text' name='gender' required onChange={(e)=> setValues({...values, gender: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='age'>Age</label>\r\n                    <input type='number' name='age' required onChange={(e)=> setValues({...values, age: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <button type='submit' className='btn btn-success'>Save</button>\r\n                </div>\r\n            </form>\r\n        </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Create\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEzB,SAASC,MAAMA,CAAA,EAAG;EAChB,oBACCD,OAAA;IAAKE,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eAC9CH,OAAA;MAAKE,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAChBH,OAAA;QAAAG,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpBP,OAAA;QAAKE,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACvCH,OAAA,CAACQ,IAAI;UAACC,EAAE,EAAC,GAAG;UAACC,KAAK,EAAC,iBAAiB;UAAAP,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACNP,OAAA;QAAMW,QAAQ,EAAEC,YAAa;QAAAT,QAAA,gBACzBH,OAAA;UAAKE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BH,OAAA;YAAOa,OAAO,EAAC,MAAM;YAAAV,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClCP,OAAA;YAAOc,IAAI,EAAC,MAAM;YAACC,IAAI,EAAC,MAAM;YAACC,QAAQ;YAACC,QAAQ,EAAGC,CAAC,IAAIC,SAAS,CAAC;cAAC,GAAGC,MAAM;cAAEL,IAAI,EAAEG,CAAC,CAACG,MAAM,CAACC;YAAK,CAAC;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvG,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BH,OAAA;YAAOa,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpCP,OAAA;YAAOc,IAAI,EAAC,OAAO;YAACC,IAAI,EAAC,OAAO;YAACC,QAAQ;YAACC,QAAQ,EAAGC,CAAC,IAAIC,SAAS,CAAC;cAAC,GAAGC,MAAM;cAAEG,KAAK,EAAEL,CAAC,CAACG,MAAM,CAACC;YAAK,CAAC;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1G,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BH,OAAA;YAAOa,OAAO,EAAC,QAAQ;YAAAV,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtCP,OAAA;YAAOc,IAAI,EAAC,MAAM;YAACC,IAAI,EAAC,QAAQ;YAACC,QAAQ;YAACC,QAAQ,EAAGC,CAAC,IAAIC,SAAS,CAAC;cAAC,GAAGC,MAAM;cAAEI,MAAM,EAAEN,CAAC,CAACG,MAAM,CAACC;YAAK,CAAC;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3G,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BH,OAAA;YAAOa,OAAO,EAAC,KAAK;YAAAV,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChCP,OAAA;YAAOc,IAAI,EAAC,QAAQ;YAACC,IAAI,EAAC,KAAK;YAACC,QAAQ;YAACC,QAAQ,EAAGC,CAAC,IAAIC,SAAS,CAAC;cAAC,GAAGC,MAAM;cAAEK,GAAG,EAAEP,CAAC,CAACG,MAAM,CAACC;YAAK,CAAC;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvG,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC5BH,OAAA;YAAQc,IAAI,EAAC,QAAQ;YAACZ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV;AAACmB,EAAA,GAhCQzB,MAAM;AAkCf,eAAeA,MAAM;AAAA,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}