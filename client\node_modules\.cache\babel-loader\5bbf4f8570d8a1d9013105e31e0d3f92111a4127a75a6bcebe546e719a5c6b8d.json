{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\client\\\\src\\\\elements\\\\Create.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Create() {\n  _s();\n  const [values, setValues] = React.useState({\n    name: '',\n    email: '',\n    gender: '',\n    age: ''\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container vh-100 vw-100 bg-primary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Add Student\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"name\",\n          children: \"Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"name\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            name: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"email\",\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          name: \"email\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            email: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"gender\",\n          children: \"Gender\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"gender\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            gender: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"age\",\n          children: \"Age\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          name: \"age\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            age: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-success\",\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 4\n  }, this);\n}\n_s(Create, \"0sadZc7/J2Tog1f8EFi9PwGN6Xs=\");\n_c = Create;\nexport default Create;\nvar _c;\n$RefreshReg$(_c, \"Create\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Create", "_s", "values", "set<PERSON><PERSON><PERSON>", "useState", "name", "email", "gender", "age", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "type", "required", "onChange", "e", "target", "value", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/client/src/elements/Create.jsx"], "sourcesContent": ["import React from 'react'\r\n\r\nfunction Create() {\r\n    const [values, setValues] = React.useState({\r\n        name: '',\r\n        email: '',\r\n        gender: '',\r\n        age: ''\r\n    })\r\n  return (\r\n   <div className='container vh-100 vw-100 bg-primary'>\r\n        <div className='row'>\r\n            <h3>Add Student</h3>\r\n           \r\n           \r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='name'>Name</label>\r\n                    <input type='text' name='name' required onChange={(e)=> setValues({...values, name: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='email'>Email</label>\r\n                    <input type='email' name='email' required onChange={(e)=> setValues({...values, email: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='gender'>Gender</label>\r\n                    <input type='text' name='gender' required onChange={(e)=> setValues({...values, gender: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='age'>Age</label>\r\n                    <input type='number' name='age' required onChange={(e)=> setValues({...values, age: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <button type='submit' className='btn btn-success'>Save</button>\r\n                </div>\r\n            \r\n    </div>\r\n   </div>\r\n  )\r\n}\r\n\r\nexport default Create\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEzB,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EACd,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGN,KAAK,CAACO,QAAQ,CAAC;IACvCC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,GAAG,EAAE;EACT,CAAC,CAAC;EACJ,oBACCT,OAAA;IAAKU,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eAC9CX,OAAA;MAAKU,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAChBX,OAAA;QAAAW,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAGhBf,OAAA;QAAKU,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BX,OAAA;UAAOgB,OAAO,EAAC,MAAM;UAAAL,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClCf,OAAA;UAAOiB,IAAI,EAAC,MAAM;UAACX,IAAI,EAAC,MAAM;UAACY,QAAQ;UAACC,QAAQ,EAAGC,CAAC,IAAIhB,SAAS,CAAC;YAAC,GAAGD,MAAM;YAAEG,IAAI,EAAEc,CAAC,CAACC,MAAM,CAACC;UAAK,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvG,CAAC,eACNf,OAAA;QAAKU,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BX,OAAA;UAAOgB,OAAO,EAAC,OAAO;UAAAL,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpCf,OAAA;UAAOiB,IAAI,EAAC,OAAO;UAACX,IAAI,EAAC,OAAO;UAACY,QAAQ;UAACC,QAAQ,EAAGC,CAAC,IAAIhB,SAAS,CAAC;YAAC,GAAGD,MAAM;YAAEI,KAAK,EAAEa,CAAC,CAACC,MAAM,CAACC;UAAK,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G,CAAC,eACNf,OAAA;QAAKU,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BX,OAAA;UAAOgB,OAAO,EAAC,QAAQ;UAAAL,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtCf,OAAA;UAAOiB,IAAI,EAAC,MAAM;UAACX,IAAI,EAAC,QAAQ;UAACY,QAAQ;UAACC,QAAQ,EAAGC,CAAC,IAAIhB,SAAS,CAAC;YAAC,GAAGD,MAAM;YAAEK,MAAM,EAAEY,CAAC,CAACC,MAAM,CAACC;UAAK,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3G,CAAC,eACNf,OAAA;QAAKU,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BX,OAAA;UAAOgB,OAAO,EAAC,KAAK;UAAAL,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChCf,OAAA;UAAOiB,IAAI,EAAC,QAAQ;UAACX,IAAI,EAAC,KAAK;UAACY,QAAQ;UAACC,QAAQ,EAAGC,CAAC,IAAIhB,SAAS,CAAC;YAAC,GAAGD,MAAM;YAAEM,GAAG,EAAEW,CAAC,CAACC,MAAM,CAACC;UAAK,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvG,CAAC,eACNf,OAAA;QAAKU,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC5BX,OAAA;UAAQiB,IAAI,EAAC,QAAQ;UAACP,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAET;AAACb,EAAA,CApCQD,MAAM;AAAAsB,EAAA,GAANtB,MAAM;AAsCf,eAAeA,MAAM;AAAA,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}