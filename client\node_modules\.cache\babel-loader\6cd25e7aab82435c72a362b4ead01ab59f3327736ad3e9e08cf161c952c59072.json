{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\client\\\\src\\\\elements\\\\Create.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Create() {\n  _s();\n  // eslint-disable-next-line no-undef\n  const [values, setValues] = useState({\n    name: '',\n    email: '',\n    gender: '',\n    age: ''\n  });\n  function handleSubmit(e) {\n    e.preventDefault();\n    axios.post('/add_user', values).then(res => {\n      console.log(res);\n    }).catch(err => {\n      console.log(err);\n    });\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container vh-100 vw-100 bg-primary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Add Student\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 12\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"name\",\n          children: \"Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"name\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            name: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"email\",\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          name: \"email\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            email: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"gender\",\n          children: \"Gender\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"gender\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            gender: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"age\",\n          children: \"Age\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          name: \"age\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            age: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-success\",\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 4\n  }, this);\n}\n_s(Create, \"0sadZc7/J2Tog1f8EFi9PwGN6Xs=\");\n_c = Create;\nexport default Create;\nvar _c;\n$RefreshReg$(_c, \"Create\");", "map": {"version": 3, "names": ["React", "useState", "axios", "jsxDEV", "_jsxDEV", "Create", "_s", "values", "set<PERSON><PERSON><PERSON>", "name", "email", "gender", "age", "handleSubmit", "e", "preventDefault", "post", "then", "res", "console", "log", "catch", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "required", "onChange", "target", "value", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/client/src/elements/Create.jsx"], "sourcesContent": ["import React, { useState } from 'react'\r\nimport axios from 'axios'\r\n\r\nfunction Create() {\r\n    // eslint-disable-next-line no-undef\r\n    const [values, setValues] = useState({\r\n        name: '',\r\n        email: '',\r\n        gender: '',\r\n        age: ''\r\n    })\r\n\r\n    function handleSubmit(e){\r\n\r\ne.preventDefault()\r\naxios.post('/add_user', values)\r\n.then(res => {\r\n    console.log(res)\r\n   \r\n})\r\n.catch((err) => {\r\n    console.log(err)\r\n})\r\n    }\r\n\r\n\r\n  \r\n  return (\r\n   <div className='container vh-100 vw-100 bg-primary'>\r\n        <div className='row'>\r\n            <h3>Add Student</h3>\r\n           \r\n           <form onSubmit={handleSubmit}></form>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='name'>Name</label>\r\n                    <input type='text' name='name' required onChange={(e)=> setValues({...values, name: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='email'>Email</label>\r\n                    <input type='email' name='email' required onChange={(e)=> setValues({...values, email: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='gender'>Gender</label>\r\n                    <input type='text' name='gender' required onChange={(e)=> setValues({...values, gender: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='age'>Age</label>\r\n                    <input type='number' name='age' required onChange={(e)=> setValues({...values, age: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <button type='submit' className='btn btn-success'>Save</button>\r\n                </div>\r\n            \r\n    </div>\r\n   </div>\r\n  )\r\n}\r\n\r\nexport default Create\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEzB,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EACd;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGP,QAAQ,CAAC;IACjCQ,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,GAAG,EAAE;EACT,CAAC,CAAC;EAEF,SAASC,YAAYA,CAACC,CAAC,EAAC;IAE5BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBb,KAAK,CAACc,IAAI,CAAC,WAAW,EAAET,MAAM,CAAC,CAC9BU,IAAI,CAACC,GAAG,IAAI;MACTC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;IAEpB,CAAC,CAAC,CACDG,KAAK,CAAEC,GAAG,IAAK;MACZH,OAAO,CAACC,GAAG,CAACE,GAAG,CAAC;IACpB,CAAC,CAAC;EACE;EAIF,oBACClB,OAAA;IAAKmB,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eAC9CpB,OAAA;MAAKmB,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAChBpB,OAAA;QAAAoB,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAErBxB,OAAA;QAAMyB,QAAQ,EAAEhB;MAAa;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAChCxB,OAAA;QAAKmB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BpB,OAAA;UAAO0B,OAAO,EAAC,MAAM;UAAAN,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClCxB,OAAA;UAAO2B,IAAI,EAAC,MAAM;UAACtB,IAAI,EAAC,MAAM;UAACuB,QAAQ;UAACC,QAAQ,EAAGnB,CAAC,IAAIN,SAAS,CAAC;YAAC,GAAGD,MAAM;YAAEE,IAAI,EAAEK,CAAC,CAACoB,MAAM,CAACC;UAAK,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvG,CAAC,eACNxB,OAAA;QAAKmB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BpB,OAAA;UAAO0B,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpCxB,OAAA;UAAO2B,IAAI,EAAC,OAAO;UAACtB,IAAI,EAAC,OAAO;UAACuB,QAAQ;UAACC,QAAQ,EAAGnB,CAAC,IAAIN,SAAS,CAAC;YAAC,GAAGD,MAAM;YAAEG,KAAK,EAAEI,CAAC,CAACoB,MAAM,CAACC;UAAK,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G,CAAC,eACNxB,OAAA;QAAKmB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BpB,OAAA;UAAO0B,OAAO,EAAC,QAAQ;UAAAN,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtCxB,OAAA;UAAO2B,IAAI,EAAC,MAAM;UAACtB,IAAI,EAAC,QAAQ;UAACuB,QAAQ;UAACC,QAAQ,EAAGnB,CAAC,IAAIN,SAAS,CAAC;YAAC,GAAGD,MAAM;YAAEI,MAAM,EAAEG,CAAC,CAACoB,MAAM,CAACC;UAAK,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3G,CAAC,eACNxB,OAAA;QAAKmB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BpB,OAAA;UAAO0B,OAAO,EAAC,KAAK;UAAAN,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChCxB,OAAA;UAAO2B,IAAI,EAAC,QAAQ;UAACtB,IAAI,EAAC,KAAK;UAACuB,QAAQ;UAACC,QAAQ,EAAGnB,CAAC,IAAIN,SAAS,CAAC;YAAC,GAAGD,MAAM;YAAEK,GAAG,EAAEE,CAAC,CAACoB,MAAM,CAACC;UAAK,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvG,CAAC,eACNxB,OAAA;QAAKmB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC5BpB,OAAA;UAAQ2B,IAAI,EAAC,QAAQ;UAACR,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAET;AAACtB,EAAA,CArDQD,MAAM;AAAA+B,EAAA,GAAN/B,MAAM;AAuDf,eAAeA,MAAM;AAAA,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}