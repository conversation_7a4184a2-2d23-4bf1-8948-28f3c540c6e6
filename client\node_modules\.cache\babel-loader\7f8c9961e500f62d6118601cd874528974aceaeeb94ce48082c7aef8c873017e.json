{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\client\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter, Routes, Route } from 'react-router-dom';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport './App.css';\nimport Home from '.elements/Home';\nimport Edit from './elements/Edit';\nimport Read from './elements/Read';\nimport Create from './elements/Create';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 32\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/edit/:id\",\n        element: /*#__PURE__*/_jsxDEV(Edit, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 40\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/read/:id\",\n        element: /*#__PURE__*/_jsxDEV(Read, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 40\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/create\",\n        element: /*#__PURE__*/_jsxDEV(Create, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 4\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Home", "Edit", "Read", "Create", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/client/src/App.js"], "sourcesContent": ["import React from 'react'\nimport {<PERSON>rowserRouter, Routes, Route} from 'react-router-dom'\nimport 'bootstrap/dist/css/bootstrap.min.css'\nimport './App.css'\n\nimport Home from '.elements/Home'\nimport Edit from './elements/Edit'\nimport Read from './elements/Read'\nimport Create from './elements/Create'\n\nfunction App() {\n  return (\n   <BrowserRouter>\n    <Routes>\n      <Route path='/' element={<Home/>}/>\n      <Route path='/edit/:id' element={<Edit/>}/>\n      <Route path='/read/:id' element={<Read/>}/>\n      <Route path='/create' element={<Create/>}/>\n    </Routes>\n   </BrowserRouter>\n\n\n  )\n}\n\nexport default App\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAAQC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAO,kBAAkB;AAC7D,OAAO,sCAAsC;AAC7C,OAAO,WAAW;AAElB,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,MAAM,MAAM,mBAAmB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACCD,OAAA,CAACR,aAAa;IAAAU,QAAA,eACbF,OAAA,CAACP,MAAM;MAAAS,QAAA,gBACLF,OAAA,CAACN,KAAK;QAACS,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEJ,OAAA,CAACL,IAAI;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACnCR,OAAA,CAACN,KAAK;QAACS,IAAI,EAAC,WAAW;QAACC,OAAO,eAAEJ,OAAA,CAACJ,IAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eAC3CR,OAAA,CAACN,KAAK;QAACS,IAAI,EAAC,WAAW;QAACC,OAAO,eAAEJ,OAAA,CAACH,IAAI;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eAC3CR,OAAA,CAACN,KAAK;QAACS,IAAI,EAAC,SAAS;QAACC,OAAO,eAAEJ,OAAA,CAACF,MAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAInB;AAACC,EAAA,GAbQR,GAAG;AAeZ,eAAeA,GAAG;AAAA,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}