{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\client\\\\src\\\\elements\\\\Create.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Create() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container vh-100 vw-100 bg-primary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Add Student\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"name\",\n          children: \"Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"name\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            name: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"email\",\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          name: \"email\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            email: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"gender\",\n          children: \"Gender\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"gender\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            gender: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"age\",\n          children: \"Age\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          name: \"age\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            age: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-success\",\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 4\n  }, this);\n}\n_c = Create;\nexport default Create;\nvar _c;\n$RefreshReg$(_c, \"Create\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Create", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "type", "name", "required", "onChange", "e", "set<PERSON><PERSON><PERSON>", "values", "target", "value", "email", "gender", "age", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/client/src/elements/Create.jsx"], "sourcesContent": ["import React from 'react'\r\n\r\nfunction Create() {\r\n    \r\n  return (\r\n   <div className='container vh-100 vw-100 bg-primary'>\r\n        <div className='row'>\r\n            <h3>Add Student</h3>\r\n           \r\n           \r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='name'>Name</label>\r\n                    <input type='text' name='name' required onChange={(e)=> setValues({...values, name: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='email'>Email</label>\r\n                    <input type='email' name='email' required onChange={(e)=> setValues({...values, email: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='gender'>Gender</label>\r\n                    <input type='text' name='gender' required onChange={(e)=> setValues({...values, gender: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='age'>Age</label>\r\n                    <input type='number' name='age' required onChange={(e)=> setValues({...values, age: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <button type='submit' className='btn btn-success'>Save</button>\r\n                </div>\r\n            \r\n    </div>\r\n   </div>\r\n  )\r\n}\r\n\r\nexport default Create\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEzB,SAASC,MAAMA,CAAA,EAAG;EAEhB,oBACCD,OAAA;IAAKE,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eAC9CH,OAAA;MAAKE,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAChBH,OAAA;QAAAG,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAGhBP,OAAA;QAAKE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BH,OAAA;UAAOQ,OAAO,EAAC,MAAM;UAAAL,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClCP,OAAA;UAAOS,IAAI,EAAC,MAAM;UAACC,IAAI,EAAC,MAAM;UAACC,QAAQ;UAACC,QAAQ,EAAGC,CAAC,IAAIC,SAAS,CAAC;YAAC,GAAGC,MAAM;YAAEL,IAAI,EAAEG,CAAC,CAACG,MAAM,CAACC;UAAK,CAAC;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvG,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BH,OAAA;UAAOQ,OAAO,EAAC,OAAO;UAAAL,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpCP,OAAA;UAAOS,IAAI,EAAC,OAAO;UAACC,IAAI,EAAC,OAAO;UAACC,QAAQ;UAACC,QAAQ,EAAGC,CAAC,IAAIC,SAAS,CAAC;YAAC,GAAGC,MAAM;YAAEG,KAAK,EAAEL,CAAC,CAACG,MAAM,CAACC;UAAK,CAAC;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BH,OAAA;UAAOQ,OAAO,EAAC,QAAQ;UAAAL,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtCP,OAAA;UAAOS,IAAI,EAAC,MAAM;UAACC,IAAI,EAAC,QAAQ;UAACC,QAAQ;UAACC,QAAQ,EAAGC,CAAC,IAAIC,SAAS,CAAC;YAAC,GAAGC,MAAM;YAAEI,MAAM,EAAEN,CAAC,CAACG,MAAM,CAACC;UAAK,CAAC;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3G,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BH,OAAA;UAAOQ,OAAO,EAAC,KAAK;UAAAL,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChCP,OAAA;UAAOS,IAAI,EAAC,QAAQ;UAACC,IAAI,EAAC,KAAK;UAACC,QAAQ;UAACC,QAAQ,EAAGC,CAAC,IAAIC,SAAS,CAAC;YAAC,GAAGC,MAAM;YAAEK,GAAG,EAAEP,CAAC,CAACG,MAAM,CAACC;UAAK,CAAC;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvG,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC5BH,OAAA;UAAQS,IAAI,EAAC,QAAQ;UAACP,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAET;AAACc,EAAA,GA/BQpB,MAAM;AAiCf,eAAeA,MAAM;AAAA,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}