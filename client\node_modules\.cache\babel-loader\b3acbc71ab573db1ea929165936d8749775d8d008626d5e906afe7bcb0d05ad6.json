{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\client\\\\src\\\\elements\\\\Create.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from \"axios\";\nimport { Link, useNavigate } from 'react-router-dom'; //navigations within application\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Create() {\n  _s();\n  const [values, setValues] = useState({\n    name: \"\",\n    email: \"\",\n    gender: \"\",\n    age: \"\"\n  });\n  function handleSubmit(e) {\n    e.preventDefault();\n    axios.post(\"/add_user\", values).then(res => {\n      const navigate = console.log(res);\n    }).catch(err => {\n      console.log(err);\n    });\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container vh-100 vw-100 bg-primary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Add Student\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"name\",\n          children: \"Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"name\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            name: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"email\",\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          name: \"email\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            email: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"gender\",\n          children: \"Gender\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"gender\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            gender: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"age\",\n          children: \"Age\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          name: \"age\",\n          required: true,\n          onChange: e => setValues({\n            ...values,\n            age: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group my-3\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-success\",\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n}\n_s(Create, \"uyu0u3QVrAJwy75ifa3g8QiSSGc=\");\n_c = Create;\nexport default Create;\nvar _c;\n$RefreshReg$(_c, \"Create\");", "map": {"version": 3, "names": ["React", "useState", "axios", "Link", "useNavigate", "jsxDEV", "_jsxDEV", "Create", "_s", "values", "set<PERSON><PERSON><PERSON>", "name", "email", "gender", "age", "handleSubmit", "e", "preventDefault", "post", "then", "res", "navigate", "console", "log", "catch", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "required", "onChange", "target", "value", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/client/src/elements/Create.jsx"], "sourcesContent": ["import React, { useState } from 'react'\r\nimport axios from \"axios\";\r\nimport {Link, useNavigate} from 'react-router-dom' //navigations within application\r\n\r\nfunction Create() {\r\n  const [values, setValues] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    gender: \"\",\r\n    age: \"\",\r\n  });\r\n\r\n  function handleSubmit(e) {\r\n    e.preventDefault();\r\n    axios\r\n      .post(\"/add_user\", values)\r\n      .then((res) => {\r\n        const navigate = \r\n        console.log(res);\r\n      })\r\n      .catch((err) => {\r\n        console.log(err);\r\n      });\r\n  }\r\n\r\n  return (\r\n    <div className=\"container vh-100 vw-100 bg-primary\">\r\n      <div className=\"row\">\r\n        <h3>Add Student</h3>\r\n\r\n        <form onSubmit={handleSubmit}></form>\r\n        <div className=\"form-group my-3\">\r\n          <label htmlFor=\"name\">Name</label>\r\n          <input\r\n            type=\"text\"\r\n            name=\"name\"\r\n            required\r\n            onChange={(e) => setValues({ ...values, name: e.target.value })}\r\n          />\r\n        </div>\r\n        <div className=\"form-group my-3\">\r\n          <label htmlFor=\"email\">Email</label>\r\n          <input\r\n            type=\"email\"\r\n            name=\"email\"\r\n            required\r\n            onChange={(e) => setValues({ ...values, email: e.target.value })}\r\n          />\r\n        </div>\r\n        <div className=\"form-group my-3\">\r\n          <label htmlFor=\"gender\">Gender</label>\r\n          <input\r\n            type=\"text\"\r\n            name=\"gender\"\r\n            required\r\n            onChange={(e) => setValues({ ...values, gender: e.target.value })}\r\n          />\r\n        </div>\r\n        <div className=\"form-group my-3\">\r\n          <label htmlFor=\"age\">Age</label>\r\n          <input\r\n            type=\"number\"\r\n            name=\"age\"\r\n            required\r\n            onChange={(e) => setValues({ ...values, age: e.target.value })}\r\n          />\r\n        </div>\r\n        <div className=\"form-group my-3\">\r\n          <button type=\"submit\" className=\"btn btn-success\">\r\n            Save\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Create;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAAQC,IAAI,EAAEC,WAAW,QAAO,kBAAkB,EAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC;IACnCU,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,GAAG,EAAE;EACP,CAAC,CAAC;EAEF,SAASC,YAAYA,CAACC,CAAC,EAAE;IACvBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBf,KAAK,CACFgB,IAAI,CAAC,WAAW,EAAET,MAAM,CAAC,CACzBU,IAAI,CAAEC,GAAG,IAAK;MACb,MAAMC,QAAQ,GACdC,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;IAClB,CAAC,CAAC,CACDI,KAAK,CAAEC,GAAG,IAAK;MACdH,OAAO,CAACC,GAAG,CAACE,GAAG,CAAC;IAClB,CAAC,CAAC;EACN;EAEA,oBACEnB,OAAA;IAAKoB,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eACjDrB,OAAA;MAAKoB,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClBrB,OAAA;QAAAqB,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEpBzB,OAAA;QAAM0B,QAAQ,EAAEjB;MAAa;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrCzB,OAAA;QAAKoB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrB,OAAA;UAAO2B,OAAO,EAAC,MAAM;UAAAN,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClCzB,OAAA;UACE4B,IAAI,EAAC,MAAM;UACXvB,IAAI,EAAC,MAAM;UACXwB,QAAQ;UACRC,QAAQ,EAAGpB,CAAC,IAAKN,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAEE,IAAI,EAAEK,CAAC,CAACqB,MAAM,CAACC;UAAM,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNzB,OAAA;QAAKoB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrB,OAAA;UAAO2B,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpCzB,OAAA;UACE4B,IAAI,EAAC,OAAO;UACZvB,IAAI,EAAC,OAAO;UACZwB,QAAQ;UACRC,QAAQ,EAAGpB,CAAC,IAAKN,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAEG,KAAK,EAAEI,CAAC,CAACqB,MAAM,CAACC;UAAM,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNzB,OAAA;QAAKoB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrB,OAAA;UAAO2B,OAAO,EAAC,QAAQ;UAAAN,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtCzB,OAAA;UACE4B,IAAI,EAAC,MAAM;UACXvB,IAAI,EAAC,QAAQ;UACbwB,QAAQ;UACRC,QAAQ,EAAGpB,CAAC,IAAKN,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAEI,MAAM,EAAEG,CAAC,CAACqB,MAAM,CAACC;UAAM,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNzB,OAAA;QAAKoB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrB,OAAA;UAAO2B,OAAO,EAAC,KAAK;UAAAN,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChCzB,OAAA;UACE4B,IAAI,EAAC,QAAQ;UACbvB,IAAI,EAAC,KAAK;UACVwB,QAAQ;UACRC,QAAQ,EAAGpB,CAAC,IAAKN,SAAS,CAAC;YAAE,GAAGD,MAAM;YAAEK,GAAG,EAAEE,CAAC,CAACqB,MAAM,CAACC;UAAM,CAAC;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNzB,OAAA;QAAKoB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BrB,OAAA;UAAQ4B,IAAI,EAAC,QAAQ;UAACR,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvB,EAAA,CAvEQD,MAAM;AAAAgC,EAAA,GAANhC,MAAM;AAyEf,eAAeA,MAAM;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}