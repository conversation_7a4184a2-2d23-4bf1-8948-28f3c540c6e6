{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\client\\\\src\\\\elements\\\\Create.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Create() {\n  _s();\n  const [values, setValues] = useState({\n    name: '',\n    email: '',\n    age: '',\n    gender: ''\n  });\n  const navigate = useNavigate();\n  function handleSubmit(e) {\n    e.preventDefault();\n    axios.post('/add_user', values).then(res => {\n      navigate('/');\n      console.log(res);\n    }).catch(err => console.log(err));\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container vh-100 vw-100 bg-primary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Add Student\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"btn btn-success\",\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"name\",\n            children: \"Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              name: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              email: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"gender\",\n            children: \"Gender\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"gender\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              gender: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"age\",\n            children: \"Age\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            name: \"age\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              age: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-success\",\n            children: \"Save\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n}\n_s(Create, \"oSC9tnkofyENRCbjy4y+RM0BIdU=\", false, function () {\n  return [useNavigate];\n});\n_c = Create;\nexport default Create;\nvar _c;\n$RefreshReg$(_c, \"Create\");", "map": {"version": 3, "names": ["React", "useState", "axios", "Link", "useNavigate", "jsxDEV", "_jsxDEV", "Create", "_s", "values", "set<PERSON><PERSON><PERSON>", "name", "email", "age", "gender", "navigate", "handleSubmit", "e", "preventDefault", "post", "then", "res", "console", "log", "catch", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onSubmit", "htmlFor", "type", "required", "onChange", "target", "value", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/client/src/elements/Create.jsx"], "sourcesContent": ["import React, { useState } from 'react'\r\nimport axios from 'axios'\r\nimport {Link, useNavigate} from 'react-router-dom'\r\n\r\nfunction Create() {\r\n    const [values, setValues] = useState({\r\n        name: '',\r\n        email: '',\r\n        age: '',\r\n        gender: ''\r\n    })\r\n\r\n    const navigate = useNavigate()\r\n\r\n    function handleSubmit(e){\r\n        e.preventDefault()\r\n\r\n        axios.post('/add_user', values)\r\n        .then((res)=>{\r\n            \r\n            navigate('/')\r\n            console.log(res)\r\n        })\r\n        .catch((err)=>console.log(err))\r\n    }\r\n  return (\r\n    <div className='container vh-100 vw-100 bg-primary'>\r\n        <div className='row'>\r\n            <h3>Add Student</h3>\r\n            <div className='d-flex justify-content-end'>\r\n                <Link to='/' className='btn btn-success'>Home</Link>\r\n            </div>\r\n            <form onSubmit={handleSubmit}>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='name'>Name</label>\r\n                    <input type='text' name='name' required onChange={(e)=> setValues({...values, name: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='email'>Email</label>\r\n                    <input type='email' name='email' required onChange={(e)=> setValues({...values, email: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='gender'>Gender</label>\r\n                    <input type='text' name='gender' required onChange={(e)=> setValues({...values, gender: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='age'>Age</label>\r\n                    <input type='number' name='age' required onChange={(e)=> setValues({...values, age: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <button type='submit' className='btn btn-success'>Save</button>\r\n                </div>\r\n            </form>\r\n        </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Create"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAAQC,IAAI,EAAEC,WAAW,QAAO,kBAAkB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAElD,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EACd,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC;IACjCU,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,GAAG,EAAE,EAAE;IACPC,MAAM,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,SAASY,YAAYA,CAACC,CAAC,EAAC;IACpBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElBhB,KAAK,CAACiB,IAAI,CAAC,WAAW,EAAEV,MAAM,CAAC,CAC9BW,IAAI,CAAEC,GAAG,IAAG;MAETN,QAAQ,CAAC,GAAG,CAAC;MACbO,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;IACpB,CAAC,CAAC,CACDG,KAAK,CAAEC,GAAG,IAAGH,OAAO,CAACC,GAAG,CAACE,GAAG,CAAC,CAAC;EACnC;EACF,oBACEnB,OAAA;IAAKoB,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eAC/CrB,OAAA;MAAKoB,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAChBrB,OAAA;QAAAqB,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpBzB,OAAA;QAAKoB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACvCrB,OAAA,CAACH,IAAI;UAAC6B,EAAE,EAAC,GAAG;UAACN,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eACNzB,OAAA;QAAM2B,QAAQ,EAAEjB,YAAa;QAAAW,QAAA,gBACzBrB,OAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BrB,OAAA;YAAO4B,OAAO,EAAC,MAAM;YAAAP,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClCzB,OAAA;YAAO6B,IAAI,EAAC,MAAM;YAACxB,IAAI,EAAC,MAAM;YAACyB,QAAQ;YAACC,QAAQ,EAAGpB,CAAC,IAAIP,SAAS,CAAC;cAAC,GAAGD,MAAM;cAAEE,IAAI,EAAEM,CAAC,CAACqB,MAAM,CAACC;YAAK,CAAC;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvG,CAAC,eACNzB,OAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BrB,OAAA;YAAO4B,OAAO,EAAC,OAAO;YAAAP,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpCzB,OAAA;YAAO6B,IAAI,EAAC,OAAO;YAACxB,IAAI,EAAC,OAAO;YAACyB,QAAQ;YAACC,QAAQ,EAAGpB,CAAC,IAAIP,SAAS,CAAC;cAAC,GAAGD,MAAM;cAAEG,KAAK,EAAEK,CAAC,CAACqB,MAAM,CAACC;YAAK,CAAC;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1G,CAAC,eACNzB,OAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BrB,OAAA;YAAO4B,OAAO,EAAC,QAAQ;YAAAP,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtCzB,OAAA;YAAO6B,IAAI,EAAC,MAAM;YAACxB,IAAI,EAAC,QAAQ;YAACyB,QAAQ;YAACC,QAAQ,EAAGpB,CAAC,IAAIP,SAAS,CAAC;cAAC,GAAGD,MAAM;cAAEK,MAAM,EAAEG,CAAC,CAACqB,MAAM,CAACC;YAAK,CAAC;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3G,CAAC,eACNzB,OAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BrB,OAAA;YAAO4B,OAAO,EAAC,KAAK;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChCzB,OAAA;YAAO6B,IAAI,EAAC,QAAQ;YAACxB,IAAI,EAAC,KAAK;YAACyB,QAAQ;YAACC,QAAQ,EAAGpB,CAAC,IAAIP,SAAS,CAAC;cAAC,GAAGD,MAAM;cAAEI,GAAG,EAAEI,CAAC,CAACqB,MAAM,CAACC;YAAK,CAAC;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvG,CAAC,eACNzB,OAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC5BrB,OAAA;YAAQ6B,IAAI,EAAC,QAAQ;YAACT,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV;AAACvB,EAAA,CApDQD,MAAM;EAAA,QAQMH,WAAW;AAAA;AAAAoC,EAAA,GARvBjC,MAAM;AAsDf,eAAeA,MAAM;AAAA,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}