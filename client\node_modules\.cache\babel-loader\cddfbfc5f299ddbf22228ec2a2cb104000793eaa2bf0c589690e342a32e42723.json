{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\client\\\\src\\\\elements\\\\Create.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport axios from \"axios\";\nimport { Link, useNavigate } from \"react-router-dom\"; //navigations within application\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Create() {\n  _s();\n  const [values, setValues] = useState({\n    name: \"\",\n    email: \"\",\n    gender: \"\",\n    age: \"\"\n  });\n  const navigate = useNavigate();\n  function handleSubmit(e) {\n    e.preventDefault();\n    axios.post(\"/add_user\", values).then(res => {\n      //navigate to home after succesfull data stored\n\n      navigate(\"/\");\n      console.log(res);\n    }).catch(err => {\n      console.log(err);\n    });\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container vh-100 vw-100 bg-primary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Add Student\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d=flex justify-content-end\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"btn btn-primary\",\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"name\",\n            children: \"Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              name: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              email: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"gender\",\n            children: \"Gender\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"gender\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              gender: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"age\",\n            children: \"Age\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            name: \"age\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              age: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-success\",\n            children: \"Save\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n}\n_s(Create, \"o3TQjmH2uRVkQxgsyq6/gFhZAz4=\", false, function () {\n  return [useNavigate];\n});\n_c = Create;\nexport default Create;\nvar _c;\n$RefreshReg$(_c, \"Create\");", "map": {"version": 3, "names": ["React", "useState", "axios", "Link", "useNavigate", "jsxDEV", "_jsxDEV", "Create", "_s", "values", "set<PERSON><PERSON><PERSON>", "name", "email", "gender", "age", "navigate", "handleSubmit", "e", "preventDefault", "post", "then", "res", "console", "log", "catch", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onSubmit", "htmlFor", "type", "required", "onChange", "target", "value", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/client/src/elements/Create.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport axios from \"axios\";\r\nimport { Link, useNavigate } from \"react-router-dom\"; //navigations within application\r\n\r\nfunction Create() {\r\n  const [values, setValues] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    gender: \"\",\r\n    age: \"\",\r\n  });\r\n\r\n  const navigate = useNavigate();\r\n\r\n  function handleSubmit(e) {\r\n    e.preventDefault();\r\n    axios\r\n      .post(\"/add_user\", values)\r\n      .then((res) => {\r\n        //navigate to home after succesfull data stored\r\n\r\n        navigate(\"/\");\r\n        console.log(res);\r\n      })\r\n      .catch((err) => {\r\n        console.log(err);\r\n      });\r\n  }\r\n\r\n  return (\r\n    <div className=\"container vh-100 vw-100 bg-primary\">\r\n      <div className=\"row\">\r\n        <h3>Add Student</h3>\r\n        <div className=\"d=flex justify-content-end\">\r\n          <Link to=\"/\" className=\"btn btn-primary\">\r\n            Home\r\n          </Link>\r\n        </div>\r\n\r\n        <form onSubmit={handleSubmit}>\r\n          <div className=\"form-group my-3\">\r\n            <label htmlFor=\"name\">Name</label>\r\n            <input\r\n              type=\"text\"\r\n              name=\"name\"\r\n              required\r\n              onChange={(e) => setValues({ ...values, name: e.target.value })}\r\n            />\r\n          </div>\r\n          <div className=\"form-group my-3\">\r\n            <label htmlFor=\"email\">Email</label>\r\n            <input\r\n              type=\"email\"\r\n              name=\"email\"\r\n              required\r\n              onChange={(e) => setValues({ ...values, email: e.target.value })}\r\n            />\r\n          </div>\r\n          <div className=\"form-group my-3\">\r\n            <label htmlFor=\"gender\">Gender</label>\r\n            <input\r\n              type=\"text\"\r\n              name=\"gender\"\r\n              required\r\n              onChange={(e) => setValues({ ...values, gender: e.target.value })}\r\n            />\r\n          </div>\r\n          <div className=\"form-group my-3\">\r\n            <label htmlFor=\"age\">Age</label>\r\n            <input\r\n              type=\"number\"\r\n              name=\"age\"\r\n              required\r\n              onChange={(e) => setValues({ ...values, age: e.target.value })}\r\n            />\r\n          </div>\r\n          <div className=\"form-group my-3\">\r\n            <button type=\"submit\" className=\"btn btn-success\">\r\n              Save\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Create;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEtD,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC;IACnCU,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,GAAG,EAAE;EACP,CAAC,CAAC;EAEF,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,SAASY,YAAYA,CAACC,CAAC,EAAE;IACvBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBhB,KAAK,CACFiB,IAAI,CAAC,WAAW,EAAEV,MAAM,CAAC,CACzBW,IAAI,CAAEC,GAAG,IAAK;MACb;;MAEAN,QAAQ,CAAC,GAAG,CAAC;MACbO,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;IAClB,CAAC,CAAC,CACDG,KAAK,CAAEC,GAAG,IAAK;MACdH,OAAO,CAACC,GAAG,CAACE,GAAG,CAAC;IAClB,CAAC,CAAC;EACN;EAEA,oBACEnB,OAAA;IAAKoB,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eACjDrB,OAAA;MAAKoB,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClBrB,OAAA;QAAAqB,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpBzB,OAAA;QAAKoB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCrB,OAAA,CAACH,IAAI;UAAC6B,EAAE,EAAC,GAAG;UAACN,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAEzC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENzB,OAAA;QAAM2B,QAAQ,EAAEjB,YAAa;QAAAW,QAAA,gBAC3BrB,OAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BrB,OAAA;YAAO4B,OAAO,EAAC,MAAM;YAAAP,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClCzB,OAAA;YACE6B,IAAI,EAAC,MAAM;YACXxB,IAAI,EAAC,MAAM;YACXyB,QAAQ;YACRC,QAAQ,EAAGpB,CAAC,IAAKP,SAAS,CAAC;cAAE,GAAGD,MAAM;cAAEE,IAAI,EAAEM,CAAC,CAACqB,MAAM,CAACC;YAAM,CAAC;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNzB,OAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BrB,OAAA;YAAO4B,OAAO,EAAC,OAAO;YAAAP,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpCzB,OAAA;YACE6B,IAAI,EAAC,OAAO;YACZxB,IAAI,EAAC,OAAO;YACZyB,QAAQ;YACRC,QAAQ,EAAGpB,CAAC,IAAKP,SAAS,CAAC;cAAE,GAAGD,MAAM;cAAEG,KAAK,EAAEK,CAAC,CAACqB,MAAM,CAACC;YAAM,CAAC;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNzB,OAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BrB,OAAA;YAAO4B,OAAO,EAAC,QAAQ;YAAAP,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtCzB,OAAA;YACE6B,IAAI,EAAC,MAAM;YACXxB,IAAI,EAAC,QAAQ;YACbyB,QAAQ;YACRC,QAAQ,EAAGpB,CAAC,IAAKP,SAAS,CAAC;cAAE,GAAGD,MAAM;cAAEI,MAAM,EAAEI,CAAC,CAACqB,MAAM,CAACC;YAAM,CAAC;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNzB,OAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BrB,OAAA;YAAO4B,OAAO,EAAC,KAAK;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChCzB,OAAA;YACE6B,IAAI,EAAC,QAAQ;YACbxB,IAAI,EAAC,KAAK;YACVyB,QAAQ;YACRC,QAAQ,EAAGpB,CAAC,IAAKP,SAAS,CAAC;cAAE,GAAGD,MAAM;cAAEK,GAAG,EAAEG,CAAC,CAACqB,MAAM,CAACC;YAAM,CAAC;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNzB,OAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BrB,OAAA;YAAQ6B,IAAI,EAAC,QAAQ;YAACT,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAElD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvB,EAAA,CAjFQD,MAAM;EAAA,QAQIH,WAAW;AAAA;AAAAoC,EAAA,GARrBjC,MAAM;AAmFf,eAAeA,MAAM;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}