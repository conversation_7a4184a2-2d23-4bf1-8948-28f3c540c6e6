{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\client\\\\src\\\\elements\\\\Create.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Create() {\n  _s();\n  const [values, setValues] = useState({\n    name: '',\n    email: '',\n    age: '',\n    gender: ''\n  });\n  const navigate = useNavigate();\n  function handleSubmit(e) {\n    e.preventDefault();\n    console.log('Submitting values:', values);\n    axios.post('/add_user', values).then(res => {\n      console.log('Success response:', res.data);\n      navigate('/');\n    }).catch(err => {\n      var _err$response;\n      console.error('Error occurred:', err);\n      console.error('Error response:', (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data);\n    });\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container vh-100 vw-100 bg-primary\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Add Student\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"btn btn-success\",\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"name\",\n            children: \"Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              name: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              email: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"gender\",\n            children: \"Gender\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"gender\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              gender: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"age\",\n            children: \"Age\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            name: \"age\",\n            required: true,\n            onChange: e => setValues({\n              ...values,\n              age: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group my-3\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-success\",\n            children: \"Save\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n}\n_s(Create, \"oSC9tnkofyENRCbjy4y+RM0BIdU=\", false, function () {\n  return [useNavigate];\n});\n_c = Create;\nexport default Create;\nvar _c;\n$RefreshReg$(_c, \"Create\");", "map": {"version": 3, "names": ["React", "useState", "axios", "Link", "useNavigate", "jsxDEV", "_jsxDEV", "Create", "_s", "values", "set<PERSON><PERSON><PERSON>", "name", "email", "age", "gender", "navigate", "handleSubmit", "e", "preventDefault", "console", "log", "post", "then", "res", "data", "catch", "err", "_err$response", "error", "response", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onSubmit", "htmlFor", "type", "required", "onChange", "target", "value", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/client/src/elements/Create.jsx"], "sourcesContent": ["import React, { useState } from 'react'\r\nimport axios from 'axios'\r\nimport {Link, useNavigate} from 'react-router-dom'\r\n\r\nfunction Create() {\r\n    const [values, setValues] = useState({\r\n        name: '',\r\n        email: '',\r\n        age: '',\r\n        gender: ''\r\n    })\r\n\r\n    const navigate = useNavigate()\r\n\r\n    function handleSubmit(e){\r\n        e.preventDefault()\r\n\r\n        console.log('Submitting values:', values)\r\n\r\n        axios.post('/add_user', values)\r\n        .then((res)=>{\r\n            console.log('Success response:', res.data)\r\n            navigate('/')\r\n        })\r\n        .catch((err)=>{\r\n            console.error('Error occurred:', err)\r\n            console.error('Error response:', err.response?.data)\r\n        })\r\n    }\r\n  return (\r\n    <div className='container vh-100 vw-100 bg-primary'>\r\n        <div className='row'>\r\n            <h3>Add Student</h3>\r\n            <div className='d-flex justify-content-end'>\r\n                <Link to='/' className='btn btn-success'>Home</Link>\r\n            </div>\r\n            <form onSubmit={handleSubmit}>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='name'>Name</label>\r\n                    <input type='text' name='name' required onChange={(e)=> setValues({...values, name: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='email'>Email</label>\r\n                    <input type='email' name='email' required onChange={(e)=> setValues({...values, email: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='gender'>Gender</label>\r\n                    <input type='text' name='gender' required onChange={(e)=> setValues({...values, gender: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <label htmlFor='age'>Age</label>\r\n                    <input type='number' name='age' required onChange={(e)=> setValues({...values, age: e.target.value})} />\r\n                </div>\r\n                <div className='form-group my-3'>\r\n                    <button type='submit' className='btn btn-success'>Save</button>\r\n                </div>\r\n            </form>\r\n        </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Create"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAAQC,IAAI,EAAEC,WAAW,QAAO,kBAAkB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAElD,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EACd,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC;IACjCU,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,GAAG,EAAE,EAAE;IACPC,MAAM,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,SAASY,YAAYA,CAACC,CAAC,EAAC;IACpBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEX,MAAM,CAAC;IAEzCP,KAAK,CAACmB,IAAI,CAAC,WAAW,EAAEZ,MAAM,CAAC,CAC9Ba,IAAI,CAAEC,GAAG,IAAG;MACTJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEG,GAAG,CAACC,IAAI,CAAC;MAC1CT,QAAQ,CAAC,GAAG,CAAC;IACjB,CAAC,CAAC,CACDU,KAAK,CAAEC,GAAG,IAAG;MAAA,IAAAC,aAAA;MACVR,OAAO,CAACS,KAAK,CAAC,iBAAiB,EAAEF,GAAG,CAAC;MACrCP,OAAO,CAACS,KAAK,CAAC,iBAAiB,GAAAD,aAAA,GAAED,GAAG,CAACG,QAAQ,cAAAF,aAAA,uBAAZA,aAAA,CAAcH,IAAI,CAAC;IACxD,CAAC,CAAC;EACN;EACF,oBACElB,OAAA;IAAKwB,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eAC/CzB,OAAA;MAAKwB,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAChBzB,OAAA;QAAAyB,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpB7B,OAAA;QAAKwB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACvCzB,OAAA,CAACH,IAAI;UAACiC,EAAE,EAAC,GAAG;UAACN,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eACN7B,OAAA;QAAM+B,QAAQ,EAAErB,YAAa;QAAAe,QAAA,gBACzBzB,OAAA;UAAKwB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BzB,OAAA;YAAOgC,OAAO,EAAC,MAAM;YAAAP,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClC7B,OAAA;YAAOiC,IAAI,EAAC,MAAM;YAAC5B,IAAI,EAAC,MAAM;YAAC6B,QAAQ;YAACC,QAAQ,EAAGxB,CAAC,IAAIP,SAAS,CAAC;cAAC,GAAGD,MAAM;cAAEE,IAAI,EAAEM,CAAC,CAACyB,MAAM,CAACC;YAAK,CAAC;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvG,CAAC,eACN7B,OAAA;UAAKwB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BzB,OAAA;YAAOgC,OAAO,EAAC,OAAO;YAAAP,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpC7B,OAAA;YAAOiC,IAAI,EAAC,OAAO;YAAC5B,IAAI,EAAC,OAAO;YAAC6B,QAAQ;YAACC,QAAQ,EAAGxB,CAAC,IAAIP,SAAS,CAAC;cAAC,GAAGD,MAAM;cAAEG,KAAK,EAAEK,CAAC,CAACyB,MAAM,CAACC;YAAK,CAAC;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1G,CAAC,eACN7B,OAAA;UAAKwB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BzB,OAAA;YAAOgC,OAAO,EAAC,QAAQ;YAAAP,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtC7B,OAAA;YAAOiC,IAAI,EAAC,MAAM;YAAC5B,IAAI,EAAC,QAAQ;YAAC6B,QAAQ;YAACC,QAAQ,EAAGxB,CAAC,IAAIP,SAAS,CAAC;cAAC,GAAGD,MAAM;cAAEK,MAAM,EAAEG,CAAC,CAACyB,MAAM,CAACC;YAAK,CAAC;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3G,CAAC,eACN7B,OAAA;UAAKwB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BzB,OAAA;YAAOgC,OAAO,EAAC,KAAK;YAAAP,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChC7B,OAAA;YAAOiC,IAAI,EAAC,QAAQ;YAAC5B,IAAI,EAAC,KAAK;YAAC6B,QAAQ;YAACC,QAAQ,EAAGxB,CAAC,IAAIP,SAAS,CAAC;cAAC,GAAGD,MAAM;cAAEI,GAAG,EAAEI,CAAC,CAACyB,MAAM,CAACC;YAAK,CAAC;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvG,CAAC,eACN7B,OAAA;UAAKwB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC5BzB,OAAA;YAAQiC,IAAI,EAAC,QAAQ;YAACT,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV;AAAC3B,EAAA,CAxDQD,MAAM;EAAA,QAQMH,WAAW;AAAA;AAAAwC,EAAA,GARvBrC,MAAM;AA0Df,eAAeA,MAAM;AAAA,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}