{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Siyoga SDP\\\\Simple Project\\\\client\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter, Routes, Route } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "jsxDEV", "_jsxDEV", "App", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Siyoga SDP/Simple Project/client/src/App.js"], "sourcesContent": ["import React from 'react'\nimport {BrowserRouter, Routes, Route} from 'react-router-dom'\n\nfunction App() {\n  return (\n    <div>\n      \n    </div>\n  )\n}\n\nexport default App\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAAQC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAO,kBAAkB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE7D,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEK,CAAC;AAEV;AAACC,EAAA,GANQL,GAAG;AAQZ,eAAeA,GAAG;AAAA,IAAAK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}