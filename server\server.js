const express = require("express")
const mysql = require("mysql2")
const cors = require("cors")
const path = require("path")

const app = express()

app.use(express.static(path.join(__dirname, "public")))

app.use(cors())
app.use(express.json())

const port = 5000

const db = mysql.createConnection({
    host: "localhost",
    user: "root",
    password: "",
    database: "students"
})


app.post('/add_user', (req, res)=>{
sql = "INSERT INTO student_details (`name`,`email`,`age`,`gender`) VALUES (?, ?, ?, ?)";
const values =[
    req.body.name,
    req.body.email,
    req.body.age,
    req.body.gender
]
db.query(sql,values, (err,result)=>{
    if(err) return res.json({message: 'Something unexpented has occured' + err})
return res.json({success: "User added successfully"})
})
})



app.get('/users', (req, res) => {
    const sql = "SELECT * FROM student_details";
    db.query(sql, (err, result) => {
        if(err) return res.json({message: 'Something unexpected has occurred: ' + err})
        return res.json(result)
    })
})

app.get("/get_student/:id", (req, res) => {
    const sql = "SELECT * FROM student_details WHERE id = ?";
    const id = req.params.id;
    db.query(sql, [id], (err, result) => {
        if(err) return res.json({message: 'Something unexpected has occurred: ' + err})
        return res.json(result)
    })
}

)

// Add/Edit user route
app.post('/edit_user/:id', (req, res) => {
    const id = req.params.id;
    const { name, email, age, gender } = req.body;
    const sql = "UPDATE student_details SET name = ?, email = ?, age = ?, gender = ? WHERE id = ?";
    db.query(sql, [name, email, age, gender, id], (err, result) => {
        if (err) return res.json({ message: 'Something unexpected has occurred: ' + err });
        return res.json({ success: "User updated successfully" });
    });
});

// Delete user route
app.delete('/delete_user/:id', (req, res) => {
    const id = req.params.id;
    const sql = "DELETE FROM student_details WHERE id = ?";
    db.query(sql, [id], (err, result) => {
        if (err) return res.json({ message: 'Something unexpected has occurred: ' + err });
        return res.json({ success: "User deleted successfully" });
    });
});



db.connect((err) => {
    if (err) {
        console.log("Error connecting to MySQL database")
    } else {
        console.log("Connected to MySQL database")
    }
})

app.listen(port, () => {
})

app.listen(port, () => {
    console.log(`Server is running on port ${port}`)
})