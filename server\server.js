const express = require("express")
const mysql = require("mysql")
const cors = require("cors")
const path = require("path")

const app = express()

app.use(express.static(path.join(__dirname, "public")))

app.use(cors())
app.use(express.json())

const port = 5000

const db = mysql.createConnection({
    host: "localhost",
    user: "root",
    password: "",
    database: "students"
})


// Get all users
app.get('/users', (req, res) => {
    const sql = "SELECT * FROM student_details";
    db.query(sql, (err, result) => {
        if(err) return res.json({message: 'Something unexpected has occurred: ' + err})
        return res.json(result)
    })
})

app.post('/add_user', (req, res)=>{
    console.log('Received request body:', req.body)

    const sql = "INSERT INTO student_details (`name`,`email`,`age`,`gender`) VALUES (?, ?, ?, ?)";
    const values =[
        req.body.name,
        req.body.email,
        req.body.age,
        req.body.gender
    ]

    console.log('SQL query:', sql)
    console.log('Values:', values)

    db.query(sql, values, (err,result)=>{
        if(err) {
            console.error('Database error:', err)
            return res.json({message: 'Something unexpected has occurred: ' + err})
        }
        console.log('Insert successful:', result)
        return res.json({success: "User added successfully"})
    })
})





db.connect((err) => {
    if (err) {
        console.log("Error connecting to MySQL database:", err)
    } else {
        console.log("Connected to MySQL database")

        // Create table if it doesn't exist
        const createTableSQL = `
            CREATE TABLE IF NOT EXISTS student_details (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) NOT NULL,
                age INT NOT NULL,
                gender VARCHAR(50) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `;

        db.query(createTableSQL, (err, result) => {
            if (err) {
                console.error("Error creating table:", err)
            } else {
                console.log("Table 'student_details' is ready")
            }
        })
    }
})

app.listen(port, () => {
    console.log(`Server is running on port ${port}`)
})