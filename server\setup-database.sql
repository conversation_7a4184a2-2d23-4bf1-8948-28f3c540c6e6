-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS students;

-- Use the students database
USE students;

-- Create the student_details table if it doesn't exist
CREATE TABLE IF NOT EXISTS student_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    age INT NOT NULL,
    gender VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Show the table structure
DESCRIBE student_details;
